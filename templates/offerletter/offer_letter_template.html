
{% load humanize %}
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Offer Letter – OPTIVEN</title>
    <style>
      @page {
        size: A4;
        margin: 20mm;
      }

      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #fff;
      }

      .offer-container {
        width: 100%;
        max-width: 700px;
        margin: 0 auto;
        padding: 20px;
        border: 1px solid #ccc;
        border-radius: 5px;
        background-color: transparent;
        position: relative;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      }

      .offer-container::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("https://portal.optiven.co.ke/assets/images/logo.jpg");
        background-size: 50%;
        background-position: center;
        background-repeat: no-repeat;
        opacity: 0.05;
        z-index: -1;
      }

      .header, .footer {
        text-align: center;
      }

      .header img {
        max-width: 200px;
        margin-bottom: 10px;
      }

      .section {
        margin-top: 30px;
      }

      .section h3 {
        margin-bottom: 10px;
        border-bottom: 1px solid #ccc;
        padding-bottom: 5px;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
      }

      table th, table td {
        text-align: left;
        padding: 8px;
        border-bottom: 1px solid #ddd;
      }

      .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
        padding-top: 20px;
      }

      .stamp {
        top: 0%;
        width: 40%;
        position: relative;
        text-align: center;
      }

      .stamp img {
        width: 100%;
        transform: rotate(-15deg);
        opacity: 0.8;
      }

      .date-overlay {
        position: absolute;
        top: 42%;
        left: 16%;
        color: red;
        transform: rotate(-15deg);
        font-weight: bold;
      }
    .tabledeco {
        font-size: 12px;
    }

    .tabledeco th, .tabledeco td {
        padding: 4px 6px;
        margin: 0;
    }

    .tabledeco tr {
        margin-bottom: 2px;
    }
    </style>
  </head>
  <body>
    <div class="offer-container">
      <div class="header">
        <img src="https://portal.optiven.co.ke/dashboard/assets/images/logobig.png" alt="Optiven Logo">
        <h2>Offer Letter Result</h2>
       
 
        <p><b>Date:</b> {{ date|slice:":10" }} at {{ date|slice:"11:16" }}</p>
        <p><b>Booking ID:</b> {{ booking_id }}</p>
        <p><b>Customer Type:</b> {{ customer_type | title }}</p>
      </div>
      {% if individuals %}
      <div class="section">
        <h3>Client Information</h3>
        <table>
          <tr><th>Full Name</th><td>{{ individuals.0.first_name }} {{ individuals.0.last_name }}</td></tr>
          <tr><th>Phone</th><td>{{ individuals.0.country_code }}{{ individuals.0.phone }}</td></tr>
          <tr><th>Email</th><td>{{ individuals.0.email }}</td></tr>
          <tr><th>National ID</th><td>{{ individuals.0.national_id }}</td></tr>
          <tr><th>KRA PIN</th><td>{{ individuals.0.KRA_Pin }}</td></tr>
          <tr><th>City/Country</th><td>{{ individuals.0.city }}, {{ individuals.0.country }}</td></tr>
          <tr><th>Preferred Contact</th><td>{{ individuals.0.preferred_contact | join:", " }}</td></tr>
        </table>
      </div>
      {% endif %}
    {% if partners %}
    <div class="section">
      <h3>Partners Information</h3>
    {% for partner in partners %}
    <table class="tabledeco">
      <tr><th>Partner #</th><td>{{ forloop.counter }}</td></tr>
      <tr><th>Full Name</th><td>{{ partner.first_name }} {{ partner.last_name }}</td></tr>
      <tr><th>Phone</th><td>{{ partner.country_code }}{{ partner.phone }}</td></tr>
      <tr><th>Email</th><td>{{ partner.email }}</td></tr>
      <tr><th>National ID</th><td>{{ partner.national_id }}</td></tr>
      <tr><th>City/Country</th><td>{{ partner.city }}, {{ partner.country }}</td></tr>
      <tr><th>Preferred Contact</th><td>{{ partner.preferred_contact|join:", " }}</td></tr>
    </table>
    <br>
    {% endfor %}
    </div>
    {% endif %}


    {% if companies %}
    <div class="section">
      <h3>Company Information</h3>
      {% for company in companies %}
      <table>
        <tr><th>Company Name</th><td>{{ company.company_name }}</td></tr>
        <tr><th>Registration Number</th><td>{{ company.company_registration_number }}</td></tr>
        <tr><th>KRA PIN</th><td>{{ company.company_kra }}</td></tr>
        <tr><th>Phone</th><td>{{ company.company_country_code }}{{ company.phone }}</td></tr>
        <tr><th>Email</th><td>{{ company.email }}</td></tr>
        <tr><th>Address</th><td>{{ company.address }}</td></tr>
        <tr><th>City/Country</th><td>{{ company.city }}, {{ company.country }}</td></tr>
        <tr><th>Preferred Contact</th><td>{{ company.preferred_contact|join:", " }}</td></tr>
      </table>
      {% if company.directors %}
      <div style="margin-top: 15px;">
        <h4>Directors</h4>
        <table>
          <tr>
            <th>#</th>
            <th>Full Name</th>
            <th>Phone</th>
            <th>Email</th>
            <th>National ID</th>
          </tr>
          {% for director in company.directors %}
          <tr>
            <td>{{ forloop.counter }}</td>
            <td>{{ director.first_name }} {{ director.last_name }}</td>
            <td>{{ director.country_codes }}{{ director.phone }}</td>
            <td>{{ director.email }}</td>
            <td>{{ director.national_id }}</td>
          </tr>
          {% endfor %}
        </table>
      </div>
      {% endif %}
      <br>
      {% endfor %}
    </div>
    {% endif %}


    {% if groups %}
    <div class="section">
        <h3>Group Information</h3>
        {% for group in groups %}
        <table>
            <tr><th>Group Name</th><td>{{ group.group_name }}</td></tr>
            
            <tr><th>KRA PIN</th><td>{{ group.Group_KRA_PIN }}</td></tr>
            <tr><th>Phone</th><td>{{ group.group_code }}{{ group.group_phone }}</td></tr>
            <tr><th>Email</th><td>{{ group.group_email }}</td></tr>
            <tr><th>Address</th><td>{{ group.address }}</td></tr>
            <tr><th>City/Country</th><td>{{ group.Group_city }}, {{ group.Group_country }}</td></tr>
            
        </table>
        {% if group.members %}
        <div style="margin-top: 15px;">
            <h4>Members</h4>
            <table>
                <tr>
                    <th>#</th>
                    <th>Full Name</th>
                    <th>Phone</th>
                    <th>Email</th>
                    <th>National ID</th>
                </tr>
                {% for member in group.members %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td>{{ member.first_name }} {{ member.last_name }}</td>
                    <td>{{ member.country_codes }}{{ member.phone }}</td>
                    <td>{{ member.email }}</td>
                    <td>{{ member.national_id }}</td>
                </tr>
                {% endfor %}
            </table>
        </div>
        {% endif %}
        <br>
        {% endfor %}
    </div>
    {% endif %}
    {% if individuals %}
      <div class="section">
        <h3>Next of Kin</h3>
        <table>
          <tr><th>Full Name</th><td>{{ individuals.0.next_of_kin.0.full_name }}</td></tr>
          <tr><th>Relationship</th><td>{{ individuals.0.next_of_kin.0.relationship }}</td></tr>
          <tr><th>Phone</th><td>{{ individuals.0.next_of_kin.0.country_code }}{{ individuals.0.next_of_kin.0.phone }}</td></tr>
          <tr><th>Email</th><td>{{ individuals.0.next_of_kin.0.email }}</td></tr>
        </table>
      </div>
    {% endif %}

      <div class="section">
        <h3>Plot & Payment Information</h3>
        <table>
          <tr>
  <th>Plot No</th><td>{{ plot_number }}</td>
</tr>
<tr>
  <th>Total Cash Price</th>
  <td>KSH {{ payment_plan.0.total_cash_price|floatformat:2|intcomma }}</td>
</tr>
<tr>
  <th>Deposit</th>
  <td>KSH {{ payment_plan.0.deposit|floatformat:2|intcomma }}</td>
</tr>
<tr>
  <th>Monthly Installments</th>
  <td>KSH {{ payment_plan.0.monthly_installments|floatformat:2|intcomma }}</td>
</tr>
<tr>
  <th>Installments Count</th>
  <td>{{ payment_plan.0.no_of_instalments|intcomma }}</td>
</tr>

        </table>
      </div>

      <div class="section">
        <h3>Terms & Conditions</h3>
        <ol>
          <li>This letter of offer does not constitute a Sale Agreement.</li>
          <li>
            Any amount paid less than the stipulated deposit will be taken as a booking fee and <b>if the purchaser does not top up the balance within 14 days the booked plot will be opened up for sale and 20% of the paid amount will be deducted as administrative cost.</b>
          </li>
          
          <li>
            In the event that the Purchaser opts to withdraw from the purchase for any reason after payment of the Purchase Price or any portion thereof, the Vendor shall refund the amount paid by the Purchaser less twenty percent (20%) of the Purchase Price as administrative and processing costs. The refund shall be effected within one hundred and eighty (180) days from the date of the Purchaser's written request for refund.
            <br><br>
            For avoidance of doubt:
            <ol type="1">
              <li>A refund option shall not be available in the event the Purchase Price has been paid in full and the transfer process is underway;</li>
              <li>The twenty percent (20%) deduction shall be calculated on the total Purchase Price of the Plot, not on the amount paid.</li>
              <li>The Purchaser shall not be entitled to any interest, profit, or appreciation accrued during the holding period prior to refund.</li>
            </ol>
          </li>
          <li>
            The purchaser should sign the Sale Agreement with full particulars within thirty (30) working days of execution of this letter of offer.
          </li>
          <li>
            The Sale Agreement will be prepared by the vendor's advocates and will incorporate the Law Society of Kenya Conditions of sale (2015 Edition) in so far as they are not inconsistent with this letter of offer.
          </li>
          <li>
            A payment plan shall be provided by the vendor at the point of sale and the same shall not be tied to the execution of the Sale Agreement.
          </li>
          <li>
            The parties hereby agree that by submitting this form you accept the terms and conditions that constitutes a valid contract between the parties.
          </li>
          <li>
            All Payments to be made to ONLY the Optiven bank accounts or <b>official Optiven Paybill, 921225</b>, and the purchaser should request for a receipt within 24hrs.
          </li>
          <li>
            All the <b>MONTHLY INSTALLMENTS MUST</b> be remitted on or before the due date based on the start date.
          </li>
        </ol>
         
  <p>
  <b>Accepted on:</b>
  {% if terms_conditions.0.acceptance_date %}
    {% with terms_conditions.0.acceptance_date as date_str %}
  {% if date_str %}
    {{ date_str|slice:":10" }} at {{ date_str|slice:"11:16" }}
  {% else %}
    Not yet accepted
  {% endif %}
{% endwith %}

  {% else %}
    Not yet accepted
  {% endif %}
</p>

      </div>

      <div class="signature-section">
        <div>
          <p>Signed By:</p><br /><br />
          <p> Ian Wachira
          <p><b>Optiven Legal Officer</b></p>
        </div>
        <div class="stamp">
          <img src="https://portal.optiven.co.ke/assets/images/opt_stamp.png" alt="Stamp" />
          <div class="date-overlay">DATE: {{ date|slice:":10" }} {{ date|slice:"11:16" }}
            </div>
        </div>
      </div>

      <div class="footer">
        <p>Thank you for choosing Optiven Ltd. Together we inspire possibilities!</p>
      </div>
    </div>
  </body>
</html>
