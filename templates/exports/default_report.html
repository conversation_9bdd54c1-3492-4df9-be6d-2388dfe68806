<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <style>
        @page {
            size: A4;
            margin: 2cm;
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 10px;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            font-size: 12px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #4472C4;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #4472C4;
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        
        .header .subtitle {
            color: #666;
            margin: 10px 0;
            font-size: 14px;
        }
        
        .meta-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
            font-size: 11px;
        }
        
        .meta-info div {
            flex: 1;
        }
        
        .meta-info strong {
            color: #4472C4;
        }
        
        .filters-section {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            border-radius: 3px;
        }
        
        .filters-section h3 {
            margin: 0 0 10px 0;
            color: #856404;
            font-size: 14px;
        }
        
        .filters-list {
            font-size: 11px;
            color: #856404;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            font-size: 11px;
        }
        
        .data-table th {
            background-color: #4472C4;
            color: white;
            padding: 12px 8px;
            text-align: left;
            font-weight: bold;
            border: 1px solid #ddd;
        }
        
        .data-table td {
            padding: 8px;
            border: 1px solid #ddd;
            vertical-align: top;
            word-wrap: break-word;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        .data-table tr:hover {
            background-color: #e8f4fd;
        }
        
        .no-data {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
            font-size: 14px;
        }
        
        .summary {
            margin-top: 30px;
            padding: 15px;
            background-color: #d1ecf1;
            border-left: 4px solid #bee5eb;
            border-radius: 3px;
        }
        
        .summary h3 {
            margin: 0 0 10px 0;
            color: #0c5460;
            font-size: 14px;
        }
        
        .summary-stats {
            display: flex;
            justify-content: space-between;
            font-size: 12px;
        }
        
        .summary-stats div {
            text-align: center;
            flex: 1;
        }
        
        .summary-stats .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #0c5460;
        }
        
        .summary-stats .stat-label {
            color: #0c5460;
            font-size: 11px;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #999;
            border-top: 1px solid #eee;
            padding-top: 15px;
        }
        
        /* Responsive table for better mobile viewing */
        @media screen and (max-width: 768px) {
            .data-table {
                font-size: 10px;
            }
            
            .data-table th,
            .data-table td {
                padding: 6px 4px;
            }
        }
        
        /* Print styles */
        @media print {
            .data-table {
                page-break-inside: auto;
            }
            
            .data-table tr {
                page-break-inside: avoid;
                page-break-after: auto;
            }
            
            .header, .summary {
                page-break-after: avoid;
            }
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <h1>{{ title }}</h1>
        <div class="subtitle">Data Export Report</div>
    </div>
    
    <!-- Meta Information -->
    <div class="meta-info">
        <div>
            <strong>Generated:</strong><br>
            {{ timestamp|date:"F d, Y" }}<br>
            {{ timestamp|date:"g:i A" }}
        </div>
        <div>
            <strong>Total Records:</strong><br>
            {{ total_records|default:"0" }}
        </div>
        <div>
            <strong>Export Type:</strong><br>
            {{ export_type|upper }}
        </div>
        {% if user %}
        <div>
            <strong>Generated By:</strong><br>
            {{ user.get_full_name|default:user.username|default:"Anonymous" }}
        </div>
        {% endif %}
    </div>
    
    <!-- Applied Filters Section -->
    {% if filters_applied %}
    <div class="filters-section">
        <h3>Applied Filters</h3>
        <div class="filters-list">
            {% for key, value in filters_applied.items %}
                <strong>{{ key|title }}:</strong> {{ value }}{% if not forloop.last %} | {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Data Table -->
    {% if data %}
        <table class="data-table">
            <thead>
                <tr>
                    {% for key in data.0.keys %}
                        <th>{{ key|title }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for item in data %}
                    <tr>
                        {% for key, value in item.items %}
                            <td>
                                {% if value %}
                                    {% if value|length > 100 %}
                                        {{ value|truncatechars:100 }}
                                    {% else %}
                                        {{ value }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Summary Section -->
        <div class="summary">
            <h3>Export Summary</h3>
            <div class="summary-stats">
                <div>
                    <div class="stat-value">{{ total_records }}</div>
                    <div class="stat-label">Total Records</div>
                </div>
                <div>
                    <div class="stat-value">{{ data|length }}</div>
                    <div class="stat-label">Records Exported</div>
                </div>
                <div>
                    <div class="stat-value">{{ filters_applied|length|default:"0" }}</div>
                    <div class="stat-label">Filters Applied</div>
                </div>
            </div>
        </div>
    {% else %}
        <div class="no-data">
            <h3>No Data Available</h3>
            <p>No records match the applied filters or the dataset is empty.</p>
        </div>
    {% endif %}
    
    <!-- Footer -->
    <div class="footer">
        This report was automatically generated on {{ timestamp|date:"F d, Y \a\t g:i A" }}.
        {% if user %}Generated by {{ user.get_full_name|default:user.username|default:"Anonymous" }}.{% endif %}
    </div>
</body>
</html>