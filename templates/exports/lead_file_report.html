<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lead Files Report - {{ title }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
            @bottom-center {
                content: "Page " counter(page) " of " counter(pages);
                font-size: 9px;
                color: #666;
            }
        }
        
        body {
            font-family: Arial, sans-serif;
            line-height: 1.2;
            color: #333;
            font-size: 11px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 8px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 14px;
            font-weight: bold;
        }
        
        .header .subtitle {
            color: #666;
            margin: 3px 0;
            font-size: 11px;
        }
        
        .meta-info {
            margin-bottom: 8px;
            font-size: 11px;
            line-height: 1.1;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
            font-size: 11px;
        }
        
        .data-table th {
            background: #f5f5f5;
            color: #333;
            padding: 4px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #ddd;
        }
        
        .data-table td {
            padding: 3px 4px;
            border: 1px solid #ddd;
            vertical-align: top;
            word-wrap: break-word;
        }
        
        .data-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .section-title {
            font-weight: bold;
            font-size: 11px;
            margin: 10px 0 5px 0;
            color: #333;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 8px;
            font-size: 11px;
        }
        
        .summary-table td {
            padding: 3px 4px;
            border: 1px solid #ddd;
        }
        
        .summary-label {
            font-weight: bold;
            background-color: #f5f5f5;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            font-size: 11px;
            background: #f9f9f9;
            border: 1px solid #ddd;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="header">
        <h1>Lead Files Report</h1>
        <div class="subtitle">{{ title }}</div>
    </div>
    
    <!-- Meta Information -->
    <div class="meta-info">
        Generated: {{ timestamp|date:"M d, Y g:i A" }} | 
        Total Records: {{ total_records|default:"0" }} | 
        Export Format: {{ export_type|upper }} | 
        Generated By: {% if user %}{{ user.get_full_name|default:user.username|default:"System" }}{% else %}System{% endif %}
    </div>
    
    <!-- Applied Filters Section -->
    {% if filters_applied %}
    <div class="section-title">Applied Filters: 
        {% for key, value in filters_applied.items %}{{ key|title }}: {{ value }}{% if not forloop.last %}, {% endif %}{% endfor %}
    </div>
    {% endif %}
    
    <!-- Data Table -->
    {% if data %}
        <div class="section-title">Lead Files Data ({{ data|length }} records)</div>
        <table class="data-table">
            <thead>
                <tr>
                    {% for key in data.0.keys %}
                        <th>{{ key|title }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for lead_file in data %}
                    <tr>
                        {% for key, value in lead_file.items %}
                            <td>
                                {% if value %}
                                    {% if value|length > 30 %}
                                        {{ value|truncatechars:30 }}
                                    {% else %}
                                        {{ value }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Summary Table -->
        <div class="section-title">Report Summary</div>
        <table class="summary-table">
            <tr>
                <td class="summary-label">Total Lead Files</td>
                <td>{{ total_records }}</td>
                <td class="summary-label">Exported Records</td>
                <td>{{ data|length }}</td>
            </tr>
            <tr>
                <td class="summary-label">Active Filters</td>
                <td>{{ filters_applied|length|default:"0" }}</td>
                <td class="summary-label">Report Period</td>
                <td>{{ timestamp|date:"M Y" }}</td>
            </tr>
        </table>
    {% else %}
        <div class="no-data">
            <strong>No Lead Files Data Available</strong><br>
            No lead file records match the applied filters or the dataset is empty.
        </div>
    {% endif %}
</body>
</html>