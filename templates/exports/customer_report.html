<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Customer Report - {{ title }}</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.2;
            color: #000;
            margin: 0;
            padding: 0;
        }
        
        .header-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            border: 1px solid #000;
        }
        
        .header-table td {
            padding: 4px 6px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .header-table .label {
            font-weight: bold;
            background-color: #f0f0f0;
            width: 25%;
        }
        
        .title {
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 10px;
        }
        
        .filters-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 10px;
            border: 1px solid #000;
        }
        
        .filters-table td {
            padding: 3px 5px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .filters-table .filter-label {
            font-weight: bold;
            background-color: #f0f0f0;
            width: 20%;
        }
        
        .data-table {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .data-table th {
            background-color: #e0e0e0;
            color: #000;
            padding: 4px 3px;
            text-align: left;
            font-weight: bold;
            font-size: 11px;
            border: 1px solid #000;
        }
        
        .data-table td {
            padding: 3px;
            border: 1px solid #000;
            font-size: 11px;
            word-wrap: break-word;
        }
        
        .summary-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            border: 1px solid #000;
        }
        
        .summary-table td {
            padding: 4px 6px;
            border: 1px solid #000;
            font-size: 11px;
        }
        
        .summary-table .summary-label {
            font-weight: bold;
            background-color: #f0f0f0;
            width: 30%;
        }
        
        .no-data {
            text-align: center;
            padding: 20px;
            font-size: 11px;
            border: 1px solid #000;
        }
        
        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin: 10px 0 5px 0;
        }
    </style>
</head>
<body>
    <!-- Header Section -->
    <div class="title">Customer Report - {{ title }}</div>
    
    <!-- Meta Information Table -->
    <table class="header-table">
        <tr>
            <td class="label">Generated</td>
            <td>{{ timestamp|date:"M d, Y g:i A" }}</td>
            <td class="label">Total Records</td>
            <td>{{ total_records|default:"0" }}</td>
        </tr>
        <tr>
            <td class="label">Export Format</td>
            <td>{{ export_type|upper }}</td>
            <td class="label">Generated By</td>
            <td>{% if user %}{{ user.get_full_name|default:user.username|default:"System" }}{% else %}System{% endif %}</td>
        </tr>
    </table>
    
    <!-- Applied Filters Table -->
    {% if filters_applied %}
    <div class="section-title">Applied Filters</div>
    <table class="filters-table">
        {% for key, value in filters_applied.items %}
        <tr>
            <td class="filter-label">{{ key|title }}</td>
            <td>{{ value }}</td>
        </tr>
        {% endfor %}
    </table>
    {% endif %}
    
    <!-- Data Table -->
    {% if data %}
        <div class="section-title">Customer Data ({{ data|length }} records)</div>
        <table class="data-table">
            <thead>
                <tr>
                    {% for key in data.0.keys %}
                        <th>{{ key|title }}</th>
                    {% endfor %}
                </tr>
            </thead>
            <tbody>
                {% for customer in data %}
                    <tr>
                        {% for key, value in customer.items %}
                            <td>
                                {% if value %}
                                    {% if value|length > 30 %}
                                        {{ value|truncatechars:30 }}
                                    {% else %}
                                        {{ value }}
                                    {% endif %}
                                {% else %}
                                    -
                                {% endif %}
                            </td>
                        {% endfor %}
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        
        <!-- Summary Table -->
        <div class="section-title">Report Summary</div>
        <table class="summary-table">
            <tr>
                <td class="summary-label">Total Customers</td>
                <td>{{ total_records }}</td>
                <td class="summary-label">Exported Records</td>
                <td>{{ data|length }}</td>
            </tr>
            <tr>
                <td class="summary-label">Active Filters</td>
                <td>{{ filters_applied|length|default:"0" }}</td>
                <td class="summary-label">Report Period</td>
                <td>{{ timestamp|date:"M Y" }}</td>
            </tr>
        </table>
    {% else %}
        <div class="no-data">
            <strong>No Customer Data Available</strong><br>
            No customer records match the applied filters or the dataset is empty.
        </div>
    {% endif %}
    
    <!-- Footer -->
    <div class="footer">
        <div class="company-info">Optiven CRM System - Customer Management Report</div>
        <div>
            This report was automatically generated on {{ timestamp|date:"F d, Y \a\t g:i A" }}.
            {% if user %}Generated by {{ user.get_full_name|default:user.username|default:"Anonymous" }}.{% endif %}
            <br>Confidential - For Internal Use Only
        </div>
    </div>
</body>
</html>