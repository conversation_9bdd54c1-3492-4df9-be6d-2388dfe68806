from celery import shared_task
from django.core.management import call_command
from django.db import connection, transaction
from sales.models import PortfolioHeader, PortfolioLine
import logging

logger = logging.getLogger(__name__)

@shared_task(bind=True, name='sales.tasks.truncate_portfolio_models')
def truncate_portfolio_models(self):
    """
    Celery task to truncate PortfolioHeader and PortfolioLine models.
    Runs daily at 12:00 PM via Celery Beat.
    """
    try:
        logger.info('Starting scheduled portfolio models truncation...')
        
        # Get initial counts for logging
        portfolio_header_count = PortfolioHeader.objects.count()
        portfolio_line_count = PortfolioLine.objects.count()
        
        logger.info(f'Found {portfolio_header_count} PortfolioHeader records and {portfolio_line_count} PortfolioLine records to truncate')
        
        # Use transaction to ensure atomicity
        with transaction.atomic():
            # Use raw SQL for better performance on large tables
            with connection.cursor() as cursor:
                # Truncate PortfolioLine first (in case of any foreign key constraints)
                cursor.execute('TRUNCATE TABLE portfolio_lines RESTART IDENTITY CASCADE')
                logger.info('Successfully truncated PortfolioLine table')
                
                # Truncate PortfolioHeader
                cursor.execute('TRUNCATE TABLE portfolio_headers RESTART IDENTITY CASCADE')
                logger.info('Successfully truncated PortfolioHeader table')
        
        success_msg = f'Portfolio truncation completed successfully. Removed {portfolio_header_count} PortfolioHeader and {portfolio_line_count} PortfolioLine records'
        logger.info(success_msg)
        
        return {
            'status': 'success',
            'message': success_msg,
            'portfolio_header_count': portfolio_header_count,
            'portfolio_line_count': portfolio_line_count
        }
        
    except Exception as e:
        error_msg = f'Error during scheduled portfolio truncation: {str(e)}'
        logger.error(error_msg, exc_info=True)
        
        # Re-raise the exception so Celery can handle retries if configured
        raise self.retry(exc=e, countdown=60, max_retries=3)

@shared_task(bind=True, name='sales.tasks.truncate_portfolio_models_safe')
def truncate_portfolio_models_safe(self):
    """
    Safe version that uses Django ORM delete instead of TRUNCATE.
    Use this if you encounter issues with foreign key constraints.
    """
    try:
        logger.info('Starting safe scheduled portfolio models truncation...')
        
        # Get initial counts for logging
        portfolio_header_count = PortfolioHeader.objects.count()
        portfolio_line_count = PortfolioLine.objects.count()
        
        logger.info(f'Found {portfolio_header_count} PortfolioHeader records and {portfolio_line_count} PortfolioLine records to delete')
        
        # Use transaction to ensure atomicity
        with transaction.atomic():
            # Delete PortfolioLine first (in case of foreign key constraints)
            PortfolioLine.objects.all().delete()
            logger.info('Successfully deleted all PortfolioLine records')
            
            # Delete PortfolioHeader
            PortfolioHeader.objects.all().delete()
            logger.info('Successfully deleted all PortfolioHeader records')
        
        success_msg = f'Safe portfolio deletion completed successfully. Removed {portfolio_header_count} PortfolioHeader and {portfolio_line_count} PortfolioLine records'
        logger.info(success_msg)
        
        return {
            'status': 'success',
            'message': success_msg,
            'portfolio_header_count': portfolio_header_count,
            'portfolio_line_count': portfolio_line_count
        }
        
    except Exception as e:
        error_msg = f'Error during safe scheduled portfolio deletion: {str(e)}'
        logger.error(error_msg, exc_info=True)
        
        # Re-raise the exception so Celery can handle retries if configured
        raise self.retry(exc=e, countdown=60, max_retries=3)