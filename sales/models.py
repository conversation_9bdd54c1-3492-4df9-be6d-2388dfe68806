from django.db import models
from customers.models import Customer
from inventory.models import Project, Plot
from users.models import User, Teams, Departments
from leads.models import LeadSourceCategory, LeadSourceSubCategory, LeadSource

class LeadFile(models.Model):
    
    purchase_type = [
        ('Cash', 'Cash'),
        ('Installment', 'Installment'),
        ('Financing', 'Financing')]
    
    lead_type_choices = [
        ('Legacy', 'Legacy'),
        ('Normal', 'Normal'),
        ('Diaspora', 'Diaspora'),
        ('Diaspora Legacy', 'Diaspora Legacy'),]
    
    lead_file_no = models.CharField(max_length=30, primary_key=True)
    lead_file_status_dropped = models.BooleanField(default=False, blank=False, null=False)
    plot= models.ForeignKey(Plot, on_delete=models.CASCADE, to_field='plotId',blank=False, null=False)
    plot_no= models.TextField(max_length=50, blank=True, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, to_field='projectId', blank=False, null=False)
    marketer = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='lead_file_marketer')
    purchase_price = models.DecimalField(max_digits=20, decimal_places=2, null=False, blank=False)
    selling_price = models.DecimalField(max_digits=20, decimal_places=2, null=False, blank=False)  # consider DecimalField if numeric
    balance_lcy = models.DecimalField(max_digits=20, decimal_places=2, null=False, blank=False)
    customer_id = models.ForeignKey(Customer, on_delete=models.CASCADE, to_field='customer_no', related_name='lead_file_customer', blank=False, null=False)
    customer_name = models.TextField( max_length=255, blank=False, null=False)
    purchase_type = models.CharField(max_length=255, choices=purchase_type, null=False, blank=False)
    commission_threshold = models.DecimalField(max_digits=20, decimal_places=2,  null=False, blank=False)
    deposit_threshold = models.DecimalField(max_digits=20, decimal_places=2,  null=False, blank=False)
    discount = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True )  # consider DecimalField if numeric
    completion_date = models.DateField(blank=False, null=False)  # consider DateField if valid date format
    no_of_installments = models.IntegerField( null=True, blank=True)
    installment_amount = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    sale_agreement_sent = models.CharField(max_length=20, null=True, blank=True)
    sale_agreement_signed = models.CharField(max_length=20, null=True, blank=True)
    total_paid = models.DecimalField(max_digits=20, decimal_places=2, blank=False, null=False,default=0.00)
    transfer_cost_charged = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    transfer_cost_paid = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    overpayments = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    refunds = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    refundable_amount = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    penalties_accrued = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    customer_lead_source = models.ForeignKey(LeadSource, on_delete=models.CASCADE, to_field='name', related_name='lead_file_customer_lead_source', blank=False, null=False)
    cat_lead_source = models.ForeignKey(LeadSourceCategory, on_delete=models.CASCADE, to_field='name', related_name='lead_file_cat_lead_source', blank=False, null=False)
    booking_id = models.CharField(max_length=30, null=True, blank=True)
    booking_date = models.DateField(null=True, blank=True)
    additional_deposit_date = models.DateField(null=True, blank=True)
    title_status = models.CharField(max_length=255, null=True, blank=True)
    credit_officer_id = models.CharField(max_length=150, null=True, blank=True)
    lead_type = models.CharField(max_length=150, choices=lead_type_choices, null=True, blank=True)
    
    class Meta:
        ordering = ['-lead_file_no']

    def __str__(self):
        return f'{self.lead_file_no} - {self.plot.plot_number}'

# Models moved from marketer_targets app
class MarketersTarget(models.Model): 
    marketer_no = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='marketer_targets')  
    period_start_date = models.DateField()  
    period_end_date = models.DateField()  
    monthly_target = models.DecimalField(max_digits=12, decimal_places=2)  
    daily_target = models.DecimalField(max_digits=12, decimal_places=2)
    MIB_achieved = models.DecimalField(max_digits=12, decimal_places=2) 
    MIB_Perfomance = models.DecimalField(max_digits=12, decimal_places=2)  

    class Meta:
        db_table = 'marketer_targets'
        verbose_name = 'Marketer Target'
        verbose_name_plural = 'Marketer Targets'
        unique_together = (('marketer_no', 'period_start_date'),)
        ordering = ['-period_start_date']

    def __str__(self):
        return f"{self.marketer_no} - {self.period_start_date}"

# Models moved from reports app
class HOSGMTarget(models.Model):
    line_no = models.IntegerField(primary_key=True)
    marketer_no = models.CharField(max_length=30)
    marketer_name = models.CharField(max_length=255)
    title = models.CharField(max_length=255)
    status = models.CharField(max_length=20)
    period_start_date = models.DateField()
    period_end_date = models.DateField()
    monthly_target = models.DecimalField(max_digits=20, decimal_places=2)
    daily_target = models.DecimalField(max_digits=20, decimal_places=2)
    MIB_achieved = models.DecimalField(max_digits=20, decimal_places=2)
    MIB_Perfomance = models.DecimalField(max_digits=20, decimal_places=2)
    commission_rate = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    commission_payable = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)

    class Meta:
        db_table = 'hos_gm_targets'
        verbose_name = 'HOS/GM Target'
        verbose_name_plural = 'HOS/GM Targets'
        ordering = ['-period_start_date']

    def __str__(self):
        return f"{self.marketer_name} - {self.title} ({self.period_start_date} to {self.period_end_date})"

class TeamTarget(models.Model):
    line_no = models.IntegerField(primary_key=True)
    team = models.CharField(max_length=255)
    period_start_date = models.DateField(null=True, blank=True)
    period_end_date = models.DateField(null=True, blank=True)
    monthly_target = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    daily_target = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    MIB_achieved = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    MIB_Perfomance = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)

    class Meta:
        db_table = 'teams_targets'
        verbose_name = 'Team Target'
        verbose_name_plural = 'Team Targets'
        ordering = ['-period_start_date']

    def __str__(self):
        return f"{self.team} ({self.period_start_date} to {self.period_end_date})"

class PortfolioHeader(models.Model):
    line_no = models.AutoField(primary_key=True)
    marketer_no = models.CharField(max_length=20)
    period_start_date = models.DateField()
    period_end_date = models.DateField()
    total_purchases = models.DecimalField(max_digits=20, decimal_places=2)
    team = models.CharField(max_length=20)
    region = models.CharField(max_length=20)
    total_collections = models.DecimalField(max_digits=20, decimal_places=2)
    total_to_collect = models.DecimalField(max_digits=20, decimal_places=2)
    total_installments_due = models.DecimalField(max_digits=20, decimal_places=2)
    total_installments_collected = models.DecimalField(max_digits=20, decimal_places=5)
    total_overdue = models.DecimalField(max_digits=20, decimal_places=5)
    total_overdue_collected = models.DecimalField(max_digits=20, decimal_places=5)
    total_previous_unpaid = models.DecimalField(max_digits=20, decimal_places=5)
    portfolio_balance = models.DecimalField(max_digits=20, decimal_places=5)
    marketer_target = models.DecimalField(max_digits=20, decimal_places=5)
    MIB_perfomance = models.DecimalField(max_digits=10, decimal_places=0)
    locality = models.CharField(max_length=255)

    class Meta:
        db_table = 'portfolio_headers'
        verbose_name = 'Portfolio Header'
        verbose_name_plural = 'Portfolio Headers'
        unique_together = (('marketer_no', 'period_start_date', 'period_end_date'),)
        ordering = ['-period_start_date']

    def __str__(self):
        return f"{self.marketer_no} - {self.team} ({self.period_start_date} to {self.period_end_date})"

class PortfolioLine(models.Model):
    no = models.AutoField(primary_key=True)
    marketer_no = models.CharField(max_length=20)
    period_start_date = models.DateField()
    period_end_date = models.DateField()
    lead_file_no = models.CharField(max_length=20)
    plot_name = models.CharField(max_length=20)
    installments_due = models.DecimalField(max_digits=20, decimal_places=2)
    installments_due_collected = models.DecimalField(max_digits=20, decimal_places=2)
    overdue_collections = models.DecimalField(max_digits=20, decimal_places=2)
    overdue_collections_collected = models.DecimalField(max_digits=20, decimal_places=2)
    previous_unpaid = models.DecimalField(max_digits=20, decimal_places=2)
    total_to_collect = models.DecimalField(max_digits=20, decimal_places=2)
    customer_number = models.CharField(max_length=20)
    total_previously_collected = models.DecimalField(max_digits=20, decimal_places=2)
    penalties_accrued = models.DecimalField(max_digits=20, decimal_places=2)
    current_balance = models.DecimalField(max_digits=20, decimal_places=2)
    due_date = models.DateField()
    completion_date = models.DateField()
    booking_date = models.DateField()
    total_collected = models.DecimalField(max_digits=20, decimal_places=5)

    class Meta:
        db_table = 'portfolio_lines'
        verbose_name = 'Portfolio Line'
        verbose_name_plural = 'Portfolio Lines'
        unique_together = (('lead_file_no','period_start_date'),)
        ordering = ['-period_start_date']

    def __str__(self):
        return f"{self.lead_file_no} - {self.plot_name} ({self.customer_number})"

# Cash on Cash Models
class CashOnCash(models.Model):
    Period = models.DateField() 
    marketer = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='cash_marketer')
    Transaction_date = models.DateField()  
    client_no = models.CharField(max_length=100)  
    client_name = models.CharField(max_length=200)  
    plot_no = models.CharField(max_length=100)  
    amount = models.DecimalField(max_digits=12, decimal_places=2)  
    bonus = models.DecimalField(max_digits=12, decimal_places=2)  
    Regional_Category = models.CharField(max_length=100)  
    
    class Meta:
        db_table = 'cash_on_cash'
        verbose_name = 'Cash on Cash'
        verbose_name_plural = 'Cash on Cash'
        ordering = ['-Period']
    
    def __str__(self):
        return f"{self.client_name} - {self.marketer} ({self.Period})"

# Commission Header Models
class CommissionHeader(models.Model):  
    emp_no = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='commission_emp_no') 
    period_start_date = models.DateField()
    period_end_date = models.DateField()  
    role = models.CharField(max_length=100)  
    Deposit_amount = models.DecimalField(max_digits=12, decimal_places=2)  
    deposit_perc = models.DecimalField(max_digits=5, decimal_places=2)  
    deposit_commission = models.DecimalField(max_digits=12, decimal_places=2)  
    installment_amount = models.DecimalField(max_digits=12, decimal_places=2)  
    installment_perc = models.DecimalField(max_digits=5, decimal_places=2)  
    installment_commission = models.DecimalField(max_digits=12, decimal_places=2)  
    Total_commission = models.DecimalField(max_digits=12, decimal_places=2)  
    Tl_gained_comm_from_members = models.DecimalField(max_digits=12, decimal_places=2)  
    rm_achieved_MIB = models.DecimalField(max_digits=12, decimal_places=2)  
    rm_commission_rate = models.DecimalField(max_digits=5, decimal_places=2)  
    rm_commission_amount = models.DecimalField(max_digits=12, decimal_places=2)  
    commisison_payable_TL = models.DecimalField(max_digits=12, decimal_places=2)  

    class Meta:
        db_table = 'commission_headers'
        verbose_name = 'Commission Header'
        verbose_name_plural = 'Commission Headers'
        ordering = ['-period_start_date']

    def __str__(self):
        return f"Commission Header for Employee {self.emp_no} ({self.period_start_date} to {self.period_end_date})"

# Commission Line Models
class CommissionLine(models.Model):
    marketer_no = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='commission_marketer_emp_no') 
    period_start_date = models.DateField()  
    period_end_date = models.DateField()  
    transaction_date = models.DateField()  
    plot_number = models.CharField(max_length=100)  
    new_deposits_collected = models.DecimalField(max_digits=12, decimal_places=2)  
    installments_collected = models.DecimalField(max_digits=12, decimal_places=2)  

    class Meta:
        db_table = 'commission_lines'
        verbose_name = 'Commission Line'
        verbose_name_plural = 'Commission Lines'
        ordering = ['-period_start_date']

    def __str__(self):
        return f"Commission Line - {self.marketer_no} ({self.period_start_date} to {self.period_end_date})"
