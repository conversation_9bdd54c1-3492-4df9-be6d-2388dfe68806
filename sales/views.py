from django.shortcuts import render
from rest_framework import viewsets, filters, mixins
from rest_framework.permissions import Is<PERSON><PERSON>ent<PERSON><PERSON>, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import (
    LeadFile, 
    MarketersTarget, 
    HOSGMTarget, 
    TeamTarget, 
    PortfolioHeader, 
    PortfolioLine,
    CashOnCash,
    CommissionHeader,
    CommissionLine
)
from .serializers import (
    LeadFileSerializer, 
    MarketersTargetSerializer, 
    HOSGMTargetSerializer, 
    TeamTargetSerializer, 
    PortfolioHeaderSerializer, 
    PortfolioLineSerializer,
    CashOnCashSerializer,
    CommissionHeaderSerializer,
    CommissionLineSerializer
)

from config.perm_filters import sales_permission_filters
from django.conf import settings
from rest_framework.exceptions import PermissionDenied

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve', ]
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

@swagger_tags(['SALES'])
class LeadFileViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for LeadFile with only list and retrieve actions
    """
    queryset = LeadFile.objects.none() 
    serializer_class = LeadFileSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name','purchase_type', 'booking_date']
    search_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name','purchase_type', 'booking_date']
    ordering_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name',]
    ordering = ['-lead_file_no']
    
    def get_queryset(self):
        base_queryset = LeadFile.objects.all().order_by('-booking_date')
        queryset = sales_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these sales records or have more than one permission.")
        return queryset
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = LeadFile.objects.all().order_by('-booking_date')
        queryset = sales_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these sales records or have more than one permission.")

        # Filter based on URL route name
        route_name = self.request.resolver_match.url_name if self.request.resolver_match else None
        
        if route_name == 'ongoing-sales':
           queryset = queryset.filter(lead_file_status_dropped=False)  # example filter

        if route_name == 'completed-sales':
            queryset = queryset.filter(balance_lcy__lte=0, lead_file_status_dropped=False)  # example filter
        
        return queryset

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return LeadFileSerializer
        return super().get_serializer_class()
    
    

    @swagger_auto_schema( operation_description="List, filter, search, order sales")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve sales")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
@swagger_tags(['SALES'])
class MarketersTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for MarketersTarget with only list and retrieve actions
    """
    queryset = MarketersTarget.objects.all()
    serializer_class = MarketersTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_no__first_name', 'marketer_no__last_name']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order marketer targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve marketer target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class HOSGMTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for HOSGM targets with only list and retrieve actions
    """
    queryset = HOSGMTarget.objects.all()
    serializer_class = HOSGMTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'status', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_name', 'title']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order HOSGM targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve HOSGM target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class TeamTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for team targets with only list and retrieve actions
    """
    queryset = TeamTarget.objects.all()
    serializer_class = TeamTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['team', 'period_start_date', 'period_end_date']
    search_fields = ['team']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order team targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve team target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class PortfolioHeaderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for portfolio headers with only list and retrieve actions
    """
    queryset = PortfolioHeader.objects.all()
    serializer_class = PortfolioHeaderSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'team', 'region', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_no', 'locality']
    ordering_fields = ['period_start_date', 'total_collections', 'marketer_target']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order portfolio headers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve portfolio header")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class PortfolioLineViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for portfolio lines with only list and retrieve actions
    """
    queryset = PortfolioLine.objects.all()
    serializer_class = PortfolioLineSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'customer_number', 'period_start_date', 'period_end_date']
    search_fields = ['lead_file_no', 'plot_name', 'customer_number']
    ordering_fields = ['due_date', 'installments_due', 'current_balance']
    ordering = ['due_date']

    @swagger_auto_schema( operation_description="List, filter, search, order portfolio lines")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve portfolio line")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CashOnCashViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for cash on cash records with only list and retrieve actions
    """
    queryset = CashOnCash.objects.all()
    serializer_class = CashOnCashSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['Period', 'marketer', 'client_no', 'plot_no', 'Regional_Category']
    search_fields = ['client_name', 'client_no', 'plot_no']
    ordering_fields = ['Period', 'Transaction_date', 'amount', 'bonus']
    ordering = ['-Period']

    @swagger_auto_schema( operation_description="List, filter, search, order cash on cash records")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve cash on cash record")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CommissionHeaderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for commission headers with only list and retrieve actions
    """
    queryset = CommissionHeader.objects.all()
    serializer_class = CommissionHeaderSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['emp_no', 'period_start_date', 'period_end_date', 'role']
    search_fields = ['emp_no__first_name', 'emp_no__last_name', 'role']
    ordering_fields = ['period_start_date', 'Total_commission', 'Deposit_amount']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order commission headers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve commission header")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CommissionLineViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for commission lines with only list and retrieve actions
    """
    queryset = CommissionLine.objects.all()
    serializer_class = CommissionLineSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'period_start_date', 'period_end_date', 'plot_number']
    search_fields = ['marketer_no__first_name', 'marketer_no__last_name', 'plot_number']
    ordering_fields = ['period_start_date', 'transaction_date', 'new_deposits_collected', 'installments_collected']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order commission lines")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve commission line")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

 

