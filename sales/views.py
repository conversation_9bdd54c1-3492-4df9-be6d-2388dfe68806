from django.shortcuts import render
from rest_framework import viewsets, filters, mixins, status
from rest_framework.permissions import IsA<PERSON><PERSON>icated, AllowAny
from rest_framework.response import Response
from rest_framework.decorators import action
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from .models import (
    LeadFile, 
    MarketersTarget, 
    HOSGMTarget, 
    TeamTarget, 
    PortfolioHeader, 
    PortfolioLine,
    CashOnCash,
    CommissionHeader,
    CommissionLine
)
from .serializers import (
    LeadFileSerializer, 
    LeadFileExportSerializer,
    MarketersTargetSerializer, 
    HOSGMTargetSerializer, 
    TeamTargetSerializer, 
    PortfolioHeaderSerializer, 
    PortfolioLineSerializer,
    CashOnCashSerializer,
    CommissionHeaderSerializer,
    CommissionLineSerializer
)
from .filterset import LeadFileFilter
from utils.export_mixins import AdvancedReportExportMixin
from config.perm_filters import sales_permission_filters
from django.conf import settings
from rest_framework.exceptions import PermissionDenied

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve', ]
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

@swagger_tags(['SALES FILTERS'])
class LeadFileFilterView(AdvancedReportExportMixin, viewsets.ViewSet):
    """
    Advanced Lead File filtering, grouping, and export ViewSet.
    
    Provides comprehensive filtering capabilities with support for:
    - Multiple value filtering (comma-separated)
    - Comparison operators for decimal and date fields (>, <, =, <=, >=)
    - Grouping and aggregation
    - Multiple export formats (PDF, Excel, Advanced Excel)
    - Permission-based data access
    
    ## Filter Examples:
    
    ### Basic Filtering:
    - `?lead_file_no=LF001` - Exact lead file number
    - `?customer_name__icontains=John` - Customer name contains "John"
    - `?purchase_type=Cash` - Cash purchases only
    - `?lead_file_status_dropped=false` - Active lead files
    
    ### Multiple Value Filtering:
    - `?marketer=EMP001,EMP002,EMP003` - Multiple marketers
    - `?project=PROJ001,PROJ002` - Multiple projects
    - `?customer_lead_source=Facebook,Website,Referral` - Multiple lead sources
    
    ### Decimal Field Comparisons:
    - `?purchase_price_gte=100000` - Purchase price >= 100,000
    - `?selling_price_lt=500000` - Selling price < 500,000
    - `?balance_lcy_gt=50000` - Balance > 50,000
    - `?total_paid_lte=200000` - Total paid <= 200,000
    - `?discount_gte=5000` - Discount >= 5,000
    
    ### Date Field Comparisons:
    - `?completion_date_gte=2024-01-01` - Completion date >= Jan 1, 2024
    - `?booking_date_lt=2024-12-31` - Booking date < Dec 31, 2024
    - `?additional_deposit_date_gte=2024-06-01` - Additional deposit date >= Jun 1, 2024
    
    ### Grouping:
    - `?group_by=marketer` - Group by marketer
    - `?group_by=project` - Group by project
    - `?group_by=purchase_type` - Group by purchase type
    - `?group_by=lead_type` - Group by lead type
    
    ### Combination Examples:
    - `?marketer=EMP001,EMP002&purchase_price_gte=100000&completion_date_gte=2024-01-01&group_by=purchase_type`
    - `?project=PROJ001&balance_lcy_gt=0&lead_file_status_dropped=false&group_by=marketer`
    
    ## Export Examples:
    
    ### PDF Export:
    ```
    POST /api/sales/lead-files-filter/export/
    Content-Type: application/json
    
    {
        "export_type": "pdf",
        "filters": {
            "purchase_price_gte": 100000,
            "completion_date_gte": "2024-01-01"
        }
    }
    ```
    
    ### Excel Export with Grouping:
    ```
    POST /api/sales/lead-files-filter/export/
    Content-Type: application/json
    
    {
        "export_type": "excel_advanced",
        "filters": {
            "marketer": "EMP001,EMP002",
            "balance_lcy_gt": 0
        },
        "group_by": "purchase_type"
    }
    ```
    """
    
    permission_classes = [AllowAny] if settings.DEBUG else [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_class = LeadFileFilter
    search_fields = [
        'lead_file_no', 'customer_name', 'plot_no', 'booking_id',
        'marketer__fullnames', 'project__name', 'customer_id__customer_no'
    ]
    ordering_fields = [
        'lead_file_no', 'purchase_price', 'selling_price', 'balance_lcy',
        'completion_date', 'booking_date', 'total_paid', 'customer_name'
    ]
    ordering = ['-booking_date']
    
    # Export configuration
    export_template_name = 'exports/lead_file_report.html'
    export_filename_prefix = 'lead_files_report'
    export_title = 'Lead Files Data Report'
    
    # Serializer configuration for export functionality
    serializer_class = LeadFileSerializer

    def get_serializer(self, *args, **kwargs):
        """Return serializer instance for export functionality"""
        return self.serializer_class(*args, **kwargs)
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if hasattr(self, 'action') and self.action in ['export']:
            return LeadFileExportSerializer
        return LeadFileSerializer
    
    def get_queryset(self):
        """Get base queryset with permission filtering"""
        if getattr(self, 'swagger_fake_view', False):
            return LeadFile.objects.none()
        
        base_queryset = LeadFile.objects.select_related(
            'marketer', 'project', 'plot', 'customer_id', 
            'customer_lead_source', 'cat_lead_source'
        ).all()
        
        # queryset = sales_permission_filters(self.request.user, base_queryset)
        # if queryset is False:
        #     raise PermissionDenied("You do not have permission to view these lead file records.")
        queryset = base_queryset
        return queryset
    
    def get_export_queryset(self, request):
        """Get filtered queryset for export - applies all filters from request"""
        from django_filters.rest_framework import DjangoFilterBackend
        
        # Get base queryset
        queryset = self.get_queryset()
        
        # Apply LeadFileFilter manually to avoid any backend issues
        try:
            lead_file_filter = LeadFileFilter(request.GET, queryset=queryset)
            filtered_queryset = lead_file_filter.qs
        except Exception as e:
            # If filtering fails, fall back to base queryset
            print(f"Filtering error: {e}")
            filtered_queryset = queryset
        
        return filtered_queryset
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action"""
        if hasattr(self, 'action') and self.action in ['export']:
            return LeadFileExportSerializer
        return LeadFileSerializer
    
    def get_export_data(self, queryset):
        """Convert queryset to list of dictionaries for export"""
        serializer = LeadFileExportSerializer(queryset, many=True)
        return serializer.data
    
    def get_applied_filters(self, request):
        """Extract applied filters for export metadata"""
        filters = {}
        
        # Lead file specific filter parameters
        filter_params = [
            'lead_file_no', 'customer_name', 'purchase_type', 'lead_type',
            'marketer', 'project', 'customer_lead_source', 'lead_file_status_dropped',
            'purchase_price', 'purchase_price_gt', 'purchase_price_gte', 'purchase_price_lt', 'purchase_price_lte',
            'selling_price', 'selling_price_gt', 'selling_price_gte', 'selling_price_lt', 'selling_price_lte',
            'balance_lcy', 'balance_lcy_gt', 'balance_lcy_gte', 'balance_lcy_lt', 'balance_lcy_lte',
            'total_paid', 'total_paid_gt', 'total_paid_gte', 'total_paid_lt', 'total_paid_lte',
            'completion_date', 'completion_date_gt', 'completion_date_gte', 'completion_date_lt', 'completion_date_lte',
            'booking_date', 'booking_date_gt', 'booking_date_gte', 'booking_date_lt', 'booking_date_lte',
            'group_by'
        ]
        
        for param in filter_params:
            value = request.GET.get(param)
            if value:
                filters[param] = value
        
        return filters
    
    def get_export_statistics(self, queryset, request):
        """Generate lead file specific statistics for exports"""
        from django.db.models import Sum, Avg, Count
        from django.utils import timezone
        
        total_count = queryset.count()
        active_count = queryset.filter(lead_file_status_dropped=False).count()
        dropped_count = queryset.filter(lead_file_status_dropped=True).count()
        
        # Financial statistics
        total_purchase_value = queryset.aggregate(Sum('purchase_price'))['purchase_price__sum'] or 0
        total_selling_value = queryset.aggregate(Sum('selling_price'))['selling_price__sum'] or 0
        total_balance = queryset.aggregate(Sum('balance_lcy'))['balance_lcy__sum'] or 0
        total_paid_amount = queryset.aggregate(Sum('total_paid'))['total_paid__sum'] or 0
        avg_purchase_price = queryset.aggregate(Avg('purchase_price'))['purchase_price__avg'] or 0
        
        # Purchase type breakdown
        cash_count = queryset.filter(purchase_type='Cash').count()
        installment_count = queryset.filter(purchase_type='Installment').count()
        financing_count = queryset.filter(purchase_type='Financing').count()
        
        return {
            'total_lead_files': total_count,
            'active_lead_files': active_count,
            'dropped_lead_files': dropped_count,
            'total_purchase_value': float(total_purchase_value),
            'total_selling_value': float(total_selling_value),
            'total_outstanding_balance': float(total_balance),
            'total_amount_paid': float(total_paid_amount),
            'average_purchase_price': float(avg_purchase_price),
            'cash_purchases': cash_count,
            'installment_purchases': installment_count,
            'financing_purchases': financing_count,
            'export_date': timezone.now(),
            'applied_filters': self.get_applied_filters(request),
        }
    
    @swagger_auto_schema(
        operation_description="""
        List and filter lead files with comprehensive filtering options.
        
        **Available Filters:**
        - Basic: lead_file_no, customer_name, purchase_type, lead_type
        - Multiple Values: marketer, project, customer_lead_source (comma-separated)
        - Decimal Comparisons: purchase_price, selling_price, balance_lcy (with _gt, _gte, _lt, _lte)
        - Date Comparisons: completion_date, booking_date, additional_deposit_date (with _gt, _gte, _lt, _lte)
        - Boolean: lead_file_status_dropped
        
        **Grouping:** Use `group_by` parameter to group results by:
        - marketer, project, purchase_type, lead_type, cat_lead_source, customer_lead_source
        
        **Examples:**
        - Multiple marketers: `?marketer=EMP001,EMP002,EMP003`
        - Price range: `?purchase_price_gte=100000&purchase_price_lte=500000`
        - Date range: `?completion_date_gte=2024-01-01&completion_date_lte=2024-12-31`
        - With grouping: `?project=PROJ001&group_by=purchase_type`
        """,
        manual_parameters=[
            openapi.Parameter('group_by', openapi.IN_QUERY, 
                            description="Group results by field: marketer, project, purchase_type, lead_type, etc.", 
                            type=openapi.TYPE_STRING),
            openapi.Parameter('marketer', openapi.IN_QUERY, 
                            description="Filter by marketer(s). Comma-separated: EMP001,EMP002", 
                            type=openapi.TYPE_STRING),
            openapi.Parameter('project', openapi.IN_QUERY, 
                            description="Filter by project(s). Comma-separated: PROJ001,PROJ002", 
                            type=openapi.TYPE_STRING),
            openapi.Parameter('purchase_price_gte', openapi.IN_QUERY, 
                            description="Purchase price greater than or equal to", 
                            type=openapi.TYPE_NUMBER),
            openapi.Parameter('purchase_price_lte', openapi.IN_QUERY, 
                            description="Purchase price less than or equal to", 
                            type=openapi.TYPE_NUMBER),
            openapi.Parameter('completion_date_gte', openapi.IN_QUERY, 
                            description="Completion date greater than or equal to (YYYY-MM-DD)", 
                            type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
            openapi.Parameter('completion_date_lte', openapi.IN_QUERY, 
                            description="Completion date less than or equal to (YYYY-MM-DD)", 
                            type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
        ],
        responses={
            200: openapi.Response(
                description="Successful response with filtered lead files",
                examples={
                    "application/json": {
                        "count": 150,
                        "results": [
                            {
                                "lead_file_no": "LF001",
                                "customer_name": "John Doe",
                                "marketer_name": "Jane Smith",
                                "project_name": "Green Valley",
                                "purchase_price": 250000.00,
                                "selling_price": 280000.00,
                                "balance_lcy": 50000.00,
                                "completion_date": "2024-12-31",
                                "purchase_type": "Installment"
                            }
                        ],
                        "grouped_data": {
                            "Cash": {"count": 45, "total_value": 12500000.00},
                            "Installment": {"count": 105, "total_value": 28750000.00}
                        }
                    }
                }
            )
        },
        tags=['SALES']
    )
    def list(self, request):
        """List lead files with filtering and optional grouping"""
        from django_filters.rest_framework import DjangoFilterBackend
        from rest_framework import filters
        from rest_framework.pagination import PageNumberPagination
        from django.db.models import Count, Q, Case, When, Value, CharField, Sum, Avg
        from django.db.models.functions import Coalesce
        
        # Get parameters
        group_by = request.query_params.get('group_by')
        group_count = request.query_params.get('group_count', 'false').lower() == 'true'
        
        # Define available grouping fields with their database paths
        grouping_fields = {
            'marketer': 'marketer__fullnames',
            'marketer_id': 'marketer__employee_no',
            'project': 'project__name',
            'project_id': 'project__projectId',
            'purchase_type': 'purchase_type',
            'lead_type': 'lead_type',
            'customer_lead_source': 'customer_lead_source__name',
            'cat_lead_source': 'cat_lead_source__name',
            'lead_file_status': 'lead_file_status_dropped',
            'completion_date': 'completion_date',
            'booking_date': 'booking_date',
        }
        
        # Get queryset
        queryset = self.get_queryset()
        
        # Apply filters using the LeadFileFilter
        filter_backend = DjangoFilterBackend()
        filtered_queryset = filter_backend.filter_queryset(request, queryset, self)
        
        # Apply search if provided
        if request.query_params.get('search'):
            search_backend = filters.SearchFilter()
            filtered_queryset = search_backend.filter_queryset(request, filtered_queryset, self)
        
        # Handle grouping
        if group_by and group_by in grouping_fields:
            group_field = grouping_fields[group_by]
            
            if group_count:
                # Return grouped counts with aggregations
                grouped_data = (
                    filtered_queryset
                    .values(group_field)
                    .annotate(
                        count=Count('lead_file_no'),
                        total_purchase_price=Sum('purchase_price'),
                        total_selling_price=Sum('selling_price'),
                        total_balance=Sum('balance_lcy'),
                        total_paid=Sum('total_paid'),
                        avg_purchase_price=Avg('purchase_price'),
                        group_name=Case(
                            When(**{f'{group_field}__isnull': True}, then=Value('Not Specified')),
                            When(**{f'{group_field}__exact': ''}, then=Value('Not Specified')),
                            default=group_field,
                            output_field=CharField()
                        )
                    )
                    .order_by('-count')
                )
                
                # Calculate additional statistics
                total_count = filtered_queryset.count()
                total_purchase_value = filtered_queryset.aggregate(Sum('purchase_price'))['purchase_price__sum'] or 0
                total_selling_value = filtered_queryset.aggregate(Sum('selling_price'))['selling_price__sum'] or 0
                total_balance_value = filtered_queryset.aggregate(Sum('balance_lcy'))['balance_lcy__sum'] or 0
                active_count = filtered_queryset.filter(lead_file_status_dropped=False).count()
                
                return Response({
                    'group_by': group_by,
                    'group_count': True,
                    'total_records': total_count,
                    'statistics': {
                        'total': total_count,
                        'active': active_count,
                        'dropped': total_count - active_count,
                        'total_purchase_value': float(total_purchase_value),
                        'total_selling_value': float(total_selling_value),
                        'total_balance_value': float(total_balance_value),
                        'groups': len(grouped_data)
                    },
                    'groups': [
                        {
                            'group_value': item['group_name'] or 'Not Specified',
                            'count': item['count'],
                            'total_purchase_price': float(item['total_purchase_price'] or 0),
                            'total_selling_price': float(item['total_selling_price'] or 0),
                            'total_balance': float(item['total_balance'] or 0),
                            'total_paid': float(item['total_paid'] or 0),
                            'avg_purchase_price': float(item['avg_purchase_price'] or 0),
                            'percentage': round((item['count'] / total_count * 100), 2) if total_count > 0 else 0
                        }
                        for item in grouped_data
                    ],
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'marketer', 'project', 'purchase_type', 'lead_type', 
                                'purchase_price_gte', 'purchase_price_lte', 
                                'completion_date_gte', 'completion_date_lte'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
            else:
                # Return grouped detailed records
                grouped_data = {}
                
                # Get distinct group values
                group_values = (
                    filtered_queryset
                    .values_list(group_field, flat=True)
                    .distinct()
                    .order_by(group_field)
                )
                
                for group_value in group_values:
                    group_name = group_value or 'Not Specified'
                    
                    # Filter records for this group
                    if group_value is None or group_value == '':
                        group_queryset = filtered_queryset.filter(
                            Q(**{f'{group_field}__isnull': True}) | Q(**{f'{group_field}__exact': ''})
                        )
                    else:
                        group_queryset = filtered_queryset.filter(**{group_field: group_value})
                    
                    # Apply ordering to group
                    ordering_backend = filters.OrderingFilter()
                    group_queryset = ordering_backend.filter_queryset(request, group_queryset, self)
                    
                    # Serialize group data
                    serializer = LeadFileSerializer(group_queryset, many=True)
                    
                    grouped_data[group_name] = {
                        'count': group_queryset.count(),
                        'records': serializer.data
                    }
                
                return Response({
                    'group_by': group_by,
                    'group_count': False,
                    'total_records': filtered_queryset.count(),
                    'groups': grouped_data,
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'marketer', 'project', 'purchase_type', 'lead_type',
                                'purchase_price_gte', 'purchase_price_lte',
                                'completion_date_gte', 'completion_date_lte'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
        
        # Standard filtering without grouping
        # Apply ordering
        ordering_backend = filters.OrderingFilter()
        filtered_queryset = ordering_backend.filter_queryset(request, filtered_queryset, self)
        
        # Set up pagination
        paginator = PageNumberPagination()
        paginator.page_size = min(int(request.query_params.get('page_size', 20)), 100)
        page = paginator.paginate_queryset(filtered_queryset, request)
        
        # Serialize data
        serializer = LeadFileSerializer(page, many=True)
        
        # Return paginated response with filter summary
        response_data = paginator.get_paginated_response(serializer.data)
        
        # Add filter summary to response
        filter_summary = {
            'applied_filters': {},
            'total_without_pagination': filtered_queryset.count()
        }
        
        # Extract applied filters from request
        filter_params = ['marketer', 'project', 'purchase_type', 'lead_type', 
                        'purchase_price_gte', 'purchase_price_lte',
                        'completion_date_gte', 'completion_date_lte',
                        'lead_file_status_dropped', 'customer_lead_source']
        
        for param in filter_params:
            if request.query_params.get(param):
                filter_summary['applied_filters'][param] = request.query_params.get(param)
        
        response_data.data['filter_summary'] = filter_summary
        
        return response_data

    # Make filter class available to DjangoFilterBackend
    filterset_class = LeadFileFilter
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    
    @swagger_auto_schema(
        operation_description="""
        Export lead files data in multiple formats (PDF, Excel, Advanced Excel).
        
        **Export Types:**
        - `pdf`: Generates a PDF report with lead files data
        - `excel`: Creates a basic Excel file with data
        - `excel_advanced`: Creates an Excel file with formatting, charts, and summary sheets
        
        **Request Body:**
        ```json
        {
            "export_type": "pdf",
            "filters": {
                "marketer": "EMP001,EMP002",
                "purchase_price_gte": 100000,
                "completion_date_gte": "2024-01-01"
            },
            "group_by": "purchase_type"
        }
        ```
        
        **Available Filters in Export:**
        - All list endpoint filters are supported
        - Use same parameter names and format
        
        **Response:**
        - Returns file download with appropriate content-type
        - PDF: application/pdf
        - Excel: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
        """,
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'export_type': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    enum=['pdf', 'excel', 'excel_advanced'],
                    description='Export format type'
                ),
                'filters': openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    description='Filter parameters (same as list endpoint)',
                    properties={
                        'marketer': openapi.Schema(type=openapi.TYPE_STRING, description='Comma-separated marketer IDs'),
                        'purchase_price_gte': openapi.Schema(type=openapi.TYPE_NUMBER, description='Minimum purchase price'),
                        'completion_date_gte': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                    }
                ),
                'group_by': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description='Field to group by (marketer, project, purchase_type, etc.)'
                )
            },
            required=['export_type'],
            example={
                "export_type": "excel_advanced",
                "filters": {
                    "marketer": "EMP001,EMP002",
                    "purchase_price_gte": 100000,
                    "balance_lcy_gt": 0
                },
                "group_by": "purchase_type"
            }
        ),
        responses={
            200: openapi.Response(
                description="File download",
                headers={
                    'Content-Disposition': openapi.Schema(type=openapi.TYPE_STRING, description='attachment; filename="lead_files_report.pdf"')
                }
            ),
            400: "Bad request - invalid parameters",
            403: "Permission denied"
        },
        tags=['SALES']
    )
    @action(detail=False, methods=['post'])
    def export(self, request):
        """Export lead files data in various formats"""
        try:
            # Get export type from request data
            export_type = request.data.get('export_type', 'pdf').lower()
            
            # Get filters and group_by from request data  
            filters = request.data.get('filters', {})
            group_by = request.data.get('group_by')
            
            # Create a new request object with the filters as GET parameters
            from django.http import QueryDict
            from copy import copy
            
            # Create a mutable copy of the request
            export_request = copy(request)
            query_dict = QueryDict('', mutable=True)
            
            # Add filters to query parameters
            for key, value in filters.items():
                query_dict[key] = value
                
            if group_by:
                query_dict['group_by'] = group_by
                
            export_request.GET = query_dict
            
            # Delegate to appropriate export method based on type
            if export_type == 'pdf':
                return self.export_pdf(export_request)
            elif export_type == 'excel':
                return self.export_excel(export_request)
            elif export_type == 'excel_advanced':
                return self.export_advanced_excel(export_request)
            else:
                return Response({
                    'error': 'Invalid export type',
                    'detail': f'Supported types: pdf, excel, excel_advanced. Got: {export_type}'
                }, status=400)
                
        except Exception as e:
            return Response({
                'error': 'Export failed',
                'detail': str(e)
            }, status=400)


@swagger_tags(['SALES'])
class LeadFileViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for LeadFile with only list and retrieve actions
    """
    queryset = LeadFile.objects.none() 
    serializer_class = LeadFileSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name','purchase_type', 'booking_date']
    search_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name','purchase_type', 'booking_date']
    ordering_fields = ['lead_file_no','lead_file_status_dropped','plot__plotId','plot__plot_no','project__projectId','project__name','marketer__employee_no','marketer__username','marketer__email','customer_id__customer_no','customer_id__customer_name','customer_lead_source__name','cat_lead_source__name',]
    ordering = ['-lead_file_no']
    
    def get_queryset(self):
        base_queryset = LeadFile.objects.all().order_by('-booking_date')
        queryset = sales_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these sales records or have more than one permission.")
        return queryset
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = LeadFile.objects.all().order_by('-booking_date')
        queryset = sales_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these sales records or have more than one permission.")

        # Filter based on URL route name
        route_name = self.request.resolver_match.url_name if self.request.resolver_match else None
        
        if route_name == 'ongoing-sales':
           queryset = queryset.filter(lead_file_status_dropped=False)  # example filter

        if route_name == 'completed-sales':
            queryset = queryset.filter(balance_lcy__lte=0, lead_file_status_dropped=False)  # example filter
        
        return queryset

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return LeadFileSerializer
        return super().get_serializer_class()
    
    

    @swagger_auto_schema( operation_description="List, filter, search, order sales")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve sales")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
@swagger_tags(['SALES'])
class MarketersTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for MarketersTarget with only list and retrieve actions
    """
    queryset = MarketersTarget.objects.all()
    serializer_class = MarketersTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_no__first_name', 'marketer_no__last_name']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order marketer targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve marketer target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class HOSGMTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for HOSGM targets with only list and retrieve actions
    """
    queryset = HOSGMTarget.objects.all()
    serializer_class = HOSGMTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'status', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_name', 'title']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order HOSGM targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve HOSGM target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class TeamTargetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for team targets with only list and retrieve actions
    """
    queryset = TeamTarget.objects.all()
    serializer_class = TeamTargetSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['team', 'period_start_date', 'period_end_date']
    search_fields = ['team']
    ordering_fields = ['period_start_date', 'monthly_target', 'MIB_achieved']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order team targets")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve team target")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class PortfolioHeaderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for portfolio headers with only list and retrieve actions
    """
    queryset = PortfolioHeader.objects.all()
    serializer_class = PortfolioHeaderSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'team', 'region', 'period_start_date', 'period_end_date']
    search_fields = ['marketer_no', 'locality']
    ordering_fields = ['period_start_date', 'total_collections', 'marketer_target']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order portfolio headers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve portfolio header")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class PortfolioLineViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for portfolio lines with only list and retrieve actions
    """
    queryset = PortfolioLine.objects.all()
    serializer_class = PortfolioLineSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'customer_number', 'period_start_date', 'period_end_date']
    search_fields = ['lead_file_no', 'plot_name', 'customer_number']
    ordering_fields = ['due_date', 'installments_due', 'current_balance']
    ordering = ['due_date']

    @swagger_auto_schema( operation_description="List, filter, search, order portfolio lines")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve portfolio line")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CashOnCashViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for cash on cash records with only list and retrieve actions
    """
    queryset = CashOnCash.objects.all()
    serializer_class = CashOnCashSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['Period', 'marketer', 'client_no', 'plot_no', 'Regional_Category']
    search_fields = ['client_name', 'client_no', 'plot_no']
    ordering_fields = ['Period', 'Transaction_date', 'amount', 'bonus']
    ordering = ['-Period']

    @swagger_auto_schema( operation_description="List, filter, search, order cash on cash records")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve cash on cash record")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CommissionHeaderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for commission headers with only list and retrieve actions
    """
    queryset = CommissionHeader.objects.all()
    serializer_class = CommissionHeaderSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['emp_no', 'period_start_date', 'period_end_date', 'role']
    search_fields = ['emp_no__first_name', 'emp_no__last_name', 'role']
    ordering_fields = ['period_start_date', 'Total_commission', 'Deposit_amount']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order commission headers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve commission header")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

@swagger_tags(['SALES'])
class CommissionLineViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for commission lines with only list and retrieve actions
    """
    queryset = CommissionLine.objects.all()
    serializer_class = CommissionLineSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer_no', 'period_start_date', 'period_end_date', 'plot_number']
    search_fields = ['marketer_no__first_name', 'marketer_no__last_name', 'plot_number']
    ordering_fields = ['period_start_date', 'transaction_date', 'new_deposits_collected', 'installments_collected']
    ordering = ['-period_start_date']

    @swagger_auto_schema( operation_description="List, filter, search, order commission lines")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema( operation_description="Retrieve commission line")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

 

