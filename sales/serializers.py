from rest_framework import serializers
from .models import (
    LeadFile, 
    MarketersTarget, 
    HOSGMTarget, 
    TeamTarget, 
    PortfolioHeader, 
    PortfolioLine,
    CashOnCash,
    CommissionHeader,
    CommissionLine
)


class LeadFileSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for LeadFile with comprehensive relationship data
    """
    # Foreign key relationship fields
    marketer_name = serializers.CharField(source='marketer.fullnames', read_only=True)
    marketer_employee_no = serializers.CharField(source='marketer.employee_no', read_only=True)
    plot_number = serializers.CharField(source='plot.plot_no', read_only=True)
    plot_location = serializers.CharField(source='plot.location', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    customer_primary_email = serializers.CharField(source='customer_id.primary_email', read_only=True)
    customer_phone = serializers.CharField(source='customer_id.phone', read_only=True)
    lead_source_name = serializers.<PERSON>r<PERSON>ield(source='customer_lead_source.name', read_only=True)
    lead_source_category_name = serializers.CharField(source='cat_lead_source.name', read_only=True)
    plot = serializers.Char<PERSON>ield(source='plot.plot_no', read_only=True)
    marketer = serializers.CharField(source='marketer.fullnames', read_only=True)
    class Meta:
        model = LeadFile
        fields = '__all__'
        
    def to_representation(self, instance):
        rep = super().to_representation(instance)
        
        # Enhanced representation with relationship data
        rep['marketer_display'] = f"{instance.marketer.fullnames} ({instance.marketer.employee_no})" if instance.marketer else None
        rep['plot_display'] = f"{instance.plot.plot_no} - {instance.plot.location}" if instance.plot and hasattr(instance.plot, 'location') else (instance.plot.plot_no if instance.plot else None)
        rep['project_display'] = instance.project.name if instance.project else None
        rep['customer_display'] = f"{instance.customer_name} ({instance.customer_id.customer_no})" if instance.customer_id else instance.customer_name
        
        # Format decimal fields
        decimal_fields = ['purchase_price', 'selling_price', 'balance_lcy', 'commission_threshold', 
                         'deposit_threshold', 'discount', 'installment_amount', 'total_paid',
                         'transfer_cost_charged', 'transfer_cost_paid', 'overpayments', 'refunds',
                         'refundable_amount', 'penalties_accrued']
        
        for field in decimal_fields:
            if rep.get(field) is not None:
                rep[f'{field}_formatted'] = f"{float(rep[field]):,.2f}"
        
        return rep


class LeadFileExportSerializer(serializers.ModelSerializer):
    """
    Specialized serializer for LeadFile exports with flattened structure
    """
    # Flattened fields for export
    marketer_name = serializers.CharField(source='marketer.fullnames', read_only=True)
    marketer_employee_no = serializers.CharField(source='marketer.employee_no', read_only=True)
    marketer_email = serializers.CharField(source='marketer.email', read_only=True)
    plot_number = serializers.CharField(source='plot.plot_no', read_only=True)
    plot_size = serializers.CharField(source='plot.plot_size', read_only=True)
    project_name = serializers.CharField(source='project.name', read_only=True)
    project_location = serializers.CharField(source='project.location', read_only=True)
    customer_primary_email = serializers.CharField(source='customer_id.primary_email', read_only=True)
    customer_phone = serializers.CharField(source='customer_id.phone', read_only=True)
    customer_type = serializers.CharField(source='customer_id.customer_type', read_only=True)
    lead_source_name = serializers.CharField(source='customer_lead_source.name', read_only=True)
    lead_source_category_name = serializers.CharField(source='cat_lead_source.name', read_only=True)
    
    class Meta:
        model = LeadFile
        fields = [
            'lead_file_no', 'lead_file_status_dropped', 'plot_no', 'customer_name',
            'purchase_type', 'lead_type', 'purchase_price', 'selling_price', 'balance_lcy',
            'commission_threshold', 'deposit_threshold', 'discount', 'completion_date',
            'no_of_installments', 'installment_amount', 'sale_agreement_sent',
            'sale_agreement_signed', 'total_paid', 'transfer_cost_charged',
            'transfer_cost_paid', 'overpayments', 'refunds', 'refundable_amount',
            'penalties_accrued', 'booking_id', 'booking_date', 'additional_deposit_date',
            'title_status', 'credit_officer_id',
            # Related fields
            'marketer_name', 'marketer_employee_no', 'marketer_email',
            'plot_number', 'plot_size', 'project_name', 'project_location',
            'customer_primary_email', 'customer_phone', 'customer_type',
            'lead_source_name', 'lead_source_category_name'
        ]

class MarketersTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketersTarget
        fields = '__all__'

class HOSGMTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = HOSGMTarget
        fields = '__all__'

class TeamTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamTarget
        fields = '__all__'

class PortfolioLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = PortfolioLine
        fields = '__all__'

class PortfolioHeaderSerializer(serializers.ModelSerializer):
    portfolio_lines = PortfolioLineSerializer(many=True, read_only=True)

    class Meta:
        model = PortfolioHeader
        fields = '__all__'

class CashOnCashSerializer(serializers.ModelSerializer):
    class Meta:
        model = CashOnCash
        fields = '__all__'

class CommissionHeaderSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommissionHeader
        fields = '__all__'

class CommissionLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommissionLine
        fields = '__all__'


