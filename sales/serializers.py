from rest_framework import serializers
from .models import (
    LeadFile, 
    MarketersTarget, 
    HOSGMTarget, 
    TeamTarget, 
    PortfolioHeader, 
    PortfolioLine,
    CashOnCash,
    CommissionHeader,
    CommissionLine
)


class LeadFileSerializer(serializers.ModelSerializer):
    """
    Serializer for LeadFile
    """
    class Meta:
        model = LeadFile
        fields = '__all__'
        
    def to_representation(self, instance):
        rep = super().to_representation(instance)

        # Replace IDs with nested objects
        rep['marketer'] = instance.marketer.fullnames if instance.marketer else None
        rep['plot'] = instance.plot.plot_no if instance.plot else None
        rep['project'] = instance.project.name if instance.project else None

        return rep

class MarketersTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketersTarget
        fields = '__all__'

class HOSGMTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = HOSGMTarget
        fields = '__all__'

class TeamTargetSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamTarget
        fields = '__all__'

class PortfolioLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = PortfolioLine
        fields = '__all__'

class PortfolioHeaderSerializer(serializers.ModelSerializer):
    portfolio_lines = PortfolioLineSerializer(many=True, read_only=True)

    class Meta:
        model = PortfolioHeader
        fields = '__all__'

class CashOnCashSerializer(serializers.ModelSerializer):
    class Meta:
        model = CashOnCash
        fields = '__all__'

class CommissionHeaderSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommissionHeader
        fields = '__all__'

class CommissionLineSerializer(serializers.ModelSerializer):
    class Meta:
        model = CommissionLine
        fields = '__all__'


