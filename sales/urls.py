from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    LeadFileViewSet,
    LeadFileFilterView,
    MarketersTargetViewSet,
    HOSGMTargetViewSet,
    TeamTargetViewSet,
    PortfolioHeaderViewSet,
    PortfolioLineViewSet,
    CashOnCashViewSet,
    CommissionHeaderViewSet,
    CommissionLineViewSet
)


router = DefaultRouter()
router.register(r'lead-files', LeadFileViewSet)
router.register(r'lead-files-filter', LeadFileFilterView, basename='lead-files-filter')
router.register(r'marketers-targets', MarketersTargetViewSet)
router.register(r'hosgm-targets', HOSGMTargetViewSet)
router.register(r'team-targets', TeamTargetViewSet)
router.register(r'portfolio-headers', PortfolioHeaderViewSet)
router.register(r'portfolio-lines', PortfolioLineViewSet)
router.register(r'cash-on-cash', CashOnCashViewSet)
router.register(r'commission-headers', CommissionHeaderViewSet)
router.register(r'commission-lines', CommissionLineViewSet)
router.register(r'sales-views', LeadFileViewSet, basename='sales-views')
router.register(r'ongoing-sales', LeadFileViewSet, basename='ongoing-sales')
router.register(r'completed-sales', LeadFileViewSet, basename='completed-sales')

urlpatterns = [
    path('', include(router.urls)),
]