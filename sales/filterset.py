from django_filters import rest_framework as filters
from django import forms
from sales.models import LeadFile
from customers.models import Customer
from inventory.models import Project, Plot
from users.models import User
from leads.models import LeadSource, LeadSourceCategory, LeadSourceSubCategory

class MultipleLeadSourceFilter(filters.BaseInFilter, filters.CharFilter):
    """
    Custom filter to handle multiple lead source names
    Accepts comma-separated values like: ?lead_source=Facebook,Website,Referral
    """
    pass

class MultipleMarketerFilter(filters.BaseInFilter, filters.CharFilter):
    """
    Custom filter to handle multiple marketer employee numbers
    Accepts comma-separated values like: ?marketer=EMP001,EMP002,EMP003
    """
    pass

class MultipleProjectFilter(filters.BaseInFilter, filters.CharFilter):
    """
    Custom filter to handle multiple project IDs
    Accepts comma-separated values like: ?project=PROJ001,PROJ002,PROJ003
    """
    pass

class MultipleCustomerFilter(filters.BaseInFilter, filters.CharFilter):
    """
    Custom filter to handle multiple customer numbers
    Accepts comma-separated values like: ?customer=CUST001,CUST002,CUST003
    """
    pass

class LeadFileFilter(filters.FilterSet):
    """Enhanced custom filter for LeadFile with comprehensive filtering options"""
    
    # Define choices explicitly to avoid DeferredAttribute issues
    PURCHASE_TYPE_CHOICES = [
        ('Cash', 'Cash'),
        ('Installment', 'Installment'),
        ('Financing', 'Financing')
    ]
    
    LEAD_TYPE_CHOICES = [
        ('Legacy', 'Legacy'),
        ('Normal', 'Normal'),
        ('Diaspora', 'Diaspora'),
        ('Diaspora Legacy', 'Diaspora Legacy'),
    ]
    
    # Basic text filters
    lead_file_no = filters.CharFilter(lookup_expr='icontains')
    plot_no = filters.CharFilter(lookup_expr='icontains')
    customer_name = filters.CharFilter(lookup_expr='icontains')
    purchase_type = filters.ChoiceFilter(choices=PURCHASE_TYPE_CHOICES)
    lead_type = filters.ChoiceFilter(choices=LEAD_TYPE_CHOICES)
    booking_id = filters.CharFilter(lookup_expr='icontains')
    title_status = filters.CharFilter(lookup_expr='icontains')
    credit_officer_id = filters.CharFilter(lookup_expr='icontains')
    
    # Boolean filters
    lead_file_status_dropped = filters.BooleanFilter()
    
    # Multiple foreign key filters
    marketer = MultipleMarketerFilter(
        field_name='marketer__employee_no',
        lookup_expr='in',
        help_text='Filter by one or more marketer employee numbers. Use comma-separated values: EMP001,EMP002,EMP003'
    )
    
    project = MultipleProjectFilter(
        field_name='project__projectId',
        lookup_expr='in',
        help_text='Filter by one or more project IDs. Use comma-separated values: PROJ001,PROJ002,PROJ003'
    )
    
    customer_id = MultipleCustomerFilter(
        field_name='customer_id__customer_no',
        lookup_expr='in',
        help_text='Filter by one or more customer numbers. Use comma-separated values: CUST001,CUST002,CUST003'
    )
    
    customer_lead_source = MultipleLeadSourceFilter(
        field_name='customer_lead_source__leadsource_id', 
        lookup_expr='in',
        help_text='Filter by one or more lead source names. Use comma-separated values: Facebook,Website,Referral'
    )
    
    lead_source_subcategory = MultipleLeadSourceFilter(
        field_name='customer_lead_source__lead_source_subcategory__cat_lead_source_id', 
        lookup_expr='in',
        help_text='Filter by one or more lead source sub category. Use comma-separated values: 123,456,778'
    )
    
    # Decimal field comparison filters - Purchase Price
    purchase_price = filters.NumberFilter(lookup_expr='exact')
    purchase_price_gt = filters.NumberFilter(
        field_name='purchase_price',
        lookup_expr='gt',
        help_text='Purchase price greater than specified amount'
    )
    purchase_price_gte = filters.NumberFilter(
        field_name='purchase_price',
        lookup_expr='gte',
        help_text='Purchase price greater than or equal to specified amount'
    )
    purchase_price_lt = filters.NumberFilter(
        field_name='purchase_price',
        lookup_expr='lt',
        help_text='Purchase price less than specified amount'
    )
    purchase_price_lte = filters.NumberFilter(
        field_name='purchase_price',
        lookup_expr='lte',
        help_text='Purchase price less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Selling Price
    selling_price = filters.NumberFilter(lookup_expr='exact')
    selling_price_gt = filters.NumberFilter(
        field_name='selling_price',
        lookup_expr='gt',
        help_text='Selling price greater than specified amount'
    )
    selling_price_gte = filters.NumberFilter(
        field_name='selling_price',
        lookup_expr='gte',
        help_text='Selling price greater than or equal to specified amount'
    )
    selling_price_lt = filters.NumberFilter(
        field_name='selling_price',
        lookup_expr='lt',
        help_text='Selling price less than specified amount'
    )
    selling_price_lte = filters.NumberFilter(
        field_name='selling_price',
        lookup_expr='lte',
        help_text='Selling price less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Balance LCY
    balance_lcy = filters.NumberFilter(lookup_expr='exact')
    balance_lcy_gt = filters.NumberFilter(
        field_name='balance_lcy',
        lookup_expr='gt',
        help_text='Balance greater than specified amount'
    )
    balance_lcy_gte = filters.NumberFilter(
        field_name='balance_lcy',
        lookup_expr='gte',
        help_text='Balance greater than or equal to specified amount'
    )
    balance_lcy_lt = filters.NumberFilter(
        field_name='balance_lcy',
        lookup_expr='lt',
        help_text='Balance less than specified amount'
    )
    balance_lcy_lte = filters.NumberFilter(
        field_name='balance_lcy',
        lookup_expr='lte',
        help_text='Balance less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Commission Threshold
    commission_threshold = filters.NumberFilter(lookup_expr='exact')
    commission_threshold_gt = filters.NumberFilter(
        field_name='commission_threshold',
        lookup_expr='gt',
        help_text='Commission threshold greater than specified amount'
    )
    commission_threshold_gte = filters.NumberFilter(
        field_name='commission_threshold',
        lookup_expr='gte',
        help_text='Commission threshold greater than or equal to specified amount'
    )
    commission_threshold_lt = filters.NumberFilter(
        field_name='commission_threshold',
        lookup_expr='lt',
        help_text='Commission threshold less than specified amount'
    )
    commission_threshold_lte = filters.NumberFilter(
        field_name='commission_threshold',
        lookup_expr='lte',
        help_text='Commission threshold less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Deposit Threshold
    deposit_threshold = filters.NumberFilter(lookup_expr='exact')
    deposit_threshold_gt = filters.NumberFilter(
        field_name='deposit_threshold',
        lookup_expr='gt',
        help_text='Deposit threshold greater than specified amount'
    )
    deposit_threshold_gte = filters.NumberFilter(
        field_name='deposit_threshold',
        lookup_expr='gte',
        help_text='Deposit threshold greater than or equal to specified amount'
    )
    deposit_threshold_lt = filters.NumberFilter(
        field_name='deposit_threshold',
        lookup_expr='lt',
        help_text='Deposit threshold less than specified amount'
    )
    deposit_threshold_lte = filters.NumberFilter(
        field_name='deposit_threshold',
        lookup_expr='lte',
        help_text='Deposit threshold less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Total Paid
    total_paid = filters.NumberFilter(lookup_expr='exact')
    total_paid_gt = filters.NumberFilter(
        field_name='total_paid',
        lookup_expr='gt',
        help_text='Total paid greater than specified amount'
    )
    total_paid_gte = filters.NumberFilter(
        field_name='total_paid',
        lookup_expr='gte',
        help_text='Total paid greater than or equal to specified amount'
    )
    total_paid_lt = filters.NumberFilter(
        field_name='total_paid',
        lookup_expr='lt',
        help_text='Total paid less than specified amount'
    )
    total_paid_lte = filters.NumberFilter(
        field_name='total_paid',
        lookup_expr='lte',
        help_text='Total paid less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Discount
    discount = filters.NumberFilter(lookup_expr='exact')
    discount_gt = filters.NumberFilter(
        field_name='discount',
        lookup_expr='gt',
        help_text='Discount greater than specified amount'
    )
    discount_gte = filters.NumberFilter(
        field_name='discount',
        lookup_expr='gte',
        help_text='Discount greater than or equal to specified amount'
    )
    discount_lt = filters.NumberFilter(
        field_name='discount',
        lookup_expr='lt',
        help_text='Discount less than specified amount'
    )
    discount_lte = filters.NumberFilter(
        field_name='discount',
        lookup_expr='lte',
        help_text='Discount less than or equal to specified amount'
    )
    
    # Decimal field comparison filters - Installment Amount
    installment_amount = filters.NumberFilter(lookup_expr='exact')
    installment_amount_gt = filters.NumberFilter(
        field_name='installment_amount',
        lookup_expr='gt',
        help_text='Installment amount greater than specified amount'
    )
    installment_amount_gte = filters.NumberFilter(
        field_name='installment_amount',
        lookup_expr='gte',
        help_text='Installment amount greater than or equal to specified amount'
    )
    installment_amount_lt = filters.NumberFilter(
        field_name='installment_amount',
        lookup_expr='lt',
        help_text='Installment amount less than specified amount'
    )
    installment_amount_lte = filters.NumberFilter(
        field_name='installment_amount',
        lookup_expr='lte',
        help_text='Installment amount less than or equal to specified amount'
    )
    
    # Integer field filters - Number of Installments
    no_of_installments = filters.NumberFilter(lookup_expr='exact')
    no_of_installments_gt = filters.NumberFilter(
        field_name='no_of_installments',
        lookup_expr='gt',
        help_text='Number of installments greater than specified value'
    )
    no_of_installments_gte = filters.NumberFilter(
        field_name='no_of_installments',
        lookup_expr='gte',
        help_text='Number of installments greater than or equal to specified value'
    )
    no_of_installments_lt = filters.NumberFilter(
        field_name='no_of_installments',
        lookup_expr='lt',
        help_text='Number of installments less than specified value'
    )
    no_of_installments_lte = filters.NumberFilter(
        field_name='no_of_installments',
        lookup_expr='lte',
        help_text='Number of installments less than or equal to specified value'
    )
    
    # Date field comparison filters - Completion Date
    completion_date = filters.DateFilter(
        lookup_expr='exact',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    completion_date_gt = filters.DateFilter(
        field_name='completion_date',
        lookup_expr='gt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Completion date after specified date'
    )
    completion_date_gte = filters.DateFilter(
        field_name='completion_date',
        lookup_expr='gte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Completion date on or after specified date'
    )
    completion_date_lt = filters.DateFilter(
        field_name='completion_date',
        lookup_expr='lt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Completion date before specified date'
    )
    completion_date_lte = filters.DateFilter(
        field_name='completion_date',
        lookup_expr='lte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Completion date on or before specified date'
    )
    
    # Date field comparison filters - Booking Date
    booking_date = filters.DateFilter(
        lookup_expr='exact',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    booking_date_gt = filters.DateFilter(
        field_name='booking_date',
        lookup_expr='gt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Booking date after specified date'
    )
    booking_date_gte = filters.DateFilter(
        field_name='booking_date',
        lookup_expr='gte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Booking date on or after specified date'
    )
    booking_date_lt = filters.DateFilter(
        field_name='booking_date',
        lookup_expr='lt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Booking date before specified date'
    )
    booking_date_lte = filters.DateFilter(
        field_name='booking_date',
        lookup_expr='lte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Booking date on or before specified date'
    )
    
    # Date field comparison filters - Additional Deposit Date
    additional_deposit_date = filters.DateFilter(
        lookup_expr='exact',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    additional_deposit_date_gt = filters.DateFilter(
        field_name='additional_deposit_date',
        lookup_expr='gt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Additional deposit date after specified date'
    )
    additional_deposit_date_gte = filters.DateFilter(
        field_name='additional_deposit_date',
        lookup_expr='gte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Additional deposit date on or after specified date'
    )
    additional_deposit_date_lt = filters.DateFilter(
        field_name='additional_deposit_date',
        lookup_expr='lt',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Additional deposit date before specified date'
    )
    additional_deposit_date_lte = filters.DateFilter(
        field_name='additional_deposit_date',
        lookup_expr='lte',
        widget=forms.DateInput(attrs={'type': 'date'}),
        help_text='Additional deposit date on or before specified date'
    )
    
    class Meta:
        model = LeadFile
        fields = {
            'lead_file_no': ['exact', 'icontains'],
            'lead_file_status_dropped': ['exact'],
            'plot_no': ['exact', 'icontains'],
            'customer_name': ['exact', 'icontains'],
            'purchase_type': ['exact'],
            'lead_type': ['exact'],
            'booking_id': ['exact', 'icontains'],
            'title_status': ['exact', 'icontains'],
            'credit_officer_id': ['exact', 'icontains'],
            'sale_agreement_sent': ['exact', 'icontains'],
            'sale_agreement_signed': ['exact', 'icontains'],
        }