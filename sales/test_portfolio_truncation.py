"""
Test script for portfolio truncation functionality.
Run this to verify the task works correctly.
"""

from django.test import TestCase
from django.core.management import call_command
from sales.models import PortfolioHeader, PortfolioLine
from sales.tasks import truncate_portfolio_models_safe
import io
import sys

def test_management_command():
    """Test the management command functionality"""
    print("Testing management command...")
    
    # Create some test data
    print("Creating test data...")
    
    # Note: You would need to create valid test data here
    # For now, we'll just test the command structure
    
    # Capture command output
    out = io.StringIO()
    try:
        call_command('truncate_portfolio', stdout=out)
        output = out.getvalue()
        print("Command output (without --confirm):")
        print(output)
        assert "Use --confirm to proceed" in output
        print("✓ Management command works correctly (confirmation required)")
    except Exception as e:
        print(f"✗ Management command failed: {e}")

def test_celery_task():
    """Test the Celery task functionality"""
    print("\nTesting Celery task...")
    
    try:
        # Get initial counts
        initial_header_count = PortfolioHeader.objects.count()
        initial_line_count = PortfolioLine.objects.count()
        
        print(f"Initial counts - Headers: {initial_header_count}, Lines: {initial_line_count}")
        
        # Run the safe task (doesn't require task queue)
        result = truncate_portfolio_models_safe()
        
        print("Task result:", result)
        
        # Verify truncation
        final_header_count = PortfolioHeader.objects.count()
        final_line_count = PortfolioLine.objects.count()
        
        print(f"Final counts - Headers: {final_header_count}, Lines: {final_line_count}")
        
        if final_header_count == 0 and final_line_count == 0:
            print("✓ Celery task works correctly")
        else:
            print("✗ Celery task did not truncate all records")
            
    except Exception as e:
        print(f"✗ Celery task failed: {e}")

def main():
    """Run all tests"""
    print("Portfolio Truncation Test Suite")
    print("=" * 40)
    
    test_management_command()
    test_celery_task()
    
    print("\n" + "=" * 40)
    print("Test suite completed")

if __name__ == "__main__":
    # This would be run in Django shell or as a Django management command
    print("Run this in Django shell: python manage.py shell < test_portfolio_truncation.py")