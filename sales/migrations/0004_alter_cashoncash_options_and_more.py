# Generated by Django 5.1.7 on 2025-07-01 14:25

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0003_alter_marketerstarget_unique_together'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='cashoncash',
            options={'ordering': ['-Period'], 'verbose_name': 'Cash on Cash', 'verbose_name_plural': 'Cash on Cash'},
        ),
        migrations.AlterModelOptions(
            name='commissionheader',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Commission Header', 'verbose_name_plural': 'Commission Headers'},
        ),
        migrations.AlterModelOptions(
            name='commissionline',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Commission Line', 'verbose_name_plural': 'Commission Lines'},
        ),
        migrations.AlterModelOptions(
            name='hosgmtarget',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'HOS/GM Target', 'verbose_name_plural': 'HOS/GM Targets'},
        ),
        migrations.AlterModelOptions(
            name='leadfile',
            options={'ordering': ['-lead_file_no']},
        ),
        migrations.AlterModelOptions(
            name='marketerstarget',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Marketer Target', 'verbose_name_plural': 'Marketer Targets'},
        ),
        migrations.AlterModelOptions(
            name='portfolioheader',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Portfolio Header', 'verbose_name_plural': 'Portfolio Headers'},
        ),
        migrations.AlterModelOptions(
            name='portfolioline',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Portfolio Line', 'verbose_name_plural': 'Portfolio Lines'},
        ),
        migrations.AlterModelOptions(
            name='teamtarget',
            options={'ordering': ['-period_start_date'], 'verbose_name': 'Team Target', 'verbose_name_plural': 'Team Targets'},
        ),
    ]
