# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0002_initial'),
        ('inventory', '0002_initial'),
        ('leads', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HOSGMTarget',
            fields=[
                ('line_no', models.IntegerField(primary_key=True, serialize=False)),
                ('marketer_no', models.Char<PERSON>ield(max_length=30)),
                ('marketer_name', models.Char<PERSON>ield(max_length=255)),
                ('title', models.CharField(max_length=255)),
                ('status', models.CharField(max_length=20)),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('monthly_target', models.DecimalField(decimal_places=2, max_digits=20)),
                ('daily_target', models.DecimalField(decimal_places=2, max_digits=20)),
                ('MIB_achieved', models.DecimalField(decimal_places=2, max_digits=20)),
                ('MIB_Perfomance', models.DecimalField(decimal_places=2, max_digits=20)),
                ('commission_rate', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('commission_payable', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
            ],
            options={
                'verbose_name': 'HOS/GM Target',
                'verbose_name_plural': 'HOS/GM Targets',
                'db_table': 'hos_gm_targets',
            },
        ),
        migrations.CreateModel(
            name='TeamTarget',
            fields=[
                ('line_no', models.IntegerField(primary_key=True, serialize=False)),
                ('team', models.CharField(max_length=255)),
                ('period_start_date', models.DateField(blank=True, null=True)),
                ('period_end_date', models.DateField(blank=True, null=True)),
                ('monthly_target', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('daily_target', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('MIB_achieved', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('MIB_Perfomance', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
            ],
            options={
                'verbose_name': 'Team Target',
                'verbose_name_plural': 'Team Targets',
                'db_table': 'teams_targets',
            },
        ),
        migrations.CreateModel(
            name='CashOnCash',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Period', models.DateField()),
                ('Transaction_date', models.DateField()),
                ('client_no', models.CharField(max_length=100)),
                ('client_name', models.CharField(max_length=200)),
                ('plot_no', models.CharField(max_length=100)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('bonus', models.DecimalField(decimal_places=2, max_digits=12)),
                ('Regional_Category', models.CharField(max_length=100)),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'verbose_name': 'Cash on Cash',
                'verbose_name_plural': 'Cash on Cash',
                'db_table': 'cash_on_cash',
            },
        ),
        migrations.CreateModel(
            name='CommissionHeader',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('role', models.CharField(max_length=100)),
                ('Deposit_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('deposit_perc', models.DecimalField(decimal_places=2, max_digits=5)),
                ('deposit_commission', models.DecimalField(decimal_places=2, max_digits=12)),
                ('installment_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('installment_perc', models.DecimalField(decimal_places=2, max_digits=5)),
                ('installment_commission', models.DecimalField(decimal_places=2, max_digits=12)),
                ('Total_commission', models.DecimalField(decimal_places=2, max_digits=12)),
                ('Tl_gained_comm_from_members', models.DecimalField(decimal_places=2, max_digits=12)),
                ('rm_achieved_MIB', models.DecimalField(decimal_places=2, max_digits=12)),
                ('rm_commission_rate', models.DecimalField(decimal_places=2, max_digits=5)),
                ('rm_commission_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('commisison_payable_TL', models.DecimalField(decimal_places=2, max_digits=12)),
                ('emp_no', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_emp_no', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'verbose_name': 'Commission Header',
                'verbose_name_plural': 'Commission Headers',
                'db_table': 'commission_headers',
            },
        ),
        migrations.CreateModel(
            name='CommissionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('transaction_date', models.DateField()),
                ('plot_number', models.CharField(max_length=100)),
                ('new_deposits_collected', models.DecimalField(decimal_places=2, max_digits=12)),
                ('installments_collected', models.DecimalField(decimal_places=2, max_digits=12)),
                ('marketer_no', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_marketer_emp_no', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'verbose_name': 'Commission Line',
                'verbose_name_plural': 'Commission Lines',
                'db_table': 'commission_lines',
            },
        ),
        migrations.CreateModel(
            name='LeadFile',
            fields=[
                ('lead_file_no', models.CharField(max_length=30, primary_key=True, serialize=False)),
                ('lead_file_status_dropped', models.BooleanField(default=False)),
                ('purchase_price', models.DecimalField(decimal_places=2, max_digits=20)),
                ('selling_price', models.DecimalField(decimal_places=2, max_digits=20)),
                ('balance_lcy', models.DecimalField(decimal_places=2, max_digits=20)),
                ('customer_name', models.TextField(max_length=255)),
                ('purchase_type', models.CharField(choices=[('Cash', 'Cash'), ('Installment', 'Installment'), ('Financing', 'Financing')], max_length=255)),
                ('commission_threshold', models.DecimalField(decimal_places=2, max_digits=20)),
                ('deposit_threshold', models.DecimalField(decimal_places=2, max_digits=20)),
                ('discount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('completion_date', models.DateField()),
                ('no_of_installments', models.IntegerField(blank=True, null=True)),
                ('installment_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('sale_agreement_sent', models.CharField(blank=True, max_length=20, null=True)),
                ('sale_agreement_signed', models.CharField(blank=True, max_length=20, null=True)),
                ('total_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=20)),
                ('transfer_cost_charged', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('transfer_cost_paid', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('overpayments', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('refunds', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('refundable_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('penalties_accrued', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('booking_id', models.CharField(blank=True, max_length=30, null=True)),
                ('booking_date', models.DateField(blank=True, null=True)),
                ('additional_deposit_date', models.DateField(blank=True, null=True)),
                ('title_status', models.CharField(blank=True, max_length=255, null=True)),
                ('credit_officer_id', models.CharField(blank=True, max_length=150, null=True)),
                ('cat_lead_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_file_cat_lead_source', to='leads.leadsourcecategory', to_field='name')),
                ('customer_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_file_customer', to='customers.customer')),
                ('customer_lead_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_file_customer_lead_source', to='leads.leadsource', to_field='name')),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_file_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('plot', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.plot')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='inventory.project')),
            ],
        ),
        migrations.CreateModel(
            name='MarketersTarget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('monthly_target', models.DecimalField(decimal_places=2, max_digits=12)),
                ('daily_target', models.DecimalField(decimal_places=2, max_digits=12)),
                ('MIB_achieved', models.DecimalField(decimal_places=2, max_digits=12)),
                ('MIB_Perfomance', models.DecimalField(decimal_places=2, max_digits=12)),
                ('marketer_no', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marketer_targets', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'verbose_name': 'Marketer Target',
                'verbose_name_plural': 'Marketer Targets',
                'db_table': 'marketer_targets',
            },
        ),
        migrations.CreateModel(
            name='PortfolioHeader',
            fields=[
                ('line_no', models.AutoField(primary_key=True, serialize=False)),
                ('marketer_no', models.CharField(max_length=20)),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('total_purchases', models.DecimalField(decimal_places=2, max_digits=20)),
                ('team', models.CharField(max_length=20)),
                ('region', models.CharField(max_length=20)),
                ('total_collections', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_to_collect', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_installments_due', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_installments_collected', models.DecimalField(decimal_places=5, max_digits=20)),
                ('total_overdue', models.DecimalField(decimal_places=5, max_digits=20)),
                ('total_overdue_collected', models.DecimalField(decimal_places=5, max_digits=20)),
                ('total_previous_unpaid', models.DecimalField(decimal_places=5, max_digits=20)),
                ('portfolio_balance', models.DecimalField(decimal_places=5, max_digits=20)),
                ('marketer_target', models.DecimalField(decimal_places=5, max_digits=20)),
                ('MIB_perfomance', models.DecimalField(decimal_places=0, max_digits=10)),
                ('locality', models.CharField(max_length=255)),
            ],
            options={
                'verbose_name': 'Portfolio Header',
                'verbose_name_plural': 'Portfolio Headers',
                'db_table': 'portfolio_headers',
                'unique_together': {('marketer_no', 'period_start_date', 'period_end_date')},
            },
        ),
        migrations.CreateModel(
            name='PortfolioLine',
            fields=[
                ('no', models.AutoField(primary_key=True, serialize=False)),
                ('marketer_no', models.CharField(max_length=20)),
                ('period_start_date', models.DateField()),
                ('period_end_date', models.DateField()),
                ('lead_file_no', models.CharField(max_length=20)),
                ('plot_name', models.CharField(max_length=20)),
                ('installments_due', models.DecimalField(decimal_places=2, max_digits=20)),
                ('installments_due_collected', models.DecimalField(decimal_places=2, max_digits=20)),
                ('overdue_collections', models.DecimalField(decimal_places=2, max_digits=20)),
                ('overdue_collections_collected', models.DecimalField(decimal_places=2, max_digits=20)),
                ('previous_unpaid', models.DecimalField(decimal_places=2, max_digits=20)),
                ('total_to_collect', models.DecimalField(decimal_places=2, max_digits=20)),
                ('customer_number', models.CharField(max_length=20)),
                ('total_previously_collected', models.DecimalField(decimal_places=2, max_digits=20)),
                ('penalties_accrued', models.DecimalField(decimal_places=2, max_digits=20)),
                ('current_balance', models.DecimalField(decimal_places=2, max_digits=20)),
                ('due_date', models.DateField()),
                ('completion_date', models.DateField()),
                ('booking_date', models.DateField()),
                ('total_collected', models.DecimalField(decimal_places=5, max_digits=20)),
            ],
            options={
                'verbose_name': 'Portfolio Line',
                'verbose_name_plural': 'Portfolio Lines',
                'db_table': 'portfolio_lines',
                'unique_together': {('lead_file_no', 'period_start_date')},
            },
        ),
    ]
