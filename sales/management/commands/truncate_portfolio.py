from django.core.management.base import BaseCommand
from django.db import connection
from sales.models import PortfolioHeader, PortfolioLine
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Truncate PortfolioHeader and PortfolioLine models'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm the truncation operation',
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(
                self.style.WARNING(
                    'This command will delete ALL data from PortfolioHeader and PortfolioLine tables. '
                    'Use --confirm to proceed.'
                )
            )
            return

        try:
            self.stdout.write('Starting portfolio models truncation...')
            
            # Get initial counts
            portfolio_header_count = PortfolioHeader.objects.count()
            portfolio_line_count = PortfolioLine.objects.count()
            
            self.stdout.write(f'Found {portfolio_header_count} PortfolioHeader records')
            self.stdout.write(f'Found {portfolio_line_count} PortfolioLine records')
            
            # Use raw SQL for better performance on large tables
            with connection.cursor() as cursor:
                # Truncate PortfolioLine first (in case of any foreign key constraints)
                cursor.execute('TRUNCATE TABLE portfolio_lines RESTART IDENTITY CASCADE')
                self.stdout.write('Truncated PortfolioLine table')
                
                # Truncate PortfolioHeader
                cursor.execute('TRUNCATE TABLE portfolio_headers RESTART IDENTITY CASCADE')
                self.stdout.write('Truncated PortfolioHeader table')
            
            # Log the operation
            logger.info(f'Portfolio truncation completed. Removed {portfolio_header_count} PortfolioHeader and {portfolio_line_count} PortfolioLine records')
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully truncated portfolio tables. '
                    f'Removed {portfolio_header_count} PortfolioHeader and {portfolio_line_count} PortfolioLine records.'
                )
            )
            
        except Exception as e:
            error_msg = f'Error truncating portfolio tables: {str(e)}'
            logger.error(error_msg)
            self.stdout.write(self.style.ERROR(error_msg))
            raise