import requests
from requests.auth import <PERSON><PERSON>P<PERSON>asic<PERSON>uth
from urllib.parse import quote
from requests_ntlm import HttpNtlm<PERSON>uth
from rest_framework import status, viewsets, filters
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from django.http import JsonResponse
from datetime import datetime
import logging
from offerletters.models import OfferLetterMain, OfferLetterGroup, OfferLetterGroupMember, OfferLetterIndividual,OfferLetterPartner,OfferLetterCompany,OfferLetterCompanyDirector,OfferLetterNextOfKin
from external_api.helpers import build_group_payload, build_group_member_payload, build_individual_payload, build_partner_payload, build_partner_member_payload, build_company_payload,build_company_member_payload, build_next_of_kin_payload
from rest_framework.decorators import api_view
from drf_yasg import openapi
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
logger = logging.getLogger(__name__)
company_name = quote("Optiven R.E")
# # Authentication credentials
auth = HttpNtlmAuth('Dennis.Mwendwa', 'Den9@24!#')

# # Headers
headers = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'application/json',
    'Content-Type': 'application/json',
}
def create_customer(data):
    # Define the API endpoint
    url = f"http://***********:8048/optiven/ODataV4/Company('{company_name}')/members"
    # Make the POST request
    response = requests.post(url, auth=auth, headers=headers, json=data)
    if response.status_code == 201: 
        return response.json()
    else:
        logger.error(f"Failed to create customer: {response.status_code} - {response.text}")
        return None
    

def sync_offer_letter_to_bc(offer_letter: OfferLetterMain):
    parent_response = None

    # --- Individual ---
    if offer_letter.customer_type.lower() == "individual":
        individual = OfferLetterIndividual.objects.get(offer_letter=offer_letter)
        payload = build_individual_payload(individual,offer_letter)
        parent_response = create_customer(payload)

    # --- Group ---
    elif offer_letter.customer_type.lower() == "group":
        group = OfferLetterGroup.objects.get(offer_letter=offer_letter)
        group_payload = build_group_payload(group,offer_letter)
        parent_response = create_customer(group_payload)

        if parent_response:
            members = OfferLetterGroupMember.objects.filter(group=group)
            for idx, m in enumerate(members, start=1):
                member_payload = build_group_member_payload(m)
                member_payload["Group_Code"] = parent_response.get("No")
                member_payload["Group_Member_No"] = idx  

                url = f"http://***********:8048/optiven/ODataV4/Company('{company_name}')/groupmembers"

                check = requests.get(
                    f"{url}?$filter=National_ID eq '{m.national_id}' and Group_Code eq '{parent_response.get('No')}'",
                    auth=auth,
                    headers=headers
                )
                if check.status_code == 200 and check.json().get("value"):
                    logger.info(f"Member {m.first_name} {m.last_name} already exists, skipping...")
                    continue

                response = requests.post(url, auth=auth, headers=headers, json=member_payload)
                if response.status_code != 201:
                    logger.error(f"Failed to create group member {m.first_name} {m.last_name}: {response.status_code} - {response.text}")

    # --- Company ---
    elif offer_letter.customer_type.lower() == "company":
        company = OfferLetterCompany.objects.get(offer_letter=offer_letter)
        company_payload = build_company_payload(company,offer_letter)
        parent_response = create_customer(company_payload)

        if parent_response:
            directors = OfferLetterCompanyDirector.objects.filter(company=company)
            for idx, m in enumerate(directors, start=1):
                member_payload = build_company_member_payload(m)
                member_payload["Group_Code"] = parent_response.get("No")
                member_payload["Group_Member_No"] = idx  

                url = f"http://***********:8048/optiven/ODataV4/Company('{company_name}')/groupmembers"
                response = requests.post(url, auth=auth, headers=headers, json=member_payload)
                if response.status_code != 201:
                    logger.error(f"Failed to create company director {m.first_name} {m.last_name}: {response.status_code} - {response.text}")

    # --- Partner ---
    elif offer_letter.customer_type.lower() == "partner":
        partner_payload = build_partner_payload(offer_letter)
        parent_response = create_customer(partner_payload)

        if parent_response:
            members = OfferLetterPartner.objects.filter(offer_letter=offer_letter)
            for idx, m in enumerate(members, start=1):
                member_payload = build_partner_member_payload(m)
                member_payload["Group_Code"] = parent_response.get("No")
                member_payload["Group_Member_No"] = idx  

                url = f"http://***********:8048/optiven/ODataV4/Company('{company_name}')/groupmembers"

                check = requests.get(
                    f"{url}?$filter=National_ID eq '{m.national_id}' and Group_Code eq '{parent_response.get('No')}'",
                    auth=auth,
                    headers=headers
                )
                if check.status_code == 200 and check.json().get("value"):
                    logger.info(f"Partner member {m.first_name} {m.last_name} already exists, skipping...")
                    continue

                response = requests.post(url, auth=auth, headers=headers, json=member_payload)
                if response.status_code != 201:
                    logger.error(
                        f"Failed to create partner member {m.first_name} {m.last_name}: "
                        f"{response.status_code} - {response.text}"
                    )

    # --- Sync Next of Kin (applies to ALL customer types) ---
    if parent_response:
        customer_no = parent_response.get("No")
        if customer_no:
            next_of_kins = OfferLetterNextOfKin.objects.filter(offer_letter=offer_letter)
            for idx, kin in enumerate(next_of_kins, start=1):
                kin_payload = build_next_of_kin_payload(kin)
                kin_payload["Customer_No"] = customer_no
                kin_payload["Line_No"] = idx  

                url = f"http://***********:8048/optiven/ODataV4/Company('{company_name}')/nextofkin"

                response = requests.post(url, auth=auth, headers=headers, json=kin_payload)
                if response.status_code != 201:
                    logger.error(
                        f"Failed to create next of kin {kin.full_name}: "
                        f"{response.status_code} - {response.text}"
                    )
                else:
                    logger.info(f"Next of kin {kin.full_name} created successfully")

    return parent_response



swagger_auto_schema(
      # Adjust permissions as needed
    method="post",
    tags=["External API"],
    operation_summary="Sync Offer Letter to Business Central",
    permission_classes=[AllowAny],
    operation_description="""
    Syncs an Offer Letter and its related data (individual, group, company, or partner) 
    from CRM to Business Central.  

    - **individual** → creates a member  
    - **group** → creates group + members  
    - **company** → creates company + directors  
    - **partner** → creates partner + members  
    """,
    manual_parameters=[
        openapi.Parameter(
            "offer_letter_id",
            openapi.IN_PATH,
            description="The ID of the OfferLetterMain record to sync",
            type=openapi.TYPE_INTEGER,
            required=True,
        )
    ],
    responses={
        200: openapi.Response(
            description="Sync successful",
            examples={
                "application/json": {
                    "message": "Sync complete",
                    "data": {
                        "No": "CL000123",
                        "Name": "John Doe",
                        "Customer_Type": "Individual",
                        "Status": "Approved"
                    }
                }
            },
        ),
        404: openapi.Response(
            description="Offer Letter not found",
            examples={"application/json": {"error": "OfferLetter with id 123 not found"}},
        ),
        500: openapi.Response(
            description="Sync failed due to server error",
            examples={"application/json": {"error": "Detailed error message"}},
        ),
    },
)
  # Add appropriate permission classes as needed
@api_view(["POST"])
@permission_classes([AllowAny])
def sync_offer_letter_endpoint(request, offer_letter_id):
    try:
        offer_letter = OfferLetterMain.objects.get(id=offer_letter_id)
    except OfferLetterMain.DoesNotExist:
        return Response(
            {"error": f"OfferLetter with id {offer_letter_id} not found"},
            status=status.HTTP_404_NOT_FOUND,
        )

    try:
        response = sync_offer_letter_to_bc(offer_letter)
        return Response(
            {"message": "Sync complete", "data": response}, status=status.HTTP_200_OK
        )
    except Exception as e:
        logger.exception("Sync failed")
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


        
