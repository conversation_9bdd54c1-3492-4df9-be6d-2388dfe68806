from offerletters.models import OfferLetterMain, OfferLetterGroup, OfferLetterGroupMember, OfferLetterIndividual,OfferLetterPartner,OfferLetterCompany,OfferLetterCompanyDirector
from inventory.models import PlotBooking
def build_individual_payload(individual,offer_letter):
    return {
        "Name": f"{individual.first_name} {individual.last_name}".strip(),
        "Phone_No": individual.phone or "",
        "Primary_Email": individual.email or "",
        "National_ID": individual.national_id or "",
        "PIN_No": individual.KRA_Pin or "",
        "Date_of_Birth": individual.DOB.strftime("%Y-%m-%d") if individual.DOB else "0001-01-01",
        "Sales_Person_Code": getattr(offer_letter.booking_id, "marketer_id", "") if offer_letter.booking_id else "",
        "Country_of_Residence": individual.country or "Kenya",
        "City": individual.city or "",
        "Customer_Type": "Individual",
        "Customer_Posting_Group": "CUSTOMERS",  # adjust to match BC setup
    }

def build_group_payload(group,offer_letter):
    return {
        "Name": group.group_name,
        # "Phone_No": group.group_phone or "",
        # "Primary_Email": group.group_email or "",
        # "PIN_No": group.Group_KRA_PIN or "",
        # "Country_of_Residence": group.Group_country or "Kenya",
        # "City": group.Group_city or "",
        "Sales_Person_Code": getattr(offer_letter.booking_id, "marketer_id", "") if offer_letter.booking_id else "",
        "Customer_Type": "Group",
        "Customer_Posting_Group": "CUSTOMERS",
        # "Group_Code": group.group_code,   # required for linking groupmembers
    }

def build_group_member_payload(member):
    return {
        "Group_Code": member.group.group_code,
        "Group_Member_No": member.member_id,  # your PK/sequence
        "National_ID": member.national_id,
        "Member_Name": f"{member.first_name} {member.last_name}",
        # "PIN_No": "",  # if you store per-member KRA/Tax Pin, map here
        # "Phone_No": member.phone or "",
        # "Email": member.email or "",
        # "Alternative_Email": "",
        # "Date_of_Birth": "0001-01-01",
        # "Gender": "",
        # "Ethnicity": "",
        # "Citizenship": "",
        # "Country_of_Residence": member.group.Group_country or "Kenya",
        # "City": member.group.Group_city or "",
        # "State": "",
        # "Age": "0",
        # "Account_Balance": 0,
    }
def build_partner_payload(offer_letter):
    # Fetch all partners for this offer letter
    partners = OfferLetterPartner.objects.filter(offer_letter=offer_letter)

    # Combine their names into one string
    partner_names = " & ".join([f"{p.first_name} {p.last_name}" for p in partners])

    payload = {
        "Name": partner_names,
        "Customer_Type": "Partners",
        "Customer_Posting_Group": "CUSTOMERS",  # Adjust as per BC config
        "Sales_Person_Code": getattr(offer_letter.booking_id, "marketer_id", "") if offer_letter.booking_id else "",
        # Optional fields if needed:
        # "Phone_No": partners.first().phone if partners.exists() else "",
        # "Primary_Email": partners.first().email if partners.exists() else "",
        # "PIN_No": partners.first().KRA_Pin if partners.exists() else "",
        # "Country_of_Residence": "Kenya",
        # "City": partners.first().city if partners.exists() else "",
    }
    return payload
 # adjust to match BC setup
    
def build_partner_member_payload(partner_member):
    return {
        # "Group_Code": partner_member.group.group_code,
        # "Group_Member_No": partner_member.member_id,  # your PK/sequence
        "National_ID": partner_member.national_id,
        "Member_Name": f"{partner_member.first_name} {partner_member.last_name}",
        # "PIN_No": "",  # if you store per-partner_member KRA/Tax Pin, map here
        # "Phone_No": partner_member.phone or "",
        # "Email": partner_member.email or "",
        # "Alternative_Email": "",
        # "Date_of_Birth": "0001-01-01",
        # "Gender": "",
        # "Ethnicity": "",
        # "Citizenship": "",
        # "Country_of_Residence": partner_member.group.Group_country or "Kenya",
        # "City": partner_member.group.Group_city or "",
        # "State": "",
        # "Age": "0",
        # "Account_Balance": 0,
    }
def build_company_payload(company,offer_letter):
    return {
        "Name": company.company_name,
        "Phone_No": company.phone or "",
        "Primary_Email": company.email or "",
        "PIN_No": company.company_kra or "",
        "Country_of_Residence": company.country or "Kenya",
        "City": company.city or "",
        "Customer_Type": "Company",
        "Customer_Posting_Group": "CUSTOMERS",  # adjust to match BC setup
        "Sales_Person_Code": getattr(offer_letter.booking_id, "marketer_id", "") if offer_letter.booking_id else "",
    }
def build_company_member_payload(company_member):
    return {
        # "Group_Code": company_member.group.group_code,
        "Group_Member_No": company_member.director_id,  # your PK/sequence
        "National_ID": company_member.national_id,
        "Member_Name": f"{company_member.first_name} {company_member.last_name}",
        # "PIN_No": "",  # if you store per-company_member KRA/Tax Pin, map here
        # "Phone_No": company_member.phone or "",
        # "Email": company_member.email or "",
        # "Alternative_Email": "",
        # "Date_of_Birth": "0001-01-01",
    }

def build_next_of_kin_payload(kin):
    return {
        "ID_No_Birth_Cert_No": "",  # map if available
        "Name": kin.full_name,
        "Relationship": kin.relationship or "",
        "Beneficiary": False,
        "Allocation_Percent": 0,
        "Contact": f"{kin.country_code}{kin.phone}" if kin.country_code else kin.phone,
        "Address": "",
        "Email": kin.email or "",
    }

