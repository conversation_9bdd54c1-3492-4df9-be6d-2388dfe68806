#!/usr/bin/env python3
"""
Test script to verify the inactive email logic works correctly.
This simulates the logic without requiring the full Django environment.
"""

import re

def simulate_inactive_email_logic(current_email, existing_emails):
    """
    Simulate the inactive email logic from the user_sync.py file
    
    Args:
        current_email: The current user's email
        existing_emails: List of existing emails in the database
    
    Returns:
        The new email that should be assigned
    """
    email_local_part = current_email.split('@')[0]
    email_domain = current_email.split('@')[1]
    
    # Determine the base email (without inactive prefix)
    if email_local_part.startswith('inactive'):
        # Extract base email by removing inactive prefix
        if '.' in email_local_part and email_local_part.split('.', 1)[0].startswith('inactive'):
            # Format: inactive{number}.original@domain
            base_email = '.'.join(email_local_part.split('.')[1:]) + '@' + email_domain
        else:
            # Format: inactive{number}original@domain (no dot separator)
            base_part = re.sub(r'^inactive\d*', '', email_local_part)
            base_email = base_part + '@' + email_domain if base_part else current_email
    else:
        # Email doesn't have inactive prefix
        base_email = current_email
    
    # Find all emails with inactive prefix that are similar to this base email
    base_local = base_email.split('@')[0]
    similar_emails = [email for email in existing_emails 
                     if email.startswith('inactive') and base_local in email]
    
    # Find the highest number used in inactive prefixes
    max_number = 0
    for email in similar_emails:
        local_part = email.split('@')[0]
        # Match inactive followed by optional number
        match = re.match(r'^inactive(\d+)?', local_part)
        if match:
            number_str = match.group(1)
            if number_str:
                try:
                    number = int(number_str)
                    max_number = max(max_number, number)
                except ValueError:
                    continue
            else:
                # 'inactive' without number is treated as inactive1
                max_number = max(max_number, 1)
    
    # Generate new email with incremented number
    new_number = max_number + 1
    
    if current_email.startswith('inactive'):
        # Current email already has inactive prefix, replace with new number
        if '.' in email_local_part and email_local_part.split('.', 1)[0].startswith('inactive'):
            base_part = '.'.join(email_local_part.split('.')[1:])
            new_email = f'inactive{new_number}.{base_part}@{email_domain}'
        else:
            # No dot separator, extract base part
            base_part = re.sub(r'^inactive\d*', '', email_local_part)
            if base_part:
                new_email = f'inactive{new_number}{base_part}@{email_domain}'
            else:
                new_email = f'inactive{new_number}@{email_domain}'
    else:
        # Add inactive prefix to original email
        new_email = f'inactive{new_number}.{current_email}'
    
    return new_email

# Test cases
def run_tests():
    print("Testing inactive email logic...\n")
    
    # Test Case 1: First time making email inactive
    print("Test 1: First time making email inactive")
    current_email = "<EMAIL>"
    existing_emails = []
    result = simulate_inactive_email_logic(current_email, existing_emails)
    print(f"Current: {current_email}")
    print(f"Existing: {existing_emails}")
    print(f"Result: {result}")
    print(f"Expected: <EMAIL>")
    print(f"✓ Pass\n" if result == "<EMAIL>" else "✗ Fail\n")
    
    # Test Case 2: Email already has inactive prefix, increment
    print("Test 2: Email already has inactive prefix")
    current_email = "<EMAIL>"
    existing_emails = ["<EMAIL>", "<EMAIL>"]
    result = simulate_inactive_email_logic(current_email, existing_emails)
    print(f"Current: {current_email}")
    print(f"Existing: {existing_emails}")
    print(f"Result: {result}")
    print(f"Expected: <EMAIL>")
    print(f"✓ Pass\n" if result == "<EMAIL>" else "✗ Fail\n")
    
    # Test Case 3: Multiple inactive emails exist, find highest number
    print("Test 3: Multiple inactive emails exist")
    current_email = "<EMAIL>"
    existing_emails = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]
    result = simulate_inactive_email_logic(current_email, existing_emails)
    print(f"Current: {current_email}")
    print(f"Existing: {existing_emails}")
    print(f"Result: {result}")
    print(f"Expected: <EMAIL>")
    print(f"✓ Pass\n" if result == "<EMAIL>" else "✗ Fail\n")
    
    # Test Case 4: Email with just 'inactive' prefix (no number)
    print("Test 4: Email with just 'inactive' prefix")
    current_email = "<EMAIL>"
    existing_emails = ["<EMAIL>", "<EMAIL>"]
    result = simulate_inactive_email_logic(current_email, existing_emails)
    print(f"Current: {current_email}")
    print(f"Existing: {existing_emails}")
    print(f"Result: {result}")
    print(f"Expected: <EMAIL>")
    print(f"✓ Pass\n" if result == "<EMAIL>" else "✗ Fail\n")

if __name__ == "__main__":
    run_tests()
