# Generated by Django 5.1.7 on 2025-06-04 12:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0002_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SiteVisit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pickup_time', models.TimeField()),
                ('pickup_date', models.DateField()),
                ('pickup_location', models.CharField(max_length=100)),
                ('special_assignment_destination', models.CharField(blank=True, max_length=100, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.Char<PERSON><PERSON>(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('In Progress', 'In Progress'), ('Completed', 'Completed'), ('Reviewed', 'Reviewed'), ('Cancelled', 'Cancelled'), ('Rejected', 'Rejected')], default='Pending', max_length=20)),
                ('driver', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='site_visit_driver', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='site_visit_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('project', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='site_visit_project', to='inventory.project')),
                ('special_assignment_assigned_to', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='special_assignment_assigned_to', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.CreateModel(
            name='SiteVisitClient',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('phone_number', models.CharField(max_length=50)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('site_visit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='site_visist_client', to='logistics.sitevisit')),
            ],
        ),
        migrations.CreateModel(
            name='SiteVisitSurvey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('booked', models.BooleanField(default=False)),
                ('visited', models.BooleanField(default=False)),
                ('amount_reserved', models.DecimalField(blank=True, decimal_places=2, max_digits=32, null=True)),
                ('plot_details', models.TextField(blank=True, null=True)),
                ('reason_not_visited', models.TextField(blank=True, null=True)),
                ('reason_not_booked', models.TextField(blank=True, null=True)),
                ('site_visit', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='site_visit_survey', to='logistics.sitevisit')),
            ],
        ),
        migrations.CreateModel(
            name='Vehicle',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('make', models.CharField(max_length=255)),
                ('model', models.CharField(max_length=255)),
                ('body_type', models.CharField(max_length=255)),
                ('number_of_seats', models.PositiveIntegerField()),
                ('engine_capacity', models.CharField(max_length=255)),
                ('vehicle_registration', models.CharField(max_length=20, unique=True)),
                ('passengers_assigned', models.BooleanField(default=False)),
                ('status', models.CharField(choices=[('Available', 'Available'), ('In Use', 'In Use'), ('Under Maintenance', 'Under Maintenance'), ('Out of Service', 'Out of Service')], default='Available', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicles', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.CreateModel(
            name='SpecialBooking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('reservation_date', models.DateField()),
                ('reservation_time', models.TimeField()),
                ('pickup_location', models.CharField(max_length=100)),
                ('destination', models.CharField(max_length=100)),
                ('reason', models.TextField(blank=True, null=True)),
                ('remarks', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('In Progress', 'In Progress'), ('Rejected', 'Rejected')], default='Pending', max_length=20)),
                ('assigned_to', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='special_booking_assigned', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('driver', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='special_booking_driver', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='special_booking_vehicle', to='logistics.vehicle')),
            ],
        ),
        migrations.AddField(
            model_name='sitevisit',
            name='vehicle',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='site_visit_vehicle', to='logistics.vehicle'),
        ),
        migrations.CreateModel(
            name='VehicleRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('pickup_time', models.TimeField()),
                ('pickup_date', models.DateField()),
                ('pickup_location', models.CharField(max_length=100)),
                ('destination_location', models.CharField(max_length=100)),
                ('number_of_passengers', models.PositiveIntegerField()),
                ('remarks', models.TextField(blank=True, null=True)),
                ('purpose', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('In Progress', 'In Progress'), ('Trip Completed', 'Trip Completed'), ('Rejected', 'Rejected')], default='Pending', max_length=20)),
                ('driver', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='driver_requested', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='vehicle_requester', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('vehicle', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='logistics.vehicle')),
            ],
        ),
    ]
