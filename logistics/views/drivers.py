
from rest_framework import viewsets, permissions
from drf_yasg.utils import swagger_auto_schema
from users.models import User
from users.serializers import UserSerializer
from rest_framework.permissions import IsAuthenticated,AllowAny
import os
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


class DriverViewSet(viewsets.ModelViewSet):
    
    queryset = User.objects.filter(is_driver=True)
    serializer_class = UserSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch']
    filterset_fields = ['employee_no', 'category', 'fullnames', 'email', 'user_id','department']
    search_fields = ['employee_no', 'category', 'fullnames', 'email', 'user_id','department']
    ordering_fields = ['user_id']

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="List all drivers",
        operation_description="Retrieve a list of all drivers in the system.",
        responses={200: UserSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Create a new driver in the system.",
        request_body=UserSerializer,
        responses={201: UserSerializer},
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)


    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Retrieve details of a specific driver.",
        responses={200: UserSerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Partially update details of a specific driver.",
        request_body=UserSerializer,
        responses={200: UserSerializer},
    )
    def partial_update(self, request, *args, **kwargs):
        
        return super().partial_update(request, *args, **kwargs)
    
