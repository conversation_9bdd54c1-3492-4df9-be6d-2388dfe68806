from inventory.models import Project
from logistics.models import SiteVisit, VehicleRequest
from rest_framework import viewsets
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated,AllowAny
import os
from django.conf import settings

class LogisticsAnalysis(viewsets.ViewSet):
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    def dashboard_stats(self, request):

        # model instances 
        sites = Project.objects.all()
        site_visits = SiteVisit.objects.all()
        vehicle_requests = VehicleRequest.objects.all()

        # count stats
        counts = {}
        counts['total_site_visit_requests'] = site_visits.count()
        counts['completed_site_visits'] = site_visits.filter(status='Completed').count()
        counts['rejected_cancelled'] = site_visits.filter(status__in=['Cancelled','Rejected']).count()
        counts['total_vehicle_request'] = vehicle_requests.count()
        counts['complete_vehicle_request'] = vehicle_requests.filter(status='Trip Completed').count()
        counts['rejected_vehicle_request'] = vehicle_requests.filter(status='Rejected').count()


        return Response(dict(counts=counts))

