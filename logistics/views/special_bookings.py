from logistics.models import SpecialBooking
from logistics.serializers import SpecialBookingSerializer
from rest_framework.permissions import IsAuthenticated,AllowAny
import os
from rest_framework import viewsets, filters
from drf_yasg.utils import swagger_auto_schema
from django.conf import settings

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

@swagger_tags(['Logistics'])
class SpecialBookingViewSet(viewsets.ModelViewSet):
    """
    ViewSet for the SpecialBooking model.
    """
    queryset = SpecialBooking.objects.all()
    serializer_class = SpecialBookingSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['pickup_location', 'destination']
    ordering_fields = '__all__'
    ordering = ['-created_at']

    @swagger_auto_schema(
        operation_summary="List all special bookings",
        operation_description="Retrieve a list of all special bookings in the system.",
        responses={200: SpecialBookingSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        print("Listing all special bookings")
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_description="Create a new special booking in the system.",
        request_body=SpecialBookingSerializer,
        responses={201: SpecialBookingSerializer},
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_description="Retrieve a specific special booking by ID.",
        responses={200: SpecialBookingSerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_description="Partially update a specific special booking by ID.",
        request_body=SpecialBookingSerializer,
        responses={200: SpecialBookingSerializer},
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(
        operation_description="Delete a specific special booking by ID.",
        responses={204: 'No Content'},
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)