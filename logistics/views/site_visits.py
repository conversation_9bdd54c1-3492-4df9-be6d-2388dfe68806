from logistics.models import SiteVisit, SiteVisitClient, SiteVisitSurvey
from logistics.serializers import SiteVisitDetailsSerializer, SiteVisitSerializer, SiteVisitSurveySerializer
from rest_framework import viewsets, permissions
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from django.db import transaction
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from rest_framework.permissions import IsAuthenticated,AllowAny
import os
from users.models import User
from utils.email import normalize_emails
from copy import deepcopy
from inventory.tasks import send_email_task
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve','partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

class SiteVisitsView(viewsets.ModelViewSet):
    
    queryset = SiteVisit.objects.all()
    serializer_class = SiteVisitSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filterset_fields = [
        'marketer', 'vehicle', 'project', 'driver',
        'pickup_date', 'pickup_location', 'status',
        'site_visist_client__name',
        'site_visist_client__phone_number',
        'site_visist_client__email',
        ]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = [
        'marketer__employee_no', 'vehicle__id', 'project__id', 'driver__employee_no',
        'pickup_date', 'pickup_location', 'status',
        'site_visist_client__name',
        'site_visist_client__phone_number',
        'site_visist_client__email',
        ]

    ordering_fields = ['marketer', 'vehicle', 'project', 'driver','pickup_date','pickup_location','status']

    def get_serializer_class(self):
        """
        Returns the serializer class based on the action.
        """
        if self.action == 'retrieve':
            return SiteVisitDetailsSerializer
        return super().get_serializer_class()
            

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="List all site visits",
        operation_description="Retrieve a list of all site visits in the system.",
        responses={200: SiteVisitSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Create a new site visit in the system.",
        request_body=SiteVisitSerializer,
        responses={201: SiteVisitSerializer},
    )
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        clientsList = data.get('clients', [])
        try:
            with transaction.atomic():
                user=request.user
                data['marketer'] = user.employee_no

                serializer = self.serializer_class(data=data)
                if serializer.is_valid():
                    visit_instance = serializer.save()

                    # Add related clients to the site visit
                    for client in clientsList:
                        SiteVisitClient.objects.create(
                            site_visit=visit_instance,
                            name=f"{client.get('firstName')} {client.get('lastName')}",
                            email=client.get('email') or None,
                            phone_number=client.get('phone')
                        )

                    # Send email to the creator
                    try:
                        if user and user.email:
                            subject = "New Site Visit Created"
                            recipients = normalize_emails([user.email])
                            # cc = ['<EMAIL>']
                            template_name = "logistics/site_visit.html"
                            visit_data = SiteVisitSerializer(visit_instance).data
                            context = {
                                "user": user.get_full_name(),
                                "status": visit_instance.status,
                                "survey": visit_data,
                                "body": "Your site visit survey has been created successfully.",
                            }

                            send_email_task.delay(
                                subject=subject,
                                recipients=recipients,
                                template_name=template_name,
                                context=deepcopy(context),
                                # cc=cc,
                            )
                    except Exception as e:
                        print(f"Email send failed: {e}")

                    return Response(
                        dict(success=True, message='Booking Successful', data=serializer.data),
                        status=201
                    )
                else:
                    return Response(
                        dict(success=False, message='Invalid data', errors=serializer.errors),
                        status=400
                    )

        except Exception as e:
            return Response(
                dict(success=False, message='Something went wrong', error=str(e)),
                status=500
            )
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Retrieve a specific site visit by ID.",
        responses={200: SiteVisitDetailsSerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Delete a specific site visit by ID.",
        responses={204: 'No Content'},
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    @swagger_auto_schema(tags=['Logistics'],operation_description="partial_update a specific site visit by ID.",
                         responses={204: 'No Content'},
    )
    def partial_update(self, request, *args, **kwargs):
        
        instance = self.get_object()
        old_status = instance.status 
        response = super().partial_update(request, *args, **kwargs)
        instance.refresh_from_db()
        new_status = instance.status
        # Check if status actually changed
        user = request.user
        if user and user.email:
            if old_status !=new_status:
                if new_status== "Approved":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit has been approved."
                if new_status== "Rejected":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit has unfortunately been rejected."
                if new_status== "In Progress":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit has been started and is in Progress."
                if new_status== "Completed":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit has been Completed, Kindly fill the survey."
                if new_status== "Cancelled":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit has been Cancelled unfortunately."
                if new_status== "Reviewed":
                    subject = f" Site Visit {new_status}"
                    body= f" This is to inform you that your site visit survey has been filled, you can proceed to book a new site visit."


                visit_data = SiteVisitSerializer().data    
                recipients = normalize_emails([instance.marketer.email])
                template_name = "logistics/site_visit.html"  # Update this to your actual template
                context = {
                    "user": instance.marketer.fullnames,
                    "status": new_status,
                    "body": body,
                    "survey": visit_data,
                }
                
                if new_status not in  ["In Progress","Completed"]:
                    send_email_task.delay(
                        subject=subject,
                        recipients=recipients,
                        template_name=template_name,
                        context=deepcopy(context)
                    )

        return response
    

class SiteVisitSurveyView(viewsets.ModelViewSet):
    queryset = SiteVisitSurvey.objects.all()
    serializer_class = SiteVisitSurveySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch']


    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="List all site visit surveys",
        operation_description="Retrieve a list of all site visit surveys in the system.",
        responses={200: SiteVisitSurveySerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Create a new site visit survey in the system.",
        request_body=SiteVisitSurveySerializer,
        responses={201: SiteVisitSurveySerializer},
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Retrieve a specific site visit survey by ID.",
        responses={200: SiteVisitSurveySerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Partially update a specific site visit survey by ID.",
        request_body=SiteVisitSurveySerializer,
        responses={200: SiteVisitSurveySerializer},
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
