from logistics.models import Vehicle, VehicleRequest
from logistics.serializers import VehicleRequestSerializer, VehiclesSerializer
from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated,AllowAny
import os
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class VehiclesView(viewsets.ModelViewSet):
    
    queryset = Vehicle.objects.all().order_by('-vehicle_registration') 
    serializer_class = VehiclesSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filterset_fields = ['make', 'model', 'vehicle_registration']
    search_fields = ['make', 'model', 'vehicle_registration']
    ordering_fields = ['id']

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="List all vehicles",
        operation_description="Retrieve a list of all vehicles in the system.",
        responses={200: VehiclesSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Create a new vehicle in the system.",
        request_body=VehiclesSerializer,
        responses={201: VehiclesSerializer},
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Retrieve details of a specific vehicle.",
        responses={200: VehiclesSerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Partially update details of a specific vehicle.",
        request_body=VehiclesSerializer,
        responses={200: VehiclesSerializer},
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(
        tags=['Logistics'],
        operation_description="Delete a specific vehicle from the system.",
        responses={204: 'No Content'},
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
    
class VehicleRequestView(viewsets.ModelViewSet):
    """
    ViewSet for handling vehicle requests.
    """
    queryset = VehicleRequest.objects.all().order_by('-created_at') 
    serializer_class = VehicleRequestSerializer
    http_method_names = ['get', 'post', 'patch', 'delete']
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filterset_fields = ['vehicle', 'requester', 'pickup_time', 'pickup_date', 'destination_location']
    search_fields = ['vehicle', 'requester', 'pickup_time', 'pickup_date', 'destination_location']
    ordering_fields = ['id', 'destination_location', 'pickup_date', 'pickup_time']


    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="List all vehicle requests",
        operation_description="Retrieve a list of all vehicle requests in the system.",
        responses={200: VehicleRequestSerializer(many=True)},
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="Create a new vehicle request.",
        request_body=VehicleRequestSerializer,
        responses={201: VehicleRequestSerializer},
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="Retrieve details of a specific vehicle request.",
        responses={200: VehicleRequestSerializer},
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary='Partially update details of a specific vehicle request.',
        operation_description="Assign driver send driver id as 'driver' in the request body. \nTo update status send 'status' in the request body with values as to approve='Approve', start trip = 'In Progress', end trip = 'Trip Completed', reject = 'Rejected'",
        request_body=VehicleRequestSerializer,
        responses={200: VehicleRequestSerializer},
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Logistics'],
        operation_summary="Delete a specific vehicle request.",
        responses={204: 'No Content'},
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

    