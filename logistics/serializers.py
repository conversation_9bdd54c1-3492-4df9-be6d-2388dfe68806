from inventory.serializers import ProjectSerializer
from logistics.models import SiteVisit, SiteVisitClient, SiteVisitSurvey, SpecialBooking, Vehicle, VehicleRequest
from rest_framework import serializers
from users.serializers import DepartmentsSerializer, UserSerializer

class VehiclesSerializer(serializers.ModelSerializer):
    """
    Serializer for the Vehicle model.
    """
    class Meta:
        model = Vehicle
        fields = '__all__'
        read_only_fields = ('id',)


class VehicleRequestSerializer(serializers.ModelSerializer):
    """
    Serializer for the VehicleRequest model.
    """
    class Meta:
        model = VehicleRequest
        fields = '__all__'
        read_only_fields = ('id',)

    def to_representation(self, instance):
        """
        Custom representation to include related fields.
        """
        representation = super().to_representation(instance)
        try: 
            if instance.vehicle:
               representation['vehicle'] = instance.vehicle.registration_number 
            else:
                representation['vehicle'] = None
        except:
            representation['vehicle'] = None
        try: 
            if instance.requester:
               representation['requester'] = instance.requester.fullnames 
            else:
                representation['requester'] = None
        except:
            representation['requester'] = None
        try: 
            if instance.driver:
               representation['driver'] = instance.driver.fullnames 
            else:
                representation['driver'] = None
        except:
            representation['driver'] = None
        return representation


class SpecialBookingSerializer(serializers.ModelSerializer):
    """
    Serializer for the SpecialBooking model.
    """
    class Meta:
        model = SpecialBooking
        fields = '__all__'
        read_only_fields = ('id',)

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        try: 
            if instance.vehicle_id:
                representation['vehicle_id'] = instance.vehicle_id.vehicle_registration 
            else:
                representation['vehicle_id'] = None
        except:
            representation['vehicle_id'] = None
        try: 
            if instance.driver_id:
               representation['driver_id'] = instance.driver_id.fullnames 
            else:
                representation['driver_id'] = None
        except:
            representation['driver_id'] = None
        representation['assigned_to'] = DepartmentsSerializer(instance.assigned_to).data if instance.assigned_to else None
        
        
        return representation
        

class SiteVisitSerializer(serializers.ModelSerializer):
    """
    Mini Serializer for the SiteVisit model.
    """
    class Meta:
        model = SiteVisit
        fields = '__all__'
        read_only_fields = ('id',)

    def to_representation(self, instance):
        """
        Custom representation to include related fields.
        """
        representation = super().to_representation(instance)
        try: 
            if instance.marketer:
               representation['marketer'] = instance.marketer.fullnames 
            else:
                representation['marketer'] = None
        except:
            representation['marketer'] = None
        try: 
            if instance.vehicle:
               representation['vehicle'] = instance.vehicle.registration_number 
            else:
                representation['vehicle'] = None
        except:
            representation['vehicle'] = None
        try: 
            if instance.project:
               representation['project'] = instance.project.name 
            else:
                representation['project'] = None
        except:
            representation['project'] = None   
        try: 
            if instance.driver:
               representation['driver'] = instance.driver.fullnames 
            else:
                representation['driver'] = None
        except:
            representation['driver'] = None

        clients = SiteVisitClient.objects.filter(site_visit=instance)
        clients_list = []
        for client in clients:
            client_dict = {
                'name': client.name,
                'phone_number': client.phone_number,
                'email': client.email,
                'client_type': client.client_type,
            }
            clients_list.append(client_dict)
        
        representation['site_visit_client'] = clients_list

        return representation


class SiteVisitDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer for the SiteVisit model.
    """
    class Meta:
        model = SiteVisit
        fields = '__all__'
        read_only_fields = ('id',)

    def to_representation(self, instance):
        """
        Custom representation to include related fields.
        """
        representation = super().to_representation(instance)
        try: 
            if instance.marketer:
               representation['marketer'] = instance.marketer.fullnames 
            else:
                representation['marketer'] = None
        except:
            representation['marketer'] = None
        try: 
            if instance.vehicle:
               representation['vehicle'] = instance.vehicle.registration_number 
            else:
                representation['vehicle'] = None
        except:
            representation['vehicle'] = None
        try: 
            if instance.project:
               representation['project'] = instance.project.name 
            else:
                representation['project'] = None
        except:
            representation['project'] = None   
        try: 
            if instance.driver:
               representation['driver'] = instance.driver.fullnames 
            else:
                representation['driver'] = None
        except:
            representation['driver'] = None


        clients = SiteVisitClient.objects.filter(site_visit=instance)
        clients_list = []
        for client in clients:
            client_dict = {
                'name': client.name,
                'phone_number': client.phone_number,
                'email': client.email,
                'client_type': client.client_type,
            }
            clients_list.append(client_dict)
        
        representation['site_visit_client'] = clients_list

        return representation
    

class SiteVisitSurveySerializer(serializers.ModelSerializer):
    class Meta:
        model = SiteVisitSurvey
        fields = '__all__'

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        
        
        try: 
            if instance.site_visit:
               rep['site_visit'] = SiteVisitSerializer(instance.site_visit).data if instance.site_visit else None
            else:
                rep['site_visit'] = None
        except:
            rep['site_visit'] = None

        return rep