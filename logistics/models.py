from django.db import models

from inventory.models import Project

# Create your models here.
class Vehicle(models.Model):
    make = models.CharField(max_length=255)
    model = models.CharField(max_length=255)
    body_type = models.CharField(max_length=255)
    number_of_seats = models.PositiveIntegerField()
    engine_capacity = models.CharField(max_length=255)
    vehicle_registration = models.CharField(max_length=20, unique=True)
    driver = models.ForeignKey('users.User',to_field='employee_no', on_delete=models.CASCADE, related_name='vehicles',null=True)
    passengers_assigned = models.BooleanField(default=False)
    status = models.CharField(max_length=20, choices=[
        ('Available', 'Available'),
        ('In Use', 'In Use'),
        ('Under Maintenance', 'Under Maintenance'),
        ('Out of Service', 'Out of Service')
    ], default='Available',null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f'{self.vehicle_registration} - {self.make} {self.model}'
    

class VehicleRequest(models.Model):
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='requests', null=True, blank=True)
    requester = models.ForeignKey('users.User',to_field='employee_no', on_delete=models.CASCADE, related_name='vehicle_requester')
    pickup_time = models.TimeField()
    pickup_date = models.DateField()
    pickup_location = models.CharField(max_length=100)
    destination_location = models.CharField(max_length=100)
    number_of_passengers = models.PositiveIntegerField()
    driver = models.ForeignKey('users.User',to_field='employee_no', on_delete=models.CASCADE, related_name='driver_requested', null=True, blank=True)
    remarks = models.TextField(blank=True, null=True)
    purpose = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=[
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('In Progress', 'In Progress'),
        ('Trip Completed', 'Trip Completed'),
        ('Rejected', 'Rejected')
    ], default='Pending')

    def __str__(self):
        return f'Request for {self.vehicle} by {self.requester.fullnames}'


class SpecialBooking(models.Model):
    reservation_date = models.DateField()
    reservation_time = models.TimeField()
    pickup_location = models.CharField(max_length=100)
    destination = models.CharField(max_length=100)
    vehicle_id = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='special_booking_vehicle')
    driver_id = models.ForeignKey('users.User', to_field='employee_no', on_delete=models.CASCADE, related_name='special_booking_driver')
    reason = models.TextField(blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    assigned_to = models.ForeignKey('users.Departments', to_field='dp_id', on_delete=models.CASCADE, related_name='special_booking_assigned', blank=True, null=True)
    status = models.CharField(max_length=20, choices=[
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('In Progress', 'In Progress'),
        ('Rejected', 'Rejected'),
        ('Completed', 'Completed'),
    ], default='Pending')


class SiteVisit(models.Model):
    marketer = models.ForeignKey('users.User', to_field='employee_no', on_delete=models.CASCADE, related_name='site_visit_marketer')
    vehicle = models.ForeignKey(Vehicle, on_delete=models.CASCADE, related_name='site_visit_vehicle', blank=True, null=True)
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='site_visit_project')
    driver = models.ForeignKey('users.User', to_field='employee_no', on_delete=models.CASCADE, related_name='site_visit_driver', blank=True, null=True)
    pickup_time = models.TimeField()
    pickup_date = models.DateField()
    pickup_location = models.CharField(max_length=100)
    special_assignment_destination = models.CharField(max_length=100, blank=True, null=True)
    special_assignment_assigned_to = models.ForeignKey('users.User',to_field='employee_no', on_delete=models.CASCADE, related_name='special_assignment_assigned_to', blank=True, null=True)
    remarks = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=20, choices=[
        ('Pending', 'Pending'),
        ('Approved', 'Approved'),
        ('In Progress', 'In Progress'),
        ('Completed', 'Completed'),
        ('Reviewed', 'Reviewed'),
        ('Cancelled', 'Cancelled'),
        ('Rejected', 'Rejected')
    ], default='Pending')
    
    class Meta:
        ordering = ['-created_at']


class SiteVisitClient(models.Model):
    site_visit = models.ForeignKey(SiteVisit, on_delete=models.CASCADE, related_name='site_visist_client')
    name = models.CharField(max_length=255)
    phone_number = models.CharField(max_length=50)
    email = models.EmailField(blank=True, null=True)
    client_type = models.CharField(max_length=50,null=True, blank=True, default=None)


class SiteVisitSurvey(models.Model):
    site_visit = models.ForeignKey(SiteVisit, on_delete=models.CASCADE, related_name='site_visit_survey')
    booked = models.BooleanField(default=False)
    visited = models.BooleanField(default=False)
    amount_reserved = models.DecimalField(max_digits=32, decimal_places=2, null=True, blank=True)
    plot_details = models.TextField(blank=True, null=True)
    reason_not_visited = models.TextField(blank=True, null=True)
    reason_not_booked = models.TextField(blank=True, null=True)