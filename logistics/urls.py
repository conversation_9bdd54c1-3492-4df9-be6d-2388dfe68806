from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from logistics.views import special_bookings, vehicles, site_visits, drivers


lead_router = DefaultRouter(trailing_slash=False)

lead_router.register('vehicles', vehicles.VehiclesView)
lead_router.register('vehicles-requests', vehicles.VehicleRequestView)
lead_router.register('site-visits', site_visits.SiteVisitsView)
lead_router.register('special-bookings', special_bookings.SpecialBookingViewSet)
lead_router.register('site-visits-surveys', site_visits.SiteVisitSurveyView)
lead_router.register('drivers', drivers.DriverViewSet)

urlpatterns = [
    path('logistics/', include(lead_router.urls)),
]

