[tool.poetry]
name = "optiven crm 2-0"
version = "0.1.0"
description = ""
authors = ["JOSEPH M MATHENGE <<EMAIL>>"]
readme = "README.md"
packages = [{include = "optiven crm 2"}]

[tool.poetry.dependencies]
python = "^3.13"
django = "^5.1.7"
djangorestframework = "^3.15.2"
psycopg2-binary = "^2.9"
python-dotenv = "^1.0.1"
markdown = "^3.7"
django-filter = "^24.3"
djangorestframework-simplejwt = "^5.3.1"
drf-yasg = "^1.21.7"
setuptools = "^75.1.0"
django-storages = "^1.14.4"
boto3 = "^1.35.50"
pillow = "^11.0.0"
gunicorn = "^23.0.0"
django-cors-headers = "^4.6.0"
pytest-django = "^4.9.0"
pytest-asyncio = "^0.24.0"
channels = {version = "^4.2.0", extras = ["extra.daphne"]}
channels-redis = "^4.2.1"
celery = {version = "^5.4.0", extras = ["extra.redis"]}
django-celery-beat = "^2.7.0"
daphne = "^4.1.2"
python-docx = "^1.1.2"
minio = "^7.2.15"
mysqlclient = "^2.2.7"
pymysql = "^1.1.1"
drf-spectacular = "^0.28.0"
weasyprint = "^65.1"
requests = "^2.32.4"
pandas = "^2.3.1"
xlsxwriter = "^3.2.5"
requests-ntlm = "^1.3.0"
google-api-python-client = "^2.187.0"
google-auth = "^2.43.0"
google-auth-httplib2 = "^0.2.1"


[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
