from django.db import models
from django.utils import timezone

class BaseReceipt(models.Model):
    line_no = models.IntegerField(primary_key=True)
    Receipt_No = models.CharField(max_length=30)
    Date_Posted = models.DateField(null=True, blank=True)
    Payment_date = models.DateField(null=True, blank=True)
    Bank_Name = models.CharField(max_length=255, null=True, blank=True)
    Bank_Account = models.CharField(max_length=30, null=True, blank=True)
    Customer_Id = models.CharField(max_length=30, null=True, blank=True)
    Customer_Name = models.CharField(max_length=255, null=True, blank=True)
    Pay_mode = models.CharField(max_length=255, null=True, blank=True)
    Lead_file_no = models.CharField(max_length=255, null=True, blank=True)
    Project_Name = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    Plot_NO = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    Marketer = models.Char<PERSON>ield(max_length=255, null=True, blank=True)
    Teams = models.CharField(max_length=30, null=True, blank=True)
    Regions = models.CharField(max_length=30, null=True, blank=True)
    Deposit_Threshold = models.CharField(max_length=30, null=True, blank=True)
    Transaction_type = models.CharField(max_length=30, null=True, blank=True)
    transfer_receipt = models.CharField(max_length=20, null=True, blank=True)
    Amount_LCY = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    Balance_LCY = models.DecimalField(max_digits=15, decimal_places=2, null=True, blank=True)
    Type = models.CharField(max_length=30, null=True, blank=True)
    status = models.CharField(max_length=20, null=True, blank=True)
    POSTED_DATE1 = models.DateField(null=True, blank=True)
    PAYMENT_DATE1 = models.DateField(null=True, blank=True)
    Narration = models.TextField(null=True, blank=True)
    assigned_to = models.CharField(max_length=16, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True
        indexes = [
            models.Index(fields=['Receipt_No']),
        ]

class PostedReceipt(BaseReceipt):
    class Meta:
        verbose_name = "Posted Receipt"
        verbose_name_plural = "Posted Receipts"
        ordering = ['-Date_Posted']

    def __str__(self):
        return f"{self.Receipt_No} - {self.Customer_Name}"

class CancelledReceipt(BaseReceipt):
    cancellation_date = models.DateTimeField(auto_now_add=True)
    cancellation_reason = models.TextField(blank=True, null=True)

    class Meta:
        verbose_name = "Cancelled Receipt"
        verbose_name_plural = "Cancelled Receipts"
        ordering = ['-cancellation_date']

    def __str__(self):
        return f"Cancelled - {self.Receipt_No} - {self.Customer_Name}"

class InstallmentSchedule(models.Model):
    member_no = models.CharField(max_length=20)
    leadfile_no = models.CharField(max_length=20)
    line_no = models.IntegerField()
    installment_no = models.IntegerField()
    installment_amount = models.CharField(max_length=20)
    remaining_Amount = models.CharField(max_length=20)
    due_date = models.DateField()
    paid = models.CharField(max_length=5)
    plot_No = models.CharField(max_length=20)
    plot_Name = models.CharField(max_length=10)
    amount_Paid = models.CharField(max_length=20)
    penaties_Accrued = models.IntegerField()
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'installment_schedule'
        verbose_name = "Installment Schedule"
        verbose_name_plural = "Installment Schedules"
        ordering = ['-due_date']
        indexes = [
            models.Index(fields=['member_no']),
            models.Index(fields=['leadfile_no']),
            models.Index(fields=['line_no']),
        ]

    def __str__(self):
        return f"{self.member_no} - Installment {self.installment_no}"

    def save(self, *args, **kwargs):
        if not self.created_at:
            self.created_at = timezone.now()
        self.updated_at = timezone.now()
        super().save(*args, **kwargs)
