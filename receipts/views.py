from django.shortcuts import render
from rest_framework import viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.permissions import AllowAny, IsAuthenticated
from .models import PostedReceipt, CancelledReceipt
from .serializers import PostedReceiptSerializer, CancelledReceiptSerializer, InstallmentScheduleSerializer
from drf_yasg.utils import swagger_auto_schema
import os
# Create your views here.

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list','retrieve']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


class PostedReceiptViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = PostedReceipt.objects.all()
    serializer_class = PostedReceiptSerializer
    # if os.environ.get('DEBUG', 'false').lower() == 'true':
    #     permission_classes = [IsAuthenticated]
    # else:
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'Pay_mode', 'Bank_Name', 'assigned_to']
    search_fields = ['Receipt_No', 'Customer_Name', 'Customer_Id', 'Lead_file_no', 'Project_Name']
    ordering_fields = ['Date_Posted', 'Amount_LCY', 'Customer_Name']
    ordering = ['-Date_Posted']
    http_method_names = ['get',]
    @swagger_auto_schema( tags=['SALES RECEIPTS'] ,operation_description="List all Posted Receipts")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema( tags=['SALES RECEIPTS'] ,operation_description="Retrieve a Posted Receipt by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

class CancelledReceiptViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = CancelledReceipt.objects.all()
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    serializer_class = CancelledReceiptSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'Pay_mode', 'Bank_Name']
    search_fields = ['Receipt_No', 'Customer_Name', 'Customer_Id', 'Lead_file_no', 'Project_Name']
    ordering_fields = ['cancellation_date', 'Amount_LCY', 'Customer_Name']
    ordering = ['-cancellation_date']
    http_method_names = ['get']
    @swagger_auto_schema( tags=['SALES RECEIPTS'] ,operation_description="List all Cancelleds Receipts")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema( tags=['SALES RECEIPTS'] ,operation_description="Retrieve a Cancelled Receipt by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

