from django.contrib import admin
from .models import PostedReceipt, CancelledReceipt, InstallmentSchedule

@admin.register(PostedReceipt)
class PostedReceiptAdmin(admin.ModelAdmin):
    list_display = ('Receipt_No', 'Customer_Name', 'Amount_LCY', 'Date_Posted', 'status', 'assigned_to')
    list_filter = ('status', 'Pay_mode', 'Bank_Name', 'assigned_to', 'Date_Posted')
    search_fields = ('Receipt_No', 'Customer_Name', 'Customer_Id', 'Lead_file_no', 'Project_Name')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'Date_Posted'
    list_per_page = 50

@admin.register(CancelledReceipt)
class CancelledReceiptAdmin(admin.ModelAdmin):
    list_display = ('Receipt_No', 'Customer_Name', 'Amount_LCY', 'cancellation_date', 'status')
    list_filter = ('status', 'Pay_mode', 'Bank_Name', 'cancellation_date')
    search_fields = ('Receipt_No', 'Customer_Name', 'Customer_Id', 'Lead_file_no', 'Project_Name')
    readonly_fields = ('created_at', 'updated_at', 'cancellation_date')
    date_hierarchy = 'cancellation_date'
    list_per_page = 50

@admin.register(InstallmentSchedule)
class InstallmentScheduleAdmin(admin.ModelAdmin):
    list_display = ('member_no', 'leadfile_no', 'installment_no', 'due_date', 'paid', 'plot_No')
    list_filter = ('paid', 'due_date', 'plot_No')
    search_fields = ('member_no', 'leadfile_no', 'plot_No', 'plot_Name')
    readonly_fields = ('created_at', 'updated_at')
    date_hierarchy = 'due_date'
    list_per_page = 50
