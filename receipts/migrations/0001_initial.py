# Generated by Django 5.1.7 on 2025-06-04 12:51

import django.utils.timezone
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CancelledReceipt',
            fields=[
                ('line_no', models.IntegerField(primary_key=True, serialize=False)),
                ('Receipt_No', models.Char<PERSON><PERSON>(max_length=30)),
                ('Date_Posted', models.DateField(blank=True, null=True)),
                ('Payment_date', models.DateField(blank=True, null=True)),
                ('Bank_Name', models.CharField(blank=True, max_length=255, null=True)),
                ('Bank_Account', models.CharField(blank=True, max_length=30, null=True)),
                ('Customer_Id', models.CharField(blank=True, max_length=30, null=True)),
                ('Customer_Name', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('Pay_mode', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('Lead_file_no', models.CharField(blank=True, max_length=255, null=True)),
                ('Project_Name', models.CharField(blank=True, max_length=255, null=True)),
                ('Plot_NO', models.CharField(blank=True, max_length=255, null=True)),
                ('Marketer', models.CharField(blank=True, max_length=255, null=True)),
                ('Teams', models.CharField(blank=True, max_length=30, null=True)),
                ('Regions', models.CharField(blank=True, max_length=30, null=True)),
                ('Deposit_Threshold', models.CharField(blank=True, max_length=30, null=True)),
                ('Transaction_type', models.CharField(blank=True, max_length=30, null=True)),
                ('transfer_receipt', models.CharField(blank=True, max_length=20, null=True)),
                ('Amount_LCY', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('Balance_LCY', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('Type', models.CharField(blank=True, max_length=30, null=True)),
                ('status', models.CharField(blank=True, max_length=20, null=True)),
                ('POSTED_DATE1', models.DateField(blank=True, null=True)),
                ('PAYMENT_DATE1', models.DateField(blank=True, null=True)),
                ('Narration', models.TextField(blank=True, null=True)),
                ('assigned_to', models.CharField(blank=True, max_length=16, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('cancellation_date', models.DateTimeField(auto_now_add=True)),
                ('cancellation_reason', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Cancelled Receipt',
                'verbose_name_plural': 'Cancelled Receipts',
                'ordering': ['-cancellation_date'],
            },
        ),
        migrations.CreateModel(
            name='PostedReceipt',
            fields=[
                ('line_no', models.IntegerField(primary_key=True, serialize=False)),
                ('Receipt_No', models.CharField(max_length=30)),
                ('Date_Posted', models.DateField(blank=True, null=True)),
                ('Payment_date', models.DateField(blank=True, null=True)),
                ('Bank_Name', models.CharField(blank=True, max_length=255, null=True)),
                ('Bank_Account', models.CharField(blank=True, max_length=30, null=True)),
                ('Customer_Id', models.CharField(blank=True, max_length=30, null=True)),
                ('Customer_Name', models.CharField(blank=True, max_length=255, null=True)),
                ('Pay_mode', models.CharField(blank=True, max_length=255, null=True)),
                ('Lead_file_no', models.CharField(blank=True, max_length=255, null=True)),
                ('Project_Name', models.CharField(blank=True, max_length=255, null=True)),
                ('Plot_NO', models.CharField(blank=True, max_length=255, null=True)),
                ('Marketer', models.CharField(blank=True, max_length=255, null=True)),
                ('Teams', models.CharField(blank=True, max_length=30, null=True)),
                ('Regions', models.CharField(blank=True, max_length=30, null=True)),
                ('Deposit_Threshold', models.CharField(blank=True, max_length=30, null=True)),
                ('Transaction_type', models.CharField(blank=True, max_length=30, null=True)),
                ('transfer_receipt', models.CharField(blank=True, max_length=20, null=True)),
                ('Amount_LCY', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('Balance_LCY', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True)),
                ('Type', models.CharField(blank=True, max_length=30, null=True)),
                ('status', models.CharField(blank=True, max_length=20, null=True)),
                ('POSTED_DATE1', models.DateField(blank=True, null=True)),
                ('PAYMENT_DATE1', models.DateField(blank=True, null=True)),
                ('Narration', models.TextField(blank=True, null=True)),
                ('assigned_to', models.CharField(blank=True, max_length=16, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Posted Receipt',
                'verbose_name_plural': 'Posted Receipts',
                'ordering': ['-Date_Posted'],
            },
        ),
        migrations.CreateModel(
            name='InstallmentSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('member_no', models.CharField(max_length=20)),
                ('leadfile_no', models.CharField(max_length=20)),
                ('line_no', models.IntegerField()),
                ('installment_no', models.IntegerField()),
                ('installment_amount', models.CharField(max_length=20)),
                ('remaining_Amount', models.CharField(max_length=20)),
                ('due_date', models.DateField()),
                ('paid', models.CharField(max_length=5)),
                ('plot_No', models.CharField(max_length=20)),
                ('plot_Name', models.CharField(max_length=10)),
                ('amount_Paid', models.CharField(max_length=20)),
                ('penaties_Accrued', models.IntegerField()),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now)),
            ],
            options={
                'verbose_name': 'Installment Schedule',
                'verbose_name_plural': 'Installment Schedules',
                'db_table': 'installment_schedule',
                'ordering': ['-due_date'],
                'indexes': [models.Index(fields=['member_no'], name='installment_member__a3c25f_idx'), models.Index(fields=['leadfile_no'], name='installment_leadfil_c362e9_idx'), models.Index(fields=['line_no'], name='installment_line_no_4bf76b_idx')],
            },
        ),
    ]
