from rest_framework import serializers
from .models import PostedReceipt, CancelledReceipt, InstallmentSchedule

class BaseReceiptSerializer(serializers.ModelSerializer):
    class Meta:
        fields = [
            'line_no', 'Receipt_No', 'Date_Posted', 'Payment_date', 'Bank_Name',
            'Bank_Account', 'Customer_Id', 'Customer_Name', 'Pay_mode',
            'Lead_file_no', 'Project_Name', 'Plot_NO', 'Marketer', 'Teams',
            'Regions', 'Deposit_Threshold', 'Transaction_type', 'transfer_receipt',
            'Amount_LCY', 'Balance_LCY', 'Type', 'status', 'POSTED_DATE1',
            'PAYMENT_DATE1', 'Narration', 'assigned_to', 'created_at', 'updated_at'
        ]
        read_only_fields = ('created_at', 'updated_at')

class PostedReceiptSerializer(BaseReceiptSerializer):
    class Meta(BaseReceiptSerializer.Meta):
        model = PostedReceipt
        fields = '__all__'

class CancelledReceiptSerializer(BaseReceiptSerializer):
    class Meta(BaseReceiptSerializer.Meta):
        model = CancelledReceipt
        fields = BaseReceiptSerializer.Meta.fields + ['cancellation_date', 'cancellation_reason']
        read_only_fields = BaseReceiptSerializer.Meta.read_only_fields + ('cancellation_date',)

class InstallmentScheduleSerializer(serializers.ModelSerializer):
    class Meta:
        model = InstallmentSchedule
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at') 