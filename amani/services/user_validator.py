"""
User Validation Service
======================

Service to validate if WhatsApp ID belongs to internal marketer.
References PHP logic from Old_Amani/amani_2.2.php lines 209-213.
"""

from typing import Optional, Tuple
from django.contrib.auth import get_user_model
from ..config import AmaniConfig

User = get_user_model()


class UserValidator:
    """Service to validate users and check marketer status"""
    
    @staticmethod
    def is_internal_marketer(wa_id: str) -> Tuple[bool, Optional[User]]:
        """
        Check if WhatsApp ID belongs to internal marketer
        
        Args:
            wa_id: WhatsApp ID (phone number)
            
        Returns:
            Tuple of (is_marketer: bool, user: Optional[User])
            
        Reference:
            PHP logic: SELECT * FROM `users` WHERE `phone_number` LIKE '$waId'
        """
        if not wa_id:
            return False, None
            
        try:
            # Query users table by phone_number
            # Using LIKE to match partial phone numbers
            user = User.objects.filter(phone_number__icontains=wa_id).first()
            
            if user and user.status == 'Active':
                return True, user
            return False, user
            
        except Exception:
            return False, None
    
    @staticmethod
    def get_user_by_phone(phone: str) -> Optional[User]:
        """
        Get user by exact phone number match
        
        Args:
            phone: Phone number string
            
        Returns:
            User object or None if not found
        """
        if not phone:
            return None
            
        try:
            return User.objects.filter(phone_number=phone, status='Active').first()
        except Exception:
            return None
    
    @staticmethod
    def get_user_by_employee_no(employee_no: str) -> Optional[User]:
        """
        Get user by employee number
        
        Args:
            employee_no: Employee number string
            
        Returns:
            User object or None if not found
        """
        if not employee_no:
            return None
            
        try:
            return User.objects.filter(employee_no=employee_no, status='Active').first()
        except Exception:
            return None
    
    @staticmethod
    def is_service_user(wa_id: str) -> bool:
        """
        Check if WhatsApp ID belongs to service user (OL/HR/365)
        
        Args:
            wa_id: WhatsApp ID (phone number)
            
        Returns:
            True if service user, False otherwise
        """
        is_marketer, user = UserValidator.is_internal_marketer(wa_id)
        
        if is_marketer and user:
            return user.employee_no == AmaniConfig.DEPARTMENT_MEMBER
        
        return False
    
    @staticmethod
    def get_marketer_info(wa_id: str) -> Optional[dict]:
        """
        Get marketer information if WhatsApp ID belongs to internal marketer
        
        Args:
            wa_id: WhatsApp ID (phone number)
            
        Returns:
            Dictionary with marketer info or None
        """
        is_marketer, user = UserValidator.is_internal_marketer(wa_id)
        
        if is_marketer and user:
            return {
                'employee_no': user.employee_no,
                'fullnames': user.fullnames,
                'email': user.email,
                'department': user.department.dp_name if user.department else None,
                'phone_number': user.phone_number,
                'status': user.status
            }
        
        return None
