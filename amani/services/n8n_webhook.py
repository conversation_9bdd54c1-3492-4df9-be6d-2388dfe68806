import requests
import json
import logging
from django.utils import timezone
from ..config import AmaniConfig

logger = logging.getLogger(__name__)

class N8NWebhookService:
    def __init__(self):
        self.n8n_webhook_url = AmaniConfig.N8N_WEBHOOK_URL
        self.timeout = AmaniConfig.N8N_WEBHOOK_TIMEOUT

    def _enrich_payload(self, payload: dict) -> dict:
        """Enrich payload with metadata"""
        enriched_payload = payload.copy()
        enriched_payload['_metadata'] = {
            'received_at': timezone.now().isoformat(),
            'source': 'amani_django_webhook',
            'django_app_version': '1.0.0',
            'crm_mapped': True
        }
        return enriched_payload

    def _map_whatsapp_to_crm(self, wa_payload: dict) -> dict:
        """Map WhatsApp Business API payload to CRM prospect format"""
        try:
            # Extract data from WhatsApp payload
            contact = wa_payload['entry'][0]['changes'][0]['value']['contacts'][0]
            message = wa_payload['entry'][0]['changes'][0]['value']['messages'][0]
            
            # Map to CRM format
            crm_data = {
                'name': contact['profile'].get('name', 'WhatsApp User'),
                'phone': '+' + contact['wa_id'],
                'lead_source': AmaniConfig.LEAD_SOURCE_ID,
                'lead_type': AmaniConfig.DEFAULT_LEAD_TYPE,
                'department': AmaniConfig.DEPARTMENT_TELEMARKETING,
                'department_member': AmaniConfig.DEPARTMENT_MEMBER,
                'comment': f"WhatsApp message: {message['text']['body']}",
                'status': AmaniConfig.DEFAULT_STATUS,
                'category': AmaniConfig.DEFAULT_CATEGORY,
                'pipeline_level': AmaniConfig.DEFAULT_PIPELINE_LEVEL,
                'is_verified': False,
                'is_converted': False,
                'date': timezone.now().isoformat()
            }
            
            logger.info(f"Mapped WhatsApp data to CRM format: {crm_data['name']} - {crm_data['phone']}")
            return crm_data
            
        except Exception as e:
            logger.error(f"Error mapping WhatsApp data to CRM: {e}")
            raise

    def _validate_crm_data(self, crm_data: dict) -> bool:
        """Validate CRM data before sending to N8N"""
        required_fields = ['name', 'phone', 'lead_source']
        for field in required_fields:
            if not crm_data.get(field):
                logger.error(f"Missing required field: {field}")
                return False
        return True

    def dispatch_to_n8n(self, payload: dict) -> dict:
        """Dispatch WhatsApp payload to N8N with CRM mapping"""
        if not self.n8n_webhook_url:
            logger.warning("N8N_WEBHOOK_URL is not configured. Skipping dispatch to N8N.")
            return {"success": False, "message": "N8N webhook URL not configured."}

        # Map WhatsApp data to CRM format before sending to N8N
        try:
            crm_mapped_data = self._map_whatsapp_to_crm(payload)
            
            # Validate CRM data
            if not self._validate_crm_data(crm_mapped_data):
                raise ValueError("Invalid CRM data after mapping")
            
            enriched_payload = self._enrich_payload(crm_mapped_data)
            
        except Exception as e:
            logger.error(f"Error mapping data: {e}")
            # Fallback to original payload if mapping fails
            enriched_payload = self._enrich_payload(payload)

        headers = {'Content-Type': 'application/json'}

        try:
            response = requests.post(
                self.n8n_webhook_url,
                data=json.dumps(enriched_payload),
                headers=headers,
                timeout=self.timeout
            )
            response.raise_for_status()
            logger.info(f"Dispatched CRM-mapped payload to N8N successfully. Status: {response.status_code}")
            return {
                "success": True,
                "status_code": response.status_code,
                "response_data": response.json() if response.content else {},
                "crm_mapped": True
            }
        except requests.exceptions.Timeout:
            logger.error(f"N8N webhook dispatch timed out after {self.timeout} seconds.")
            raise Exception(f"N8N webhook dispatch timed out.")
        except requests.exceptions.RequestException as e:
            logger.error(f"Error dispatching to N8N webhook: {e}")
            raise Exception(f"Failed to dispatch to N8N: {e}")
        except json.JSONDecodeError:
            logger.error(f"N8N webhook returned non-JSON response: {response.text}")
            raise Exception(f"N8N webhook returned invalid JSON response.")