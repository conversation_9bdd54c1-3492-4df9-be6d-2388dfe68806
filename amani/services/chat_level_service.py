"""
Chat Level Service
==================

Core business logic for the Chat Levels System - a hierarchical menu system
that guides WhatsApp users through conversational flows.

This service handles:
- User input validation against level options
- Navigation logic (Home, Previous, Next Level)
- Conversation state management
- Response generation
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from django.utils import timezone
from django.db import transaction, connection
from ..models import AmaniLevel, AmaniChatLevel, Amani

logger = logging.getLogger(__name__)


class ChatLevelService:
    """
    Core service for managing chat levels and user conversations.

    This service implements the hierarchical menu system where users navigate
    through numbered options, similar to an IVR system via WhatsApp.
    """

    # Special navigation options
    OPTION_HOME = 0
    OPTION_PREVIOUS = 98

    # Conversation timeout (12 hours)
    CONVERSATION_TIMEOUT_HOURS = 12

    @staticmethod
    def get_or_create_chat_level(wa_id: str, user_type: str = 'new', customer_no: str = None) -> AmaniChatLevel:
        """
        Get existing chat level or create new one with user-type awareness.

        Matches Old_Amani PHP logic exactly:
        - Check for recent messages (< 12 hours)
        - If recent messages exist: use/create chat level
        - If no recent messages: delete any existing chat level and start fresh

        Args:
            wa_id: WhatsApp ID of the user
            user_type: 'customer', 'prospect', 'new'
            customer_no: Customer number for customers

        Returns:
            AmaniChatLevel: User's current or new chat level state
        """
        try:
            # Check for recent conversation (< 12 hours) - matches PHP logic
            timeout_threshold = timezone.now() - timedelta(hours=ChatLevelService.CONVERSATION_TIMEOUT_HOURS)

            # Look for recent messages from this user - matches PHP query
            recent_messages = Amani.objects.filter(
                waId=wa_id,
                Amani_time__gte=timeout_threshold
            ).exists()

            if recent_messages:
                # Has recent messages - check for existing chat level or create new one
                existing_chat_level = AmaniChatLevel.objects.filter(waID=wa_id).first()

                if existing_chat_level:
                    # Update timestamp and return existing chat level
                    existing_chat_level.date = timezone.now()
                    existing_chat_level.save()
                    logger.info(f"Retrieved existing chat level for {wa_id}: level {existing_chat_level.chat_level}, step '{existing_chat_level.step}'")
                    return existing_chat_level
                else:
                    # No existing chat level but has recent messages - create new one with appropriate starting level
                    menu_config = ChatLevelService.get_user_type_specific_menu(user_type, customer_no)
                    chat_level = AmaniChatLevel.objects.create(
                        waID=wa_id,
                        step='1',
                        chat_level=menu_config['start_level'],
                        date=timezone.now()
                    )
                    logger.info(f"Created new chat level for {wa_id} ({user_type}): level {chat_level.chat_level}, step '{chat_level.step}'")
                    return chat_level
            else:
                # No recent messages - delete any existing chat level and start fresh (matches PHP logic)
                AmaniChatLevel.objects.filter(waID=wa_id).delete()

                # Create with appropriate starting level based on user type
                menu_config = ChatLevelService.get_user_type_specific_menu(user_type, customer_no)
                chat_level = AmaniChatLevel.objects.create(
                    waID=wa_id,
                    step='1',
                    chat_level=menu_config['start_level'],
                    date=timezone.now()
                )
                logger.info(f"Created new conversation for {wa_id} ({user_type}): level {chat_level.chat_level}, step '{chat_level.step}'")
                return chat_level

        except Exception as e:
            logger.error(f"Error getting/creating chat level for {wa_id}: {e}")
            # Fallback: create basic level with appropriate starting level
            menu_config = ChatLevelService.get_user_type_specific_menu(user_type, customer_no)
            chat_level, _ = AmaniChatLevel.objects.get_or_create(
                waID=wa_id,
                defaults={
                    'step': '1',
                    'chat_level': menu_config['start_level'],
                    'date': timezone.now()
                }
            )
            return chat_level

    @staticmethod
    def validate_user_input(level_code: int, user_input: str) -> Dict[str, Any]:
        """
        Validate user input against valid options for the current level.

        Args:
            level_code: Current level code
            user_input: User's input (should be numeric)

        Returns:
            Dict with validation result:
            {
                'is_valid': bool,
                'selected_option': int or None,
                'error_message': str or None,
                'is_navigation': bool
            }
        """
        try:
            # Clean and validate input
            user_input = user_input.strip()

            # Check if input is numeric
            if not user_input.isdigit():
                return {
                    'is_valid': False,
                    'selected_option': None,
                    'error_message': "Please enter a valid number.",
                    'is_navigation': False
                }

            selected_option = int(user_input)

            # Get level options
            level = AmaniLevel.objects.filter(Level_code=level_code).first()
            if not level:
                return {
                    'is_valid': False,
                    'selected_option': None,
                    'error_message': "Sorry, this menu option is not available.",
                    'is_navigation': False
                }

            # Parse valid options (comma-separated)
            try:
                valid_options = [int(opt.strip()) for opt in level.level_options.split(',') if opt.strip()]
            except ValueError:
                logger.error(f"Invalid level_options format for level {level_code}: '{level.level_options}'")
                return {
                    'is_valid': False,
                    'selected_option': None,
                    'error_message': "Menu configuration error. Please try again later.",
                    'is_navigation': False
                }

            # Check if selected option is valid
            # Special navigation options (0=Home, 98=Previous) are always valid
            if selected_option in valid_options or selected_option in [ChatLevelService.OPTION_HOME, ChatLevelService.OPTION_PREVIOUS]:
                is_navigation = selected_option in [ChatLevelService.OPTION_HOME, ChatLevelService.OPTION_PREVIOUS]
                return {
                    'is_valid': True,
                    'selected_option': selected_option,
                    'error_message': None,
                    'is_navigation': is_navigation
                }
            else:
                # Invalid option - include navigation options in available choices
                all_available = sorted(list(set(valid_options + [ChatLevelService.OPTION_HOME, ChatLevelService.OPTION_PREVIOUS])))
                return {
                    'is_valid': False,
                    'selected_option': None,
                    'error_message': f"Invalid option '{selected_option}'. Please choose from: {', '.join(map(str, all_available))}",
                    'is_navigation': False
                }

        except Exception as e:
            logger.error(f"Error validating input '{user_input}' for level {level_code}: {e}")
            return {
                'is_valid': False,
                'selected_option': None,
                'error_message': "An error occurred. Please try again.",
                'is_navigation': False
            }

    @staticmethod
    def process_navigation(current_level: int, current_step: str, user_input: str) -> Dict[str, Any]:
        """
        Process navigation based on user input.

        Args:
            current_level: Current level code
            current_step: Current step path (e.g., "1,2,3")
            user_input: User's input

        Returns:
            Dict with navigation result:
            {
                'new_level': int,
                'new_step': str,
                'action': str ('home', 'previous', 'next', 'error'),
                'message': str or None
            }
        """
        try:
            # First validate the input
            validation = ChatLevelService.validate_user_input(current_level, user_input)

            if not validation['is_valid']:
                return {
                    'new_level': current_level,
                    'new_step': current_step,
                    'action': 'error',
                    'message': validation['error_message']
                }

            selected_option = validation['selected_option']

            # Handle special navigation options
            if selected_option == ChatLevelService.OPTION_HOME:
                # Home - reset to level 1
                return {
                    'new_level': 1,
                    'new_step': '1',
                    'action': 'home',
                    'message': None
                }

            elif selected_option == ChatLevelService.OPTION_PREVIOUS:
                # Previous menu - go back one level
                step_array = current_step.split(',')
                if len(step_array) > 1:
                    # Remove last step
                    step_array.pop()
                    new_step = ','.join(step_array)
                    # Calculate new level from remaining steps
                    new_level = int(''.join(step_array))
                else:
                    # Already at top level, stay at level 1
                    new_step = '1'
                    new_level = 1

                return {
                    'new_level': new_level,
                    'new_step': new_step,
                    'action': 'previous',
                    'message': None
                }

            else:
                # Next level - append option to current level
                new_level = int(str(current_level) + str(selected_option))
                new_step = current_step + ',' + str(selected_option)

                return {
                    'new_level': new_level,
                    'new_step': new_step,
                    'action': 'next',
                    'message': None
                }

        except Exception as e:
            logger.error(f"Error processing navigation for level {current_level}, step '{current_step}', input '{user_input}': {e}")
            return {
                'new_level': current_level,
                'new_step': current_step,
                'action': 'error',
                'message': 'Navigation error. Please try again.'
            }

    @staticmethod
    def get_level_response(level_code: int) -> Optional[AmaniLevel]:
        """
        Get the response data for a specific level.

        Args:
            level_code: Level code to retrieve

        Returns:
            AmaniLevel object or None if not found
        """
        try:
            level = AmaniLevel.objects.filter(Level_code=level_code).first()
            if level:
                logger.debug(f"Retrieved level {level_code}: '{level.level_message[:50]}...'")
                return level
            else:
                logger.warning(f"Level {level_code} not found in database")
                return None

        except Exception as e:
            logger.error(f"Error retrieving level {level_code}: {e}")
            return None

    @staticmethod
    def update_chat_state(wa_id: str, new_level: int, new_step: str) -> bool:
        """
        Update the user's chat state.

        Args:
            wa_id: WhatsApp ID
            new_level: New level code
            new_step: New step path

        Returns:
            bool: Success status
        """
        try:
            with transaction.atomic():
                chat_level, created = AmaniChatLevel.objects.get_or_create(
                    waID=wa_id,
                    defaults={
                        'step': new_step,
                        'chat_level': new_level,
                        'date': timezone.now()
                    }
                )

                if not created:
                    chat_level.step = new_step
                    chat_level.chat_level = new_level
                    chat_level.date = timezone.now()
                    chat_level.save()

                logger.info(f"Updated chat state for {wa_id}: level {new_level}, step '{new_step}'")
                return True

        except Exception as e:
            logger.error(f"Error updating chat state for {wa_id}: {e}")
            return False

    @staticmethod
    def process_user_message(wa_id: str, message: str, user_type: str = 'new', customer_no: str = None) -> Dict[str, Any]:
        """
        Main method to process a user message and determine the response.

        This is the primary entry point for processing WhatsApp messages
        through the chat levels system.

        Args:
            wa_id: WhatsApp ID of the user
            message: User's message text
            user_type: 'customer', 'prospect', 'new'
            customer_no: Customer number for customers

        Returns:
            Dict with processing result:
            {
                'success': bool,
                'response_message': str or None,
                'has_document': bool,
                'document_path': str or None,
                'error_message': str or None,
                'level_data': dict or None
            }
        """
        try:
            # Handle empty messages (greetings) - return main menu (level 1)
            if not message or message.strip() == '':
                logger.info(f"Empty message from {wa_id} - returning main menu (level 1)")
                level_data = ChatLevelService.get_level_response(1)
                if level_data:
                    # Update chat state to level 1
                    ChatLevelService.update_chat_state(wa_id, 1, '1')
                    return {
                        'success': True,
                        'response_message': level_data.level_message,
                        'has_document': bool(level_data.has_document),
                        'document_path': level_data.has_document.url if level_data.has_document else None,
                        'error_message': None,
                        'level_data': level_data
                    }
                else:
                    # Fallback if level 1 not found
                    return {
                        'success': False,
                        'response_message': "Welcome! Please select an option from the menu.",
                        'has_document': False,
                        'document_path': None,
                        'error_message': "Level 1 not found",
                        'level_data': None
                    }
            
            # Get or create chat level with user type awareness
            chat_level = ChatLevelService.get_or_create_chat_level(wa_id, user_type, customer_no)
            current_level = chat_level.chat_level
            current_step = chat_level.step

            logger.info(f"Processing message from {wa_id}: '{message}' at level {current_level}, step '{current_step}'")

            # Process navigation
            navigation = ChatLevelService.process_navigation(current_level, current_step, message)

            if navigation['action'] == 'error':
                # Validation/navigation error
                return {
                    'success': False,
                    'response_message': navigation['message'],
                    'has_document': False,
                    'document_path': None,
                    'error_message': navigation['message'],
                    'level_data': None
                }

            # Get response for new level
            new_level = navigation['new_level']

            # Special handling for title status levels (customer-specific)
            if user_type == 'customer' and customer_no and new_level >= 211 and new_level <= 214:
                # Handle title status responses
                if new_level == 211:  # All titles
                    response_message = ChatLevelService.generate_title_status_response(customer_no, 'all')
                elif new_level == 212:  # Ready titles
                    response_message = ChatLevelService.generate_title_status_response(customer_no, 'ready')
                elif new_level == 213:  # Processing titles
                    response_message = ChatLevelService.generate_title_status_response(customer_no, 'processing')
                elif new_level == 214:  # Pending titles
                    response_message = ChatLevelService.generate_title_status_response(customer_no, 'pending')

                # Update chat state
                success = ChatLevelService.update_chat_state(wa_id, new_level, navigation['new_step'])

                return {
                    'success': True,
                    'response_message': response_message,
                    'has_document': False,
                    'document_path': None,
                    'error_message': None,
                    'level_data': {
                        'Level_code': new_level,
                        'level_message': response_message,
                        'level_options': '0,98',
                        'has_document': None,
                        'is_last': '1'
                    }
                }

            level_data = ChatLevelService.get_level_response(new_level)

            if not level_data:
                error_msg = f"Sorry, level {new_level} is not available. Returning to main menu."
                # Reset to level 1
                ChatLevelService.update_chat_state(wa_id, 1, '1')
                level_data = ChatLevelService.get_level_response(1)

                if not level_data:
                    return {
                        'success': False,
                        'response_message': "System error. Please try again later.",
                        'has_document': False,
                        'document_path': None,
                        'error_message': "Level not found and fallback failed",
                        'level_data': None
                    }

                return {
                    'success': False,
                    'response_message': error_msg + "\n\n" + level_data.level_message,
                    'has_document': False,
                    'document_path': None,
                    'error_message': error_msg,
                    'level_data': level_data
                }

            # Update chat state
            success = ChatLevelService.update_chat_state(wa_id, new_level, navigation['new_step'])

            if not success:
                logger.warning(f"Failed to update chat state for {wa_id}, but continuing with response")

            # Return response data
            return {
                'success': True,
                'response_message': level_data.level_message,
                'has_document': bool(level_data.has_document),
                'document_path': level_data.has_document.url if level_data.has_document else None,
                'error_message': None,
                'level_data': level_data
            }

        except Exception as e:
            logger.error(f"Error processing user message from {wa_id}: {e}")
            return {
                'success': False,
                'response_message': "An error occurred. Please try again.",
                'has_document': False,
                'document_path': None,
                'error_message': str(e),
                'level_data': None
            }

    @staticmethod
    def _reset_chat_level(wa_id: str) -> None:
        """
        Reset chat level for a user (delete existing state).

        Args:
            wa_id: WhatsApp ID
        """
        try:
            deleted_count, _ = AmaniChatLevel.objects.filter(waID=wa_id).delete()
            if deleted_count > 0:
                logger.info(f"Reset chat level for {wa_id} (deleted {deleted_count} records)")
        except Exception as e:
            logger.error(f"Error resetting chat level for {wa_id}: {e}")

    @staticmethod
    def get_conversation_summary(wa_id: str) -> Dict[str, Any]:
        """
        Get a summary of the user's current conversation state.

        Args:
            wa_id: WhatsApp ID

        Returns:
            Dict with conversation summary
        """
        try:
            chat_level = AmaniChatLevel.objects.filter(waID=wa_id).first()

            if not chat_level:
                return {
                    'has_conversation': False,
                    'current_level': None,
                    'current_step': None,
                    'last_activity': None,
                    'level_message': None
                }

            level_data = ChatLevelService.get_level_response(chat_level.chat_level)

            return {
                'has_conversation': True,
                'current_level': chat_level.chat_level,
                'current_step': chat_level.step,
                'last_activity': chat_level.date,
                'level_message': level_data.level_message[:100] + '...' if level_data and len(level_data.level_message) > 100 else level_data.level_message if level_data else None
            }

        except Exception as e:
            logger.error(f"Error getting conversation summary for {wa_id}: {e}")
            return {
                'has_conversation': False,
                'error': str(e)
            }

    @staticmethod
    def get_customer_title_status(customer_no: str) -> Dict[str, Any]:
        """
        Get title deed status for a specific customer

        Args:
            customer_no: Customer number

        Returns:
            Dict with title status information
        """
        try:
            with connection.cursor() as cursor:
                # Query lead files for this customer with title status
                cursor.execute("""
                    SELECT
                        lf.lead_file_no,
                        lf.plot_no,
                        lf.title_status,
                        lf.completion_date,
                        lf.purchase_price,
                        lf.total_paid
                    FROM sales_leadfile lf
                    WHERE lf.customer_id_id = %s
                    ORDER BY lf.completion_date DESC
                """, [customer_no])

                columns = ['lead_file_no', 'plot_no', 'title_status', 'completion_date', 'purchase_price', 'total_paid']
                results = []

                for row in cursor.fetchall():
                    plot_data = dict(zip(columns, row))
                    results.append(plot_data)

                # Categorize by status
                ready_titles = [r for r in results if r['title_status'] and any(status in r['title_status'].lower() for status in ['ready', 'completed', 'issued'])]
                processing_titles = [r for r in results if r['title_status'] and any(status in r['title_status'].lower() for status in ['process', 'progress', 'under review'])]
                pending_titles = [r for r in results if not r['title_status'] or any(status in r['title_status'].lower() for status in ['pending', 'awaiting', 'not started'])]

                return {
                    'success': True,
                    'total_plots': len(results),
                    'ready_titles': len(ready_titles),
                    'processing_titles': len(processing_titles),
                    'pending_titles': len(pending_titles),
                    'plots': results,
                    'summary': {
                        'ready': ready_titles,
                        'processing': processing_titles,
                        'pending': pending_titles
                    }
                }

        except Exception as e:
            logger.error(f"Error getting title status for customer {customer_no}: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_plots': 0,
                'ready_titles': 0,
                'processing_titles': 0,
                'pending_titles': 0,
                'plots': [],
                'summary': {'ready': [], 'processing': [], 'pending': []}
            }

    @staticmethod
    def get_user_type_specific_menu(user_type: str, customer_no: str = None) -> Dict[str, Any]:
        """
        Get appropriate starting menu based on user type

        Args:
            user_type: 'customer', 'prospect', 'new'
            customer_no: Customer number for customers

        Returns:
            Dict with menu level code and context
        """
        if user_type == 'customer' and customer_no:
            # Start with customer menu (Level 2)
            return {
                'start_level': 2,
                'user_type': 'customer',
                'customer_no': customer_no,
                'context': 'customer_services'
            }
        elif user_type == 'prospect':
            # Start with prospect menu (Level 1)
            return {
                'start_level': 1,
                'user_type': 'prospect',
                'context': 'prospect_inquiry'
            }
        else:
            # Start with general menu (Level 1)
            return {
                'start_level': 1,
                'user_type': 'new',
                'context': 'general_inquiry'
            }

    @staticmethod
    def generate_title_status_response(customer_no: str, option: str = 'all') -> str:
        """
        Generate formatted title status response

        Args:
            customer_no: Customer number
            option: 'all', 'ready', 'processing', 'pending'
        """
        title_data = ChatLevelService.get_customer_title_status(customer_no)

        if not title_data['success']:
            return "Unable to retrieve title status. Please try again later."

        if title_data['total_plots'] == 0:
            return "No plot records found for your account."

        response = f"*Your Title Deed Status*\n\n"
        response += f"Total Plots: {title_data['total_plots']}\n"
        response += f"Ready: {title_data['ready_titles']}\n"
        response += f"Processing: {title_data['processing_titles']}\n"
        response += f"Pending: {title_data['pending_titles']}\n\n"

        if option == 'ready' and title_data['ready_titles'] > 0:
            response += "*Ready for Collection:*\n"
            for plot in title_data['summary']['ready'][:3]:  # Limit to 3 for WhatsApp
                plot_name = plot.get('plot_no', 'Unknown')
                response += f"• Plot {plot_name}\n"
        elif option == 'processing' and title_data['processing_titles'] > 0:
            response += "*Currently Processing:*\n"
            for plot in title_data['summary']['processing'][:3]:
                plot_name = plot.get('plot_no', 'Unknown')
                response += f"• Plot {plot_name}\n"
        elif option == 'pending' and title_data['pending_titles'] > 0:
            response += "*Pending Titles:*\n"
            for plot in title_data['summary']['pending'][:3]:
                plot_name = plot.get('plot_no', 'Unknown')
                response += f"• Plot {plot_name}\n"

        if title_data['ready_titles'] > 0:
            response += "\n*For collection details, call: 0712345678*"
        else:
            response += "\n*We'll notify you when titles are ready for collection.*"

        return response

    @staticmethod
    def get_system_stats() -> Dict[str, Any]:
        """
        Get basic system statistics.

        Returns:
            Dict with basic system metrics
        """
        try:
            # Count total records
            total_levels = AmaniLevel.objects.count()
            total_chat_levels = AmaniChatLevel.objects.count()
            total_messages = Amani.objects.count()

            # Count active conversations (last 12 hours)
            active_threshold = timezone.now() - timedelta(hours=ChatLevelService.CONVERSATION_TIMEOUT_HOURS)
            active_conversations = AmaniChatLevel.objects.filter(date__gte=active_threshold).count()

            return {
                'total_levels': total_levels,
                'total_chat_levels': total_chat_levels,
                'total_messages': total_messages,
                'active_conversations': active_conversations
            }

        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            return {
                'error': str(e),
                'total_levels': 0,
                'total_chat_levels': 0,
                'total_messages': 0,
                'active_conversations': 0
            }
