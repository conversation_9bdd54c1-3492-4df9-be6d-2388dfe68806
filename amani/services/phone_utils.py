"""
Phone Number Utilities
=====================

Utilities for phone number formatting and validation.
Reuses logic from leads/views.py for consistency.
"""

import re
from typing import Optional
from ..config import AmaniConfig


class PhoneUtils:
    """Phone number formatting and validation utilities"""
    
    @staticmethod
    def normalize_phone_number(phone: str) -> str:
        """
        Normalize phone number to E.164 format (+254...)
        
        Args:
            phone: Raw phone number string
            
        Returns:
            Normalized phone number in E.164 format
            
        Raises:
            ValueError: If phone number is invalid
        """
        if not phone:
            raise ValueError("Phone number cannot be empty")
            
        # Remove spaces, dashes, and leading plus
        phone = phone.replace(" ", "").replace("-", "")
        
        # Ensure phone starts with '+'
        if not phone.startswith("+"):
            # If phone starts with '0', replace with country code (assume Kenya +254)
            if phone.startswith("0"):
                phone = AmaniConfig.DEFAULT_COUNTRY_CODE + phone[1:]
            # If phone starts with country code without '+', add '+'
            elif phone[:3] == "254":
                phone = "+" + phone
            else:
                # Default: add '+' at the start
                phone = "+" + phone
        
        # Validate length (international numbers are usually 10-15 digits after '+')
        digits_only = phone[1:]
        if not digits_only.isdigit():
            raise ValueError("Phone number must contain only digits after +")
        if len(digits_only) < 10:
            raise ValueError("Phone number is too short")
        if len(digits_only) > 15:
            raise ValueError("Phone number is too long")
            
        return phone
    
    @staticmethod
    def validate_phone_number(phone: str) -> bool:
        """
        Validate phone number format and length
        
        Args:
            phone: Phone number string to validate
            
        Returns:
            True if valid, False otherwise
        """
        try:
            PhoneUtils.normalize_phone_number(phone)
            return True
        except ValueError:
            return False
    
    @staticmethod
    def extract_country_code(phone: str) -> Optional[str]:
        """
        Extract country code from phone number
        
        Args:
            phone: Phone number string
            
        Returns:
            Country code (e.g., '+254') or None if not found
        """
        if not phone:
            return None
            
        # Match country codes (1-4 digits after +)
        match = re.match(r'^\+(\d{1,4})', phone)
        if match:
            return '+' + match.group(1)
        return None
    
    @staticmethod
    def format_for_display(phone: str) -> str:
        """
        Format phone number for display purposes
        
        Args:
            phone: Phone number string
            
        Returns:
            Formatted phone number for display
        """
        try:
            normalized = PhoneUtils.normalize_phone_number(phone)
            # Format as +254 XXX XXX XXX
            if len(normalized) == 13 and normalized.startswith('+254'):
                return f"{normalized[:4]} {normalized[4:7]} {normalized[7:10]} {normalized[10:]}"
            return normalized
        except ValueError:
            return phone
    
    @staticmethod
    def is_kenyan_number(phone: str) -> bool:
        """
        Check if phone number is Kenyan (+254)
        
        Args:
            phone: Phone number string
            
        Returns:
            True if Kenyan number, False otherwise
        """
        try:
            normalized = PhoneUtils.normalize_phone_number(phone)
            return normalized.startswith('+254')
        except ValueError:
            return False
