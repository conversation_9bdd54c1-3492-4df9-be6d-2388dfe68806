"""
Phone Number Lookup Service
==========================

Service to search phone numbers across users, customers, and prospects tables.
Handles different user types and returns appropriate information for routing.
"""

import logging
from typing import Optional, Dict, Any, Tuple
from django.contrib.auth import get_user_model
from django.db import connection
from ..config import AmaniConfig

logger = logging.getLogger(__name__)

User = get_user_model()


class PhoneLookupService:
    """Service to lookup phone numbers across multiple tables"""

    @staticmethod
    def search_phone_number(phone: str) -> Dict[str, Any]:
        """Search phone number across all tables and return comprehensive results."""
        if not phone:
            return PhoneLookupService._not_found_response('Phone number is required')

        clean_phone = PhoneLookupService._clean_phone_for_search(phone)
        last_9_digits = PhoneLookupService._get_last_9_digits(clean_phone)

        # Search all tables simultaneously
        results = PhoneLookupService._search_all_tables(last_9_digits)

        # Build final result
        return PhoneLookupService._build_comprehensive_result(clean_phone, last_9_digits, results)

    @staticmethod
    def _not_found_response(message: str) -> Dict[str, Any]:
        """Return standardized not found response."""
        return {
            'found': False,
            'user_type': 'unknown',
            'message': message
        }

    @staticmethod
    def _search_all_tables(last_9_digits: str) -> Dict[str, Any]:
        """Search all tables simultaneously and return results."""
        return {
            'user': PhoneLookupService._search_users_table(last_9_digits),
            'customer': PhoneLookupService._search_customers_table(last_9_digits),
            'prospect': PhoneLookupService._search_prospects_table(last_9_digits)
        }

    @staticmethod
    def _build_comprehensive_result(clean_phone: str, last_9_digits: str, results: Dict[str, Any]) -> Dict[str, Any]:
        """Build comprehensive result from all search results."""
        # Determine primary user_type based on priority: user > customer > prospect > new
        user_type = 'new'
        found = False
        messages = []

        user_result = results['user']
        customer_result = results['customer']
        prospect_result = results['prospect']

        if user_result.get('found'):
            user_type = 'internal_user'
            found = True
            messages.append('User found')

        if customer_result.get('found'):
            if user_type == 'new':
                user_type = 'customer'
            found = True
            messages.append('Customer found')

        if prospect_result.get('found'):
            if user_type == 'new':
                user_type = 'prospect'
            found = True
            messages.append('Prospect found')

        # Build base result
        result = {
            'found': found,
            'user_type': user_type,
            'clean_phone': clean_phone,
            'last_9_digits': last_9_digits,
            'message': ' | '.join(messages) if messages else 'Phone number not found in database. New prospect.'
        }

        # Add detailed sections
        PhoneLookupService._add_details_to_result(result, user_result, customer_result, prospect_result)

        # Add backward compatibility fields
        PhoneLookupService._add_compatibility_fields(result, user_result, customer_result, prospect_result)

        # Add relationship flags
        result.update({
            'is_employee': user_result.get('found', False),
            'is_customer': customer_result.get('found', False),
            'is_prospect': prospect_result.get('found', False)
        })

        logger.info(f"Phone lookup: found={found}, user_type={user_type}, "
                   f"relationships={{employee: {result['is_employee']}, customer: {result['is_customer']}, prospect: {result['is_prospect']}}}")

        return result

    @staticmethod
    def _add_details_to_result(result: Dict[str, Any], user_result: Dict[str, Any],
                              customer_result: Dict[str, Any], prospect_result: Dict[str, Any]) -> None:
        """Add detailed sections to result dictionary."""
        if user_result.get('found'):
            result['user_details'] = {
                'user_id': user_result.get('user_id'),
                'employee_no': user_result.get('employee_no'),
                'fullnames': user_result.get('fullnames'),
                'email': user_result.get('email'),
                'phone_number': user_result.get('phone_number'),
                'department': user_result.get('department'),
                'permissions': user_result.get('permissions'),
                'office': user_result.get('office'),
                'is_marketer': user_result.get('is_marketer')
            }

        if customer_result.get('found'):
            result['customer_details'] = {
                'customer_no': customer_result.get('customer_no'),
                'name': customer_result.get('name'),
                'phone': customer_result.get('phone'),
                'email': customer_result.get('email'),
                'created_date': customer_result.get('created_date'),
                'lead_source': customer_result.get('lead_source'),
                'department_member': customer_result.get('department_member')
            }

        if prospect_result.get('found'):
            result['prospect_details'] = {
                'prospect_id': prospect_result.get('prospect_id'),
                'name': prospect_result.get('name'),
                'phone': prospect_result.get('phone'),
                'email': prospect_result.get('email'),
                'status': prospect_result.get('status'),
                'lead_source': prospect_result.get('lead_source'),
                'created_date': prospect_result.get('created_date'),
                'department': prospect_result.get('department'),
                'department_member': prospect_result.get('department_member'),
                'category': prospect_result.get('category'),
                'pipeline_level': prospect_result.get('pipeline_level')
            }

    @staticmethod
    def _add_compatibility_fields(result: Dict[str, Any], user_result: Dict[str, Any],
                                 customer_result: Dict[str, Any], prospect_result: Dict[str, Any]) -> None:
        """Add backward compatibility fields to result."""
        if user_result.get('found'):
            result.update({
                'user_id': user_result.get('user_id'),
                'employee_no': user_result.get('employee_no'),
                'fullnames': user_result.get('fullnames'),
                'email': user_result.get('email'),
                'phone_number': user_result.get('phone_number')
            })
        elif customer_result.get('found'):
            result.update({
                'customer_no': customer_result.get('customer_no'),
                'name': customer_result.get('name'),
                'phone': customer_result.get('phone'),
                'email': customer_result.get('email')
            })
        elif prospect_result.get('found'):
            result.update({
                'prospect_id': prospect_result.get('prospect_id'),
                'name': prospect_result.get('name'),
                'phone': prospect_result.get('phone'),
                'email': prospect_result.get('email')
            })

    @staticmethod
    def _get_last_9_digits(phone: str) -> str:
        """Extract last 9 digits of phone number for search."""
        if not phone:
            return ""
        digits_only = ''.join(filter(str.isdigit, phone))
        return digits_only[-9:] if len(digits_only) >= 9 else digits_only

    @staticmethod
    def _clean_phone_for_search(phone: str) -> str:
        """Clean phone number for database operations."""
        if not phone:
            return ""

        # Remove non-digit characters except + at beginning
        clean = phone.replace(" ", "").replace("-", "").replace("(", "").replace(")", "").replace(".", "").strip()

        # Normalize Kenyan phone formats
        if clean.startswith('+254'):
            clean = '254' + clean[4:]
        elif clean.startswith('0') and len(clean) == 10:
            clean = '254' + clean[1:]

        if len(clean) < 9:
            logger.warning(f"Phone number too short: {phone} -> {clean}")

        return clean

    @staticmethod
    def _search_users_table(last_9_digits: str) -> Dict[str, Any]:
        """Search users table for internal staff using Django ORM for reliability."""
        try:
            logger.info(f"Searching users table for phone pattern: %{last_9_digits}%")

            # Handle phone numbers with spaces in database
            # Search for both the continuous digits and spaced format
            search_patterns = [
                last_9_digits,  # '100368483'
                f"{last_9_digits[:3]} {last_9_digits[3:6]} {last_9_digits[6:]}"  # '100 368 483'
            ]

            user = None
            for pattern in search_patterns:
                logger.info(f"Trying search pattern: %{pattern}%")
                user = User.objects.filter(
                    phone_number__icontains=pattern,  # Case-insensitive contains
                    status='Active'
                ).first()
                if user:
                    logger.info(f"Found user with pattern '%{pattern}%': {user.employee_no}")
                    break

            logger.info(f"Users table query executed. User found: {user is not None}")

            if user:
                logger.info(f"User found: {user.employee_no} - {user.fullnames} with phone: {user.phone_number}")

                result = {
                    'found': True,
                    'user_type': 'internal_user',
                    'user_id': user.id,
                    'employee_no': user.employee_no,
                    'fullnames': user.fullnames,
                    'email': user.email,
                    'phone_number': user.phone_number,
                    'department': user.department.dp_name if hasattr(user, 'department') and user.department else None,
                    'permissions': PhoneLookupService._get_user_permissions(user),
                    'office': user.office,
                    'is_marketer': user.is_marketer,
                    'message': f'Internal user found: {user.fullnames} ({user.employee_no})'
                }

                logger.info(f"Found user in users table: {user.employee_no} with phone containing pattern")
                return result

        except Exception as e:
            logger.error(f"Error searching users table: {e}")
            logger.error(f"Exception type: {type(e).__name__}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")

        return {'found': False, 'user_type': 'unknown'}

    @staticmethod
    def _search_customers_table(last_9_digits: str) -> Dict[str, Any]:
        """Search customers table using flexible phone matching."""
        try:
            # Handle phone numbers with spaces in database
            search_patterns = [
                f'%{last_9_digits}%',  # '100368483'
                f'%{last_9_digits[:3]} {last_9_digits[3:6]} {last_9_digits[6:]}%'  # '100 368 483'
            ]

            for pattern in search_patterns:
                with connection.cursor() as amani:
                    amani.execute("""
                        SELECT customer_no, customer_name, phone, primary_email,
                               date_of_registration, lead_source_id, marketer_id
                        FROM customers_customer
                        WHERE phone LIKE %s
                        LIMIT 1
                    """, [pattern])

                    row = amani.fetchone()
                    if row:
                        columns = ['customer_no', 'customer_name', 'phone', 'primary_email',
                                 'date_of_registration', 'lead_source_id', 'marketer_id']
                        customer_data = dict(zip(columns, row))

                        return {
                            'found': True,
                            'user_type': 'customer',
                            'customer_no': customer_data['customer_no'],
                            'name': customer_data['customer_name'],
                            'phone': customer_data['phone'],
                            'email': customer_data['primary_email'],
                            'created_date': customer_data['date_of_registration'],
                            'lead_source': customer_data['lead_source_id'],
                            'marketer_id': customer_data['marketer_id'],
                            'message': f'Customer found: {customer_data["customer_name"]} (Customer #{customer_data["customer_no"]})'
                        }

        except Exception as e:
            logger.error(f"Error searching customers table: {e}")

        return {'found': False, 'user_type': 'unknown'}

    @staticmethod
    def _search_prospects_table(last_9_digits: str) -> Dict[str, Any]:
        """Search prospects table using flexible phone matching."""
        try:
            # Handle phone numbers with spaces in database
            search_patterns = [
                f'%{last_9_digits}%',  # '100368483'
                f'%{last_9_digits[:3]} {last_9_digits[3:6]} {last_9_digits[6:]}%'  # '100 368 483'
            ]

            for pattern in search_patterns:
                with connection.cursor() as amani:
                    amani.execute("""
                        SELECT id, name, phone, email, status, lead_source_id,
                               date, department_id, department_member_id, category, pipeline_level
                        FROM leads_prospects
                        WHERE phone LIKE %s
                        LIMIT 1
                    """, [pattern])

                    row = amani.fetchone()
                    if row:
                        columns = ['id', 'name', 'phone', 'email', 'status', 'lead_source_id',
                                 'date', 'department_id', 'department_member_id', 'category', 'pipeline_level']
                        prospect_data = dict(zip(columns, row))

                        return {
                            'found': True,
                            'user_type': 'prospect',
                            'prospect_id': prospect_data['id'],
                            'name': prospect_data['name'],
                            'phone': prospect_data['phone'],
                            'email': prospect_data['email'],
                            'status': prospect_data['status'],
                            'lead_source': prospect_data['lead_source_id'],
                            'created_date': prospect_data['date'],
                            'department': prospect_data['department_id'],
                            'department_member': prospect_data['department_member_id'],
                            'category': prospect_data['category'],
                            'pipeline_level': prospect_data['pipeline_level'],
                            'message': f'Prospect found: {prospect_data["name"]} (Prospect #{prospect_data["id"]})'
                        }

        except Exception as e:
            logger.error(f"Error searching prospects table: {e}")

        return {'found': False, 'user_type': 'unknown'}

    @staticmethod
    def _get_user_permissions(user: User) -> Dict[str, Any]:
        """Get basic user permissions for routing"""
        return {
            'is_admin': user.is_superuser,
            'is_staff': user.is_staff,
            'groups': list(user.groups.values_list('name', flat=True))
        }

    @staticmethod
    def create_prospect_from_phone(phone: str, name: str = None, profile_name: str = None) -> Dict[str, Any]:
        """Create a new prospect from phone number."""
        try:
            if not phone:
                return {'success': False, 'error': 'Phone number is required'}

            clean_phone = PhoneLookupService._clean_phone_for_search(phone)
            if len(clean_phone) < 9:
                return {'success': False, 'error': 'Invalid phone number format'}

            # Check if prospect already exists
            last_9 = PhoneLookupService._get_last_9_digits(clean_phone)
            if PhoneLookupService._search_prospects_table(last_9)['found']:
                return {'success': False, 'error': 'Prospect already exists'}

            # Create new prospect
            with connection.cursor() as amani:
                prospect_name = name or profile_name or 'WhatsApp User'
                amani.execute("""
                    INSERT INTO leads_prospects (
                        name, phone, lead_source_id, lead_type, department_id,
                        department_member_id, status, category, pipeline_level,
                        is_verified, is_converted
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """, [
                    prospect_name, clean_phone, AmaniConfig.LEAD_SOURCE_ID,
                    'allocated', AmaniConfig.DEPARTMENT_TELEMARKETING,
                    AmaniConfig.DEPARTMENT_MEMBER, 'Active', 'Warm', 'New', False, False
                ])

                prospect_id = amani.lastrowid
                logger.info(f"Created prospect: {prospect_id} for {clean_phone}")

                return {
                    'success': True,
                    'prospect_id': prospect_id,
                    'name': prospect_name,
                    'phone': clean_phone
                }

        except Exception as e:
            logger.error(f"Error creating prospect: {e}")
            return {'success': False, 'error': str(e)}
