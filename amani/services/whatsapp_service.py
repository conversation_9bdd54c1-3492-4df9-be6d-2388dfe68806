"""
WhatsApp Service
================

Handles WhatsApp Business API interactions for sending text messages and documents.

This service provides methods to send messages via WhatsApp Business API,
similar to the PHP implementation in the old Amani system.
"""

import os
import logging
import requests
from typing import Optional, Dict, Any
from urllib.parse import urlencode
import mimetypes

logger = logging.getLogger(__name__)


class WhatsAppService:
    """
    Service for sending WhatsApp messages via Business API.

    Handles both text messages and document sending with proper error handling
    and authentication.
    """

    # WhatsApp API configuration
    BASE_URL = "https://live-mt-server.wati.io"
    API_VERSION = "5076"
    API_BASE_URL = f"{BASE_URL}/{API_VERSION}/api/v1"

    # Get credentials from environment
    BEARER_TOKEN = os.environ.get('AMANI_WHATSAPP_BEARER_TOKEN', '')
    PHONE_NUMBER_ID = os.environ.get('AMANI_WHATSAPP_PHONE_NUMBER_ID', '')

    @staticmethod
    def _get_headers() -> Dict[str, str]:
        """Get standard headers for WhatsApp API requests."""
        return {
            'accept': '*/*',
            'Authorization': f'Bearer {WhatsAppService.BEARER_TOKEN}',
            'Content-Type': 'application/json'
        }

    @staticmethod
    def _make_request(method: str, endpoint: str, data: Dict[str, Any] = None,
                     files: Dict[str, Any] = None, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Make HTTP request to WhatsApp API with error handling.

        Args:
            method: HTTP method (GET, POST, etc.)
            endpoint: API endpoint
            data: Request data for JSON requests
            files: File data for multipart requests
            params: URL parameters

        Returns:
            Dict with request result:
            {
                'success': bool,
                'response': dict or None,
                'error': str or None,
                'status_code': int or None
            }
        """
        try:
            url = f"{WhatsAppService.API_BASE_URL}{endpoint}"

            # Prepare request data
            headers = WhatsAppService._get_headers()

            if files:
                # Multipart request for files - remove Content-Type header
                headers.pop('Content-Type', None)
                response = requests.request(method, url, headers=headers, files=files, params=params, timeout=30)
            elif data and isinstance(data, dict):
                # JSON request
                response = requests.request(method, url, headers=headers, json=data, params=params, timeout=30)
            else:
                # Simple request
                response = requests.request(method, url, headers=headers, params=params, timeout=30)

            # Check response
            if response.status_code in [200, 201]:
                try:
                    result = response.json()
                    logger.info(f"WhatsApp API {method} {endpoint} - Success: {response.status_code}")
                    return {
                        'success': True,
                        'response': result,
                        'error': None,
                        'status_code': response.status_code
                    }
                except ValueError:
                    logger.info(f"WhatsApp API {method} {endpoint} - Success (no JSON): {response.status_code}")
                    return {
                        'success': True,
                        'response': {'message': 'Success'},
                        'error': None,
                        'status_code': response.status_code
                    }
            else:
                error_msg = f"WhatsApp API error: {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f" - {error_data}"
                except ValueError:
                    error_msg += f" - {response.text[:200]}"

                logger.error(f"WhatsApp API {method} {endpoint} - {error_msg}")
                return {
                    'success': False,
                    'response': None,
                    'error': error_msg,
                    'status_code': response.status_code
                }

        except requests.exceptions.Timeout:
            error_msg = "WhatsApp API request timeout"
            logger.error(error_msg)
            return {
                'success': False,
                'response': None,
                'error': error_msg,
                'status_code': None
            }

        except requests.exceptions.RequestException as e:
            error_msg = f"WhatsApp API request error: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'response': None,
                'error': error_msg,
                'status_code': None
            }

        except Exception as e:
            error_msg = f"Unexpected error in WhatsApp API request: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'response': None,
                'error': error_msg,
                'status_code': None
            }

    @staticmethod
    def send_text_message(wa_id: str, message: str, reply_context_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a text message via WhatsApp.

        Args:
            wa_id: WhatsApp ID of recipient
            message: Message text to send
            reply_context_id: Optional message ID to reply to

        Returns:
            Dict with send result:
            {
                'success': bool,
                'message_id': str or None,
                'error': str or None
            }
        """
        try:
            if not wa_id or not message:
                return {
                    'success': False,
                    'message_id': None,
                    'error': 'WhatsApp ID and message are required'
                }

            if not WhatsAppService.BEARER_TOKEN:
                return {
                    'success': False,
                    'message_id': None,
                    'error': 'WhatsApp API token not configured'
                }

            # Prepare endpoint
            endpoint = f'/sendSessionMessage/{wa_id}'

            # Prepare parameters
            params = {
                'messageText': message
            }

            if reply_context_id:
                params['replyContextId'] = reply_context_id

            # Make request
            result = WhatsAppService._make_request('POST', endpoint, params=params)

            if result['success']:
                logger.info(f"Sent text message to {wa_id}: '{message[:50]}...'")
                return {
                    'success': True,
                    'message_id': result.get('response', {}).get('id'),
                    'error': None
                }
            else:
                logger.error(f"Failed to send text message to {wa_id}: {result['error']}")
                return {
                    'success': False,
                    'message_id': None,
                    'error': result['error']
                }

        except Exception as e:
            error_msg = f"Error sending text message: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message_id': None,
                'error': error_msg
            }

    @staticmethod
    def send_document(wa_id: str, document_path: str, caption: str = "",
                     reply_context_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a document via WhatsApp.

        Args:
            wa_id: WhatsApp ID of recipient
            document_path: Path to document file
            caption: Optional caption for the document
            reply_context_id: Optional message ID to reply to

        Returns:
            Dict with send result:
            {
                'success': bool,
                'message_id': str or None,
                'error': str or None
            }
        """
        try:
            if not wa_id or not document_path:
                return {
                    'success': False,
                    'message_id': None,
                    'error': 'WhatsApp ID and document path are required'
                }

            if not WhatsAppService.BEARER_TOKEN:
                return {
                    'success': False,
                    'message_id': None,
                    'error': 'WhatsApp API token not configured'
                }

            # Check if file exists
            if not os.path.exists(document_path):
                return {
                    'success': False,
                    'message_id': None,
                    'error': f'Document file not found: {document_path}'
                }

            # Get file info
            file_name = os.path.basename(document_path)
            mime_type, _ = mimetypes.guess_type(document_path)

            if not mime_type:
                mime_type = 'application/octet-stream'

            # Prepare endpoint
            endpoint = f'/sendSessionFile/{wa_id}'

            # Prepare parameters
            params = {}
            if caption:
                params['caption'] = caption
            if reply_context_id:
                params['replyContextId'] = reply_context_id

            # Prepare file data
            with open(document_path, 'rb') as file:
                files = {
                    'file': (file_name, file, mime_type)
                }

                # Make request
                result = WhatsAppService._make_request('POST', endpoint, files=files, params=params)

            if result['success']:
                logger.info(f"Sent document to {wa_id}: {file_name}")
                return {
                    'success': True,
                    'message_id': result.get('response', {}).get('id'),
                    'error': None
                }
            else:
                logger.error(f"Failed to send document to {wa_id}: {result['error']}")
                return {
                    'success': False,
                    'message_id': None,
                    'error': result['error']
                }

        except Exception as e:
            error_msg = f"Error sending document: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message_id': None,
                'error': error_msg
            }

    @staticmethod
    def send_chat_level_response(wa_id: str, response_data: Dict[str, Any],
                               reply_context_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Send a chat level response (text or document).

        Args:
            wa_id: WhatsApp ID of recipient
            response_data: Response data from ChatLevelService.process_user_message()
            reply_context_id: Optional message ID to reply to

        Returns:
            Dict with send result
        """
        try:
            if not response_data.get('success', False):
                # Send error message
                error_message = response_data.get('error_message', 'An error occurred.')
                return WhatsAppService.send_text_message(wa_id, error_message, reply_context_id)

            # Check if response has document
            if response_data.get('has_document', False) and response_data.get('document_path'):
                # Send document with message as caption
                document_path = response_data['document_path']
                caption = response_data.get('response_message', '')

                # Convert relative URL to absolute file path if needed
                if document_path.startswith('/'):
                    # Already absolute path
                    pass
                elif document_path.startswith('http'):
                    # URL - not supported for file sending
                    logger.warning(f"Document path is URL, not file path: {document_path}")
                    # Fall back to text message
                    return WhatsAppService.send_text_message(wa_id, caption, reply_context_id)
                else:
                    # Relative path - convert to absolute
                    from django.conf import settings
                    document_path = os.path.join(settings.MEDIA_ROOT, document_path)

                return WhatsAppService.send_document(wa_id, document_path, caption, reply_context_id)

            else:
                # Send text message
                message = response_data.get('response_message', '')
                return WhatsAppService.send_text_message(wa_id, message, reply_context_id)

        except Exception as e:
            error_msg = f"Error sending chat level response: {str(e)}"
            logger.error(error_msg)
            return {
                'success': False,
                'message_id': None,
                'error': error_msg
            }

    @staticmethod
    def validate_credentials() -> Dict[str, Any]:
        """
        Validate WhatsApp API credentials.

        Returns:
            Dict with validation result
        """
        try:
            if not WhatsAppService.BEARER_TOKEN:
                return {
                    'valid': False,
                    'error': 'WhatsApp API token not configured'
                }

            # Try a simple API call to validate token
            result = WhatsAppService._make_request('GET', '/profile')

            if result['success']:
                return {
                    'valid': True,
                    'error': None
                }
            else:
                return {
                    'valid': False,
                    'error': f'API validation failed: {result["error"]}'
                }

        except Exception as e:
            return {
                'valid': False,
                'error': f'Validation error: {str(e)}'
            }
