# Generated by Django 5.1.7 on 2025-10-21 07:27

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Amani',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Amani_time', models.DateTimeField()),
                ('created', models.CharField(max_length=255)),
                ('whatsappMessageId', models.CharField(max_length=255)),
                ('conversationId', models.CharField(max_length=255)),
                ('ticketId', models.CharField(blank=True, max_length=255, null=True)),
                ('text', models.TextField(blank=True, null=True)),
                ('type', models.CharField(blank=True, max_length=255, null=True)),
                ('data', models.TextField(blank=True, null=True)),
                ('sourceId', models.Char<PERSON><PERSON>(blank=True, max_length=255, null=True)),
                ('sourceUrl', models.TextField(blank=True, null=True)),
                ('timestamp', models.Char<PERSON>ield(blank=True, max_length=255, null=True)),
                ('owner', models.CharField(blank=True, max_length=255, null=True)),
                ('eventType', models.CharField(blank=True, max_length=255, null=True)),
                ('statusString', models.CharField(blank=True, max_length=255, null=True)),
                ('avatarUrl', models.TextField(blank=True, null=True)),
                ('assignedId', models.CharField(blank=True, max_length=255, null=True)),
                ('operatorName', models.CharField(blank=True, max_length=255, null=True)),
                ('operatorEmail', models.CharField(blank=True, max_length=255, null=True)),
                ('waId', models.CharField(blank=True, max_length=255, null=True)),
                ('messageContact', models.CharField(blank=True, max_length=255, null=True)),
                ('senderName', models.CharField(blank=True, max_length=255, null=True)),
                ('listReply', models.CharField(blank=True, max_length=255, null=True)),
                ('interactiveButtonReply', models.CharField(blank=True, max_length=255, null=True)),
                ('buttonReply', models.CharField(blank=True, max_length=255, null=True)),
                ('replyContextId', models.CharField(blank=True, max_length=255, null=True)),
                ('sourceType', models.CharField(blank=True, max_length=255, null=True)),
                ('frequentlyForwarded', models.CharField(blank=True, max_length=255, null=True)),
                ('forwarded', models.CharField(blank=True, max_length=255, null=True)),
            ],
            options={
                'verbose_name': 'Amani Message',
                'verbose_name_plural': 'Amani Messages',
                'db_table': 'AMANI',
            },
        ),
        migrations.CreateModel(
            name='AmaniChatLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('waID', models.CharField(max_length=20)),
                ('step', models.CharField(max_length=255)),
                ('chat_level', models.IntegerField()),
                ('date', models.DateTimeField()),
            ],
            options={
                'verbose_name': 'Amani Chat Level',
                'verbose_name_plural': 'Amani Chat Levels',
                'db_table': 'Amani_chat_level',
            },
        ),
        migrations.CreateModel(
            name='AmaniLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level_code', models.IntegerField(unique=True)),
                ('level_options', models.CharField(max_length=255)),
                ('level_message', models.TextField()),
                ('has_document', models.FileField(blank=True, null=True, upload_to='amani_files/')),
                ('is_last', models.CharField(blank=True, max_length=25, null=True)),
            ],
            options={
                'verbose_name': 'Amani Level',
                'verbose_name_plural': 'Amani Levels',
                'db_table': 'Amani_levels',
            },
        ),
    ]
