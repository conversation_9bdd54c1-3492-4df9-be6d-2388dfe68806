{"nodes": [{"parameters": {"jsCode": "// Merge incoming payload with central configs so downstream nodes see *both*\nconst incoming = $input.first()?.json ?? {};\n\nconst apiConfig = {\n  crmUrl: 'https://prod.crm.optiven.co.ke',\n  apiKey: 'kjhghfdsdfgyuhikuyrewqewreydtyuojnbghfrt65435tjcesgh',\n  csrfToken: 'zXpspoKAG72p2eDzorcSCuwOsfb5VSjTwQYqPd6EptMQ0J1Kkdq8TN9rqxaeMdQt',\n  whatsappApiUrl: 'https://graph.facebook.com/v18.0',\n  leadSourceId: 18,\n  department: 'TELEMARKETING',\n  departmentMember: 'OL/HR/365',\n};\n\nconst aiConfig = {\n  model: 'gpt-4',\n  temperature: 0.7,\n  maxTokens: 1000,\n  systemMessage:\n    'You are <PERSON>ani, a professional and helpful customer service chatbot for Optiven Group Kenya. You assist customers with property inquiries, plot information, bookings, and general customer service. Always be helpful, professional, and informative.',\n};\n\nconst workflowConfig = {\n  timeout: 30000,\n  retryAttempts: 3,\n  retryDelay: 1000,\n};\n\n// Validation\nconst errors = [];\nconst warnings = [];\nif (!apiConfig.crmUrl || !apiConfig.apiKey || !apiConfig.csrfToken) errors.push('Critical API configuration missing');\nif (!aiConfig.model || !aiConfig.systemMessage) warnings.push('AI configuration incomplete');\n\nreturn {\n  json: {\n    ...incoming,                // <= keep WhatsApp payload here\n    apiConfig,\n    aiConfig,\n    workflowConfig,\n    validation: {\n      errors,\n      warnings,\n      isValid: errors.length === 0,\n    },\n    timestamp: new Date().toISOString(),\n    workflow: 'amani-whatsapp',\n    validation_status: errors.length === 0 ? 'PASSED' : 'FAILED',\n  },\n};\n"}, "id": "a0832c18-b546-43fe-8ba0-09c32fae35ee", "name": "Configuration Manager", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-896, -1108]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-check", "leftValue": "={{ $json.validation.isValid }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "61ef5de4-8261-4f83-8d13-dca0fa1bdb2b", "name": "Configuration Validation Gate", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [-672, -1108]}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"error\": \"Configuration validation failed\", \"details\": $json.validation.errors, \"warnings\": $json.validation.warnings } }}", "options": {}}, "id": "3e547c77-f7c1-4d8d-87a2-62a932e21b99", "name": "Configuration Error Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [-448, -1012]}, {"parameters": {"workflowId": {"__rl": true, "value": "={{ $env.OPTIMIZED_PRODUCTION_WORKFLOW_ID }}", "mode": "list", "cachedResultName": "Optimized Production Workflow"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"chatInput": "={{ $json.message_text || 'Hello' }}", "sessionId": "={{ $json.wa_id }}", "history": [], "userContext": {"wa_id": "={{ $json.wa_id }}", "profile_name": "={{ $json.profile_name }}", "is_internal_user": "={{ $json.user_type === 'internal_user' }}", "user_type": "={{ $json.user_type }}", "user_details": "={{ $json }}", "source": "whatsapp_amani"}}, "matchingColumns": [], "schema": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "options": {}}, "type": "n8n-nodes-base.executeWorkflow", "typeVersion": 1.2, "position": [2864, -1568], "id": "e40591a6-d624-4671-9719-66bf3795c793", "name": "Execute Workflow"}, {"parameters": {"jsCode": "// Robust extractor for WhatsApp payloads coming from either:\n// 1) n8n WhatsApp Trigger output (flattened), or\n// 2) Raw Meta webhook (entry[0].changes[0].value...)\n\n// Start with current item (this still includes apiConfig/aiConfig/workflowConfig from upstream)\nconst upstream = $input.first().json;\n\n// Helper: safely grab from raw Meta webhook shape\nconst rawEntry = upstream.entry?.[0];\nconst rawChanges = rawEntry?.changes?.[0];\nconst rawValue = rawChanges?.value;\n\n// Detect shapes\nconst isTriggerShape = Array.isArray(upstream?.messages) && Array.isArray(upstream?.contacts);\nconst isRawMetaShape = !!rawValue?.messages && !!rawValue?.contacts;\n\n// Extractors\nlet contacts, messages, metadata;\nif (isTriggerShape) {\n  contacts = upstream.contacts?.[0];\n  messages = upstream.messages?.[0];\n  metadata = upstream.metadata;\n} else if (isRawMetaShape) {\n  contacts = rawValue.contacts?.[0];\n  messages = rawValue.messages?.[0];\n  metadata = rawValue.metadata;\n} else {\n  throw new Error('Invalid WhatsApp payload structure: expected WhatsApp Trigger output or raw Meta webhook.');\n}\n\n// Validate minimum fields\nif (!contacts || !messages) {\n  throw new Error('Invalid WhatsApp payload structure: missing contacts/messages.');\n}\n\n// Core fields\nconst waId = contacts.wa_id || messages.from; // fallback if needed\nconst profileName = contacts.profile?.name || 'WhatsApp User';\nconst messageType = messages.type;\nconst messageText = messages.text?.body || ''; // only for text messages\nconst messageId = messages.id;\nconst tsSec = Number(messages.timestamp || upstream.timestamp); // seconds epoch in WhatsApp payloads\nconst timestampIso = Number.isFinite(tsSec) ? new Date(tsSec * 1000).toISOString() : new Date().toISOString();\n\n// Only support text for now\nif (messageType !== 'text') {\n  throw new Error(`Only text messages are supported (received type: ${messageType}).`);\n}\n\n// Merge with upstream so we keep apiConfig/aiConfig/workflowConfig for later nodes\nreturn {\n  json: {\n    ...upstream, // preserve configs + anything else\n    wa_id: waId,\n    profile_name: profileName,\n    message_text: messageText,\n    message_id: messageId,\n    timestamp: timestampIso,\n    // pass-thru convenience fields (your later nodes reference these)\n    amani_message_id: upstream.amani_message_id,\n    normalized_phone: upstream.normalized_phone,\n    received_at: upstream.received_at,\n    // useful meta for sending replies\n    wa_metadata_phone_number_id: metadata?.phone_number_id,\n    wa_display_phone_number: metadata?.display_phone_number,\n  }\n};\n"}, "id": "01bdbe7a-dde7-4d58-8e93-e102ae469eb4", "name": "Extract WhatsApp Data", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-448, -1204]}, {"parameters": {"url": "={{ $json.apiConfig.crmUrl }}/api/amani/phone-lookup/", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendQuery": true, "queryParameters": {"parameters": [{"name": "phone", "value": "={{ $('Extract WhatsApp Data').first().json.wa_id }}"}, {"name": "create_prospect", "value": "true"}, {"name": "prospect_name", "value": "={{ $('Extract WhatsApp Data').first().json.profile_name }}"}]}, "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-CSRFTOKEN", "value": "={{ $json.apiConfig.csrfToken }}"}, {"name": "X-API-KEY", "value": "={{ $json.apiConfig.apiKey }}"}]}, "options": {"timeout": "={{ $json.workflowConfig.timeout }}"}}, "id": "993fdddb-6b89-494d-96cf-d48bdfb42be9", "name": "Phone Number Lookup", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [0, -1276], "credentials": {"httpHeaderAuth": {"id": "QmKijsk7OouKurVp", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "internal-user-check", "leftValue": "={{ $json.user_type }}", "rightValue": "internal_user", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "dbd3c17f-ca34-475e-bb58-691c86930d8e", "name": "Internal User Router", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [224, -1276]}, {"parameters": {"jsCode": "// Handle user routing based on type\nconst phoneLookupData = $('Phone Number Lookup').first().json;\nconst whatsappData = $('Extract WhatsApp Data').first().json;\n\n// Check if this is a response to a name request (contains user name)\nconst userInput = whatsappData.message_text || '';\nconst isNameResponse = userInput.length > 2 && !userInput.toLowerCase().includes('help') && !userInput.toLowerCase().includes('search') && !userInput.toLowerCase().includes('find');\n\n// Handle new users\nif (phoneLookupData.user_type === 'new') {\n  if (isNameResponse) {\n    // User provided name, create prospect and proceed\n    const prospectName = userInput.trim();\n    return {\n      json: {\n        ...phoneLookupData,\n        user_type: 'prospect',\n        name: prospectName,\n        prospect_created: true,\n        prospect_name: prospectName,\n        message_text: whatsappData.message_text,\n        profile_name: prospectName,\n        wa_id: whatsappData.wa_id,\n        timestamp: whatsappData.timestamp\n      }\n    };\n  } else {\n    // Ask for name\n    return {\n      json: {\n        ai_response: 'Hi! 👋 I don\\'t have your information in our system yet. May I have your full name so I can assist you better? This will help me create a profile for you in our system.',\n        wa_id: whatsappData.wa_id,\n        profile_name: whatsappData.profile_name,\n        user_type: 'new',\n        requires_name: true,\n        response_type: 'new_user_greeting',\n        timestamp: whatsappData.timestamp\n      }\n    };\n  }\n} else {\n  // If it's customer or prospect, proceed to Lightning Intent Classifier\n  return {\n    json: {\n      ...phoneLookupData,\n      message_text: whatsappData.message_text,\n      profile_name: phoneLookupData.name || whatsappData.profile_name,\n      wa_id: whatsappData.wa_id,\n      timestamp: whatsappData.timestamp\n    }\n  };\n}"}, "id": "1ff86a4d-8ae2-4a0b-bfa7-40fcca5ed0b2", "name": "New User Handler", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [448, -1152]}, {"parameters": {"jsCode": "const whatsappData = $('Extract WhatsApp Data').first().json;\nconst phoneLookupData = $('Phone Number Lookup').first().json;\nconst userInput = whatsappData.message_text || '';\nconst processedInput = userInput.toLowerCase().trim();\nconst waId = whatsappData.wa_id;\nconst profileName = phoneLookupData.name || whatsappData.profile_name || 'WhatsApp User';\n\n// Instant response patterns\nconst patterns = {\n  greeting: {\n    test: /^(hi|hello|hey|good\\s*(morning|afternoon|evening)|greetings)\\b/i,\n    response: 'Hello ' + profileName + '! 👋 I\\'m <PERSON><PERSON>, your Optiven assistant. How can I help you today?',\n    priority: 'instant'\n  },\n  help: {\n    test: /\\b(help|what\\s*can|capabilities|features|assist)\\b/i,\n    response: '💡 **I can help you with:**\\n🏘️ Property and plot information\\n💰 Payment and booking status\\n📞 Customer service inquiries\\n🔍 Plot availability and details\\n📋 General Optiven information\\n\\nJust ask in plain language!',\n    priority: 'instant'\n  },\n  thanks: {\n    test: /\\b(thank|thanks|appreciate|grateful)\\b/i,\n    response: 'You\\'re very welcome! 🙌 Happy to help anytime. Is there anything else I can assist you with?',\n    priority: 'instant'\n  },\n  property_inquiry: {\n    test: /\\b(property|plot|land|buy|purchase|invest|available|price)\\b/i,\n    response: '🏘️ I\\'d be happy to help with your property inquiry! I can provide information about available plots, pricing, and help with bookings. Could you tell me more about what you\\'re looking for?',\n    priority: 'instant'\n  },\n  bye: {\n    test: /\\b(bye|goodbye|see\\s*you|farewell|later)\\b/i,\n    response: 'Goodbye! 👋 If you have any more questions about Optiven properties, feel free to message me anytime. Have a great day!',\n    priority: 'instant'\n  }\n};\n\n// Check for instant responses\nlet intent = 'conversational';\nlet instantResponse = null;\nlet priority = 'standard';\n\n// Customize greeting for internal users\nif (phoneLookupData.user_type === 'internal_user' && /^(hi|hello|hey|good\\s*(morning|afternoon|evening)|greetings)\\b/i.test(processedInput)) {\n  intent = 'greeting';\n  instantResponse = 'Hello ' + profileName + '! 👋 Welcome back to the Optiven CRM system. I can help you search customers, prospects, plots, view reports, and manage leads. What would you like to do today?';\n  priority = 'instant';\n} else {\n  for (const [name, pattern] of Object.entries(patterns)) {\n    if (pattern.test.test(processedInput)) {\n      intent = name;\n      instantResponse = pattern.response;\n      priority = pattern.priority;\n      break;\n    }\n  }\n}\n\n// Extract basic entities\nconst entities = {\n  plot_numbers: (processedInput.match(/\\b(plot|plt)[-_]?\\d+\\b/gi) || []),\n  amounts: (processedInput.match(/\\b\\d+(?:,\\d{3})*(?:\\.\\d{2})?\\s*(?:ksh|kes|shillings|dollars)?\\b/gi) || []),\n  phones: (processedInput.match(/\\b0\\d{9}\\b/g) || [])\n};\n\n// Check if needs AI (complex queries)\nconst needsAI = priority !== 'instant' || \n  entities.plot_numbers.length > 0 ||\n  processedInput.includes('search') ||\n  processedInput.includes('find') ||\n  processedInput.length > 50;\n\nreturn {\n  json: {\n    originalInput: userInput,\n    processedInput: processedInput,\n    intent: intent,\n    priority: priority,\n    requiresAI: needsAI,\n    instantResponse: instantResponse,\n    entities: entities,\n    wa_id: waId,\n    profile_name: profileName,\n    hasSearchableEntities: entities.plot_numbers.length > 0,\n    timestamp: whatsappData.timestamp,\n    user_type: phoneLookupData.user_type,\n    user_details: phoneLookupData,\n    customer_no: phoneLookupData.customer_no,\n    prospect_id: phoneLookupData.prospect_id\n  }\n};"}, "id": "dedda3c6-282c-4fd6-8735-364844ca5305", "name": "Lightning Intent Classifier", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [672, -1152]}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "priority-check", "leftValue": "={{ $json.priority }}", "rightValue": "instant", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "133263cb-9937-466b-8bd7-577ec40a1963", "name": "Routing Decision", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [896, -1152]}, {"parameters": {"jsCode": "// Format instant response for WhatsApp\nconst intentData = $('Lightning Intent Classifier').first().json;\nconst phoneLookupData = $('Phone Number Lookup').first().json;\n\nreturn {\n  json: {\n    ai_response: intentData.instantResponse,\n    wa_id: intentData.wa_id,\n    profile_name: intentData.profile_name,\n    intent: intentData.intent,\n    priority: intentData.priority,\n    is_instant_response: true,\n    response_type: 'instant',\n    timestamp: intentData.timestamp,\n    user_type: phoneLookupData.user_type,\n    user_details: phoneLookupData,\n    customer_no: phoneLookupData.customer_no,\n    prospect_id: phoneLookupData.prospect_id\n  }\n};"}, "id": "c54d1772-6994-4071-835f-2698cdb96748", "name": "Instant Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2864, -840]}, {"parameters": {"url": "={{ $json.apiConfig.crmUrl }}/api/amani/chat-levels/", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-CSRFTOKEN", "value": "={{ $json.apiConfig.csrfToken }}"}, {"name": "X-API-KEY", "value": "={{ $json.apiConfig.apiKey }}"}]}, "options": {"timeout": "={{ $json.workflowConfig.timeout }}"}}, "id": "ebc71232-ea53-4699-851f-5c415c84b6dd", "name": "Get Chat Level", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1120, -1276], "credentials": {"httpHeaderAuth": {"id": "QmKijsk7OouKurVp", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.length }}", "rightValue": 0, "operator": {"type": "number", "operation": "gt"}}], "combinator": "and"}, "options": {}}, "id": "f335b228-a327-4576-a42f-cc0d37b319c1", "name": "Check Existing Chat", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [1344, -1276]}, {"parameters": {"url": "={{ $('Configuration Manager').item.json.apiConfig.crmUrl }}/api/amani/chat-levels/", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-CSRFTOKEN", "value": "={{ $json.apiConfig.csrfToken }}"}, {"name": "X-API-KEY", "value": "={{ $json.apiConfig.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "waID", "value": "={{ $('Extract WhatsApp Data').first().json.wa_id }}"}, {"name": "chat_level", "value": 1}, {"name": "step", "value": 1}]}, "options": {"timeout": "={{ $json.workflowConfig.timeout }}"}}, "id": "c6b88eae-2084-45ea-9b7f-6dd1d4ff89c4", "name": "Create Chat Level", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1568, -1156], "credentials": {"httpHeaderAuth": {"id": "QmKijsk7OouKurVp", "name": "Header Auth account"}}}, {"parameters": {"jsCode": "// Get chat level data\nconst chatLevelData = $input.first().json;\nconst whatsappData = $('Extract WhatsApp Data').first().json;\n\n// Determine current chat level and step\nlet currentLevel = 1;\nlet currentStep = 1;\n\nif (chatLevelData && chatLevelData.length > 0) {\n  const latestChat = chatLevelData[0];\n  currentLevel = latestChat.chat_level || 1;\n  currentStep = latestChat.step || 1;\n}\n\n// Prepare context for AI agent\nconst context = {\n  wa_id: whatsappData.wa_id,\n  profile_name: whatsappData.profile_name,\n  message_text: whatsappData.message_text,\n  current_level: currentLevel,\n  current_step: currentStep,\n  conversation_history: chatLevelData || [],\n  timestamp: whatsappData.timestamp\n};\n\nreturn {\n  json: context\n};"}, "id": "c20bfb4c-d46f-4fdf-8959-35c449a63517", "name": "Prepare AI Context", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1792, -1276]}, {"parameters": {"promptType": "define", "text": "={{ $('Configuration Manager').item.json.aiConfig.systemMessage }}", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2.1, "position": [2016, -1276], "id": "c21fd0ba-0ae0-4590-9f40-50278a662213", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "gpt-4"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [2112, -1052], "id": "4e1e050b-8ed8-4192-b2f3-c3d01d96b315", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "XZ7DpjWZoH8I8X3J", "name": "CRM"}}}, {"parameters": {"jsCode": "// Process AI response and determine next actions\nconst aiResponse = $input.first().json;\nconst context = $('Prepare AI Context').first().json;\nconst intentData = $('Lightning Intent Classifier').first().json;\nconst phoneLookupData = $('Phone Number Lookup').first().json;\n\n// Extract AI message - handle both AI Agent output and intent classifier output\nconst aiMessage = aiResponse.message || aiResponse.content || intentData.instantResponse || 'Thank you for your message. How can I help you with your property needs?';\n\n// Determine if we need to collect contact information\nconst needsContactInfo = context.current_level >= 3 ||\n  aiMessage.toLowerCase().includes('contact') ||\n  aiMessage.toLowerCase().includes('phone') ||\n  aiMessage.toLowerCase().includes('email');\n\n// Determine if we should create a lead\nconst shouldCreateLead = context.current_level >= 3 && needsContactInfo;\n\n// Determine next chat level\nlet nextLevel = context.current_level;\nlet nextStep = context.current_step + 1;\n\nif (context.current_level === 1 && context.message_text.toLowerCase().includes('property')) {\n  nextLevel = 2;\n  nextStep = 1;\n} else if (context.current_level === 2 && needsContactInfo) {\n  nextLevel = 3;\n  nextStep = 1;\n} else if (context.current_level === 3 && shouldCreateLead) {\n  nextLevel = 4;\n  nextStep = 1;\n}\n\nreturn {\n  json: {\n    ai_response: aiMessage,\n    wa_id: context.wa_id,\n    profile_name: context.profile_name,\n    current_level: context.current_level,\n    current_step: context.current_step,\n    next_level: nextLevel,\n    next_step: nextStep,\n    needs_contact_info: needsContactInfo,\n    should_create_lead: shouldCreateLead,\n    original_message: context.message_text,\n    intent: intentData.intent,\n    response_type: 'ai',\n    user_type: phoneLookupData.user_type,\n    user_details: phoneLookupData,\n    customer_no: phoneLookupData.customer_no,\n    prospect_id: phoneLookupData.prospect_id\n  }\n};"}, "id": "de04fa0c-4bc5-48fa-a429-5fc53a727b83", "name": "Process AI Response", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [2416, -1276]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "condition-1", "leftValue": "={{ $json.should_create_lead }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equal"}}], "combinator": "and"}, "options": {}}, "id": "935fb883-2fa9-4a75-922f-edef3ceaf97f", "name": "Check Create Lead", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [2640, -1180]}, {"parameters": {"url": "={{ $json.apiConfig.crmUrl }}/api/leads/prospects/", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-CSRFTOKEN", "value": "={{ $json.apiConfig.csrfToken }}"}, {"name": "X-API-KEY", "value": "={{ $json.apiConfig.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "name", "value": "={{ $json.profile_name }}"}, {"name": "phone", "value": "={{ $('Extract WhatsApp Data').first().json.normalized_phone }}"}, {"name": "lead_source", "value": "={{ $json.apiConfig.leadSourceId }}"}, {"name": "lead_type", "value": "allocated"}, {"name": "department", "value": "={{ $json.apiConfig.department }}"}, {"name": "department_member", "value": "={{ $json.apiConfig.departmentMember }}"}, {"name": "comment", "value": "={{ 'WhatsApp AI Chat - ' + $json.original_message + ' | AI Response: ' + $json.ai_response }}"}, {"name": "status", "value": "Active"}, {"name": "category", "value": "Warm"}, {"name": "pipeline_level", "value": "New"}, {"name": "is_verified", "value": false}, {"name": "is_converted", "value": false}]}, "options": {"timeout": "={{ $json.workflowConfig.timeout }}"}}, "id": "a752a166-8087-4bbd-9164-a7c8572f738c", "name": "Create CRM Lead", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2864, -1032], "credentials": {"httpHeaderAuth": {"id": "QmKijsk7OouKurVp", "name": "Header Auth account"}}}, {"parameters": {"url": "={{ $('Configuration Manager').item.json.apiConfig.crmUrl }}/api/amani/chat-levels/", "authentication": "predefinedCredentialType", "nodeCredentialType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "accept", "value": "application/json"}, {"name": "Content-Type", "value": "application/json"}, {"name": "X-CSRFTOKEN", "value": "={{ $json.apiConfig.csrfToken }}"}, {"name": "X-API-KEY", "value": "={{ $json.apiConfig.apiKey }}"}]}, "sendBody": true, "bodyParameters": {"parameters": [{"name": "waID", "value": "={{ $json.wa_id }}"}, {"name": "chat_level", "value": "={{ $json.next_level }}"}, {"name": "step", "value": "={{ $json.next_step }}"}]}, "options": {"timeout": "={{ $json.workflowConfig.timeout }}"}}, "id": "956215ec-54dd-4b32-898e-e8d11bb0ebdb", "name": "Update Chat Level", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2864, -1348], "credentials": {"httpHeaderAuth": {"id": "QmKijsk7OouKurVp", "name": "Header Auth account"}}}, {"parameters": {"operation": "send", "phoneNumberId": "={{ $('WhatsApp Trigger').item.json.metadata.phone_number_id }}", "recipientPhoneNumber": "={{ $('WhatsApp Trigger').item.json.contacts[0].wa_id }}", "textBody": "={{ $json.ai_response || $json.instantResponse || 'Thank you for your message. How can I help you with your property needs?' }}", "additionalFields": {}}, "type": "n8n-nodes-base.whatsApp", "typeVersion": 1, "position": [3088, -1276], "id": "06a209c4-f793-4123-b796-b2fe871c47cd", "name": "Send WhatsApp Response", "webhookId": "amani-whatsapp-response", "credentials": {"whatsAppApi": {"id": "rARecLyUrJLHiYst", "name": "WhatsApp account"}}}, {"parameters": {"respondWith": "json", "responseBody": "={{ { \"success\": true, \"message\": \"WhatsApp message processed successfully\", \"response\": $json.ai_response || $json.instantResponse, \"response_type\": $json.response_type || \"ai\", \"lead_created\": $json.should_create_lead, \"chat_level\": $json.next_level, \"intent\": $json.intent, \"user_type\": $json.user_type, \"user_details\": $json.user_details, \"customer_no\": $json.customer_no, \"prospect_id\": $json.prospect_id } }}", "options": {}}, "id": "46f6f8a2-632c-4b1e-8f2e-55773a42dade", "name": "Final Response", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [3312, -1276]}, {"parameters": {"updates": ["messages"], "options": {}}, "type": "n8n-nodes-base.whatsAppTrigger", "typeVersion": 1, "position": [-1120, -1108], "id": "577fc6ca-6fb8-4008-a6f0-cc55e458f7a6", "name": "<PERSON><PERSON><PERSON><PERSON>", "webhookId": "amani-whatsapp-webhook", "credentials": {"whatsAppTriggerApi": {"id": "hFu3WniTVHEyBqqd", "name": "WhatsApp OAuth account"}}}], "connections": {"Configuration Manager": {"main": [[{"node": "Configuration Validation Gate", "type": "main", "index": 0}]]}, "Configuration Validation Gate": {"main": [[{"node": "Configuration Error Response", "type": "main", "index": 0}], [{"node": "Extract WhatsApp Data", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Send WhatsApp Response", "type": "main", "index": 0}]]}, "Extract WhatsApp Data": {"main": [[{"node": "Phone Number Lookup", "type": "main", "index": 0}]]}, "Phone Number Lookup": {"main": [[{"node": "Internal User Router", "type": "main", "index": 0}]]}, "Internal User Router": {"main": [[{"node": "Execute Workflow", "type": "main", "index": 0}], [{"node": "New User Handler", "type": "main", "index": 0}]]}, "New User Handler": {"main": [[{"node": "Lightning Intent Classifier", "type": "main", "index": 0}], [{"node": "Send WhatsApp Response", "type": "main", "index": 0}]]}, "Lightning Intent Classifier": {"main": [[{"node": "Routing Decision", "type": "main", "index": 0}]]}, "Routing Decision": {"main": [[{"node": "Instant Response", "type": "main", "index": 0}], [{"node": "Get Chat Level", "type": "main", "index": 0}]]}, "Instant Response": {"main": [[{"node": "Send WhatsApp Response", "type": "main", "index": 0}]]}, "Get Chat Level": {"main": [[{"node": "Check Existing Chat", "type": "main", "index": 0}]]}, "Check Existing Chat": {"main": [[{"node": "Prepare AI Context", "type": "main", "index": 0}], [{"node": "Create Chat Level", "type": "main", "index": 0}]]}, "Create Chat Level": {"main": [[{"node": "Prepare AI Context", "type": "main", "index": 0}]]}, "Prepare AI Context": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Process AI Response", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Process AI Response": {"main": [[{"node": "Check Create Lead", "type": "main", "index": 0}, {"node": "Update Chat Level", "type": "main", "index": 0}]]}, "Check Create Lead": {"main": [[{"node": "Create CRM Lead", "type": "main", "index": 0}]]}, "Create CRM Lead": {"main": [[{"node": "Send WhatsApp Response", "type": "main", "index": 0}]]}, "Update Chat Level": {"main": [[{"node": "Send WhatsApp Response", "type": "main", "index": 0}]]}, "Send WhatsApp Response": {"main": [[{"node": "Final Response", "type": "main", "index": 0}]]}, "WhatsApp Trigger": {"main": [[{"node": "Configuration Manager", "type": "main", "index": 0}]]}}, "pinData": {"WhatsApp Trigger": [{"messaging_product": "whatsapp", "metadata": {"display_phone_number": "15556416996", "phone_number_id": "853506934511515"}, "contacts": [{"profile": {"name": "N M"}, "wa_id": "254100368483"}], "messages": [{"from": "254100368483", "id": "wamid.HBgMMjU0MTAwMzY4NDgzFQIAEhggQUMzRjMyOEJCRjM3RUY0NTY5RjE3N0NCOEQ2MTYyNEIA", "timestamp": "1761512067", "text": {"body": "Hey"}, "type": "text"}], "field": "messages"}]}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "dee87b2d45ba70e3be0b5abfa9380a5ca0ad2d863153329dab6df6a3c7e3c488"}}