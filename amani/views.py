from rest_framework import viewsets, filters, status, views
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from django.db import connection
import logging

from .models import AmaniLevel, AmaniChatLevel, Amani
from .serializers import AmaniLevelSerializer, AmaniChatLevelSerializer, AmaniSerializer
from .services.phone_utils import PhoneUtils
from .services.user_validator import UserValidator
from .services.phone_lookup import PhoneLookupService
from .services.n8n_webhook import N8NWebhookService
from .services.chat_level_service import ChatLevelService
from .services.whatsapp_service import WhatsAppService
from .config import AmaniConfig

from rest_framework import status as http_status

import os
from rest_framework.parsers import MultiPartParser, FormParser

logger = logging.getLogger(__name__)


class AmaniLevelViewSet(viewsets.ModelViewSet):
    queryset = AmaniLevel.objects.all()
    serializer_class = AmaniLevelSerializer
    parser_classes = [MultiPartParser, FormParser]
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    search_fields = ['Level_code', 'level_options', 'level_message']
    filterset_fields = ['Level_code', 'is_last']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="List, filter, and search Amani Levels"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Retrieve an Amani Level"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Create an Amani Level"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Update an Amani Level"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Delete an Amani Level"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class AmaniChatLevelViewSet(viewsets.ModelViewSet):
    queryset = AmaniChatLevel.objects.all()
    serializer_class = AmaniChatLevelSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['waID', 'step']
    filterset_fields = ['waID', 'chat_level', 'date']
    http_method_names = ['get', 'post', 'patch', 'delete']

    def get_queryset(self):
        """
        Custom queryset filtering for n8n workflow compatibility.
        Supports filtering by waID via query parameters.
        """
        queryset = super().get_queryset()
        wa_id = self.request.query_params.get('waID')
        if wa_id:
            queryset = queryset.filter(waID=wa_id)
        return queryset

    def perform_create(self, serializer):
        """
        Custom perform_create for n8n workflow compatibility.
        Handles upsert behavior for chat levels.
        """
        wa_id = self.request.data.get('waID')
        chat_level = self.request.data.get('chat_level', 1)
        step = self.request.data.get('step', '1')

        # Check if chat level exists for this waID
        existing_chat_level = AmaniChatLevel.objects.filter(waID=wa_id).first()

        if existing_chat_level:
            # Update existing chat level
            existing_chat_level.chat_level = chat_level
            existing_chat_level.step = step
            existing_chat_level.save()
            # Don't call serializer.save() since we already saved
            return existing_chat_level
        else:
            # Create new chat level
            return serializer.save()

    def create(self, request, *args, **kwargs):
        """
        Custom create method for n8n workflow compatibility.
        Always updates existing chat level or creates new one for waID (upsert behavior).
        """
        wa_id = request.data.get('waID')

        if not wa_id:
            return Response(
                {"error": "waID is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Check if chat level exists for this waID
        existing_chat_level = AmaniChatLevel.objects.filter(waID=wa_id).first()

        if existing_chat_level:
            # Update existing chat level
            existing_chat_level.chat_level = request.data.get('chat_level', 1)
            existing_chat_level.step = request.data.get('step', '1')
            existing_chat_level.save()

            serializer = self.get_serializer(existing_chat_level)
            return Response(
                serializer.data,
                status=status.HTTP_200_OK
            )
        else:
            # Create new - use standard DRF create
            return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="List, filter, and search Amani Chat Levels"
    )
    def list(self, request, *args, **kwargs):
        """
        Custom list method for n8n workflow compatibility.
        Returns plain array instead of paginated response.
        """
        queryset = self.filter_queryset(self.get_queryset())
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Retrieve an Amani Chat Level"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Create an Amani Chat Level"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Update an Amani Chat Level"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Delete an Amani Chat Level"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class AmaniViewSet(viewsets.ModelViewSet):
    queryset = Amani.objects.all()
    serializer_class = AmaniSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['whatsappMessageId', 'conversationId', 'ticketId', 'text', 'waId', 'senderName', 'operatorName']
    filterset_fields = [
        'conversationId', 'ticketId', 'type', 'eventType', 'statusString', 
        'waId', 'messageContact', 'senderName', 'operatorName', 'operatorEmail'
    ]
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="List, filter, and search Amani Messages"
    )
    def list(self, request, *args, **kwargs):
        all_messages = self.queryset
        message_stats = {
            'total_messages': all_messages.count(),
            'unique_conversations': all_messages.values('conversationId').distinct().count(),
            'unique_wa_ids': all_messages.values('waId').distinct().count(),
            'messages_with_tickets': all_messages.exclude(ticketId__isnull=True).exclude(ticketId='').count(),
        }
        
        response = super().list(request, *args, **kwargs)
        if isinstance(response.data, dict):
            response.data['message_stats'] = message_stats
        return response

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Retrieve an Amani Message"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Create an Amani Message"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Update an Amani Message"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Delete an Amani Message"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class PhoneLookupView(views.APIView):
    """
    Phone number lookup endpoint for WhatsApp integration
    """
    permission_classes = [AllowAny]
    http_method_names = ['get', 'post']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_summary="Search phone number in database",
        operation_description="Searches phone number across users, customers, and prospects tables",
        manual_parameters=[
            openapi.Parameter(
                'phone',
                openapi.IN_QUERY,
                description="Phone number to search",
                type=openapi.TYPE_STRING,
                required=True
            ),
            openapi.Parameter(
                'create_prospect',
                openapi.IN_QUERY,
                description="Create prospect if not found (true/false)",
                type=openapi.TYPE_BOOLEAN,
                required=False
            ),
            openapi.Parameter(
                'prospect_name',
                openapi.IN_QUERY,
                description="Name for new prospect (if creating)",
                type=openapi.TYPE_STRING,
                required=False
            )
        ],
        responses={
            200: openapi.Response(
                description="Phone number lookup result",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'found': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'user_type': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            enum=['internal_user', 'customer', 'prospect', 'new', 'unknown']
                        ),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'user_details': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'prospect_created': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                    }
                )
            ),
            400: openapi.Response(description="Phone number is required"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def get(self, request):
        """Search phone number via GET request"""
        phone = request.query_params.get('phone')
        create_prospect = request.query_params.get('create_prospect', 'false').lower() == 'true'
        prospect_name = request.query_params.get('prospect_name')

        return self._process_lookup(phone, create_prospect, prospect_name)

    @swagger_auto_schema(
        tags=['Amani'],
        operation_summary="Search phone number in database (POST)",
        operation_description="Searches phone number across users, customers, and prospects tables",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'phone': openapi.Schema(type=openapi.TYPE_STRING, description="Phone number to search"),
                'create_prospect': openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Create prospect if not found"),
                'prospect_name': openapi.Schema(type=openapi.TYPE_STRING, description="Name for new prospect")
            },
            required=['phone']
        ),
        responses={
            200: openapi.Response(
                description="Phone number lookup result",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'found': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'user_type': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            enum=['internal_user', 'customer', 'prospect', 'new', 'unknown']
                        ),
                        'message': openapi.Schema(type=openapi.TYPE_STRING),
                        'user_details': openapi.Schema(type=openapi.TYPE_OBJECT),
                        'prospect_created': openapi.Schema(type=openapi.TYPE_BOOLEAN)
                    }
                )
            ),
            400: openapi.Response(description="Phone number is required"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def post(self, request):
        """Search phone number via POST request"""
        phone = request.data.get('phone')
        create_prospect = request.data.get('create_prospect', False)
        prospect_name = request.data.get('prospect_name')

        return self._process_lookup(phone, create_prospect, prospect_name)

    def _process_lookup(self, phone: str, create_prospect: bool = False, prospect_name: str = None):
        """Process phone number lookup"""
        try:
            if not phone:
                return Response(
                    {"error": "Phone number is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Search phone number
            lookup_result = PhoneLookupService.search_phone_number(phone)

            # If not found and create_prospect is True, create a new prospect
            if not lookup_result['found'] and lookup_result['user_type'] == 'new' and create_prospect:
                prospect_result = PhoneLookupService.create_prospect_from_phone(phone, prospect_name, prospect_name)

                if prospect_result['success']:
                    lookup_result.update({
                        'found': True,
                        'user_type': 'prospect',
                        'prospect_id': prospect_result['prospect_id'],
                        'name': prospect_result['name'],
                        'phone': prospect_result['phone'],
                        'prospect_created': True,
                        'message': prospect_result['message']
                    })
                else:
                    lookup_result.update({
                        'prospect_creation_failed': True,
                        'creation_error': prospect_result['error'],
                        'message': prospect_result['message']
                    })

            logger.info(f"Phone lookup for {phone}: {lookup_result}")
            return Response(lookup_result, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error in phone lookup: {str(e)}")
            return Response(
                {"error": "Internal server error", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TitleStatusView(views.APIView):
    """
    API endpoint for retrieving customer title status information
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_summary="Get customer title status",
        operation_description="Retrieve title deed status for a specific customer",
        manual_parameters=[
            openapi.Parameter(
                'customer_no',
                openapi.IN_QUERY,
                description="Customer number",
                type=openapi.TYPE_STRING,
                required=True
            )
        ],
        responses={
            200: openapi.Response(
                description="Title status information",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'total_plots': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'ready_titles': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'processing_titles': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'pending_titles': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'plots': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        ),
                        'summary': openapi.Schema(type=openapi.TYPE_OBJECT)
                    }
                )
            ),
            400: openapi.Response(description="Customer number required"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def get(self, request):
        """Get title status for a customer"""
        customer_no = request.query_params.get('customer_no')

        if not customer_no:
            return Response(
                {"error": "customer_no parameter is required"},
                status=http_status.HTTP_400_BAD_REQUEST
            )

        title_status = ChatLevelService.get_customer_title_status(customer_no)
        return Response(title_status, status=http_status.HTTP_200_OK)


class AmaniWebhookView(views.APIView):
    """
    Webhook endpoint for receiving WhatsApp Business API messages
    """
    permission_classes = [AllowAny]
    http_method_names = ['post']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_summary="Process WhatsApp Business API webhook",
        operation_description="Receives WhatsApp messages and dispatches to N8N with CRM mapping",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'entry': openapi.Schema(
                    type=openapi.TYPE_ARRAY,
                    items=openapi.Schema(type=openapi.TYPE_OBJECT)
                )
            }
        ),
        responses={
            200: openapi.Response(description="Message processed successfully"),
            400: openapi.Response(description="Invalid payload"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def post(self, request):
        try:
            payload = request.data

            # Validate WhatsApp Business API payload structure
            if not self._validate_whatsapp_payload(payload):
                return Response(
                    {"error": "Invalid WhatsApp Business API payload structure"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Extract and validate required fields
            wa_id = self._extract_wa_id(payload)
            if not wa_id:
                return Response(
                    {"error": "waId is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Store raw message
            amani_message = self._store_message(payload)

            # Check if sender is internal marketer
            is_marketer, marketer_user = UserValidator.is_internal_marketer(wa_id)

            # Normalize phone number
            normalized_phone = PhoneUtils.normalize_phone_number(wa_id)

            # Determine user type and customer info for chat levels
            user_type = 'new'
            customer_no = None
            prospect_id = None

            # Lookup phone number to determine user type
            phone_lookup_result = PhoneLookupService.search_phone_number(normalized_phone)
            if phone_lookup_result['found']:
                user_type = phone_lookup_result['user_type']
                customer_no = phone_lookup_result.get('customer_no')
                prospect_id = phone_lookup_result.get('prospect_id')

            # Process chat levels (only for non-marketers and text messages)
            chat_level_processed = False
            chat_level_result = None

            if not is_marketer and amani_message.type == 'text' and amani_message.text:
                try:
                    # Process message through chat levels system with user type awareness
                    chat_level_result = ChatLevelService.process_user_message(
                        wa_id=wa_id,
                        message=amani_message.text,
                        user_type=user_type,
                        customer_no=customer_no
                    )

                    if chat_level_result['success']:
                        # Send chat level response via WhatsApp
                        whatsapp_result = WhatsAppService.send_chat_level_response(
                            wa_id=wa_id,
                            response_data=chat_level_result,
                            reply_context_id=amani_message.whatsappMessageId
                        )

                        chat_level_processed = True
                        logger.info(f"Chat level processed for {wa_id}: {whatsapp_result}")
                    else:
                        logger.warning(f"Chat level processing failed for {wa_id}: {chat_level_result.get('error_message')}")

                except Exception as e:
                    logger.error(f"Error processing chat levels for {wa_id}: {e}")
                    # Continue with N8N processing even if chat levels fail

            # Enrich payload with additional data
            enriched_payload = payload.copy()
            enriched_payload.update({
                'amani_message_id': amani_message.id,
                'is_internal_marketer': is_marketer,
                'normalized_phone': normalized_phone,
                'received_at': timezone.now().isoformat(),
                'marketer_info': UserValidator.get_marketer_info(wa_id) if is_marketer else None,
                'chat_level_processed': chat_level_processed,
                'chat_level_result': chat_level_result
            })

            # Dispatch to N8N (with CRM mapping) - only if not processed by chat levels
            n8n_result = None
            if not chat_level_processed:
                n8n_service = N8NWebhookService()
                n8n_result = n8n_service.dispatch_to_n8n(enriched_payload)

            logger.info(f"Processed WhatsApp message from {wa_id}, Chat Level: {chat_level_processed}, N8N result: {n8n_result}")

            response_data = {
                "success": True,
                "message": "WhatsApp message processed successfully",
                "amani_message_id": amani_message.id,
                "chat_level_processed": chat_level_processed,
                "is_internal_marketer": is_marketer
            }

            if chat_level_processed:
                response_data.update({
                    "chat_level_result": chat_level_result,
                    "response_sent": True
                })
            else:
                response_data.update({
                    "n8n_dispatch": n8n_result,
                    "crm_mapped": n8n_result.get('crm_mapped', False) if n8n_result else False
                })

            return Response(response_data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error processing WhatsApp webhook: {str(e)}")
            return Response(
                {"error": "Internal server error", "details": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _validate_whatsapp_payload(self, payload: dict) -> bool:
        """Validate WhatsApp Business API payload structure"""
        try:
            return (
                'entry' in payload and
                len(payload['entry']) > 0 and
                'changes' in payload['entry'][0] and
                len(payload['entry'][0]['changes']) > 0 and
                'value' in payload['entry'][0]['changes'][0] and
                'contacts' in payload['entry'][0]['changes'][0]['value'] and
                'messages' in payload['entry'][0]['changes'][0]['value']
            )
        except (KeyError, IndexError, TypeError):
            return False

    def _extract_wa_id(self, payload: dict) -> str:
        """Extract WhatsApp ID from payload"""
        try:
            return payload['entry'][0]['changes'][0]['value']['contacts'][0]['wa_id']
        except (KeyError, IndexError, TypeError):
            return None

    def _store_message(self, payload: dict) -> Amani:
        """Store raw WhatsApp message in database"""
        try:
            # Extract message data
            contact = payload['entry'][0]['changes'][0]['value']['contacts'][0]
            message = payload['entry'][0]['changes'][0]['value']['messages'][0]

            # Create Amani instance
            amani_message = Amani.objects.create(
                waId=contact['wa_id'],
                senderName=contact['profile'].get('name', 'WhatsApp User'),
                text=message['text']['body'],
                type=message.get('type', 'text'),
                timestamp=message.get('timestamp', ''),
                whatsappMessageId=message.get('id', ''),
                conversationId=f"conv_{contact['wa_id']}",
                Amani_time=timezone.now(),
                created=str(timezone.now()),
                eventType='message',
                statusString='received',
                owner=False,
                forwarded=message.get('forwarded', False)
            )

            logger.info(f"Stored WhatsApp message: {amani_message.id}")
            return amani_message

        except Exception as e:
            logger.error(f"Error storing message: {e}")
            raise


class ChatLevelProcessorView(views.APIView):
    """
    Dedicated endpoint for n8n workflow to process chat levels.
    Handles menu navigation, personalized messages, and document attachments.
    """
    permission_classes = [AllowAny]
    http_method_names = ['post']

    @swagger_auto_schema(
        tags=['Amani'],
        operation_description="Process user message through chat levels system for n8n workflow",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['wa_id', 'message'],
            properties={
                'wa_id': openapi.Schema(type=openapi.TYPE_STRING, description='WhatsApp ID'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='User message'),
                'user_type': openapi.Schema(type=openapi.TYPE_STRING, description='User type: customer, prospect, new'),
                'customer_no': openapi.Schema(type=openapi.TYPE_STRING, description='Customer number if customer'),
                'profile_name': openapi.Schema(type=openapi.TYPE_STRING, description='User profile name for personalization'),
            }
        ),
        responses={
            200: openapi.Response(
                description='Chat level processed successfully',
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'success': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'response_message': openapi.Schema(type=openapi.TYPE_STRING),
                        'has_document': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                        'document_path': openapi.Schema(type=openapi.TYPE_STRING),
                        'level_code': openapi.Schema(type=openapi.TYPE_INTEGER),
                        'level_options': openapi.Schema(type=openapi.TYPE_STRING),
                        'personalized': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                    }
                )
            )
        }
    )
    def post(self, request):
        """
        Process user message through chat levels system.
        
        Returns menu response with personalized user name and navigation options.
        """
        try:
            wa_id = request.data.get('wa_id') or request.data.get('waID')
            message = request.data.get('message') or request.data.get('message_text') or ''
            user_type = request.data.get('user_type', 'new')
            customer_no = request.data.get('customer_no')
            profile_name = request.data.get('profile_name') or request.data.get('name') or 'Valued Customer'

            if not wa_id:
                return Response(
                    {"success": False, "error": "wa_id is required"},
                    status=http_status.HTTP_400_BAD_REQUEST
                )

            # Process message through chat levels system
            result = ChatLevelService.process_user_message(
                wa_id=wa_id,
                message=message,
                user_type=user_type,
                customer_no=customer_no
            )

            if not result['success']:
                return Response(
                    {
                        "success": False,
                        "error": result.get('error_message', 'Processing failed'),
                        "response_message": result.get('response_message', 'An error occurred.')
                    },
                    status=http_status.HTTP_200_OK  # Return 200 so n8n can handle the error message
                )

            # Personalize the message by replacing [User_Name] placeholder
            response_message = result['response_message']
            if '[User_Name]' in response_message:
                response_message = response_message.replace('[User_Name]', profile_name)

            # Prepare response
            level_data = result.get('level_data')
            response_data = {
                "success": True,
                "response_message": response_message,
                "has_document": result.get('has_document', False),
                "document_path": result.get('document_path'),
                "level_code": level_data.Level_code if level_data else None,
                "level_options": level_data.level_options if level_data else None,
                "personalized": True,
                "wa_id": wa_id,
                "user_type": user_type,
                "customer_no": customer_no
            }

            return Response(response_data, status=http_status.HTTP_200_OK)

        except Exception as e:
            logger.exception(f"Error processing chat level for n8n workflow: {e}")
            return Response(
                {
                    "success": False,
                    "error": str(e),
                    "response_message": "An error occurred. Please try again."
                },
                status=http_status.HTTP_500_INTERNAL_SERVER_ERROR
            )
