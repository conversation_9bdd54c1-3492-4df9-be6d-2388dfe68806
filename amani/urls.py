from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import AmaniLevelViewSet, AmaniChatLevelViewSet, AmaniViewSet, PhoneLookupView, AmaniWebhookView, TitleStatusView, ChatLevelProcessorView

router = DefaultRouter()
router.register(r'levels', AmaniLevelViewSet)
router.register(r'chat-levels', AmaniChatLevelViewSet)
router.register(r'messages', AmaniViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('webhook/', AmaniWebhookView.as_view(), name='amani-webhook'),
    path('phone-lookup/', PhoneLookupView.as_view(), name='phone-lookup'),
    path('title-status/', TitleStatusView.as_view(), name='title-status'),
    path('process-chat-level/', ChatLevelProcessorView.as_view(), name='process-chat-level'),
]