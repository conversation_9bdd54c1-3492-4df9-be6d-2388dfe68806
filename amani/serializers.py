from rest_framework import serializers
from .models import AmaniLevel, AmaniChatLevel, Amani


class AmaniLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = AmaniLevel
        fields = '__all__'


class AmaniChatLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = AmaniChatLevel
        fields = '__all__'
        read_only_fields = ['date']  # Make date read-only so it's auto-set


class AmaniSerializer(serializers.ModelSerializer):
    class Meta:
        model = Amani
        fields = '__all__'