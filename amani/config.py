from django.conf import settings
import os

class AmaniConfig:
    # CRM Integration (matching original PHP implementation)
    CRM_API_URL = os.environ.get('AMANI_CRM_URL', 'https://prod.crm.optiven.co.ke')
    CRM_API_KEY = os.environ.get('AMANI_CRM_API_KEY', 'kjhghfdsdfgyuhikuyrewqewreydtyuojnbghfrt65435tjcesgh')
    CRM_CSRF_TOKEN = os.environ.get('AMANI_CRM_CSRF_TOKEN', 'zXpspoKAG72p2eDzorcSCuwOsfb5VSjTwQYqPd6EptMQ0J1Kkdq8TN9rqxaeMdQt')
    LEAD_SOURCE_ID = int(os.environ.get('AMANI_LEAD_SOURCE_ID', 18))  # Default to 18 for AMANI CHATBOT
    DEPARTMENT_TELEMARKETING = int(os.environ.get('AMANI_DEPARTMENT_TELEMARKETING', 21))  # TELEMARKETING department ID
    DEPARTMENT_MEMBER = os.environ.get('AMANI_DEPARTMENT_MEMBER', 'OL/HR/365')

    # N8N Webhook
    N8N_WEBHOOK_URL = os.environ.get('AMANI_N8N_WEBHOOK_URL', 'http://localhost:5678/webhook/amani-whatsapp')
    N8N_WEBHOOK_TIMEOUT = int(os.environ.get('AMANI_N8N_WEBHOOK_TIMEOUT', 10))  # seconds

    # Phone Number Utilities
    DEFAULT_COUNTRY_CODE = os.environ.get('AMANI_DEFAULT_COUNTRY_CODE', '+254')

    # WhatsApp Business API Configuration
    WHATSAPP_BUSINESS_API_URL = os.environ.get('AMANI_WHATSAPP_API_URL', 'https://graph.facebook.com/v18.0')
    WHATSAPP_PHONE_NUMBER_ID = os.environ.get('AMANI_WHATSAPP_PHONE_NUMBER_ID', 'your_phone_number_id')
    WHATSAPP_ACCESS_TOKEN = os.environ.get('AMANI_WHATSAPP_ACCESS_TOKEN', 'your_access_token')
    WHATSAPP_VERIFY_TOKEN = os.environ.get('AMANI_WHATSAPP_VERIFY_TOKEN', 'your_webhook_verify_token')

    # CRM Mapping Configuration
    DEFAULT_LEAD_TYPE = os.environ.get('AMANI_DEFAULT_LEAD_TYPE', 'allocated')
    DEFAULT_STATUS = os.environ.get('AMANI_DEFAULT_STATUS', 'Active')
    DEFAULT_CATEGORY = os.environ.get('AMANI_DEFAULT_CATEGORY', 'Warm')
    DEFAULT_PIPELINE_LEVEL = os.environ.get('AMANI_DEFAULT_PIPELINE_LEVEL', 'New')

    # Logging
    LOG_LEVEL = os.environ.get('AMANI_LOG_LEVEL', 'INFO')

    @classmethod
    def validate_config(cls):
        errors = []
        # Check for PHP-compatible values
        if not cls.CRM_API_KEY or cls.CRM_API_KEY == 'your_crm_api_key_here':
            errors.append("AMANI_CRM_API_KEY is not set or is default.")
        if not cls.CRM_CSRF_TOKEN:
            errors.append("AMANI_CRM_CSRF_TOKEN is not set.")
        if not cls.N8N_WEBHOOK_URL or cls.N8N_WEBHOOK_URL == 'http://localhost:5678/webhook/amani-whatsapp':
            errors.append("AMANI_N8N_WEBHOOK_URL is not set or is default.")
        if not cls.WHATSAPP_ACCESS_TOKEN or cls.WHATSAPP_ACCESS_TOKEN == 'your_access_token':
            errors.append("AMANI_WHATSAPP_ACCESS_TOKEN is not set or is default.")
        return errors

    @classmethod
    def get_crm_endpoint(cls):
        """Get the CRM API endpoint for prospect creation"""
        return f"{cls.CRM_API_URL}/api/leads/prospects/"

    @classmethod
    def get_whatsapp_api_endpoint(cls):
        """Get WhatsApp Business API endpoint"""
        return f"{cls.WHATSAPP_BUSINESS_API_URL}/{cls.WHATSAPP_PHONE_NUMBER_ID}/messages"
