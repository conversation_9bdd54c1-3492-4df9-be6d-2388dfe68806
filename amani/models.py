from django.db import models

class AmaniLevel(models.Model):
    Level_code = models.IntegerField(unique=True)
    level_options = models.CharField(max_length=255)
    level_message = models.TextField()
    has_document = models.FileField(upload_to='amani_files/', blank=True, null=True)
    is_last = models.CharField(max_length=25, blank=True, null=True)

    class Meta:
        db_table = 'Amani_levels'
        verbose_name = 'Amani Level'
        verbose_name_plural = 'Amani Levels'

    def __str__(self):
        return f"Level {self.Level_code} - {self.level_options}"


class AmaniChatLevel(models.Model):
    waID = models.CharField(max_length=20)
    step = models.CharField(max_length=255)
    chat_level = models.IntegerField()
    date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'Amani_chat_level'
        verbose_name = 'Amani Chat Level'
        verbose_name_plural = 'Amani Chat Levels'

    def __str__(self):
        return f"{self.waID} - Step: {self.step}"


class Amani(models.Model):
    Amani_time = models.DateTimeField()
    created = models.CharField(max_length=255)
    whatsappMessageId = models.CharField(max_length=255)
    conversationId = models.CharField(max_length=255)
    ticketId = models.CharField(max_length=255, blank=True, null=True)
    text = models.TextField(blank=True, null=True)
    type = models.CharField(max_length=255, blank=True, null=True)
    data = models.TextField(blank=True, null=True)
    sourceId = models.CharField(max_length=255, blank=True, null=True)
    sourceUrl = models.TextField(blank=True, null=True)
    timestamp = models.CharField(max_length=255, blank=True, null=True)
    owner = models.CharField(max_length=255, blank=True, null=True)
    eventType = models.CharField(max_length=255, blank=True, null=True)
    statusString = models.CharField(max_length=255, blank=True, null=True)
    avatarUrl = models.TextField(blank=True, null=True)
    assignedId = models.CharField(max_length=255, blank=True, null=True)
    operatorName = models.CharField(max_length=255, blank=True, null=True)
    operatorEmail = models.CharField(max_length=255, blank=True, null=True)
    waId = models.CharField(max_length=255, blank=True, null=True)
    messageContact = models.CharField(max_length=255, blank=True, null=True)
    senderName = models.CharField(max_length=255, blank=True, null=True)
    listReply = models.CharField(max_length=255, blank=True, null=True)
    interactiveButtonReply = models.CharField(max_length=255, blank=True, null=True)
    buttonReply = models.CharField(max_length=255, blank=True, null=True)
    replyContextId = models.CharField(max_length=255, blank=True, null=True)
    sourceType = models.CharField(max_length=255, blank=True, null=True)
    frequentlyForwarded = models.CharField(max_length=255, blank=True, null=True)
    forwarded = models.CharField(max_length=255, blank=True, null=True)

    class Meta:
        db_table = 'AMANI'
        verbose_name = 'Amani Message'
        verbose_name_plural = 'Amani Messages'

    def __str__(self):
        return f"Amani Message {self.id} from {self.waId}"
