from django.urls import path, include
from dashboards.views.data_dashboard import DataDashboardCountStatsView
from dashboards.views.directors import AdministrativeStatistics, PerformanceStatistics
from rest_framework.routers import DefaultRouter
from dashboards.views.accounts_index import AccountsIndexView
from dashboards.views.credit_team_index import CreditsTeamIndexView,CreditsTeamDetailsView,PortfolioLinefilterViewSet,UnallocatedLeadfilesViewSet
from dashboards.views.diaspora_index import DiasporaIndexView,DiasporaRegionView,DiasporaRegionLeadSourceView
from dashboards.views.gm_dashboard import GMBookingsReport, GMMIBFromReceiptsReports, GMSiteVisitsReport
from dashboards.views.hos_hq_index import HqHosIndexView,GlobalHosIndexView,MarketerIndexView
# Import the comprehensive teleteam views from teleteam_index.py
from dashboards.views.teleteam_index import (
    TeleteamDashboardView,
    AllCustomersView,
    TelemarketingCustomersView,
    AllProspectsView,
    UnallocatedLeadsView,
    AllSalesView,
    TeleteamSalesView
)

# Import HR dashboard views
from dashboards.views.hr_index import (
    OfficeCardsView,
    OfficePeriodsView,
    PeriodMarketersView
)

# Import digital team dashboard views
from dashboards.views.digital_index import (
    DigitalDashboardView,
    AllCustomersView as DigitalAllCustomersView,
    DigitalCustomersView,
    AllProspectsView as DigitalAllProspectsView,
    UnallocatedLeadsView as DigitalUnallocatedLeadsView,
    AllSalesView as DigitalAllSalesView,
    DigitalTeamSalesView
)
from dashboards.views.main_dashboard import (
    MainDashView,
)

router = DefaultRouter()
router.register(r'accounts-index', AccountsIndexView, basename='accounts-index')
router.register(r'accounts-credits-team-index', CreditsTeamIndexView, basename='credits-team-index')
router.register(r'accounts-credits-team-details', CreditsTeamDetailsView, basename='credits-team-details')
router.register(r'portfolio-line-filter', PortfolioLinefilterViewSet, basename='portfolio-line-filter')
router.register(r'unallocated-leadfiles', UnallocatedLeadfilesViewSet, basename='unallocated-leadfiles')


# Comprehensive teleteam views
router.register(r'teleteam-dashboard', TeleteamDashboardView, basename='teleteam-dashboard')
router.register(r'all-customers', AllCustomersView, basename='all-customers')
router.register(r'telemarketing-customers', TelemarketingCustomersView, basename='telemarketing-customers')
router.register(r'all-prospects', AllProspectsView, basename='all-prospects')
router.register(r'unallocated-leads', UnallocatedLeadsView, basename='unallocated-leads')
router.register(r'all-sales', AllSalesView, basename='all-sales')
router.register(r'teleteam-sales', TeleteamSalesView, basename='teleteam-sales')

# HR dashboard views
router.register(r'hr-office-cards', OfficeCardsView, basename='hr-office-cards')
router.register(r'hr-office-periods', OfficePeriodsView, basename='hr-office-periods')
router.register(r'hr-period-marketers', PeriodMarketersView, basename='hr-period-marketers')

# Digital team dashboard views
router.register(r'digital-dashboard', DigitalDashboardView, basename='digital-dashboard')
router.register(r'digital-all-customers', DigitalAllCustomersView, basename='digital-all-customers')
router.register(r'digital-customers', DigitalCustomersView, basename='digital-customers')
router.register(r'digital-all-prospects', DigitalAllProspectsView, basename='digital-all-prospects')
router.register(r'digital-unallocated-leads', DigitalUnallocatedLeadsView, basename='digital-unallocated-leads')
router.register(r'digital-all-sales', DigitalAllSalesView, basename='digital-all-sales')
router.register(r'digital-team-sales', DigitalTeamSalesView, basename='digital-team-sales')
# DIASPORA DASH VIEWS 

router.register(r'diaspora-index', DiasporaIndexView, basename='diaspora-index')
router.register(r'diaspora-region', DiasporaRegionView, basename='diaspora-region')
router.register(r'diaspora-region-lead-source', DiasporaRegionLeadSourceView, basename='diaspora-region-lead-source')

# HQ HOS DASH
router.register(r'hq-hos-index', HqHosIndexView, basename='hq-hos-index')
router.register(r'Global-hos-index', GlobalHosIndexView, basename='Global-hos-index')
router.register(r'Marketer-index', MarketerIndexView, basename='Marketer-index')
 # Main Dashboard View
router.register(r'main-dashboard', MainDashView, basename='main-dashboard')



urlpatterns = [
    path('', include(router.urls)),
    path('data-dashboard-stats/', DataDashboardCountStatsView.as_view(), name='data-dashboard-counts-stats'),
    path('directors-admin-stats/', AdministrativeStatistics.as_view(), name='directors-admin-stats'),
    path('directors-perform-stats/', PerformanceStatistics.as_view(), name='directors-perfom-stats'),
    path('gm-site-visits-report/', GMSiteVisitsReport.as_view(), name='gm-site-visits-report'),
    path('gm-bookings-report/', GMBookingsReport.as_view(), name='gm-bookings-report'),
    path('gm-mib/', GMMIBFromReceiptsReports.as_view(), name='gm-mib-receipts'),
] 