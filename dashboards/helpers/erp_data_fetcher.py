from django.db import connections
import MySQLdb.cursors


import logging
logger = logging.getLogger(__name__)

def erp_db_connector_query(__database, query, fetch_count):
    # Get database connection
    conn = connections[__database]
    conn.ensure_connection()

    # Use context manager for proper cursor handling
    with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cursor:
        cursor.execute(query)

        results = None

        if fetch_count == 'all':
            results = cursor.fetchall()
        elif fetch_count == 'one':
            results = cursor.fetchone()

        return results 


def fetch_employee_data_from_erp():
    """
    Fetches employee data from ERP database
    Returns: List of dictionaries containing employee data with null handling
    """
    
    try:
        # SQL query to fetch employee data with null handling
        query = """
            SELECT 
                COALESCE(employee_no_id, '') as employee_no,
                COALESCE(first_name, '') as first_name,
                COALESCE(last_name, '') as last_name,
                COALESCE(middle_name, '') as middle_name,
                COALESCE(marital_status, '') as marital_status,
                COALESCE(gender, '') as gender
            FROM `accounts_employeebiodetails`
        """

        employees = erp_db_connector_query('erp', query, 'all')        
        return employees

    except Exception as e:
        logger.error(f"Error fetching employee data: {str(e)}")
        return []
    

def fetch_leaves_data_from_erp():
    '''
    Fetch leaves data from erp
    '''

    try:
        # SQL query to fetch leaves data with null handling
        query = """
            SELECT 
                COALESCE(leave_type_name_id, '') as leave_type,
                COALESCE(leave_status, '') as leave_status,
                COALESCE(no_of_days_applied, '') as no_of_days,
            FROM `hrm_leaveapplication`
        """

        leaves = erp_db_connector_query('erp', query, 'all')        
        return leaves

    except Exception as e:
        logger.error(f"Error fetching leave data: {str(e)}")
        return []



def teams_performance_from_erp():
    '''
    Fetch teams performance data from erp
    '''

    try:
        # SQL query to fetch teams performance
        query = """
                SELECT tt.`team`,tt.`period_start_date`,tt.`monthly_target`,tt.`MIB_achieved`,tt.`MIB_Perfomance` 
                FROM teams_targets tt JOIN ( SELECT team, MAX(period_start_date) AS max_start_date FROM 
                teams_targets GROUP BY team ) latest ON tt.team = latest.team AND tt.period_start_date = latest.max_start_date 
                JOIN users_teams ut ON tt.team = ut.team WHERE ut.inactive = 0 ORDER BY tt.period_start_date DESC;"""

        teams_performance = erp_db_connector_query('reports', query, 'all')        
        return teams_performance

    except Exception as e:
        logger.error(f"Error fetching teams performance data: {str(e)}")
        return []



def teams_performance_from_erp():
    '''
    Fetch collections data from erp
    '''

    try:
        # SQL queries
        # current marketing month
        getmonth_sql = """
            SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
        getmonth_sql_row = erp_db_connector_query('reports', getmonth_sql, 'one')
        period_start_date = getmonth_sql_row['period_start_date'] if getmonth_sql_row else None
        period_end_date = getmonth_sql_row['period_end_date'] if getmonth_sql_row else None
        
        marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None


        overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE;
            """
        
        installments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE; """
        installments_today_sql_row = erp_db_connector_query('reports', installments_today_sql, 'one')
        installments_due_today = installments_today_sql_row['no'] or 0 if installments_today_sql_row else 0

        overdue_sql = """
            SELECT 
                SUM(pl.overdue_collections_collected) AS collected_total, 
                SUM(pl.overdue_collections) AS overdue_total
            FROM portfolio_lines pl
            JOIN (
                SELECT period_start_date, period_end_date 
                FROM portfolio_lines 
                ORDER BY period_start_date DESC 
                LIMIT 1
            ) latest 
            ON pl.period_start_date = latest.period_start_date 
                AND pl.period_end_date = latest.period_end_date 
            WHERE pl.overdue_collections <> 0;
        """
        overdue_sql_row = erp_db_connector_query('reports', overdue_sql, 'one')
        overdue_collections_collected = overdue_sql_row['collected_total'] or 0 if overdue_sql_row else 0
        overdue_collections = overdue_sql_row['overdue_total'] or 0 if overdue_sql_row else 0

        # --- Sales below threshold
        sales_below_threshold_sql = """
            SELECT COUNT(lead_file_no) AS al 
            FROM sales_leadfile 
            WHERE marketer_id IS NOT NULL 
                AND lead_file_status_dropped=0 
                AND total_paid < deposit_threshold;
        """
        sales_below_threshold_sql_row = erp_db_connector_query('reports', sales_below_threshold_sql, 'one')
        sales_deposits_below_threshold = sales_below_threshold_sql_row['al'] or 0 if sales_below_threshold_sql_row else 0

        # --- Overdue below threshold
        overdue_below_threshold_sql = """
            SELECT COUNT(lead_file_no) AS al 
            FROM sales_leadfile 
            WHERE marketer_id IS NOT NULL 
                AND lead_file_status_dropped=0 
                AND total_paid < deposit_threshold 
                AND Additional_deposit_date < CURRENT_DATE;
        """
        overdue_below_threshold_sql_row = erp_db_connector_query('reports', overdue_below_threshold_sql, 'one')
        overdue_below_threshold = overdue_below_threshold_sql_row['al'] or 0 if overdue_below_threshold_sql_row else 0
        
        
        overdue_collections_this_month_sql = f""" SELECT SUM(`overdue_collections_collected`) AS no,SUM(`overdue_collections`) AS sum FROM `portfolio_lines` WHERE `overdue_collections`!=0 AND 
                                                    (`period_start_date`= '{period_start_date}' AND `period_end_date`= '{period_end_date}' );"""
        overdue_collections_this_month_sql_row = erp_db_connector_query('reports', overdue_collections_this_month_sql, 'one')
        expected_monthly_installments_collected = overdue_collections_this_month_sql_row['no'] or 0 if overdue_collections_this_month_sql_row else 0
        expected_monthly_installments = overdue_collections_this_month_sql_row['sum'] or 0 if overdue_collections_this_month_sql_row else 0
    

        _collections = {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                    "Expected_Monthly_Installments": expected_monthly_installments,
                    "EXPECTED_Monthly_installments_collected":  expected_monthly_installments_collected,
                },  
        return _collections

    except Exception as e:
        logger.error(f"Error fetching collections data: {str(e)}")
        return []

