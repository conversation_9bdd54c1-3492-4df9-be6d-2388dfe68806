from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
# from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime
import MySQLdb.cursors
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



@swagger_tags(['Dashboard-accounts'])
class CreditsTeamIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema( operation_description="Returns dashboard data for Credits Team")
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        def fetch_one(query, error_message, dict_cursor=False):
            try:
                cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
                with conn.connection.cursor(cursor_class) as cur:
                    cur.execute(query)
                    return cur.fetchone()
            except Exception as e:
                raise Exception(f"{error_message}: {str(e)}")

        try:
            # --- 1. installments due today
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE; """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today", dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            
            # --- 2. Overdue Collections
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0;
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections", dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold;
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold", dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE;
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            overdue_collections_this_month_sql = f""" SELECT SUM(`overdue_collections_collected`) AS no,SUM(`overdue_collections`) AS sum FROM `portfolio_lines` WHERE `overdue_collections`!=0 AND 
                                                      (`period_start_date`= '{period_start_date}' AND `period_end_date`= '{period_end_date}' );"""
            row = fetch_one(overdue_collections_this_month_sql, "Error fetching overdue collections for this month", dict_cursor=True)
            expected_monthly_installments_collected = row['no'] or 0 if row else 0
            expected_monthly_installments = row['sum'] or 0 if row else 0
            
            
            
            
            # all teams and performances
            credit_officers_sql = """
                SELECT DISTINCT `credit_officer_id`,uu.`fullnames` FROM `sales_leadfile` slf JOIN users_user uu ON slf.`credit_officer_id` = uu.`erp_user_id` WHERE credit_officer_id !='';"""
            # try:
            #     with connections["reports"].cursor() as cur:
            #         cur.execute(all_teams_sql)
            #         all_teams_data = cur.fetchall()
            # except Exception as e:
            #     return Response({"error fetching all teams data": str(e)}, status=500)
            
            try:
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(credit_officers_sql)
                    credit_officers_data = cur.fetchall()
            except Exception as e:
                return Response({"error fetching all teams data": str(e)}, status=500)
            
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                    "Expected_Monthly_Installments": expected_monthly_installments,
                    "EXPECTED_Monthly_installments_collected":  expected_monthly_installments_collected,
                },
                "Current_Month": {
                    "Period_Start_Date": period_start_date,
                    "Period_End_Date": period_end_date
                },
                "Credits_Teams_Performance": credit_officers_data
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)

@swagger_tags(['Dashboard-accounts'])
class CreditsTeamDetailsView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Returns dashboard data for one CREDITS TEAM",
        manual_parameters=[
            openapi.Parameter(
                name='Credit_officer_erp_id',in_=openapi.IN_QUERY,description='ERP ID of the credit officer EG "OPTIVEN-SERVER DANIEL"',
                type=openapi.TYPE_STRING,required=True,
            ),
        ],
    )
    def list(self, request):
        
        credit_officer_erp_id = request.query_params.get('Credit_officer_erp_id', None)
        if not credit_officer_erp_id:
            return Response({"error": "Credit_officer_erp_id is required"}, status=400)
        
        conn = connections["reports"]
        conn.ensure_connection()

        def fetch_one(query, error_message, dict_cursor=False):
            try:
                cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
                with conn.connection.cursor(cursor_class) as cur:
                    cur.execute(query)
                    return cur.fetchone()
            except Exception as e:
                raise Exception(f"{error_message}: {str(e)}")

        try:
            # --- 1. installments due today
            inatallments_today_sql = f"""SELECT COUNT(`no`)AS no, SUM(`installments_due`) AS tins FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no` 
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}'); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today", dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            installments_due_today_total = row['tins'] or 0 if row else 0
            
            getmonth_sql = """SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            overdue_collections_sql = f"""SELECT SUM(`overdue_collections_collected`) AS no,SUM(`overdue_collections`) AS sum FROM `portfolio_lines` WHERE `overdue_collections`!=0 AND 
                                          (`period_start_date`= '{period_start_date}' AND `period_end_date`= '{period_end_date}' ) AND `lead_file_no` 
                                            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}')"""
            row = fetch_one(overdue_collections_sql, "Error fetching overdue collections", dict_cursor=True)
            overdue_collections_collected = row['no'] or 0 if row else 0
            overdue_collections = row['sum'] or 0 if row else 0
            
            
            sales_below_threshold_sql = f"""SELECT COUNT(`lead_file_no`) AS al FROM `sales_leadfile` WHERE `marketer_id` IS NOT NULL 
            AND `lead_file_status_dropped`=0 
            AND `total_paid`<`deposit_threshold` AND `credit_officer_id`='{credit_officer_erp_id}'; """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold", dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0
            
            overdue_below_threshold_sql = f""" SELECT COUNT(`lead_file_no`) AS al FROM `sales_leadfile` WHERE `marketer_id` IS NOT NULL 
            AND `lead_file_status_dropped`= 0 AND `total_paid`<`deposit_threshold` and `Additional_deposit_date`< CURRENT_DATE  
            AND `credit_officer_id`='{credit_officer_erp_id}';"""
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            expected_monthly_installments_collected_sql = f""" SELECT COUNT(`no`) as no, SUM(installments_due) AS sum , SUM(installments_due_collected) AS sum2  FROM `portfolio_lines` 
            WHERE `installments_due`!= 0 AND (`period_start_date`= '{period_start_date}' AND `period_end_date`= '{period_end_date}' ) AND `lead_file_no` 
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}');"""
            row = fetch_one(expected_monthly_installments_collected_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            monthly_installments_due = row['sum'] or 0 if row else 0
            monthly_installments_due_collected = row['sum2'] or 0 if row else 0
            total_expexted_installments = monthly_installments_due - monthly_installments_due_collected
            
            customers_count_sql = f""" SELECT  COUNT(DISTINCT customer_id_id) AS distinct_customers, SUM(`balance_lcy`) AS total_paid_sum,
                                    COUNT(`lead_file_no`) AS total_sales_sum FROM `sales_leadfile`
                                     WHERE `credit_officer_id`='{credit_officer_erp_id}' AND `lead_file_status_dropped`= 0 AND `balance_lcy` > 0 ;"""
            row = fetch_one(customers_count_sql, "Error fetching customers count", dict_cursor=True)
            customer_count = row['distinct_customers'] or 0 if row else 0
            sales_count = row['total_sales_sum'] or 0 if row else 0
            portfolio_total_paid = row['total_paid_sum'] or 0 if row else 0
            
            
            instsllmentsduetoday_collected_sql = f""" SELECT SUM(`Amount_LCY`) as amount  FROM `receipts_postedreceipt`
                                                        WHERE `Transaction_type` LIKE 'Installment' AND  `POSTED_DATE1`= CURRENT_DATE AND `Type`='Posted' AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}') AND 
                                                        `Lead_file_no` IN(SELECT `lead_file_no` FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE)"""
            row = fetch_one(instsllmentsduetoday_collected_sql, "Error fetching installments due today collected", dict_cursor=True)
            installments_due_today_collected = row['amount'] or 0 if row else 0
            
            additionaldeposits_installments_collected_sql = f"""SELECT SUM(`Amount_LCY`) as amount FROM `receipts_postedreceipt`
                                                        WHERE (`Transaction_type` LIKE '%Installment%' OR `Transaction_type` LIKE '%Additional Deposit%' OR `Transaction_type` LIKE '%Deposit%')AND
                                                        `Type`='Posted' AND  `POSTED_DATE1`= CURRENT_DATE  AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}') """
            row = fetch_one(additionaldeposits_installments_collected_sql, "Error fetching additional deposits installments collected", dict_cursor=True)
            additionaldeposits_installments_collected = row['amount'] or 0 if row else 0
            
            finalpaymentscollected_sql = f"""SELECT SUM(`Amount_LCY`) as amount ,count(`Amount_LCY`) as no FROM `receipts_postedreceipt`
                                                        WHERE `Transaction_type` LIKE 'Final Payment' AND  `POSTED_DATE1`= CURRENT_DATE  AND `Type`='Posted' AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}')"""
            row = fetch_one(finalpaymentscollected_sql, "final payments data failed", dict_cursor=True)
            finalpaymentscollected_mib = row['amount'] or 0 if row else 0
            finalpaymentscollected_no_of_payments = row['no'] or 0 if row else 0
            
            
            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "installments_due_today": installments_due_today,
                "installments_due_today_total": installments_due_today_total,
                "overdue_collections_collected": overdue_collections_collected,
                "overdue_collections": overdue_collections,
                "all_overdue_collections": overdue_collections_collected + overdue_collections,
                "Current_Month": {
                    "Period_Start_Date": period_start_date,
                    "Period_End_Date": period_end_date
                },
                "sales_below_threshold_count": sales_deposits_below_threshold,
                "overdue_below_threshold_count": overdue_below_threshold,
                "monthly_installments_due": monthly_installments_due,
                "monthly_installments_due_collected": monthly_installments_due_collected,
                "total_expexted_installments": total_expexted_installments,
                
                "Portfolio":{
                    "all_customers": customer_count,
                    "all_sales": sales_count,
                    "portfolio_total_paid": portfolio_total_paid,    
                },
                "installments_collected_today": installments_due_today_collected,
                "additionaldeposits_installments_collected": additionaldeposits_installments_collected,
                "finalpaymentscollected_mib": finalpaymentscollected_mib,
                "finalpaymentscollected_no_of_payments": finalpaymentscollected_no_of_payments
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)
            
            
            


