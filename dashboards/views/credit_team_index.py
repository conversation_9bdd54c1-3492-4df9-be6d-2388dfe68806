from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
# from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime
import MySQLdb.cursors
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



@swagger_tags(['Dashboard-accounts'])
class CreditsTeamIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema( operation_description="Returns dashboard data for Credits Team")
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        def fetch_one(query, error_message, dict_cursor=False):
            try:
                cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
                with conn.connection.cursor(cursor_class) as cur:
                    cur.execute(query)
                    return cur.fetchone()
            except Exception as e:
                raise Exception(f"{error_message}: {str(e)}")

        try:

            # --- MIB DATA
            # --- 1. Daily MIB

            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL 
                AND sf.credit_officer_id IS NOT NULL 
            """
            
            # Daily MIB
            time_clause = "WHERE POSTED_DATE1 = CURRENT_DATE " + where_clause
            transaction_clause = time_clause + " AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"
            

            mib_sql=f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """

            print(mib_sql)
            row = fetch_one(mib_sql, "Error fetching daily MIB", dict_cursor=True)
            mib_today = (row['posted_total'] - row['canceled_total']) or 0 if row else 0



            time_clause = f"WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"
            transaction_clause = f"{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"
            monthly_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_mib_sql, "Error fetching monthly MIB", dict_cursor=True)
            mib_this_month = (row['posted_total'] - row['canceled_total']) or 0 if row else 0



            # Monthly Installments
            transaction_clause = f"{time_clause} AND Transaction_type = 'Installment'"
            
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_installments_sql, "Error fetching monthly installments", dict_cursor=True)
            monthly_installments_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Transfer Costs
            transaction_clause = f"{time_clause} AND Transaction_type = 'Transfer Cost'"
            
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_transferCosts_sql, "Error fetching monthly transfer costs", dict_cursor=True)
            monthly_transferCosts_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Deposits
            transaction_clause = f"{time_clause} AND Transaction_type = 'Deposit'"
            
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_deposits_sql, "Error fetching monthly deposits", dict_cursor=True)
            monthly_deposits_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Additional Deposits
            transaction_clause = f"{time_clause} AND Transaction_type = 'Additional Deposit'"
            
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt r JOIN sales_leadfile sf ON r.Lead_file_no = sf.lead_file_no
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(additional_deposits_sql, "Error fetching monthly additional deposits", dict_cursor=True)
            additional_deposits_data = (row['posted_total'] - row['canceled_total']) if row else 0


            # --- 1. installments due today
            installments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE; """
            row = fetch_one(installments_today_sql, "Error fetching installments due today", dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            
            # --- 2. Overdue Collections
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0;
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections", dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold;
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold", dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE;
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            overdue_collections_this_month_sql = f""" SELECT SUM(`overdue_collections_collected`) AS no,SUM(`overdue_collections`) AS sum FROM `portfolio_lines` WHERE `overdue_collections`!=0 AND 
                                                      (`period_start_date`= '{period_start_date}' AND `period_end_date`= '{period_end_date}' );"""
            row = fetch_one(overdue_collections_this_month_sql, "Error fetching overdue collections for this month", dict_cursor=True)
            expected_monthly_installments_collected = row['no'] or 0 if row else 0
            expected_monthly_installments = row['sum'] or 0 if row else 0
            
            
            
            
            # all teams and performances
            credit_officers_sql = """
                SELECT DISTINCT `credit_officer_id`,uu.`fullnames` FROM `sales_leadfile` slf JOIN users_user uu ON slf.`credit_officer_id` = uu.`erp_user_id` WHERE credit_officer_id !='';"""
            # try:
            #     with connections["reports"].cursor() as cur:
            #         cur.execute(all_teams_sql)
            #         all_teams_data = cur.fetchall()
            # except Exception as e:
            #     return Response({"error fetching all teams data": str(e)}, status=500)
            
            try:
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(credit_officers_sql)
                    credit_officers_data = cur.fetchall()
            except Exception as e:
                return Response({"error fetching all teams data": str(e)}, status=500)
            
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({

                "MIB": {
                    "Daily_MIB": mib_today,
                    "Monthly_MIB": mib_this_month,
                    "Monthly_installments": monthly_installments_data,
                    "Monthly_deposits": monthly_deposits_data,
                    "Additional_deposits": additional_deposits_data,
                    "Transfer_costs": monthly_transferCosts_data,
                    "Other_payments": mib_this_month - (monthly_installments_data + monthly_deposits_data + additional_deposits_data + monthly_transferCosts_data)
                },
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                    "Expected_Monthly_Installments": expected_monthly_installments,
                    "EXPECTED_Monthly_installments_collected":  expected_monthly_installments_collected,
                },
                "Current_Month": {
                    "Period_Start_Date": period_start_date,
                    "Period_End_Date": period_end_date
                },
                "Credits_Teams_Performance": credit_officers_data
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)

@swagger_tags(['Dashboard-accounts'])
class CreditsTeamDetailsView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Returns dashboard data for one CREDITS TEAM",
        manual_parameters=[
            openapi.Parameter(
                name='Credit_officer_erp_id',in_=openapi.IN_QUERY,description='ERP ID of the credit officer EG "OPTIVEN-SERVER DANIEL"',
                type=openapi.TYPE_STRING,required=True,
            ),
        ],
    )
    def list(self, request):
        
        credit_officer_erp_id = request.query_params.get('Credit_officer_erp_id', None)
        if not credit_officer_erp_id:
            return Response({"error": "Credit_officer_erp_id is required"}, status=400)
        
        conn = connections["reports"]
        conn.ensure_connection()

        def fetch_one(query, error_message, dict_cursor=False):
            try:
                cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
                with conn.connection.cursor(cursor_class) as cur:
                    cur.execute(query)
                    return cur.fetchone()
            except Exception as e:
                raise Exception(f"{error_message}: {str(e)}")

        try:
            # --- 1. installments due today
            inatallments_today_sql = f"""SELECT COUNT(`no`)AS no, SUM(`installments_due`) AS tins FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no` 
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}'); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today", dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            installments_due_today_total = row['tins'] or 0 if row else 0
            
            getmonth_sql = """SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            overdue_collections_sql = f"""SELECT SUM(`overdue_collections_collected`) AS no,SUM(`overdue_collections`) AS sum FROM `portfolio_lines` WHERE `overdue_collections`!=0 AND 
                                          `period_start_date`= '{period_start_date}' AND `lead_file_no` 
                                            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}')"""
            # print(overdue_collections_sql)
            row = fetch_one(overdue_collections_sql, "Error fetching overdue collections", dict_cursor=True)
            overdue_collections_collected = row['no'] or 0 if row else 0
            overdue_collections = row['sum'] or 0 if row else 0
            
            
            sales_below_threshold_sql = f"""SELECT COUNT(`lead_file_no`) AS al FROM `sales_leadfile` WHERE `marketer_id` IS NOT NULL 
            AND `lead_file_status_dropped`=0 
            AND `total_paid`<`deposit_threshold` AND `credit_officer_id`='{credit_officer_erp_id}'; """
            # print(sales_below_threshold_sql)
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold", dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0
            
            overdue_below_threshold_sql = f""" SELECT COUNT(`lead_file_no`) AS al FROM `sales_leadfile` WHERE `marketer_id` IS NOT NULL 
            AND `lead_file_status_dropped`= 0 AND `total_paid`<`deposit_threshold` and `Additional_deposit_date`< CURRENT_DATE  
            AND `credit_officer_id`='{credit_officer_erp_id}';"""
            # print(overdue_below_threshold_sql)
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            expected_monthly_installments_collected_sql = f""" SELECT COUNT(`no`) as no, SUM(installments_due) AS sum , SUM(installments_due_collected) AS sum2  FROM `portfolio_lines` 
            WHERE `installments_due`!= 0 AND `period_start_date`= '{period_start_date}' AND `lead_file_no` 
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}');"""
            # print(expected_monthly_installments_collected_sql)
            row = fetch_one(expected_monthly_installments_collected_sql, "Error fetching Overdue below Threshold", dict_cursor=True)
            monthly_installments_due = row['sum'] or 0 if row else 0
            monthly_installments_due_collected = row['sum2'] or 0 if row else 0
            total_expexted_installments = monthly_installments_due - monthly_installments_due_collected
            
            customers_count_sql = f""" SELECT  COUNT(DISTINCT customer_id_id) AS distinct_customers, SUM(`balance_lcy`) AS total_paid_sum,
                                    COUNT(`lead_file_no`) AS total_sales_sum FROM `sales_leadfile`
                                     WHERE `credit_officer_id`='{credit_officer_erp_id}' AND `lead_file_status_dropped`= 0 AND `balance_lcy` > 0 ;"""
            # print(customers_count_sql)  
            row = fetch_one(customers_count_sql, "Error fetching customers count", dict_cursor=True)
            customer_count = row['distinct_customers'] or 0 if row else 0
            sales_count = row['total_sales_sum'] or 0 if row else 0
            portfolio_total_paid = row['total_paid_sum'] or 0 if row else 0
            
            
            instsllmentsduetoday_collected_sql = f""" SELECT SUM(`Amount_LCY`) as amount  FROM `receipts_postedreceipt`
                                                        WHERE `Transaction_type` LIKE 'Installment' AND  `POSTED_DATE1`= CURRENT_DATE AND `Type`='Posted' AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}') AND 
                                                        `Lead_file_no` IN(SELECT `lead_file_no` FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE)"""
            row = fetch_one(instsllmentsduetoday_collected_sql, "Error fetching installments due today collected", dict_cursor=True)
            installments_due_today_collected = row['amount'] or 0 if row else 0
            
            additionaldeposits_installments_collected_sql = f"""SELECT SUM(`Amount_LCY`) as amount FROM `receipts_postedreceipt`
                                                        WHERE (`Transaction_type` LIKE '%Installment%' OR `Transaction_type` LIKE '%Additional Deposit%' OR `Transaction_type` LIKE '%Deposit%')AND
                                                        `Type`='Posted' AND  `POSTED_DATE1`= CURRENT_DATE  AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}') """
            row = fetch_one(additionaldeposits_installments_collected_sql, "Error fetching additional deposits installments collected", dict_cursor=True)
            additionaldeposits_installments_collected = row['amount'] or 0 if row else 0
            
            finalpaymentscollected_sql = f"""SELECT SUM(`Amount_LCY`) as amount ,count(`Amount_LCY`) as no FROM `receipts_postedreceipt`
                                                        WHERE `Transaction_type` LIKE 'Final Payment' AND  `POSTED_DATE1`= CURRENT_DATE  AND `Type`='Posted' AND `Lead_file_no` IN 
                                                        (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `credit_officer_id`='{credit_officer_erp_id}')"""
            row = fetch_one(finalpaymentscollected_sql, "final payments data failed", dict_cursor=True)
            finalpaymentscollected_mib = row['amount'] or 0 if row else 0
            finalpaymentscollected_no_of_payments = row['no'] or 0 if row else 0
            
            
            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "installments_due_today": installments_due_today,
                "installments_due_today_total": installments_due_today_total,
                "overdue_collections_collected": overdue_collections_collected,
                "overdue_collections": overdue_collections,
                "all_overdue_collections": overdue_collections_collected + overdue_collections,
                "Current_Month": {
                    "Period_Start_Date": period_start_date,
                    "Period_End_Date": period_end_date
                },
                "sales_below_threshold_count": sales_deposits_below_threshold,
                "overdue_below_threshold_count": overdue_below_threshold,
                "monthly_installments_due": monthly_installments_due,
                "monthly_installments_due_collected": monthly_installments_due_collected,
                "total_expexted_installments": total_expexted_installments,
                
                "Portfolio":{
                    "all_customers": customer_count,
                    "all_sales": sales_count,
                    "portfolio_total_paid": portfolio_total_paid,    
                },
                "installments_collected_today": installments_due_today_collected,
                "additionaldeposits_installments_collected": additionaldeposits_installments_collected,
                "finalpaymentscollected_mib": finalpaymentscollected_mib,
                "finalpaymentscollected_no_of_payments": finalpaymentscollected_no_of_payments
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)
        



@swagger_tags(['Dashboard-accounts'])
class PortfolioLinefilterViewSet(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="""
        Credit Team Portfolio Data API Endpoint
        This endpoint returns filtered portfolio data for Credits Team performance analysis.
        Allows filtering by credit officer, date period, and various numeric and date field conditions.
        
        Required Parameters:
            Credit_officer_erp_id (str): ERP ID of the credit officer
                Example: "OPTIVEN-SERVER DANIEL"
            period_start_date (str): Period start date in YYYY-MM-DD format
                Example: "2024-08-01"
            period_end_date (str): Period end date in YYYY-MM-DD format
                Example: "2024-08-31"
        Optional Numeric Field Filters:
            The following fields support numeric and date filtering with comparison operators:
            
            - installments_due
            - installments_due_collected
            - overdue_collections
            - overdue_collections_collected
            - previous_unpaid
            - total_to_collect
            - total_previously_collected
            - penalties_accrued
            - current_balance
            - total_collected
            -

        Date Field Filters:
            
            - due_date  
            - completion_date
            - booking_date

            
        Available Operators (append to field name):

            _gt: Greater than
                Example: installments_due_gt=1000
            _gte: Greater than or equal to
                Example: installments_due_gte=1000
            _lt: Less than
                Example: current_balance_lt=5000
            _lte: Less than or equal to
                Example: current_balance_lte=5000
            _eq: Equal to
                Example: overdue_collections_eq=0


        Usage Examples:
            Basic query:
            GET /api/endpoint/?Credit_officer_erp_id=OPTIVEN-SERVER%20DANIEL&period_start_date=2024-08-01&period_end_date=2024-08-31
            With date filter:
            GET /api/endpoint/?Credit_officer_erp_id=OPTIVEN-SERVER%20DANIEL&period_start_date=2024-08-01&period_end_date=2024-08-31&due_date_gte=2024-08-15
            With multiple filters:
            GET /api/endpoint/?Credit_officer_erp_id=OPTIVEN-SERVER%20DANIEL&period_start_date=2024-08-01&period_end_date=2024-08-31&due_date_gte=2024-08-15&current_balance_gt=1000
            Multiple date filters:
            GET /api/endpoint/?Credit_officer_erp_id=OPTIVEN-SERVER%20DANIEL&period_start_date=2024-08-01&period_end_date=2024-08-31&due_date_gte=2024-08-15&completion_date_lte=2024-12-31
        Response Format:
            {
                "credit_officer_erp_id": "OPTIVEN-SERVER DANIEL",
                "period_start_date": "2024-08-01", 
                "period_end_date": "2024-08-31",
                "total_records": 150,
                "portfolio_lines": [
                    {
                        "lead_file_no": "LF001",
                        "due_date": "2024-08-15",
                        "completion_date": "2024-12-31",
                        "booking_date": "2024-07-01",
                        "installments_due": 2500.00,
                        "current_balance": 45000.00,
                        "customer_id_id": "CUST001",
                        ...
        Error Responses:
            400: Missing required parameters or invalid date/numeric values
            500: Database connection or query execution errors
        Returns filtered portfolio data for Credits Team performance""",
        manual_parameters=[
            openapi.Parameter(
                name='Credit_officer_erp_id',in_=openapi.IN_QUERY,description='ERP ID of the credit officer EG "OPTIVEN-SERVER DANIEL"',
                type=openapi.TYPE_STRING,required=True,
            ),
            openapi.Parameter(
                name='period_start_date',in_=openapi.IN_QUERY,description='Period Start Date EG "2024-08-01"',
                type=openapi.TYPE_STRING,required=True,
            ),
            openapi.Parameter(
                name='period_end_date',in_=openapi.IN_QUERY,description='Period End Date EG "2024-08-31"',
                type=openapi.TYPE_STRING,required=True,
            ),
            openapi.Parameter(
                name='due_date_gte',in_=openapi.IN_QUERY,description='Due date greater than or equal to (YYYY-MM-DD) EG "2024-08-15"',
                type=openapi.TYPE_STRING,required=False,
            ),
            openapi.Parameter(
                name='current_balance_gt',in_=openapi.IN_QUERY,description='Current balance greater than',
                type=openapi.TYPE_NUMBER,required=False,
            ),
        ],
    )
    def list(self, request):
        credit_officer_erp_id = request.query_params.get('Credit_officer_erp_id', None)
        period_start_date = request.query_params.get('period_start_date', None)
        period_end_date = request.query_params.get('period_end_date', None)
        
        if not all([credit_officer_erp_id, period_start_date, period_end_date]):
            return Response({"error": "Credit_officer_erp_id, period_start_date, and period_end_date are required"}, status=400)
        
        conn = connections["reports"]
        conn.ensure_connection()
        
        # Build WHERE conditions for decimal and date field filters
        filters = []
        params = {}
        
        # Define decimal fields and their filter mappings
        decimal_fields = [
            'installments_due', 'installments_due_collected', 'overdue_collections',
            'overdue_collections_collected', 'previous_unpaid', 'total_to_collect',
            'total_previously_collected', 'penalties_accrued', 'current_balance', 'total_collected'
        ]
        
        # Define date fields that support filtering
        date_fields = ['due_date', 'completion_date', 'booking_date']
        
        operators = {
            'gt': '>',
            'gte': '>=',
            'lt': '<',
            'lte': '<=',
            'eq': '='
        }
        
        # Handle decimal field filters
        for field in decimal_fields:
            for op_suffix, op_symbol in operators.items():
                param_name = f"{field}_{op_suffix}"
                param_value = request.query_params.get(param_name)
                
                if param_value is not None:
                    try:
                        param_value = float(param_value)
                        filters.append(f"`{field}` {op_symbol} %({param_name})s")
                        params[param_name] = param_value
                    except ValueError:
                        return Response({"error": f"Invalid numeric value for {param_name}"}, status=400)
        
        # Handle date field filters
        for field in date_fields:
            for op_suffix, op_symbol in operators.items():
                param_name = f"{field}_{op_suffix}"
                param_value = request.query_params.get(param_name)
                
                if param_value is not None:
                    try:
                        # Validate date format (YYYY-MM-DD)
                        from datetime import datetime
                        datetime.strptime(param_value, '%Y-%m-%d')
                        filters.append(f"`{field}` {op_symbol} %({param_name})s")
                        params[param_name] = param_value
                    except ValueError:
                        return Response({"error": f"Invalid date format for {param_name}. Use YYYY-MM-DD format."}, status=400)
        
        # Build the SQL query
        base_sql = """
            SELECT pl.*, slf.customer_id_id 
            FROM portfolio_lines pl
            JOIN sales_leadfile slf ON pl.lead_file_no = slf.lead_file_no
            WHERE slf.credit_officer_id = %(credit_officer_erp_id)s
            AND pl.period_start_date = %(period_start_date)s
            AND pl.period_end_date = %(period_end_date)s
        """
        
        params.update({
            'credit_officer_erp_id': credit_officer_erp_id,
            'period_start_date': period_start_date,
            'period_end_date': period_end_date
        })
        
        if filters:
            base_sql += " AND " + " AND ".join(filters)
        
        try:
            with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                cur.execute(base_sql, params)
                portfolio_data = cur.fetchall()
            
            # Close connections
            for c in connections.all():
                c.close()
            
            return Response({
                "credit_officer_erp_id": credit_officer_erp_id,
                "period_start_date": period_start_date,
                "period_end_date": period_end_date,
                "total_records": len(portfolio_data),
                "portfolio_lines": portfolio_data
            })
            
        except Exception as e:
            return Response({"error": str(e)}, status=500)




@swagger_tags(['Dashboard-accounts'])
class UnallocatedLeadfilesViewSet(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        operation_description="Returns unallocated lead files data with pagination",
        manual_parameters=[
            openapi.Parameter(
                name='page',in_=openapi.IN_QUERY,description='Page number (default: 1)',
                type=openapi.TYPE_INTEGER,required=False,
            ),
            openapi.Parameter(
                name='page_size',in_=openapi.IN_QUERY,description='Number of records per page (default: 50, max: 1000)',
                type=openapi.TYPE_INTEGER,required=False,
            ),
        ],
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        def fetch_one(query, error_message, dict_cursor=False):
            try:
                cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
                with conn.connection.cursor(cursor_class) as cur:
                    cur.execute(query)
                    return cur.fetchone()
            except Exception as e:
                raise Exception(f"{error_message}: {str(e)}")

        try:
            # Get pagination parameters
            page = int(request.query_params.get('page', 1))
            page_size = int(request.query_params.get('page_size', 50))
            
            # Validate pagination parameters
            if page < 1:
                page = 1
            if page_size < 1:
                page_size = 50
            if page_size > 1000:
                page_size = 1000
                
            # Calculate offset
            offset = (page - 1) * page_size

            # Get total count first
            count_sql = """SELECT COUNT(*) as total FROM `sales_leadfile` WHERE `lead_file_status_dropped`=0 AND `balance_lcy`> 0 AND (`credit_officer_id` IS NULL or `credit_officer_id`='');"""
            
            try:
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(count_sql)
                    count_result = cur.fetchone()
                    total_count = count_result['total'] if count_result else 0
            except Exception as e:
                return Response({"error": f"Error fetching count: {str(e)}"}, status=500)

            # Get paginated unallocated lead files
            sql = """SELECT * FROM `sales_leadfile` WHERE `lead_file_status_dropped`=0 AND `balance_lcy`> 0 AND (`credit_officer_id` IS NULL or `credit_officer_id`='') 
                     ORDER BY `lead_file_no` LIMIT %s OFFSET %s;"""
            
            try:
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(sql, (page_size, offset))
                    leadfiles = cur.fetchall()
            except Exception as e:
                return Response({"error": f"Error fetching unallocated leadfiles: {str(e)}"}, status=500)

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size
            has_next = page < total_pages
            has_previous = page > 1

            # --- Close all connections
            for c in connections.all():
                c.close()
            
            return Response({
                "pagination": {
                    "current_page": page,
                    "page_size": page_size,
                    "total_records": total_count,
                    "total_pages": total_pages,
                    "has_next": has_next,
                    "has_previous": has_previous,
                    "next_page": page + 1 if has_next else None,
                    "previous_page": page - 1 if has_previous else None
                },
                "records_on_page": len(leadfiles),
                "Unallocated_Leadfiles": leadfiles
            })
        except ValueError as e:
            return Response({"error": "Invalid pagination parameters. Page and page_size must be integers."}, status=400)
        except Exception as e:
            return Response({"error": str(e)}, status=500)  
    

