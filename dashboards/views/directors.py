from dashboards.helpers.erp_data_fetcher import fetch_employee_data_from_erp, fetch_leaves_data_from_erp, teams_performance_from_erp
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.conf import settings

from collections import defaultdict

from users.models import Departments, User




def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


class AdministrativeStatistics(APIView):
    
    """
    View to retrieve administrative statistics.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [AllowAny]

    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Directors-dashboard'],
        operation_summary="Get directors adminstrative dashboard statistics",
        responses={200: 'Statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        try:
            departments= Departments.objects.all()
            departments_count = departments.count()

            employees = User.objects.filter(status='Active')
            employees_count = employees.count()

            # Get employee biodata from erp database 
            employee_bioDetails = fetch_employee_data_from_erp()
            if len(employee_bioDetails) < 1: 
                return Response({"error": "Failed to fetch employee biodata"}, status=500)
            
            #TODO: employee turn over
            #TODO: performance rating
            
            # demographic overview
            gender_dist = {}
            for gender in employees.values_list('gender', flat=True).distinct():
                gender_count =employees.filter(gender=gender).count()
                gender_perc = (gender_count / employees_count) * 100
                gender_dist[gender] = gender_count
                gender_dist[f"{gender}_perc"] = f"{round(gender_perc, 2)}%"
            
            marital_statuses = [emp.get('marital_status', '') for emp in employee_bioDetails if emp.get('marital_status')]
            total = len(marital_statuses)
            
            from collections import Counter
            marital_distribution = {}
            if total > 0:
                counts = Counter(marital_statuses)
                for status, count in counts.items():
                    perc = (count / total) * 100
                    marital_distribution[status] = count
                    marital_distribution[f"{status}_perc"] = f"{round(perc, 2)}%"

            # Department employee distribution
            departments_distribution_list = []
            departments_gender_distribution_list = []
            for department in departments:
                department_distribution = {}

                department_employees = User.objects.filter(department__dp_name=department.dp_name)
                department_active_employees_count =department_employees.filter(status__in=['Active', 'Converted']).count()
                department_inactive_employees_count =department_employees.filter(status='Inactive').count()

                department_distribution['department'] = department.dp_name
                department_distribution['employees_count'] = department_employees.count()
                department_distribution['active_employees_count'] = department_active_employees_count
                department_distribution['inactive_employees_count'] = department_inactive_employees_count
                departments_distribution_list.append(department_distribution)
                
                departments_gender_distribution_single_list = []
                for dept_gender in department_employees.values_list('gender', flat=True).distinct():
                    departments_gender_distribution_single_dict = {}
                    dept_gender_count = department_employees.filter(gender=dept_gender).count()
                    dept_gender_perc = (dept_gender_count / department_employees.count()) * 100
                    departments_gender_distribution_single_dict['gender'] = dept_gender
                    departments_gender_distribution_single_dict['total_gender_count'] = dept_gender_count
                    departments_gender_distribution_single_dict[f"gender_perc"] = f"{round(dept_gender_perc, 2)}%"
                    departments_gender_distribution_single_list.append(departments_gender_distribution_single_dict)
                
                departments_gender_distribution = {}
                departments_gender_distribution['department'] = department.dp_name
                departments_gender_distribution['gender_distribution'] = departments_gender_distribution_single_list
                departments_gender_distribution_list.append(departments_gender_distribution)
                

            #TODO: organization chart

            statistics = {
                "departments_count": departments_count,
                "employees_count": employees_count,
                "gender_distribution": gender_dist,
                "marital_distribution": marital_distribution,
                "department_overview": departments_distribution_list,
                "departments_gender_distribution": departments_gender_distribution_list
            }
            
            return Response(dict(statistics=statistics, success=True), status=200)
        
        except Exception as e:
            return Response(dict(error=str(e), success=False), status=500)
        

class PerformanceStatistics(APIView):
    
    """
    View to retrieve performance statistics.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [AllowAny]

    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Directors-dashboard'],
        operation_summary="Get directors performance dashboard statistics",
        responses={200: 'Statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        try:

            leaves = fetch_leaves_data_from_erp()
            teams_performance = teams_performance_from_erp()

            # --- Status Aggregation ---
            status_counter = defaultdict(lambda: {"applications": 0, "days": 0})
            for leave in leaves:
                status = leave.get("leave_status", "")
                days = int(leave.get("no_of_days") or 0)
                if status:
                    status_counter[status]["applications"] += 1
                    status_counter[status]["days"] += days
            
            leave_status_count = [
                {"status": status, "applications": data["applications"], "days": data["days"]}
                for status, data in status_counter.items()
            ]

            # --- Type Aggregation ---
            type_counter = defaultdict(lambda: {"applications": 0, "days": 0})
            for leave in leaves:
                leave_type = leave.get("leave_type", "")
                days = int(leave.get("no_of_days") or 0)
                if leave_type:
                    type_counter[leave_type]["applications"] += 1
                    type_counter[leave_type]["days"] += days
            
            leave_types_count = [
                {"type": leave_type, "applications": data["applications"], "days": data["days"]}
                for leave_type, data in type_counter.items()
            ]

            leaves_stats = {
                'leave_status_count':leave_status_count,
                'leave_types_count':leave_types_count,
            }


            statistics = {
                "leaves_stats": leaves_stats,
                "teams_performance": teams_performance,
            }
            
            return Response(dict(statistics=statistics, success=True), status=200)
        except Exception as e:
            return Response(dict(error=str(e), success=False), status=500)