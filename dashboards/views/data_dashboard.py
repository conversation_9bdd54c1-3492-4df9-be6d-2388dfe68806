from customers.models import Customer
from leads.models import LeadSource, LeadSourceCategory, LeadSourceSubCategory, Prospects
from sales.models import LeadFile
from django.db.models import Sum
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework import views
from rest_framework.response import Response
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class DataDashboardCountStatsView(views.APIView):
    
    """
    View to retrieve data dashboard statistics.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags = ['Dashboard-accounts'],
        operation_summary="Data Dashboard Statistics",
        responses={200: 'Data dashboard statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        customers = Customer.objects.all()
        customers_count = customers.count()

        leads = Prospects.objects.all()
        leads_count = leads.count()

        lead_sources = LeadSource.objects.all()
        lead_sources_count = lead_sources.count()

        lead_sources_categories = LeadSourceCategory.objects.all()
        lead_sources_categories_count = lead_sources_categories.count()

        lead_sources_subcategories = LeadSourceSubCategory.objects.all()
        lead_sources_subcategories_count = lead_sources_subcategories.count()

        sales = LeadFile.objects.all()
        total_sales = sales.aggregate(Sum('total_paid')).get('total_paid__sum', 0) or 0



        statistics = {
            "customers_count": customers_count,
            "leads_count": leads_count,
            "lead_sources_count": lead_sources_count,
            "lead_sources_subcategories_count": lead_sources_subcategories_count,
            "lead_sources_categories_count": lead_sources_categories_count,
            "total_sales": total_sales,
        }
        
        return Response(statistics, status=200)