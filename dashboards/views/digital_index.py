from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import action
from rest_framework import status
import MySQLdb.cursors
import logging
from typing import Dict, List, Optional, Tuple
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

"""
DIGITAL TEAM COMPREHENSIVE VIEWS:
==================================

This module provides comprehensive digital team functionality including:
1. Digital Dashboard View - Dashboard metrics and counts for digital team
2. All Customers View - Show ALL Optiven customers with essential details
3. Digital Customers View - Focused view for digital team customers only
4. All Prospects View - Complete prospect management with actions
5. Unallocated Leads View - Shows leads that need assignment
6. All Sales View - Complete sales overview
7. Digital Team Sales View - Sales filtered for digital team members

Key Features:
- Secure parameterized queries
- Comprehensive search and filtering
- Pagination for large datasets
- Action buttons for prospect management
- Department-based filtering for digital team
- Real-time dashboard metrics
"""

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        methods = ['list', 'create', 'retrieve', 'update', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                if hasattr(method, '_swagger_auto_schema'):
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

class DatabaseHelper:
    """
    Helper class for secure database operations
    """
    
    @staticmethod
    def execute_query(conn, query: str, params: List = None, fetch_one: bool = False, dict_cursor: bool = True) -> Optional[Dict]:
        """
        Execute a parameterized query safely
        """
        try:
            cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
            with conn.connection.cursor(cursor_class) as cur:
                if params:
                    logger.debug(f"Executing query: {query} with params: {params}")
                    cur.execute(query, params)
                else:
                    logger.debug(f"Executing query: {query}")
                    cur.execute(query)
                
                if fetch_one:
                    return cur.fetchone()
                else:
                    return cur.fetchall()
                    
        except Exception as e:
            logger.error(f"Database query failed: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise Exception(f"Database query failed: {str(e)}")
    
    @staticmethod
    def validate_pagination_params(page: str, page_size: str) -> Tuple[int, int, int]:
        """
        Validate and sanitize pagination parameters
        """
        try:
            page = max(1, int(page or 1))
            page_size = min(max(1, int(page_size or 10)), 100)  # Cap at 100
            offset = (page - 1) * page_size
            return page, page_size, offset
        except (ValueError, TypeError):
            return 1, 10, 0
    
    @staticmethod
    def sanitize_search_term(search: str) -> str:
        """
        Sanitize search term to prevent injection
        """
        if not search:
            return ""
        search = str(search).strip()[:100]
        return search

class CustomPagination(PageNumberPagination):
    """
    Custom pagination class for consistent pagination across all views
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

@swagger_tags(['Dashboard-digital'])
class DigitalDashboardView(ViewSet):
    """
    Comprehensive digital team dashboard with counts and metrics for all categories
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns comprehensive digital team dashboard metrics",
        responses={
            200: openapi.Response(
                description="Successful response",
                examples={
                    "application/json": {
                        "customers": {
                            "total_optiven_customers": 1500,
                            "digital_customers": 800
                        },
                        "sales": {
                            "total_sales": 1200,
                            "digital_team_sales": 300,
                            "active_sales": 1000,
                            "completed_sales": 200
                        },
                        "leads": {
                            "total_leads": 600,
                            "unallocated_leads": 50,
                            "converted_leads": 200
                        }
                    }
                }
            )
        }
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        try:
            # Single optimized query to get all dashboard metrics
            dashboard_sql = """
                SELECT 
                    -- Customer metrics
                    (SELECT COUNT(*) FROM customers_customer) as total_optiven_customers,
                    (SELECT COUNT(DISTINCT c.customer_no) 
                     FROM customers_customer c 
                     INNER JOIN sales_leadfile sf ON c.customer_no = sf.customer_id_id 
                     WHERE sf.marketer_id IN (
                         SELECT employee_no FROM users_user 
                         WHERE department_id LIKE %s OR department_id LIKE %s
                     )) as digital_customers,
                    
                    -- Sales metrics
                    (SELECT COUNT(*) FROM sales_leadfile) as total_sales,
                    (SELECT COUNT(*) FROM sales_leadfile sf 
                     WHERE sf.marketer_id IN (
                         SELECT employee_no FROM users_user 
                         WHERE department_id LIKE %s OR department_id LIKE %s
                     )) as digital_team_sales,
                    (SELECT COUNT(*) FROM sales_leadfile WHERE lead_file_status_dropped = %s) as active_sales,
                    (SELECT COUNT(*) FROM sales_leadfile WHERE lead_file_status_dropped = %s) as completed_sales,
                    
                    -- Lead metrics  
                    (SELECT COUNT(*) FROM leads_prospects) as total_optiven_leads,
                    (SELECT COUNT(*) FROM leads_prospects WHERE department_id LIKE %s OR department_id LIKE %s) as total_leads,
                    (SELECT COUNT(*) FROM leads_prospects WHERE marketer_id IS NULL AND (department_id LIKE %s OR department_id LIKE %s)) as unallocated_leads,
                    (SELECT COUNT(*) FROM leads_prospects WHERE marketer_id IS NOT NULL AND (department_id LIKE %s OR department_id LIKE %s)) as allocated_prospects,
                    (SELECT COUNT(*) FROM leads_prospects WHERE is_converted = %s AND (department_id LIKE %s OR department_id LIKE %s)) as converted_leads,
                    (SELECT COUNT(*) FROM leads_prospects WHERE category = %s AND (department_id LIKE %s OR department_id LIKE %s)) as hot_prospects,
                    (SELECT COUNT(*) FROM leads_prospects WHERE category = %s AND (department_id LIKE %s OR department_id LIKE %s)) as warm_prospects,
                    (SELECT COUNT(*) FROM leads_prospects WHERE category = %s AND (department_id LIKE %s OR department_id LIKE %s)) as cold_prospects
            """

            params = [
                10, '%DM%',  # digital_customers
                10, '%DM%',  # digital_team_sales
                0, 1,  # active/completed sales
                10, '%DM%',  # digital_total_leads
                10, '%DM%',  # unallocated_leads, 
                10, '%DM%',  # allocated leads 
                True, 10, '%DM%', # converted_leads
                'Hot', 10, '%DM%', # Hot prospect category
                'Warm', 10, '%DM%', # Warm prospect category
                'Cold', 10, '%DM%',  # Cold prospect category
            ]

            result = DatabaseHelper.execute_query(conn, dashboard_sql, params, fetch_one=True)
            
            if not result:
                return Response({
                    "customers": {"total_optiven_customers": 0, "digital_customers": 0},
                    "sales": {"total_sales": 0, "digital_team_sales": 0, 
                            "active_sales": 0, "completed_sales": 0},
                    "leads": {"total_optiven_leads": 0,"total_leads": 0, "unallocated_leads": 0, "converted_leads": 0,"allocated_prospects": 0, "hot_prospects": 0, 
                                "warm_prospects": 0, "cold_prospects": 0}
                })

            return Response({
                "customers": {
                    "total_optiven_customers": result['total_optiven_customers'] or 0,
                    "digital_customers": result['digital_customers'] or 0
                },
                "sales": {
                    "total_sales": result['total_sales'] or 0,
                    "digital_team_sales": result['digital_team_sales'] or 0,
                    "active_sales": result['active_sales'] or 0,
                    "completed_sales": result['completed_sales'] or 0
                },
                "leads": {
                    "total_optiven_leads": result['total_optiven_leads'] or 0,
                    "total_leads": result['total_leads'] or 0,
                    "unallocated_leads": result['unallocated_leads'] or 0,
                    "converted_leads": result['converted_leads'] or 0,
                    "allocated_prospects": result['allocated_prospects'] or 0,
                    "hot_prospects": result['hot_prospects'] or 0,
                    "warm_prospects": result['warm_prospects'] or 0,
                    "cold_prospects": result['cold_prospects'] or 0
                }
            })

        except Exception as e:
            logger.error(f"DigitalDashboardView error: {str(e)}")
            return Response({"message": "Failed to fetch dashboard data", "error":f"{e}"}, status=500)


@swagger_tags(['Dashboard-digital'])
class AllCustomersView(ViewSet):
    """
    All Optiven customers view for digital team
    Shows: customer_id, customer_name, phone_number, primary_email, plot_number, marketer with employee_no
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns all Optiven customers with essential details",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search by name, phone, email, or customer ID", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page (max 100)", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))

        try:
            base_query = """
                SELECT DISTINCT
                    c.customer_no,
                    c.customer_name,
                    c.phone,
                    c.primary_email,
                    u.employee_no as marketer_employee_no,
                    u.fullnames as marketer_name
                FROM customers_customer c
                LEFT JOIN sales_leadfile sf ON c.customer_no = sf.customer_id_id
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
            """

            params = []

            if search:
                base_query += """
                    WHERE (
                        c.customer_name LIKE %s
                        OR c.phone LIKE %s
                        OR c.primary_email LIKE %s
                        OR c.customer_no LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 4)

            # Get total count first with a simpler query
            count_query = """
                SELECT COUNT(DISTINCT c.customer_no) as total 
                FROM customers_customer c
                LEFT JOIN sales_leadfile sf ON c.customer_no = sf.customer_id_id
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
            """
            
            count_params = []
            if search:
                count_query += """
                    WHERE (
                        c.customer_name LIKE %s
                        OR c.phone LIKE %s
                        OR c.primary_email LIKE %s
                        OR c.customer_no LIKE %s
                    )
                """
                count_params.extend([search_param] * 4)
            
            count_result = DatabaseHelper.execute_query(conn, count_query, count_params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY c.customer_name ASC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            customers = DatabaseHelper.execute_query(conn, base_query, params)

            processed_customers = []
            for customer in customers or []:
                processed_customers.append({
                    "customer_id": customer['customer_no'],
                    "customer_name": customer['customer_name'],
                    "phone_number": customer['phone'],
                    "primary_email": customer['primary_email'],
                    "plot_numbers": "Available on request",  # Simplified to avoid complex joins
                    "marketer_employee_no": customer['marketer_employee_no'],
                    "marketer_name": customer['marketer_name']
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_customers
            })

        except Exception as e:
            logger.error(f"AllCustomersView error: {str(e)}")
            return Response({
                "error": "Failed to fetch customers data",
                "count": 0,
                "results": []
            }, status=500)


@swagger_tags(['Dashboard-digital'])
class DigitalCustomersView(ViewSet):
    """
    Digital customers view
    Shows: customer_no, name, national_id, kra, phone_number, email, marketer, employee_no
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns digital customers with detailed info",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search by any field", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))

        try:
            base_query = """
                SELECT DISTINCT
                    c.customer_no,
                    c.customer_name as name,
                    c.national_id,
                    c.kra_pin as kra,
                    c.phone as phone_number,
                    c.primary_email as email,
                    u.fullnames as marketer,
                    u.employee_no
                FROM customers_customer c
                INNER JOIN sales_leadfile sf ON c.customer_no = sf.customer_id_id
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
                WHERE sf.marketer_id IN (
                    SELECT employee_no FROM users_user 
                    WHERE department_id LIKE %s OR department_id LIKE %s
                )
            """

            params = [10, '%DM%']

            if search:
                base_query += """
                    AND (
                        c.customer_name LIKE %s
                        OR c.national_id LIKE %s
                        OR c.kra_pin LIKE %s
                        OR c.phone LIKE %s
                        OR c.primary_email LIKE %s
                        OR u.fullnames LIKE %s
                        OR u.employee_no LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 7)

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            count_result = DatabaseHelper.execute_query(conn, count_query, params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY c.customer_name ASC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            customers = DatabaseHelper.execute_query(conn, base_query, params)

            processed_customers = []
            for customer in customers or []:
                processed_customers.append({
                    "customer_no": customer['customer_no'],
                    "name": customer['name'],
                    "national_id": customer['national_id'],
                    "kra": customer['kra'],
                    "phone_number": customer['phone_number'],
                    "email": customer['email'],
                    "marketer": customer['marketer'],
                    "employee_no": customer['employee_no']
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_customers
            })

        except Exception as e:
            logger.error(f"DigitalCustomersView error: {str(e)}")
            return Response({
                "error": "Failed to fetch digital customers data",
                "count": 0,
                "results": []
            }, status=500)


@swagger_tags(['Dashboard-digital'])
class AllProspectsView(ViewSet):
    """
    All prospects view with management actions
    Shows: name, phone_number, allocated_marketer, digital_marketer_in_charge, leadsource_title
    Actions: edit prospect, reallocate marketer/digital marketer, add feedback, view feedback
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns all prospects with management capabilities",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search prospects", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('category', openapi.IN_QUERY, description="Filter by category", type=openapi.TYPE_STRING, enum=['Hot', 'Warm', 'Cold'], required=False),
            openapi.Parameter('status', openapi.IN_QUERY, description="Filter by status", type=openapi.TYPE_STRING, enum=['Active', 'Dormant'], required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))
        category = request.query_params.get('category', '')
        status_filter = request.query_params.get('status', '')

        try:
            base_query = """
                SELECT 
                    p.id as prospect_id,
                    p.name,
                    p.phone as phone_number,
                    u_marketer.fullnames as allocated_marketer,
                    u_marketer.employee_no as marketer_employee_no,
                    u_dept.fullnames as digital_marketer_in_charge,
                    u_dept.employee_no as digital_marketer_employee_no,
                    ls.name as leadsource_title,
                    p.category,
                    p.status,
                    p.pipeline_level,
                    p.is_converted,
                    p.date as created_date
                FROM leads_prospects p
                LEFT JOIN users_user u_marketer ON p.marketer_id = u_marketer.employee_no
                LEFT JOIN users_user u_dept ON p.department_member_id = u_dept.employee_no
                LEFT JOIN leads_leadsource ls ON p.lead_source_id = ls.leadsource_id
                WHERE 1=1
            """

            params = []

            if search:
                base_query += """
                    AND (
                        p.name LIKE %s
                        OR p.phone LIKE %s
                        OR u_marketer.fullnames LIKE %s
                        OR u_dept.fullnames LIKE %s
                        OR ls.name LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 5)

            if category:
                base_query += " AND p.category = %s"
                params.append(category)

            if status_filter:
                base_query += " AND p.status = %s"
                params.append(status_filter)

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            count_result = DatabaseHelper.execute_query(conn, count_query, params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY p.date DESC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            prospects = DatabaseHelper.execute_query(conn, base_query, params)

            processed_prospects = []
            for prospect in prospects or []:
                processed_prospects.append({
                    "prospect_id": prospect['prospect_id'],
                    "name": prospect['name'],
                    "phone_number": prospect['phone_number'],
                    "allocated_marketer": prospect['allocated_marketer'],
                    "marketer_employee_no": prospect['marketer_employee_no'],
                    "digital_marketer_in_charge": prospect['digital_marketer_in_charge'],
                    "digital_marketer_employee_no": prospect['digital_marketer_employee_no'],
                    "leadsource_title": prospect['leadsource_title'],
                    "category": prospect['category'],
                    "status": prospect['status'],
                    "pipeline_level": prospect['pipeline_level'],
                    "is_converted": bool(prospect['is_converted']),
                    "created_date": prospect['created_date'].strftime('%Y-%m-%d %H:%M:%S') if prospect['created_date'] else None,
                    "actions": {
                        "edit_url": f"/api/prospects/{prospect['prospect_id']}/edit/",
                        "reallocate_url": f"/api/prospects/{prospect['prospect_id']}/reallocate/",
                        "add_feedback_url": f"/api/prospects/{prospect['prospect_id']}/feedback/",
                        "view_feedback_url": f"/api/prospects/{prospect['prospect_id']}/feedback/list/"
                    }
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_prospects
            })

        except Exception as e:
            logger.error(f"AllProspectsView error: {str(e)}")
            return Response({
                "error": "Failed to fetch prospects data",
                "count": 0,
                "results": []
            }, status=500)


@swagger_tags(['Dashboard-digital'])
class UnallocatedLeadsView(ViewSet):
    """
    View for unallocated leads that need assignment
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns unallocated leads that need assignment",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search unallocated leads", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('member_id', openapi.IN_QUERY, description="Member ID", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('leadsource_id', openapi.IN_QUERY, description="Leadsource ID", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('creation_start', openapi.IN_QUERY, description="Creation start date", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('creation_end', openapi.IN_QUERY, description="Creation end date", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))

        try:
            base_query = """
                SELECT 
                    p.id as prospect_id,
                    p.name,
                    p.phone as phone_number,
                    p.email,
                    p.city,
                    p.country,
                    ls.name as leadsource_title,
                    p.category,
                    p.department_id,
                    p.department_member_id,
                    p.lead_source_id,
                    p.pipeline_level,
                    p.date as created_date,
                    p.comment
                FROM leads_prospects p
                LEFT JOIN leads_leadsource ls ON p.lead_source_id = ls.leadsource_id
                WHERE p.marketer_id IS NULL
                AND p.status = %s
            """

            params = ['Active']

            if search:
                base_query += """
                    AND (
                        p.name LIKE %s
                        OR p.phone LIKE %s
                        OR p.email LIKE %s
                        OR ls.name LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 4)

            if 'member_id' in request.query_params and request.query_params.get('member_id') != '':
                base_query += " AND p.department_member_id = %s"
                params.append(request.query_params.get('member_id'))
            if 'leadsource_id' in request.query_params and request.query_params.get('leadsource_id') != '':
                base_query += " AND p.lead_source_id = %s"
                params.append(request.query_params.get('leadsource_id'))
            if 'creation_start' in request.query_params and request.query_params.get('creation_start') != '':
                base_query += " AND p.date >= %s"
                params.append(request.query_params.get('creation_start'))
            if 'creation_end' in request.query_params and request.query_params.get('creation_end') != '':
                base_query += " AND p.date <= %s"
                params.append(request.query_params.get('creation_end'))

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            count_result = DatabaseHelper.execute_query(conn, count_query, params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY p.date DESC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            leads = DatabaseHelper.execute_query(conn, base_query, params)

            processed_leads = []
            for lead in leads or []:
                processed_leads.append({
                    "prospect_id": lead['prospect_id'],
                    "name": lead['name'],
                    "phone_number": lead['phone_number'],
                    "email": lead['email'],
                    "city": lead['city'],
                    "country": lead['country'],
                    "leadsource_title": lead['leadsource_title'],
                    "category": lead['category'],
                    "department_id": lead['department_id'],
                    "department_member_id": lead['department_member_id'],
                    "lead_source_id": lead['lead_source_id'],
                    "pipeline_level": lead['pipeline_level'],
                    "created_date": lead['created_date'].strftime('%Y-%m-%d %H:%M:%S') if lead['created_date'] else None,
                    "comment": lead['comment'],
                    "actions": {
                        "assign_marketer_url": f"/api/prospects/{lead['prospect_id']}/assign/",
                        "edit_url": f"/api/prospects/{lead['prospect_id']}/edit/"
                    }
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_leads
            })

        except Exception as e:
            logger.error(f"UnallocatedLeadsView error: {str(e)}")
            return Response({
                "error": "Failed to fetch unallocated leads data",
                "count": 0,
                "results": []
            }, status=500)


@swagger_tags(['Dashboard-digital'])
class AllSalesView(ViewSet):
    """
    All sales view for comprehensive sales overview
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns all sales with comprehensive details",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search sales", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('status', openapi.IN_QUERY, description="Filter by status", type=openapi.TYPE_STRING, enum=['active', 'dropped'], required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))
        status_filter = request.query_params.get('status', 'active').lower()

        try:
            # Simplified base query without complex joins
            base_query = """
                SELECT 
                    sf.lead_file_no,
                    sf.customer_id_id as customer_no,
                    sf.customer_name,
                    sf.selling_price,
                    sf.total_paid,
                    sf.balance_lcy,
                    sf.booking_date,
                    u.fullnames as marketer_name,
                    u.employee_no as marketer_employee_no,
                    sf.lead_file_status_dropped
                FROM sales_leadfile sf
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
                WHERE 1=1
            """

            params = []

            # Add status filter
            if status_filter == 'active':
                base_query += " AND sf.lead_file_status_dropped = %s"
                params.append(0)
            elif status_filter == 'dropped':
                base_query += " AND sf.lead_file_status_dropped = %s"
                params.append(1)

            if search:
                base_query += """
                    AND (
                        sf.lead_file_no LIKE %s
                        OR sf.customer_name LIKE %s
                        OR sf.customer_id_id LIKE %s
                        OR u.fullnames LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 4)

            # Get total count with simplified query
            count_query = """
                SELECT COUNT(*) as total 
                FROM sales_leadfile sf
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
                WHERE 1=1
            """
            
            count_params = []
            
            if status_filter == 'active':
                count_query += " AND sf.lead_file_status_dropped = %s"
                count_params.append(0)
            elif status_filter == 'dropped':
                count_query += " AND sf.lead_file_status_dropped = %s"
                count_params.append(1)
            
            if search:
                count_query += """
                    AND (
                        sf.lead_file_no LIKE %s
                        OR sf.customer_name LIKE %s
                        OR sf.customer_id_id LIKE %s
                        OR u.fullnames LIKE %s
                    )
                """
                count_params.extend([search_param] * 4)
            
            count_result = DatabaseHelper.execute_query(conn, count_query, count_params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY sf.booking_date DESC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            sales = DatabaseHelper.execute_query(conn, base_query, params)

            processed_sales = []
            for sale in sales or []:
                processed_sales.append({
                    "lead_file_no": sale['lead_file_no'],
                    "customer_no": sale['customer_no'],
                    "customer_name": sale['customer_name'],
                    "selling_price": float(sale['selling_price']) if sale['selling_price'] else 0,
                    "total_paid": float(sale['total_paid']) if sale['total_paid'] else 0,
                    "balance": float(sale['balance_lcy']) if sale['balance_lcy'] else 0,
                    "booking_date": sale['booking_date'].strftime('%Y-%m-%d') if sale['booking_date'] else None,
                    "marketer_name": sale['marketer_name'],
                    "marketer_employee_no": sale['marketer_employee_no'],
                    "plot_number": "Available on request",  # Simplified to avoid complex joins
                    "project_name": "Available on request",  # Simplified to avoid complex joins
                    "status": "dropped" if sale['lead_file_status_dropped'] else "active"
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_sales
            })

        except Exception as e:
            logger.error(f"AllSalesView error: {str(e)}")
            return Response({
                "error": "Failed to fetch all sales data",
                "count": 0,
                "results": []
            }, status=500)


@swagger_tags(['Dashboard-digital'])
class DigitalTeamSalesView(ViewSet):
    """
    Digital team-specific sales view filtered for digital team members
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['Dashboard-digital'],
        operation_description="Returns sales for digital team members only",
        manual_parameters=[
            openapi.Parameter('search', openapi.IN_QUERY, description="Search digital team sales", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('status', openapi.IN_QUERY, description="Filter by status", type=openapi.TYPE_STRING, enum=['active', 'dropped'], required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        conn = connections["reports"]
        conn.ensure_connection()

        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))
        status_filter = request.query_params.get('status', 'active').lower()

        try:
            # Simplified base query without complex joins
            base_query = """
                SELECT 
                    sf.lead_file_no,
                    sf.customer_id_id as customer_no,
                    sf.customer_name,
                    sf.selling_price,
                    sf.total_paid,
                    sf.balance_lcy,
                    sf.booking_date,
                    u.fullnames as marketer_name,
                    u.employee_no as marketer_employee_no,
                    sf.lead_file_status_dropped
                FROM sales_leadfile sf
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
                WHERE sf.marketer_id IN (
                    SELECT employee_no FROM users_user 
                    WHERE department_id LIKE %s OR department_id LIKE %s
                )
            """

            params = [10, '%DM%']

            # Add status filter
            if status_filter == 'active':
                base_query += " AND sf.lead_file_status_dropped = %s"
                params.append(0)
            elif status_filter == 'dropped':
                base_query += " AND sf.lead_file_status_dropped = %s"
                params.append(1)

            if search:
                base_query += """
                    AND (
                        sf.lead_file_no LIKE %s
                        OR sf.customer_name LIKE %s
                        OR sf.customer_id_id LIKE %s
                        OR u.fullnames LIKE %s
                    )
                """
                search_param = f"%{search}%"
                params.extend([search_param] * 4)

            # Get total count with simplified query
            count_query = """
                SELECT COUNT(*) as total 
                FROM sales_leadfile sf
                LEFT JOIN users_user u ON sf.marketer_id = u.employee_no
                WHERE sf.marketer_id IN (
                    SELECT employee_no FROM users_user 
                    WHERE department_id LIKE %s OR department_id LIKE %s
                )
            """
            
            count_params = [10, '%DM%']
            
            if status_filter == 'active':
                count_query += " AND sf.lead_file_status_dropped = %s"
                count_params.append(0)
            elif status_filter == 'dropped':
                count_query += " AND sf.lead_file_status_dropped = %s"
                count_params.append(1)
            
            if search:
                count_query += """
                    AND (
                        sf.lead_file_no LIKE %s
                        OR sf.customer_name LIKE %s
                        OR sf.customer_id_id LIKE %s
                        OR u.fullnames LIKE %s
                    )
                """
                count_params.extend([search_param] * 4)
            
            count_result = DatabaseHelper.execute_query(conn, count_query, count_params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination
            base_query += " ORDER BY sf.booking_date DESC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            sales = DatabaseHelper.execute_query(conn, base_query, params)

            processed_sales = []
            for sale in sales or []:
                processed_sales.append({
                    "lead_file_no": sale['lead_file_no'],
                    "customer_no": sale['customer_no'],
                    "customer_name": sale['customer_name'],
                    "selling_price": float(sale['selling_price']) if sale['selling_price'] else 0,
                    "total_paid": float(sale['total_paid']) if sale['total_paid'] else 0,
                    "balance": float(sale['balance_lcy']) if sale['balance_lcy'] else 0,
                    "booking_date": sale['booking_date'].strftime('%Y-%m-%d') if sale['booking_date'] else None,
                    "marketer_name": sale['marketer_name'],
                    "marketer_employee_no": sale['marketer_employee_no'],
                    "plot_number": "Available on request",  # Simplified to avoid complex joins
                    "project_name": "Available on request",  # Simplified to avoid complex joins
                    "status": "dropped" if sale['lead_file_status_dropped'] else "active"
                })

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0

            return Response({
                "count": total_count,
                "total_pages": total_pages,
                "current_page": page,
                "page_size": page_size,
                "next": page < total_pages,
                "previous": page > 1,
                "results": processed_sales
            })

        except Exception as e:
            logger.error(f"DigitalTeamSalesView error: {str(e)}")
            return Response({
                "error": "Failed to fetch digital team sales data",
                "count": 0,
                "results": []
            }, status=500) 