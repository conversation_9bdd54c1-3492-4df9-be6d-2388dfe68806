from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime
import MySQLdb.cursors
import logging
from django.conf import settings




def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")

        


@swagger_tags(['Main-Dashboard'])
class MainDashView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema(
        operation_description="Returns Everything required for the main dashboard.",
        manual_parameters=[
            
            openapi.Parameter(name='EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by Employee Number.', required=False,default='ALL',
            )
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        employee_no = request.query_params.get("EMPLOYEE_NO", "ALL")
    
        if not employee_no:
           return Response({"error": "Employee number is required."}, status=400)
        if employee_no == "ALL":
            return Response({"error": "Employee number is required."}, status=400)
        
        # Fetch 5 most recent sales reminders
        reminders_query = """ SELECT * FROM `services_reminder` WHERE `created_by_id` =  %s ORDER BY `services_reminder`.`created_at` DESC LIMIT 5;"""
        params=[employee_no] 
        sql = reminders_query.replace("\n", " ").strip()
        if not sql:
            reminders_data =  {"error": "No reminders_query SQL query generated."}
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                reminders_data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            reminders_data = {"error": str(e)}
        if not reminders_data:
            reminders_data =  {"error": "No reminders found."}
        
        #fetch 5 most recent notifications
        notifications_query = """ SELECT * FROM `services_notification` WHERE `recipient_id` =  %s ORDER BY `services_notification`.`created_at` DESC LIMIT 5;"""
        params=[employee_no]
        sql = notifications_query.replace("\n", " ").strip()
        if not sql:
            notifications_data =  {"error": "No notifications_query SQL query generated."}
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                notifications_data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            notifications_data = {"error": str(e)}
        if not notifications_data:
            notifications_data =  {"error": "No notifications found."}
            
        #fetch 5 most recent notes
        notes_query = """ SELECT * FROM `services_note` WHERE `created_by_id` = %s ORDER BY `services_note`.`created_at` DESC LIMIT 5;"""
        params=[employee_no]
        sql = notes_query.replace("\n", " ").strip()
        if not sql:
            notes_data =  {"error": "No notes_query SQL query generated."}
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                notes_data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            notes_data = {"error": str(e)}
        if not notes_data:
            notes_data =  {"error": "No notes found."}
        
        #fetch 5 most recent TICKETS
        tickets_query = """ SELECT * FROM `ticketing_ticket` WHERE `user_id` = %s ORDER BY `ticketing_ticket`.`created_at` DESC LIMIT 5;"""
        params=[employee_no]
        sql = tickets_query.replace("\n", " ").strip()
        if not sql:
            tickets_data =  {"error": "No tickets_query SQL query generated."}
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                tickets_data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            tickets_data = {"error": str(e)}
        if not tickets_data:
            tickets_data =  {"error": "No tickets found."}

        # --- Close all connections
        for c in connections.all():
                c.close()
        

        return Response({
            "reminders": reminders_data,
            "notifications": notifications_data,
            "notes": notes_data,
            "tickets": tickets_data
        })