from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
# from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime
import MySQLdb.cursors
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")


@swagger_tags(['Dashboard-Sales'])
class HqHosIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema( operation_description="Returns dashboard data for HQ HOS main page")
    def list(self, request):
        
        try: 
            
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month",params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB
            
            
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'))"""
            time_clause = f"""WHERE POSTED_DATE1 = CURRENT_DATE {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(daily_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            daily_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
         
            
            # monthly mib
            time_clause = f"""WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            monthy_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthy_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthy_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly installments
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Installment' """
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_installments_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_installments_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly TRANSFercosts
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Transfer Cost' """
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_transferCosts_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_transferCosts_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Deposit' """
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY ADDITIONAL DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Additional Deposit' """
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(additional_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            additional_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0

            # --- 2. Overdue Collections
            
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN 
            ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ')); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today",params=(), dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 and pl.`lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'));
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections",params=(), dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'));
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold",params=(), dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE
                  AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 
            AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'));
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold",params=(), dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            TotalPurchasePrices_sql = """SELECT SUM(`purchase_price`) AS 'pp', SUM(`balance_lcy`) AS b, SUM(`total_paid`) AS tp 
            FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'); """
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching Total Purchase Prices",params=(), dict_cursor=True)
            TotalPurchasePrices = row['pp'] or 0 if row else 0
            TotalPaid = row['tp'] or 0 if row else 0
            TotalOutstandingBalances = row['b'] or 0 if row else 0
            
            TotalActiveSales_sql = """SELECT COUNT(`lead_file_no`)
            AS 'al' FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ');"""
            row = fetch_one(TotalActiveSales_sql, "Error fetching Total Active Sales",params=(), dict_cursor=True)    
            TotalActiveSales = row['al'] or 0 if row else 0
            
            Total_Expected_Collections_sql = """ SELECT SUM(total_to_collect) as total_to_collect, SUM(installments_due) as total_installments_due, SUM(previous_unpaid) as total_previous_unpaid, SUM(`overdue_collections`) 
                    as total_overdue FROM `portfolio_lines` WHERE `lead_file_no` IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0  AND 
                    `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='HQ'));"""
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching Total Expected Collections",params=(), dict_cursor=True)
            Total_Expected_Collections = row['total_to_collect'] or 0 if row else 0
            CurrentExpectedInstallments = row['total_installments_due'] or 0 if row else 0
            AccruedMissedInstallments = row['total_previous_unpaid'] or 0 if row else 0
            OverdueInstallments = row['total_overdue'] or 0 if row else 0
            
            
            # TEAMS
            teams_sql = """SELECT * FROM `users_teams` WHERE `office` = 'HQ' """
            
            try:
                conn = connections["reports"]
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(teams_sql)
                    teams_data = cur.fetchall()
            except Exception as e:
                return Response({"error fetching all teams data": str(e)}, status=500)
            
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "markreting_month": marketing_month,
                
                "MIB":
                    {
                        "Daily_MIB": daily_mib_data,
                        "Monthly_MIB": monthy_mib_data,
                        "Monthly_Transfer_Costs": monthly_transferCosts_data,
                        "Monthly_Deposits": monthly_deposits_data,
                        "Monthly_Additional_Deposits": additional_deposits_data,
                        "Monthly_Installments": monthly_installments_data,
                    },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                    },
                
                "Teams": teams_data
            })
            
        except Exception as e:
            return Response({"error": str(e)}, status=500)


@swagger_tags(['Dashboard-Sales'])
class GlobalHosIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema( operation_description="Returns dashboard data for Global HOS main page")
    def list(self, request):
        
        try: 
            
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month",params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB
            
            
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'))"""
            time_clause = f"""WHERE POSTED_DATE1 = CURRENT_DATE {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(daily_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            daily_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
         
            
            # monthly mib
            time_clause = f"""WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            monthy_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthy_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthy_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly installments
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Installment' """
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_installments_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_installments_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly TRANSFercosts
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Transfer Cost' """
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_transferCosts_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_transferCosts_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Deposit' """
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY ADDITIONAL DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Additional Deposit' """
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(additional_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            additional_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0

            # --- 2. Overdue Collections
            
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN 
            ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global')); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today",params=(), dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 and pl.`lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'));
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections",params=(), dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'));
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold",params=(), dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE
                  AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 
            AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'));
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold",params=(), dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            TotalPurchasePrices_sql = """SELECT SUM(`purchase_price`) AS 'pp', SUM(`balance_lcy`) AS b, SUM(`total_paid`) AS tp 
            FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'); """
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching Total Purchase Prices",params=(), dict_cursor=True)
            TotalPurchasePrices = row['pp'] or 0 if row else 0
            TotalPaid = row['tp'] or 0 if row else 0
            TotalOutstandingBalances = row['b'] or 0 if row else 0
            
            TotalActiveSales_sql = """SELECT COUNT(`lead_file_no`)
            AS 'al' FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global');"""
            row = fetch_one(TotalActiveSales_sql, "Error fetching Total Active Sales",params=(), dict_cursor=True)    
            TotalActiveSales = row['al'] or 0 if row else 0
            
            Total_Expected_Collections_sql = """ SELECT SUM(total_to_collect) as total_to_collect, SUM(installments_due) as total_installments_due, SUM(previous_unpaid) as total_previous_unpaid, SUM(`overdue_collections`) 
                    as total_overdue FROM `portfolio_lines` WHERE `lead_file_no` IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0  AND 
                    `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`='Global'));"""
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching Total Expected Collections",params=(), dict_cursor=True)
            Total_Expected_Collections = row['total_to_collect'] or 0 if row else 0
            CurrentExpectedInstallments = row['total_installments_due'] or 0 if row else 0
            AccruedMissedInstallments = row['total_previous_unpaid'] or 0 if row else 0
            OverdueInstallments = row['total_overdue'] or 0 if row else 0
            
            
            # TEAMS
            teams_sql = """SELECT * FROM `users_teams` WHERE `office` = 'Global' """
            
            try:
                conn = connections["reports"]
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(teams_sql)
                    teams_data = cur.fetchall()
            except Exception as e:
                return Response({"error fetching all teams data": str(e)}, status=500)
            
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "markreting_month": marketing_month,
                
                "MIB":
                    {
                        "Daily_MIB": daily_mib_data,
                        "Monthly_MIB": monthy_mib_data,
                        "Monthly_Transfer_Costs": monthly_transferCosts_data,
                        "Monthly_Deposits": monthly_deposits_data,
                        "Monthly_Additional_Deposits": additional_deposits_data,
                        "Monthly_Installments": monthly_installments_data,
                    },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                    },
                
                "Teams": teams_data
            })
            
        except Exception as e:
            return Response({"error": str(e)}, status=500)

@swagger_tags(['Dashboard-Sales'])
class MarketerIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema( operation_description="Returns dashboard data for Global HOS main page",
           manual_parameters=[
            openapi.Parameter(
                name='Marketer_employee_no',in_=openapi.IN_QUERY,description='Employee no  eg "OL/HR/790"',
                type=openapi.TYPE_STRING,required=True,
            ),
        ]              
                         )
    def list(self, request):
        
        
        Marketer_employee_no = request.query_params.get('Marketer_employee_no', None)
        if not Marketer_employee_no:
            return Response({"error": "Marketer_employee_no is required"}, status=400)
        try: 
            
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month",params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB
            
            
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id`= %s)"""
            time_clause = f"""WHERE POSTED_DATE1 = CURRENT_DATE {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(daily_mib_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            daily_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
         
            
            # monthly mib
            time_clause = f"""WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            monthy_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthy_mib_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            monthy_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly installments
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Installment' """
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_installments_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            monthly_installments_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly TRANSFercosts
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Transfer Cost' """
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_transferCosts_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            monthly_transferCosts_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Deposit' """
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_deposits_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            monthly_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY ADDITIONAL DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Additional Deposit' """
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(additional_deposits_sql, "Error fetching Daily MIB",params=(Marketer_employee_no,Marketer_employee_no), dict_cursor=True)
            additional_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0

            # --- 2. Overdue Collections
            
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id`= %s); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today",params=(Marketer_employee_no), dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 and pl.`lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id`= %s);
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections",params=(Marketer_employee_no), dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND 
            `marketer_id`= %s);
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold",params=(Marketer_employee_no), dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE
                  AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 
            AND `marketer_id`= %s);
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold",params=(Marketer_employee_no), dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            TotalPurchasePrices_sql = """SELECT SUM(`purchase_price`) AS 'pp', SUM(`balance_lcy`) AS b, SUM(`total_paid`) AS tp 
            FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id`= %s; """
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching Total Purchase Prices",params=(Marketer_employee_no), dict_cursor=True)
            TotalPurchasePrices = row['pp'] or 0 if row else 0
            TotalPaid = row['tp'] or 0 if row else 0
            TotalOutstandingBalances = row['b'] or 0 if row else 0
            
            TotalActiveSales_sql = """SELECT COUNT(`lead_file_no`)
            AS 'al' FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `marketer_id`= %s;"""
            row = fetch_one(TotalActiveSales_sql, "Error fetching Total Active Sales",params=(Marketer_employee_no), dict_cursor=True)    
            TotalActiveSales = row['al'] or 0 if row else 0
            
            Total_Expected_Collections_sql = """ SELECT SUM(total_to_collect) as total_to_collect, SUM(installments_due) as total_installments_due, SUM(previous_unpaid) as total_previous_unpaid, SUM(`overdue_collections`) 
                    as total_overdue FROM `portfolio_lines` WHERE `lead_file_no` IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0  AND 
                    `marketer_id`= %s);"""
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching Total Expected Collections",params=(Marketer_employee_no), dict_cursor=True)
            Total_Expected_Collections = row['total_to_collect'] or 0 if row else 0
            CurrentExpectedInstallments = row['total_installments_due'] or 0 if row else 0
            AccruedMissedInstallments = row['total_previous_unpaid'] or 0 if row else 0
            OverdueInstallments = row['total_overdue'] or 0 if row else 0
            
            
           

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "markreting_month": marketing_month,
                
                "MIB":
                    {
                        "Daily_MIB": daily_mib_data,
                        "Monthly_MIB": monthy_mib_data,
                        "Monthly_Transfer_Costs": monthly_transferCosts_data,
                        "Monthly_Deposits": monthly_deposits_data,
                        "Monthly_Additional_Deposits": additional_deposits_data,
                        "Monthly_Installments": monthly_installments_data,
                    },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                    },
                
            })
            
        except Exception as e:
            return Response({"error": str(e)}, status=500)
