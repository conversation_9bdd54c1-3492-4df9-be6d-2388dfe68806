from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
# from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime
import MySQLdb.cursors
from django.conf import settings

from leads.models import DiasporaRegions, LeadSource, LeadSourceSubCategory


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")


@swagger_tags(['Dashboard-diaspora'])
class DiasporaIndexView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema( operation_description="Returns dashboard data for diaspora main page")
    def list(self, request):
        
        try: 
            
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month",params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB
            
            
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id` IN (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3))"""
            time_clause = f"""WHERE POSTED_DATE1 = CURRENT_DATE {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(daily_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            daily_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
         
            
            # monthly mib
            time_clause = f"""WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            monthy_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthy_mib_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthy_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly installments
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Installment' """
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_installments_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_installments_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly TRANSFercosts
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Transfer Cost' """
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_transferCosts_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_transferCosts_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Deposit' """
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            monthly_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY ADDITIONAL DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Additional Deposit' """
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(additional_deposits_sql, "Error fetching Daily MIB",params=(), dict_cursor=True)
            additional_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0

            # --- 2. Overdue Collections
            
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN 
            (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3)); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today",params=(), dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 and pl.`lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN 
            (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3));
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections",params=(), dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN 
            (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3));
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold",params=(), dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE
                  AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN 
            (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3));
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold",params=(), dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            TotalPurchasePrices_sql = """SELECT SUM(`purchase_price`) AS 'pp', SUM(`balance_lcy`) AS b, SUM(`total_paid`) AS tp 
            FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3); """
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching Total Purchase Prices",params=(), dict_cursor=True)
            TotalPurchasePrices = row['pp'] or 0 if row else 0
            TotalPaid = row['tp'] or 0 if row else 0
            TotalOutstandingBalances = row['b'] or 0 if row else 0
            
            TotalActiveSales_sql = """SELECT COUNT(`lead_file_no`)
            AS 'al' FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` IN (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3);"""
            row = fetch_one(TotalActiveSales_sql, "Error fetching Total Active Sales",params=(), dict_cursor=True)    
            TotalActiveSales = row['al'] or 0 if row else 0
            
            Total_Expected_Collections_sql = """ SELECT SUM(total_to_collect) as total_to_collect, SUM(installments_due) as total_installments_due, SUM(previous_unpaid) as total_previous_unpaid, SUM(`overdue_collections`) 
                    as total_overdue FROM `portfolio_lines` WHERE `lead_file_no` IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0  AND 
                    `customer_lead_source_id` IN (SELECT `name` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id` = 3));"""
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching Total Expected Collections",params=(), dict_cursor=True)
            Total_Expected_Collections = row['total_to_collect'] or 0 if row else 0
            CurrentExpectedInstallments = row['total_installments_due'] or 0 if row else 0
            AccruedMissedInstallments = row['total_previous_unpaid'] or 0 if row else 0
            OverdueInstallments = row['total_overdue'] or 0 if row else 0
            
            
            # DIASPORA REGIONS
            all_regions_sql = """SELECT sc.cat_lead_source_id,sc.name,sc.manager_name,sc.id as region_id,IFNULL(SUM(lf.total_paid), 0) AS total_paid_sum, COUNT(DISTINCT ls.leadsource_id) 
            AS leadsource_count FROM leads_leadsourcesubcategory sc LEFT JOIN leads_leadsource ls ON ls.lead_source_subcategory_id = sc.cat_lead_source_id LEFT JOIN sales_leadfile lf 
            ON lf.customer_lead_source_id = ls.`name` AND lf.lead_file_status_dropped != 'Yes' WHERE sc.lead_source_category_id = 3 GROUP BY sc.cat_lead_source_id,sc.name;"""
            
            try:
                conn = connections["reports"]
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(all_regions_sql)
                    diaspora_regions_data = cur.fetchall()
            except Exception as e:
                return Response({"error fetching all teams data": str(e)}, status=500)
            
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                "markreting_month": marketing_month,
                
                "MIB":
                    {
                        "Daily_MIB": daily_mib_data,
                        "Monthly_MIB": monthy_mib_data,
                        "Monthly_Transfer_Costs": monthly_transferCosts_data,
                        "Monthly_Deposits": monthly_deposits_data,
                        "Monthly_Additional_Deposits": additional_deposits_data,
                        "Monthly_Installments": monthly_installments_data,
                    },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                    },
                
                "DiasporaRegions": diaspora_regions_data
            })
            
        except Exception as e:
            return Response({"error": str(e)}, status=500)




@swagger_tags(['Dashboard-diaspora'])
class DiasporaRegionView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema( operation_description="Returns dashboard data for diaspora regions individual page",
            manual_parameters=[
            openapi.Parameter(
                name='Region_Id',in_=openapi.IN_QUERY,description='regions id eg "3"',
                type=openapi.TYPE_INTEGER,required=True,
            ),
        ],)
    
    def list(self, request):
    
        Region_Id = request.query_params.get('Region_Id', None)
        if not Region_Id:
            return Response({"error": "Region_Id is required"}, status=400)
        
        try:
            # Current marketing month
            getmonth_sql = """
                SELECT period_start_date, period_end_date 
                FROM `portfolio_lines` 
                ORDER BY `portfolio_lines`.`period_start_date` DESC 
                LIMIT 1;
            """
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month = [period_start_date, period_end_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB ---
            
            # Base WHERE clause for filtering by region
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL 
                AND `lead_file_no` IN (
                    SELECT `lead_file_no` 
                    FROM `sales_leadfile` 
                    WHERE `lead_file_status_dropped` = 0 
                    AND `customer_lead_source_id` IN (
                        SELECT `name` 
                        FROM `leads_leadsource` 
                        WHERE `lead_source_subcategory_id` = %s
                    )
                )
            """
            
            # Daily MIB
            time_clause = "WHERE POSTED_DATE1 = CURRENT_DATE " + where_clause
            transaction_clause = time_clause + " AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"
            
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            # Need 2 Region_Id params (one for each subquery)
            row = fetch_one(daily_mib_sql, "Error fetching daily MIB for the region", params=(Region_Id, Region_Id), dict_cursor=True)
            daily_mib_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly MIB
            time_clause = f"WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"
            transaction_clause = f"{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"
            
            monthly_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_mib_sql, "Error fetching monthly MIB", params=(Region_Id, Region_Id), dict_cursor=True)
            monthly_mib_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Installments
            transaction_clause = f"{time_clause} AND Transaction_type = 'Installment'"
            
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_installments_sql, "Error fetching monthly installments", params=(Region_Id, Region_Id), dict_cursor=True)
            monthly_installments_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Transfer Costs
            transaction_clause = f"{time_clause} AND Transaction_type = 'Transfer Cost'"
            
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_transferCosts_sql, "Error fetching monthly transfer costs", params=(Region_Id, Region_Id), dict_cursor=True)
            monthly_transferCosts_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Deposits
            transaction_clause = f"{time_clause} AND Transaction_type = 'Deposit'"
            
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(monthly_deposits_sql, "Error fetching monthly deposits", params=(Region_Id, Region_Id), dict_cursor=True)
            monthly_deposits_data = (row['posted_total'] - row['canceled_total']) if row else 0
            
            # Monthly Additional Deposits
            transaction_clause = f"{time_clause} AND Transaction_type = 'Additional Deposit'"
            
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt 
                        {transaction_clause}
                    ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt 
                        {transaction_clause}
                    ), 0) AS canceled_total;
            """
            
            row = fetch_one(additional_deposits_sql, "Error fetching monthly additional deposits", params=(Region_Id, Region_Id), dict_cursor=True)
            additional_deposits_data = (row['posted_total'] - row['canceled_total']) if row else 0

            # --- 2. Collections ---
            
            # Installments due today
            installments_today_sql = """
                SELECT COUNT(`no`) AS no 
                FROM `portfolio_lines` 
                WHERE `due_date` = CURRENT_DATE 
                AND `lead_file_no` IN (
                    SELECT `lead_file_no` 
                    FROM `sales_leadfile` 
                    WHERE `lead_file_status_dropped` = 0 
                    AND `customer_lead_source_id` IN (
                        SELECT `name` 
                        FROM `leads_leadsource` 
                        WHERE `lead_source_subcategory_id` = %s
                    )
                );
            """
            
            row = fetch_one(installments_today_sql, "Error fetching installments due today", params=(Region_Id,), dict_cursor=True)
            installments_due_today = (row['no'] or 0) if row else 0
            
            # Overdue collections
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 
                AND pl.`lead_file_no` IN (
                    SELECT `lead_file_no` 
                    FROM `sales_leadfile` 
                    WHERE `lead_file_status_dropped` = 0 
                    AND `customer_lead_source_id` IN (
                        SELECT `name` 
                        FROM `leads_leadsource` 
                        WHERE `lead_source_subcategory_id` = %s
                    )
                );
            """
            
            row = fetch_one(overdue_sql, "Error fetching overdue collections", params=(Region_Id,), dict_cursor=True)
            overdue_collections_collected = (row['collected_total'] or 0) if row else 0
            overdue_collections = (row['overdue_total'] or 0) if row else 0

            # Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                AND lead_file_status_dropped = 0 
                AND total_paid < deposit_threshold 
                AND `customer_lead_source_id` IN (
                    SELECT `name` 
                    FROM `leads_leadsource` 
                    WHERE `lead_source_subcategory_id` = %s
                );
            """
            
            row = fetch_one(sales_below_threshold_sql, "Error fetching sales below threshold", params=(Region_Id,), dict_cursor=True)
            sales_deposits_below_threshold = (row['al'] or 0) if row else 0

            # Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                AND lead_file_status_dropped = 0 
                AND total_paid < deposit_threshold 
                AND Additional_deposit_date < CURRENT_DATE
                AND `customer_lead_source_id` IN (
                    SELECT `name` 
                    FROM `leads_leadsource` 
                    WHERE `lead_source_subcategory_id` = %s
                );
            """
            
            row = fetch_one(overdue_below_threshold_sql, "Error fetching overdue below threshold", params=(Region_Id,), dict_cursor=True)
            overdue_below_threshold = (row['al'] or 0) if row else 0
            
            # --- 3. Sales Overview ---
            
            # Total purchase prices and balances
            TotalPurchasePrices_sql = """
                SELECT 
                    SUM(`purchase_price`) AS 'pp', 
                    SUM(`balance_lcy`) AS b, 
                    SUM(`total_paid`) AS tp 
                FROM `sales_leadfile` 
                WHERE `lead_file_status_dropped` = 0 
                AND `customer_lead_source_id` IN (
                    SELECT `name` 
                    FROM `leads_leadsource` 
                    WHERE `lead_source_subcategory_id` = %s
                );
            """
            
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching total purchase prices", params=(Region_Id,), dict_cursor=True)
            TotalPurchasePrices = (row['pp'] or 0) if row else 0
            TotalPaid = (row['tp'] or 0) if row else 0
            TotalOutstandingBalances = (row['b'] or 0) if row else 0
            
            # Total active sales
            TotalActiveSales_sql = """
                SELECT COUNT(`lead_file_no`) AS 'al' 
                FROM `sales_leadfile` 
                WHERE `lead_file_status_dropped` = 0 
                AND `customer_lead_source_id` IN (
                    SELECT `name` 
                    FROM `leads_leadsource` 
                    WHERE `lead_source_subcategory_id` = %s
                );
            """
            
            row = fetch_one(TotalActiveSales_sql, "Error fetching total active sales", params=(Region_Id,), dict_cursor=True)    
            TotalActiveSales = (row['al'] or 0) if row else 0
            
            # --- 4. Monthly Expected Collections ---
            
            Total_Expected_Collections_sql = """
                SELECT 
                    SUM(total_to_collect) as total_to_collect, 
                    SUM(installments_due) as total_installments_due, 
                    SUM(previous_unpaid) as total_previous_unpaid, 
                    SUM(`overdue_collections`) as total_overdue 
                FROM `portfolio_lines` 
                WHERE `lead_file_no` IN (
                    SELECT `lead_file_no` 
                    FROM `sales_leadfile` 
                    WHERE `lead_file_status_dropped` = 0  
                    AND `customer_lead_source_id` IN (
                        SELECT `name` 
                        FROM `leads_leadsource` 
                        WHERE `lead_source_subcategory_id` = %s
                    )
                );
            """
            
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching total expected collections", params=(Region_Id,), dict_cursor=True)
            Total_Expected_Collections = (row['total_to_collect'] or 0) if row else 0
            CurrentExpectedInstallments = (row['total_installments_due'] or 0) if row else 0
            AccruedMissedInstallments = (row['total_previous_unpaid'] or 0) if row else 0
            OverdueInstallments = (row['total_overdue'] or 0) if row else 0
            
            # --- 5. Regions Lead Sources ---
            
            all_regions_lead_sources = """
                SELECT 
                    `id`, `name`, `description`, `ongoing_sales`, `dropped_sales`, 
                    `completed_sales`, `active_leads`, `dormant_leads`,
                    `last_updated`, `manager_name`, `manager_id` 
                FROM `leads_leadsource` 
                WHERE `lead_source_subcategory_id` = %s;
            """

            # --- 6. Region details --- 
            region_data = LeadSourceSubCategory.objects.filter(id=Region_Id).values(
               'id', 'cat_lead_source_id', 'lead_source_category', 'name', 'manager_name', 'manager_id'
            ).first()
            
            try:
                conn = connections["reports"]
                conn.ensure_connection()
                with conn.connection.cursor(MySQLdb.cursors.DictCursor) as cur:
                    cur.execute(all_regions_lead_sources, (Region_Id,))
                    lead_sources_data = cur.fetchall()
            except Exception as e:
                return Response({"error": f"Error fetching all teams data: {str(e)}"}, status=500)
            
            # --- Close all connections ---
            for c in connections.all():
                c.close()

            return Response({
                "marketing_month": marketing_month,
                "Region_Details": region_data,
                
                "MIB": {
                    "Daily_MIB": daily_mib_data,
                    "Monthly_MIB": monthly_mib_data,
                    "Monthly_Transfer_Costs": monthly_transferCosts_data,
                    "Monthly_Deposits": monthly_deposits_data,
                    "Monthly_Additional_Deposits": additional_deposits_data,
                    "Monthly_Installments": monthly_installments_data,
                },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                },
                
                "DiasporaRegions": lead_sources_data
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)


@swagger_tags(['Dashboard-diaspora'])
class DiasporaRegionLeadSourceView(ViewSet):
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema( operation_description="Returns dashboard data for diaspora regions LEADSOURCE DATA",
            manual_parameters=[
            openapi.Parameter(
                name='LeadSource_id',in_=openapi.IN_QUERY,description='LeadSource_id eg "3"',
                type=openapi.TYPE_INTEGER,required=True,
            ),
        ],)
    def list(self, request):
        
        LeadSource_id = request.query_params.get('LeadSource_id', None)
        if not LeadSource_id:
            return Response({"error": "LeadSource_id is required"}, status=400)
        
        
        try:
            
            # current marketing month
            getmonth_sql = """
                SELECT period_start_date,period_end_date FROM `portfolio_lines` ORDER BY `portfolio_lines`.`period_start_date` DESC limit 1;"""
            row = fetch_one(getmonth_sql, "Error fetching current marketing month",params=(), dict_cursor=True)
            period_start_date = row['period_start_date'] if row else None
            period_end_date = row['period_end_date'] if row else None
            
            marketing_month= [period_end_date, period_start_date] if period_start_date and period_end_date else None
            
            # --- 1. Daily MIB
            
            
            where_clause = """
                AND Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id`=%s)"""
            time_clause = "WHERE POSTED_DATE1 = CURRENT_DATE " + where_clause
            transaction_clause = time_clause + " AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"
            daily_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            
            row = fetch_one(daily_mib_sql, "Error fetching  daily  MIB for the region", params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            daily_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            
            # monthly mib
            time_clause = f"""WHERE MONTH(POSTED_DATE1) = MONTH(CURRENT_DATE) AND YEAR(POSTED_DATE1) = YEAR(CURRENT_DATE) {where_clause}"""
            transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""
            monthy_mib_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthy_mib_sql, "Error fetching Daily MIB",params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            monthy_mib_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly installments
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Installment' """
            monthly_installments_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_installments_sql, "Error fetching Daily MIB",params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            monthly_installments_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # monthly TRANSFercosts
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Transfer Cost' """
            monthly_transferCosts_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_transferCosts_sql, "Error fetching Daily MIB",params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            monthly_transferCosts_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Deposit' """
            monthly_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(monthly_deposits_sql, "Error fetching Daily MIB",params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            monthly_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0
            
            # MONTHLY ADDITIONAL DEPOSITS
            transaction_clause = f"""{time_clause} AND Transaction_type = 'Additional Deposit' """
            additional_deposits_sql = f"""
                SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;
            """
            row = fetch_one(additional_deposits_sql, "Error fetching Daily MIB",params=(LeadSource_id,LeadSource_id,), dict_cursor=True)
            additional_deposits_data = row['posted_total'] - row['canceled_total'] if row else 0

            # --- 2. Overdue Collections
            
            inatallments_today_sql = """SELECT COUNT(`no`)AS no FROM `portfolio_lines` WHERE `due_date`= CURRENT_DATE AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` =%s); """
            row = fetch_one(inatallments_today_sql, "Error fetching installments due today",params=(LeadSource_id), dict_cursor=True)
            installments_due_today = row['no'] or 0 if row else 0
            
            
            overdue_sql = """
                SELECT 
                    SUM(pl.overdue_collections_collected) AS collected_total, 
                    SUM(pl.overdue_collections) AS overdue_total
                FROM portfolio_lines pl
                JOIN (
                    SELECT period_start_date, period_end_date 
                    FROM portfolio_lines 
                    ORDER BY period_start_date DESC 
                    LIMIT 1
                ) latest 
                ON pl.period_start_date = latest.period_start_date 
                   AND pl.period_end_date = latest.period_end_date 
                WHERE pl.overdue_collections <> 0 and pl.`lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` =%s);
            """
            row = fetch_one(overdue_sql, "Error fetching overdue collections",params=(LeadSource_id), dict_cursor=True)
            overdue_collections_collected = row['collected_total'] or 0 if row else 0
            overdue_collections = row['overdue_total'] or 0 if row else 0

            # --- 3. Sales below threshold
            sales_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold AND `lead_file_no`
            IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id`=%s);
            """
            row = fetch_one(sales_below_threshold_sql, "Error fetching Sales below Threshold",params=(LeadSource_id), dict_cursor=True)
            sales_deposits_below_threshold = row['al'] or 0 if row else 0

            # --- 4. Overdue below threshold
            overdue_below_threshold_sql = """
                SELECT COUNT(lead_file_no) AS al 
                FROM sales_leadfile 
                WHERE marketer_id IS NOT NULL 
                  AND lead_file_status_dropped=0 
                  AND total_paid < deposit_threshold 
                  AND Additional_deposit_date < CURRENT_DATE
                  AND `customer_lead_source_id`= %s;
            """
            row = fetch_one(overdue_below_threshold_sql, "Error fetching Overdue below Threshold",params=(LeadSource_id), dict_cursor=True)
            overdue_below_threshold = row['al'] or 0 if row else 0
            
            
            TotalPurchasePrices_sql = """SELECT SUM(`purchase_price`) AS 'pp', SUM(`balance_lcy`) AS b, SUM(`total_paid`) AS tp 
            FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` =%s; """
            row = fetch_one(TotalPurchasePrices_sql, "Error fetching Total Purchase Prices",params=(LeadSource_id), dict_cursor=True)
            TotalPurchasePrices = row['pp'] or 0 if row else 0
            TotalPaid = row['tp'] or 0 if row else 0
            TotalOutstandingBalances = row['b'] or 0 if row else 0
            
            TotalActiveSales_sql = """SELECT COUNT(`lead_file_no`)
            AS 'al' FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND `customer_lead_source_id` =%s;"""
            row = fetch_one(TotalActiveSales_sql, "Error fetching Total Active Sales",params=(LeadSource_id), dict_cursor=True)    
            TotalActiveSales = row['al'] or 0 if row else 0
            
            Total_Expected_Collections_sql = """ SELECT SUM(total_to_collect) as total_to_collect, SUM(installments_due) as total_installments_due, SUM(previous_unpaid) as total_previous_unpaid, SUM(`overdue_collections`) 
                    as total_overdue FROM `portfolio_lines` WHERE `lead_file_no` IN (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0  AND 
                    `customer_lead_source_id` =%s);"""
            row = fetch_one(Total_Expected_Collections_sql, "Error fetching Total Expected Collections",params=(LeadSource_id), dict_cursor=True)
            Total_Expected_Collections = row['total_to_collect'] or 0 if row else 0
            CurrentExpectedInstallments = row['total_installments_due'] or 0 if row else 0
            AccruedMissedInstallments = row['total_previous_unpaid'] or 0 if row else 0
            OverdueInstallments = row['total_overdue'] or 0 if row else 0
            
            
            leadsource_data = LeadSource.objects.filter(id=LeadSource_id).values(
                'id', 
                'leadsource_id',
                'lead_source_subcategory',
                'name',
                'description',
                'qr_code',
                'ref_code',
                'link',
                'sales',
                'ongoing_sales',
                'dropped_sales',
                'completed_sales',
                'active_leads',
                'dormant_leads',
                'manager',
                'manager_name',
                'managing_team',
                'last_updated',
                'created_at',
            ).first()
            

            # --- Close all connections
            for c in connections.all():
                c.close()

            return Response({
                'leadsource': leadsource_data,
                "markreting_month": marketing_month,
                
                "MIB":
                    {
                        "Daily_MIB": daily_mib_data,
                        "Monthly_MIB": monthy_mib_data,
                        "Monthly_Transfer_Costs": monthly_transferCosts_data,
                        "Monthly_Deposits": monthly_deposits_data,
                        "Monthly_Additional_Deposits": additional_deposits_data,
                        "Monthly_Installments": monthly_installments_data,
                    },
            
                "Collections": {
                    "Installments_Due_Today": installments_due_today,
                    "Overdue_Collections_Collected": overdue_collections_collected,
                    "Overdue_Collections": overdue_collections,
                    "ALL_Overdue_Collections": overdue_collections_collected + overdue_collections,
                    "Sales_Deposits_Below_Threshold": sales_deposits_below_threshold,
                    "Overdue_Below_Threshold": overdue_below_threshold,
                },
                "SalesOverview": {
                    "TotalPurchasePrices": TotalPurchasePrices,
                    "TotalPaid": TotalPaid,
                    "TotalOutstandingBalances": TotalOutstandingBalances,
                    "TotalActiveSales": TotalActiveSales,
                },
                "MonthlyExpectedCollections": {
                    "Total_Expected_Collections": Total_Expected_Collections,
                    "CurrentExpectedInstallments": CurrentExpectedInstallments,
                    "AccruedMissedInstallments": AccruedMissedInstallments,
                    "OverdueInstallments": OverdueInstallments,
                    },
                
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)

