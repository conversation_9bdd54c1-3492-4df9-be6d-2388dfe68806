
from datetime import datetime, timed<PERSON><PERSON>
from rest_framework.views import APIView
from rest_framework.viewsets import ViewSet
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import AllowAny
from drf_yasg import openapi

from django.db.models import OuterRef, Ex<PERSON>, Sum, Q, F
from django.db.models.functions import TruncDate

from amani.services.phone_lookup import User
from inventory.models import PlotBooking
from inventory.serializers import PlotBookingSerializer
from logistics.models import SiteVisit
from logistics.serializers import SiteVisitSerializer
from receipts.models import CancelledReceipt, PostedReceipt
from receipts.serializers import PostedReceiptSerializer, CancelledReceiptSerializer
from sales.models import MarketersTarget
from users.models import Teams





def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class GMSiteVisitsReport(APIView):
    """
    View to retrieve general manager site visit statistics.

    This endpoint provides comprehensive site visit statistics for a specific region
    within a date range, including status breakdowns and detailed site visit data.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager site visit statistics",
        operation_description="""
        Retrieves comprehensive site visit statistics for general manager dashboard.

        **Features:**
        - Filter by region and date range
        - Status-based filtering (default: 'Approved')
        - Complete status breakdown counts
        - Detailed site visit data with serialized information

        **Status Options:**
        - Pending, Approved, In Progress, Completed, Reviewed, Cancelled, Rejected

        **Regions:**
        - PACIFIC TEAM, ATLANTIC TEAM

        **Example Usage:**
        ```
        GET /api/gm-site-visits-report/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&status=Approved
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                'region',
                openapi.IN_QUERY,
                description="Region to filter site visits by (required)",
                type=openapi.TYPE_STRING,
                required=True,
                example="PACIFIC TEAM"
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Start date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-01"
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="End date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description="Status to filter site visits by (optional, default: 'Approved')",
                type=openapi.TYPE_STRING,
                enum=['Pending', 'Approved', 'In Progress', 'Completed', 'Reviewed', 'Cancelled', 'Rejected'],
                required=False,
                default='Approved',
                example="Approved"
            ),
        ],
        responses={
            200: openapi.Response(
                description="Site visit statistics retrieved successfully",
                examples={
                    "application/json": {
                        "message": "15 Approved site visits found for region PACIFIC TEAM between 2024-01-01 and 2024-01-31.",
                        "total_site_visits": 25,
                        "status_counts": {
                            "pending": 3,
                            "approved": 15,
                            "in_progress": 4,
                            "completed": 2,
                            "reviewed": 1,
                            "cancelled": 0,
                            "rejected": 0
                        },
                        "site_visits": [
                            {
                                "id": 1,
                                "pickup_date": "2024-01-15",
                                "status": "Approved",
                                "marketer": {
                                    "id": 1,
                                    "name": "John Doe",
                                    "region": "PACIFIC TEAM"
                                }
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(
                description="Bad request - Missing required parameters",
                examples={
                    "application/json": {
                        "error": "Region is required"
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    "application/json": {
                        "error": "An unexpected error occurred"
                    }
                }
            )
        }
    )
    
    def get(self, request):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        status = params.get('status', '')
        marketer = params.get('marketer', '')
        team = params.get('team', '')

        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)

        # site visite for the specified region and date range
        site_visits = SiteVisit.objects.filter(created_at__range=[start_date, end_date], marketer__region=region).order_by('-created_at')

        # Get counts for all statuses        
        status_counts = {
            'pending': site_visits.filter(status='Pending').count(),
            'approved': site_visits.filter(status='Approved').count(),
            'in_progress': site_visits.filter(status='In Progress').count(),
            'completed': site_visits.filter(status='Completed').count(),
            'reviewed': site_visits.filter(status='Reviewed').count(),
            'cancelled': site_visits.filter(status='Cancelled').count(),
            'rejected': site_visits.filter(status='Rejected').count(),
        }

        # Filter by status
        if status: 
            site_visits = site_visits.filter(status=status)

        # generate teams with number of bookings 
        teams_site_visits = {}
        for team in site_visits.values_list('marketer__team__team', flat=True).distinct():
            teams_site_visits[team] = site_visits.filter(marketer__team__team=team).count()

        # filter by marketer 
        if marketer and team == '':
            site_visits = site_visits.filter(marketer__employee_no=marketer)
        
        # filter by team 
        if team and marketer == '':
            site_visits = site_visits.filter(marketer__team__team=team)

        # filter by both team and marketer
        if team and marketer: 
            site_visits = site_visits.filter(marketer__team__team=team, marketer__employee_no=marketer)


        

        serializer = SiteVisitSerializer(site_visits, many=True)        

        data = {
            'message': f"{site_visits.count()} {status} site visits found for region {region} between {start_date} and {end_date}.",
            "total_site_visits": site_visits.count(),
            "status_counts": status_counts,
            "teams_site_visits": teams_site_visits,
            'site_visits': serializer.data
        }
        return Response(data)
    


class GMBookingsReport(APIView):
    """
    View to retrieve general manager booking statistics.

    This endpoint provides comprehensive plot booking statistics for a specific region
    within a date range, including booking type and status breakdowns with detailed booking data.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager booking statistics",
        operation_description="""
        Retrieves comprehensive plot booking statistics for general manager dashboard.

        **Features:**
        - Filter by region and date range (required)
        - Optional booking type filtering
        - Complete booking type breakdown (MPESA, SPECIAL, OTHER, DIASPORA)
        - Complete booking status breakdown (OPEN, OLD, TIMED, DONE, WAITING, SUSPENDED, REJECTED, REVERTED)
        - Detailed plot booking data with serialized information

        **Regions:**
        - PACIFIC TEAM, ATLANTIC TEAM

        **Booking Types:**
        - MPESA: Mobile money bookings
        - SPECIAL: Special arrangement bookings
        - OTHER: Other payment method bookings
        - DIASPORA: Diaspora customer bookings

        **Booking Statuses:**
        - OPEN, OLD, TIMED, DONE, WAITING, SUSPENDED, REJECTED, REVERTED

        **Example Usage:**
        ```
        GET /api/gm-bookings-report/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&booking_type=SPECIAL
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                'region',
                openapi.IN_QUERY,
                description="Region to filter plot bookings by (required)",
                type=openapi.TYPE_STRING,
                required=True,
                example="PACIFIC TEAM"
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Start date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-01"
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="End date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'booking_type',
                openapi.IN_QUERY,
                description="Booking type to filter by (optional)",
                type=openapi.TYPE_STRING,
                enum=['MPESA', 'SPECIAL', 'OTHER', 'DIASPORA'],
                required=False,
                example="SPECIAL"
            ),
        ],
        responses={
            200: openapi.Response(
                description="Booking statistics retrieved successfully",
                examples={
                    "application/json": {
                        "message": "SPECIAL bookings report generated successfully.",
                        "total_bookings": 45,
                        "booking_types_count": {
                            "mpesa": 120,
                            "special": 45,
                            "other": 30,
                            "diaspora": 25
                        },
                        "booking_status_counts": {
                            "open": 15,
                            "old": 5,
                            "timed": 10,
                            "done": 8,
                            "waiting": 4,
                            "suspended": 2,
                            "rejected": 1,
                            "reverted": 0
                        },
                        "plot_bookings": [
                            {
                                "id": 1,
                                "creation_date": "2024-01-15",
                                "booking_type": "SPECIAL",
                                "status": "OPEN",
                                "marketer": {
                                    "id": 1,
                                    "name": "John Doe",
                                    "region": "PACIFIC TEAM"
                                }
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(
                description="Bad request - Missing required parameters",
                examples={
                    "application/json": {
                        "error": "Region is required"
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    "application/json": {
                        "error": "An unexpected error occurred"
                    }
                }
            )
        }
    )
    
    def get(self, request):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        booking_type = params.get('booking_type', '')
        marketer = params.get('marketer', '')
        team = params.get('team', '')


        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)
        
        plot_bookings = PlotBooking.objects.filter(
            creation_date__range=[start_date, end_date],
            marketer__region=region
        ).exclude(status ='TIMED').order_by('-creation_date')

        booking_types_count = {
            'mpesa': plot_bookings.filter(booking_type='MPESA').count(),
            'special': plot_bookings.filter(booking_type='SPECIAL').count(),
            'other': plot_bookings.filter(booking_type='OTHER').count(),
            'diaspora': plot_bookings.filter(booking_type='DIASPORA').count(),
        }

        # booking_status_counts = {
        #     'open': plot_bookings.filter(status='OPEN').count(),
        #     'old': plot_bookings.filter(status='OLD').count(),
        #     'done': plot_bookings.filter(status='DONE').count(),
        #     'waiting': plot_bookings.filter(status='WAITING').count(),
        #     'suspended': plot_bookings.filter(status='SUSPENDED').count(),
        #     'rejected': plot_bookings.filter(status='REJECTED').count(),
        #     'reverted': plot_bookings.filter(status='REVERTED').count(),
        # }

        # Filter by booking type
        if booking_type:
            plot_bookings = plot_bookings.filter(booking_type=booking_type)

        # generate teams with number of bookings 
        teams_bookings = {}
        for team in plot_bookings.values_list('marketer__team__team', flat=True).distinct():
            teams_bookings[team] = plot_bookings.filter(marketer__team__team=team).count()

        # filter by marketer 
        if marketer and team == '':
            plot_bookings = plot_bookings.filter(marketer__employee_no=marketer)
        
        # filter by team 
        if team and marketer == '':
            plot_bookings = plot_bookings.filter(marketer__team__team=team)

        # filter by both team and marketer
        if team and marketer: 
            plot_bookings = plot_bookings.filter(marketer__team__team=team, marketer__employee_no=marketer)

        serializer = PlotBookingSerializer(plot_bookings, many=True)


        data = {
            'message': f"{booking_type} bookings report genereted successfully.",
            'total_bookings': plot_bookings.count(),
            'booking_types_count': booking_types_count,
            # 'booking_status_counts': booking_status_counts,
            'teams_bookings': teams_bookings,
            'plot_bookings': serializer.data,
        }
        return Response(data)


class GMMIBFromReceiptsReports(APIView):
    """
    View to retrieve general manager MIB (Money In Bank) from receipts report.
    
    Provides receipt statistics for a specific region within a date range,
    with optional filtering by marketer and team.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get MIB from receipts report for general manager",
        operation_description="""
        Retrieves comprehensive MIB (Money In Bank) from receipts report.

        **Features:**
        - Filter by region and date range (required)
        - Optional marketer and team filtering
        - Daily performance breakdown by team and member
        - Excludes cancelled receipts from posted receipts

        **Example Usage:**
        ```
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&Marketer=John Doe
        ```
        """,
        manual_parameters=[
            openapi.Parameter('region', openapi.IN_QUERY, 
                description="Region to filter receipts by (required)", 
                type=openapi.TYPE_STRING, required=True, example="PACIFIC TEAM"),
            openapi.Parameter('start_date', openapi.IN_QUERY, 
                description="Start date (YYYY-MM-DD, required)", 
                type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, required=True, example="2024-01-01"),
            openapi.Parameter('end_date', openapi.IN_QUERY, 
                description="End date (YYYY-MM-DD, required)", 
                type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, required=True, example="2024-01-31"),
            openapi.Parameter('Marketer', openapi.IN_QUERY, 
                description="Filter by specific marketer (optional)", 
                type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('Team', openapi.IN_QUERY, 
                description="Filter by specific team (optional)", 
                type=openapi.TYPE_STRING, required=False),
        ],
        responses={
            200: "MIB report retrieved successfully",
            400: "Bad request - Missing required parameters",
            404: "No receipts found for the given criteria",
            500: "Internal server error"
        }
    )
    def get(self, request):
        # Validate params
        validation = self._validate_parameters(request.query_params)
        if validation.get("error"):
            return Response(validation, status=validation.get("status", 400))
        params = validation["params"]

        # Fetch region employees (CONVERSION department) and their employee_no list
        region_employees_qs = User.objects.filter(region=params["region"], department__dp_name="CONVERSION", status="Active")

        employee_numbers = list(region_employees_qs.values_list("employee_no", flat=True))
        if not employee_numbers:
            return Response({
                "error": f"No users found in region '{params['region']}'",
                "message": "Please check if the region name is correct"
            }, status=400)

        # Build posted_receipts queryset with DB-level exclusion of cancelled receipts
        posted_qs = self._get_posted_receipts_qs(
            employee_numbers,
            params["start_date"],
            params["end_date"],
            marketer=params.get("marketer"),
            team=params.get("team")
        )

        # If there are no receipts, return 404
        if not posted_qs.exists():
            return Response({
                "error": "No receipts found for the given criteria",
                "filters_applied": self._get_filters_dict(params)
            }, status=404)

        # Summary aggregates (DB-level)
        posted_aggregates = posted_qs.aggregate(
            posted_total_amount=Sum("Amount_LCY"),
            posted_total_balance=Sum("Balance_LCY")
        )
        # fallback for count using .count()
        posted_count = posted_qs.count()
        posted_total_amount = posted_aggregates.get("posted_total_amount") or 0
        posted_total_balance = posted_aggregates.get("posted_total_balance") or 0

        # Build teams MIB and member performance (optimized)
        teams_mib_data = []
        if not params.get("marketer"):
            teams_mib_data = self._calculate_teams_performance(
                params["region"],
                params.get("team"),
                params["start_date"],
                params["end_date"],
                region_employees_qs,
                posted_qs
            )

        # Serialize posted receipts
        serialized_page = PostedReceiptSerializer(posted_qs.order_by("-POSTED_DATE1"), many=True).data

        posted_data = {
            "count": posted_count,
            "total_amount": posted_total_amount,
            "total_balance": posted_total_balance,
            "serialized_data": serialized_page
        }

        response = self._build_response(params, posted_data, teams_mib_data)
        return Response(response)

    # ---------------------------
    # Helpers
    # ---------------------------
    def _validate_parameters(self, query_params):
        region = (query_params.get("region") or "").strip()
        start_date = (query_params.get("start_date") or "").strip()
        end_date = (query_params.get("end_date") or "").strip()
        marketer = (query_params.get("Marketer") or "").strip()
        team = (query_params.get("Team") or "").strip()

        if not region:
            return {"error": "Region is required", "status": 400}
        if not start_date or not end_date:
            return {"error": "Start date and end date are required", "status": 400}

        # Validate date format
        try:
            datetime.strptime(start_date, "%Y-%m-%d")
            datetime.strptime(end_date, "%Y-%m-%d")
        except ValueError:
            return {"error": "Dates must be in YYYY-MM-DD format", "status": 400}

        return {
            "params": {
                "region": region,
                "start_date": start_date,
                "end_date": end_date,
                "marketer": marketer or None,
                "team": team or None
            }
        }


    def _get_posted_receipts_qs(self, employee_numbers, start_date, end_date, marketer=None, team=None):
        """
        Build a PostedReceipt queryset that excludes cancelled receipts using Exists subquery.
        """
        filters = {
            "POSTED_DATE1__range": [start_date, end_date],
            "Marketer__in": employee_numbers
        }
        if marketer:
            filters["Marketer"] = marketer
        if team:
            filters["Teams"] = team

        cancelled_subq = CancelledReceipt.objects.filter(
            Receipt_No=OuterRef("Receipt_No"),
            Amount_LCY=OuterRef("Amount_LCY")
        )

        qs = PostedReceipt.objects.filter(**filters).exclude(Exists(cancelled_subq))
        return qs

    
    def _calculate_teams_performance(self, region, team_filter, start_date, end_date, region_employees_qs, posted_qs):
        """
        Returns list of team dicts with:
        - team_name
        - team_mib_posted (sum from posted_qs)
        - members: list of members with daily breakdowns and targets
        """
        # Precompute date range
        start_dt = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_dt = datetime.strptime(end_date, "%Y-%m-%d").date()
        days_count = (end_dt - start_dt).days + 1
        date_list = [start_dt + timedelta(days=i) for i in range(days_count)]

        # Get teams in region (filtered if requested)
        teams_qs = Teams.objects.filter(office=region, inactive=False)
        if team_filter:
            teams_qs = teams_qs.filter(team=team_filter)

        # Map employee_no -> User instance (for fast lookup)
        employees_by_emp_no = {u.employee_no: u for u in region_employees_qs}

        # Fetch latest marketer targets for region employees in one pass:
        # Strategy: order by marketer_no, -id (or -created_at) and pick first per marketer
        targets_qs = MarketersTarget.objects.filter(marketer_no__status="Active",marketer_no__employee_no__in=employees_by_emp_no.keys()).order_by("-id")
        latest_targets = {}
        for t in targets_qs:
            emp_no = t.marketer_no.employee_no
            if emp_no not in latest_targets:
                latest_targets[emp_no] = t

        # Precompute team totals from posted_qs (DB aggregation grouped by Teams)
        team_totals_qs = posted_qs.values("Teams").annotate(team_mib_posted=Sum("Amount_LCY"))
        team_totals = {item["Teams"]: item["team_mib_posted"] or 0 for item in team_totals_qs}

        teams_data = []

        for team in teams_qs:
            team_name = team.team
            # Get team members (User) who belong to this team AND are in region_employees
            team_members = [
                emp for emp in employees_by_emp_no.values()
                if getattr(emp, "team", None) and getattr(emp.team, "team", None) == team_name
            ]

            members_data = []
            for member in team_members:
                mdata = self._calculate_member_performance_optimized(
                    member,
                    # start_date,
                    # end_date,
                    start_dt,
                    days_count,
                    latest_targets.get(member.employee_no),
                    posted_qs
                )
                if mdata:
                    members_data.append(mdata)

            teams_data.append({
                "team_name": team_name,
                "members": members_data,
                "team_mib_posted": team_totals.get(team_name, 0)
            })

        return teams_data
    

    def _calculate_member_performance_optimized(self, member, start_dt_date, days_count, marketer_target_obj, posted_qs_root):
        if not marketer_target_obj:
            return None

        daily_target = marketer_target_obj.daily_target or 0
        total_target = daily_target * days_count

        member_qs = posted_qs_root.filter(Marketer=member.employee_no)

        daily_totals_qs = (
            member_qs
            .annotate(posted_date_only=TruncDate('POSTED_DATE1'))
            .values("posted_date_only")
            .annotate(total=Sum("Amount_LCY"))
            .order_by('-posted_date_only')
        )

        daily_totals = {str(item["posted_date_only"]): (item["total"] or 0) for item in daily_totals_qs}

        member_mib = member_qs.aggregate(member_mib=Sum("Amount_LCY")).get("member_mib") or 0

        member_data = {
            "member_name": f"{member.first_name} {member.last_name}",
            "member_target": total_target,
            "member_mib": member_mib
        }

        for i in range(days_count):
            current_date = start_dt_date + timedelta(days=i)
            member_data[str(current_date)] = daily_totals.get(str(current_date), 0)

        return member_data
    

    def _process_receipts(self, receipts_list, serializer_class):
        """
        Kept for compatibility, but in optimized code we serialize queryset directly.
        This function accepts either a queryset or list; if queryset, prefer DB aggregates.
        """
        if not receipts_list:
            return {
                "count": 0,
                "total_amount": 0,
                "total_balance": 0,
                "serialized_data": []
            }

        # If it's a queryset, use DB aggregate for totals
        try:
            total_amount = receipts_list.aggregate(total_amount=Sum("Amount_LCY")).get("total_amount") or 0
            total_balance = receipts_list.aggregate(total_balance=Sum("Balance_LCY")).get("total_balance") or 0
            count = receipts_list.count()
        except Exception:
            # fallback for lists
            total_amount = sum(getattr(r, "Amount_LCY", 0) or 0 for r in receipts_list)
            total_balance = sum(getattr(r, "Balance_LCY", 0) or 0 for r in receipts_list)
            count = len(receipts_list)

        serialized_data = serializer_class(receipts_list[:1000], many=True).data if not hasattr(receipts_list, "iterator") else serializer_class(receipts_list, many=True).data

        return {
            "count": count,
            "total_amount": total_amount,
            "total_balance": total_balance,
            "serialized_data": serialized_data
        }

    def _get_filters_dict(self, params):
        return {
            "region": params["region"],
            "start_date": params["start_date"],
            "end_date": params["end_date"],
            "marketer": params.get("marketer") or None,
            "team": params.get("team") or None,
        }

    def _build_response(self, params, posted_data, teams_mib_data):
        return {
            "message": f"MIB from receipts report for region {params['region']} between {params['start_date']} and {params['end_date']}.",
            "summary": {
                "total_receipts_count": posted_data["count"],
                "posted_receipts_count": posted_data["count"],
                "posted_total_amount": posted_data["total_amount"],
                "posted_total_balance": posted_data["total_balance"]
            },
            "group": {
                "teams_mib": teams_mib_data
            },
            "data": {
                "posted_receipts": posted_data["serialized_data"]
            },
            "filters_applied": self._get_filters_dict(params)
        }
    

    