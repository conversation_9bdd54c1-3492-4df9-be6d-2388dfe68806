
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi

from inventory.models import PlotBooking
from inventory.serializers import PlotBookingSerializer
from logistics.models import SiteVisit
from logistics.serializers import SiteVisitSerializer




def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class GMSiteVisitsReport(APIView):
    """
    View to retrieve general manager site visit statistics.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager site visit statistics",
        responses={200: 'Site visit statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        status = params.get('status', 'Approved')

        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)

        # site visite for the specified region and date range
        site_visits = SiteVisit.objects.filter(pickup_date__range=[start_date, end_date], marketer__region=region)

        # Filter by status
        selected_status_site_visits = site_visits.filter(status=status)

        # Get counts for all statuses        
        status_counts = {
            'pending': site_visits.filter(status='Pending').count(),
            'approved': site_visits.filter(status='Approved').count(),
            'in_progress': site_visits.filter(status='In Progress').count(),
            'completed': site_visits.filter(status='Completed').count(),
            'reviewed': site_visits.filter(status='Reviewed').count(),
            'cancelled': site_visits.filter(status='Cancelled').count(),
            'rejected': site_visits.filter(status='Rejected').count(),
        }
        

        serializer = SiteVisitSerializer(selected_status_site_visits, many=True)        

        data = {
            'message': f"{selected_status_site_visits.count()} {status} site visits found for region {region} between {start_date} and {end_date}.",
            "total_site_visits": site_visits.count(),
            "status_counts": status_counts,
            'site_visits': serializer.data
        }
        return Response(data)
    


class GMBookingsReport(APIView):
    """
    View to retrieve general manager special booking statistics.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager special booking statistics",
        responses={200: 'Special booking statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        booking_type = params.get('booking_type', '')


        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)
        
        plot_bookings = PlotBooking.objects.filter(
            creation_date__range=[start_date, end_date],
            marketer__region=region
        )

        booking_types_count = {
            'mpesa': plot_bookings.filter(booking_type='MPESA').count(),
            'special': plot_bookings.filter(booking_type='SPECIAL').count(),
            'other': plot_bookings.filter(booking_type='OTHER').count(),
            'diaspora': plot_bookings.filter(booking_type='DIASPORA').count(),
        }

        booking_status_counts = {
            'open': plot_bookings.filter(status='OPEN').count(),
            'old': plot_bookings.filter(status='OLD').count(),
            'timed': plot_bookings.filter(status='TIMED').count(),
            'done': plot_bookings.filter(status='DONE').count(),
            'waiting': plot_bookings.filter(status='WAITING').count(),
            'suspended': plot_bookings.filter(status='SUSPENDED').count(),
            'rejected': plot_bookings.filter(status='REJECTED').count(),
            'reverted': plot_bookings.filter(status='REVERTED').count(),
        }

        # Filter by booking type
        if booking_type:
            plot_bookings = plot_bookings.filter(booking_type=booking_type)

        serializer = PlotBookingSerializer(plot_bookings, many=True)


        data = {
            'message': f"{booking_type} bookings report genereted successfully.",
            'total_bookings': plot_bookings.count(),
            'booking_types_count': booking_types_count,
            'booking_status_counts': booking_status_counts,
            'plot_bookings': serializer.data,
        }
        return Response(data)
    

