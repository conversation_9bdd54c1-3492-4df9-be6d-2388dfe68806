
from rest_framework.views import APIView
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import AllowAny
from drf_yasg import openapi

from django.db.models import Sum

from amani.services.phone_lookup import User
from inventory.models import PlotBooking
from inventory.serializers import PlotBookingSerializer
from logistics.models import SiteVisit
from logistics.serializers import SiteVisitSerializer
from receipts.models import CancelledReceipt, PostedReceipt
from receipts.serializers import PostedReceiptSerializer, CancelledReceiptSerializer





def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class GMSiteVisitsReport(APIView):
    """
    View to retrieve general manager site visit statistics.

    This endpoint provides comprehensive site visit statistics for a specific region
    within a date range, including status breakdowns and detailed site visit data.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager site visit statistics",
        operation_description="""
        Retrieves comprehensive site visit statistics for general manager dashboard.

        **Features:**
        - Filter by region and date range
        - Status-based filtering (default: 'Approved')
        - Complete status breakdown counts
        - Detailed site visit data with serialized information

        **Status Options:**
        - Pending, Approved, In Progress, Completed, Reviewed, Cancelled, Rejected

        **Regions:**
        - PACIFIC TEAM, ATLANTIC TEAM

        **Example Usage:**
        ```
        GET /api/gm-site-visits-report/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&status=Approved
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                'region',
                openapi.IN_QUERY,
                description="Region to filter site visits by (required)",
                type=openapi.TYPE_STRING,
                required=True,
                example="PACIFIC TEAM"
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Start date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-01"
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="End date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'status',
                openapi.IN_QUERY,
                description="Status to filter site visits by (optional, default: 'Approved')",
                type=openapi.TYPE_STRING,
                enum=['Pending', 'Approved', 'In Progress', 'Completed', 'Reviewed', 'Cancelled', 'Rejected'],
                required=False,
                default='Approved',
                example="Approved"
            ),
        ],
        responses={
            200: openapi.Response(
                description="Site visit statistics retrieved successfully",
                examples={
                    "application/json": {
                        "message": "15 Approved site visits found for region PACIFIC TEAM between 2024-01-01 and 2024-01-31.",
                        "total_site_visits": 25,
                        "status_counts": {
                            "pending": 3,
                            "approved": 15,
                            "in_progress": 4,
                            "completed": 2,
                            "reviewed": 1,
                            "cancelled": 0,
                            "rejected": 0
                        },
                        "site_visits": [
                            {
                                "id": 1,
                                "pickup_date": "2024-01-15",
                                "status": "Approved",
                                "marketer": {
                                    "id": 1,
                                    "name": "John Doe",
                                    "region": "PACIFIC TEAM"
                                }
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(
                description="Bad request - Missing required parameters",
                examples={
                    "application/json": {
                        "error": "Region is required"
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    "application/json": {
                        "error": "An unexpected error occurred"
                    }
                }
            )
        }
    )
    
    def get(self, request):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        status = params.get('status', 'Approved')

        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)

        # site visite for the specified region and date range
        site_visits = SiteVisit.objects.filter(pickup_date__range=[start_date, end_date], marketer__region=region).order_by('-pickup_date')

        # Filter by status
        selected_status_site_visits = site_visits.filter(status=status)

        # Get counts for all statuses        
        status_counts = {
            'pending': site_visits.filter(status='Pending').count(),
            'approved': site_visits.filter(status='Approved').count(),
            'in_progress': site_visits.filter(status='In Progress').count(),
            'completed': site_visits.filter(status='Completed').count(),
            'reviewed': site_visits.filter(status='Reviewed').count(),
            'cancelled': site_visits.filter(status='Cancelled').count(),
            'rejected': site_visits.filter(status='Rejected').count(),
        }
        

        serializer = SiteVisitSerializer(selected_status_site_visits, many=True)        

        data = {
            'message': f"{selected_status_site_visits.count()} {status} site visits found for region {region} between {start_date} and {end_date}.",
            "total_site_visits": site_visits.count(),
            "status_counts": status_counts,
            'site_visits': serializer.data
        }
        return Response(data)
    


class GMBookingsReport(APIView):
    """
    View to retrieve general manager booking statistics.

    This endpoint provides comprehensive plot booking statistics for a specific region
    within a date range, including booking type and status breakdowns with detailed booking data.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get general manager booking statistics",
        operation_description="""
        Retrieves comprehensive plot booking statistics for general manager dashboard.

        **Features:**
        - Filter by region and date range (required)
        - Optional booking type filtering
        - Complete booking type breakdown (MPESA, SPECIAL, OTHER, DIASPORA)
        - Complete booking status breakdown (OPEN, OLD, TIMED, DONE, WAITING, SUSPENDED, REJECTED, REVERTED)
        - Detailed plot booking data with serialized information

        **Regions:**
        - PACIFIC TEAM, ATLANTIC TEAM

        **Booking Types:**
        - MPESA: Mobile money bookings
        - SPECIAL: Special arrangement bookings
        - OTHER: Other payment method bookings
        - DIASPORA: Diaspora customer bookings

        **Booking Statuses:**
        - OPEN, OLD, TIMED, DONE, WAITING, SUSPENDED, REJECTED, REVERTED

        **Example Usage:**
        ```
        GET /api/gm-bookings-report/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&booking_type=SPECIAL
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                'region',
                openapi.IN_QUERY,
                description="Region to filter plot bookings by (required)",
                type=openapi.TYPE_STRING,
                required=True,
                example="PACIFIC TEAM"
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Start date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-01"
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="End date for filtering (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'booking_type',
                openapi.IN_QUERY,
                description="Booking type to filter by (optional)",
                type=openapi.TYPE_STRING,
                enum=['MPESA', 'SPECIAL', 'OTHER', 'DIASPORA'],
                required=False,
                example="SPECIAL"
            ),
        ],
        responses={
            200: openapi.Response(
                description="Booking statistics retrieved successfully",
                examples={
                    "application/json": {
                        "message": "SPECIAL bookings report generated successfully.",
                        "total_bookings": 45,
                        "booking_types_count": {
                            "mpesa": 120,
                            "special": 45,
                            "other": 30,
                            "diaspora": 25
                        },
                        "booking_status_counts": {
                            "open": 15,
                            "old": 5,
                            "timed": 10,
                            "done": 8,
                            "waiting": 4,
                            "suspended": 2,
                            "rejected": 1,
                            "reverted": 0
                        },
                        "plot_bookings": [
                            {
                                "id": 1,
                                "creation_date": "2024-01-15",
                                "booking_type": "SPECIAL",
                                "status": "OPEN",
                                "marketer": {
                                    "id": 1,
                                    "name": "John Doe",
                                    "region": "PACIFIC TEAM"
                                }
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(
                description="Bad request - Missing required parameters",
                examples={
                    "application/json": {
                        "error": "Region is required"
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    "application/json": {
                        "error": "An unexpected error occurred"
                    }
                }
            )
        }
    )
    
    def get(self, request):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        booking_type = params.get('booking_type', '')


        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)
        
        plot_bookings = PlotBooking.objects.filter(
            creation_date__range=[start_date, end_date],
            marketer__region=region
        ).order_by('-creation_date')

        booking_types_count = {
            'mpesa': plot_bookings.filter(booking_type='MPESA').count(),
            'special': plot_bookings.filter(booking_type='SPECIAL').count(),
            'other': plot_bookings.filter(booking_type='OTHER').count(),
            'diaspora': plot_bookings.filter(booking_type='DIASPORA').count(),
        }

        booking_status_counts = {
            'open': plot_bookings.filter(status='OPEN').count(),
            'old': plot_bookings.filter(status='OLD').count(),
            'timed': plot_bookings.filter(status='TIMED').count(),
            'done': plot_bookings.filter(status='DONE').count(),
            'waiting': plot_bookings.filter(status='WAITING').count(),
            'suspended': plot_bookings.filter(status='SUSPENDED').count(),
            'rejected': plot_bookings.filter(status='REJECTED').count(),
            'reverted': plot_bookings.filter(status='REVERTED').count(),
        }

        # Filter by booking type
        if booking_type:
            plot_bookings = plot_bookings.filter(booking_type=booking_type)

        serializer = PlotBookingSerializer(plot_bookings, many=True)


        data = {
            'message': f"{booking_type} bookings report genereted successfully.",
            'total_bookings': plot_bookings.count(),
            'booking_types_count': booking_types_count,
            'booking_status_counts': booking_status_counts,
            'plot_bookings': serializer.data,
        }
        return Response(data)
    

class GMMIBFromReceiptsReports(APIView):
    """
    View to retrieve general manager MIB (Money In Bank) from receipts report.

    This endpoint provides comprehensive receipt statistics including both posted and cancelled receipts
    for a specific region within a date range, with additional filtering options for receipt type,
    marketer, and team.
    """
    permission_classes = [AllowAny]
    http_method_names = ['get']

    @swagger_auto_schema(
        tags=['GM-dashboard'],
        operation_summary="Get MIB from receipts report for general manager",
        operation_description="""
        Retrieves comprehensive MIB (Money In Bank) from receipts report for general manager dashboard.

        **Features:**
        - Filter by region and date range (required)
        - Optional receipt type filtering (Posted, Cancelled, or All)
        - Optional marketer and team filtering
        - Separate cancellation date range for cancelled receipts
        - Detailed receipt data with serialized information
        - Separate counts for posted and cancelled receipts

        **Receipt Types:**
        - **Posted**: Only posted receipts within the date range
        - **Cancelled**: Only cancelled receipts (with optional cancellation date range)
        - **All/Empty**: Both posted and cancelled receipts (default behavior)

        **Filtering Logic:**
        - When `receipt_type=Posted`: Returns only posted receipts
        - When `receipt_type=Cancelled`: Returns only cancelled receipts
        - When `receipt_type` is empty: Returns both posted and cancelled receipts
        - Additional filters (Marketer, Team) are applied to the selected receipt types

        **Example Usage:**
        ```
        # Get all receipts for a region and date range
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31

        # Get only posted receipts
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&receipt_type=Posted

        # Get cancelled receipts with specific cancellation date range
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&receipt_type=Cancelled&start_cancellation_date=2024-01-15&end_cancellation_date=2024-01-31

        # Filter by specific marketer and team
        GET /api/gm-mib/?region=PACIFIC TEAM&start_date=2024-01-01&end_date=2024-01-31&Marketer=John Doe&Team=Sales Team A
        ```
        """,
        manual_parameters=[
            openapi.Parameter(
                'region',
                openapi.IN_QUERY,
                description="Region to filter receipts by (required)",
                type=openapi.TYPE_STRING,
                required=True,
                example="PACIFIC TEAM"
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Start date for filtering receipts (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-01"
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="End date for filtering receipts (YYYY-MM-DD format, required)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=True,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'receipt_type',
                openapi.IN_QUERY,
                description="Type of receipts to retrieve (optional, default: all types)",
                type=openapi.TYPE_STRING,
                enum=['Posted', 'Cancelled'],
                required=False,
                example="Posted"
            ),
            openapi.Parameter(
                'start_cancellation_date',
                openapi.IN_QUERY,
                description="Start date for cancellation filtering (YYYY-MM-DD format, only used with receipt_type=Cancelled)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
                example="2024-01-15"
            ),
            openapi.Parameter(
                'end_cancellation_date',
                openapi.IN_QUERY,
                description="End date for cancellation filtering (YYYY-MM-DD format, only used with receipt_type=Cancelled)",
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
                example="2024-01-31"
            ),
            openapi.Parameter(
                'Marketer',
                openapi.IN_QUERY,
                description="Filter by specific marketer (optional)",
                type=openapi.TYPE_STRING,
                required=False,
                example="John Doe"
            ),
            openapi.Parameter(
                'Team',
                openapi.IN_QUERY,
                description="Filter by specific team (optional)",
                type=openapi.TYPE_STRING,
                required=False,
                example="Sales Team A"
            ),
        ],
        responses={
            200: openapi.Response(
                description="MIB from receipts report retrieved successfully",
                examples={
                    "application/json": {
                        "message": "MIB from receipts report for region PACIFIC TEAM between 2024-01-01 and 2024-01-31.",
                        "posted_receipts": [
                            {
                                "line_no": 1,
                                "Receipt_No": "RCP001",
                                "Date_Posted": "2024-01-15",
                                "Payment_date": "2024-01-15",
                                "Bank_Name": "KCB Bank",
                                "Customer_Name": "John Doe",
                                "Pay_mode": "MPESA",
                                "Amount_LCY": "50000.00",
                                "Balance_LCY": "450000.00",
                                "Marketer": "Jane Smith",
                                "Teams": "Sales Team A",
                                "status": "Posted"
                            }
                        ],
                        "cancelled_receipts": [
                            {
                                "line_no": 2,
                                "Receipt_No": "RCP002",
                                "Date_Posted": "2024-01-20",
                                "Customer_Name": "Alice Johnson",
                                "Amount_LCY": "25000.00",
                                "cancellation_date": "2024-01-25T10:30:00Z",
                                "cancellation_reason": "Customer request"
                            }
                        ],
                        "posted_receipts_count": 15,
                        "cancelled_receipts_count": 3
                    }
                }
            ),
            400: openapi.Response(
                description="Bad request - Missing required parameters or no receipts found",
                examples={
                    "application/json": {
                        "error": "Region is required"
                    }
                }
            ),
            500: openapi.Response(
                description="Internal server error",
                examples={
                    "application/json": {
                        "error": "An unexpected error occurred"
                    }
                }
            )
        }
    )

    def get(self, request):
        params = request.query_params
        region = params.get('region', '')
        start_date = params.get('start_date', '')
        end_date = params.get('end_date', '')
        receipt_type = params.get('receipt_type', '')
        start_cancellation_date = params.get('start_cancellation_date', '')
        end_cancellation_date = params.get('end_cancellation_date', '')
        Marketer = params.get('Marketer', '')
        Teams = params.get('Team', '')

        if not region:
            return Response({"error": "Region is required"}, status=400)
        if not start_date or not end_date:
            return Response({"error": "Start date and end date are required"}, status=400)

        posted_receipts = None
        cancelled_receipts = None


        users_in_region = [users_in_region.append(user.employee_no) for user in User.objects.filter(region=region)]

        # Posted receipts
        if receipt_type == 'Posted':
            posted_receipts = PostedReceipt.objects.filter( 
                POSTED_DATE1__range=[start_date, end_date], Marketer__in=users_in_region
            ).order_by('-POSTED_DATE1')
        
        # Cancelled receipts
        if receipt_type == 'Cancelled':
            if not start_cancellation_date or not end_cancellation_date:
                cancelled_receipts = CancelledReceipt.objects.filter( POSTED_DATE1__range=[start_date, end_date], Marketer__in=users_in_region).order_by('-POSTED_DATE1')
            else:
                cancelled_receipts = CancelledReceipt.objects.filter(
                    POSTED_DATE1__range=[start_date, end_date],
                    cancellation_date__range=[start_cancellation_date, end_cancellation_date], Marketer__in=users_in_region
                ).order_by('-POSTED_DATE1')

        # All receipts
        if receipt_type == '':
            posted_receipts = PostedReceipt.objects.filter( 
                POSTED_DATE1__range=[start_date, end_date]
            ).order_by('-POSTED_DATE1')
            cancelled_receipts = CancelledReceipt.objects.filter( POSTED_DATE1__range=[start_date, end_date]).order_by('-POSTED_DATE1')

        # handle case where no receipts found
        if posted_receipts is None and cancelled_receipts is None:
            return Response({"error": "No receipts found for the given criteria"}, status=400)
        

        # Additional filters when marketer and teams are provided
        if Marketer:
            if posted_receipts:
                posted_receipts = posted_receipts.filter(Marketer=Marketer)
            if cancelled_receipts:
                cancelled_receipts = cancelled_receipts.filter(Marketer=Marketer)
        
        if Teams:
            if posted_receipts:
                posted_receipts = posted_receipts.filter(Teams=Teams)
            if cancelled_receipts:
                cancelled_receipts = cancelled_receipts.filter(Teams=Teams)

        # serializers
        posted_receipts_serializer = None
        cancelled_receipts_serializer = None
        posted_total_amount = 0
        posted_total_balance = 0
        cancelled_total_amount = 0
        cancelled_total_balance = 0

        if posted_receipts: 
            posted_total_amount = posted_receipts.aggregate(Sum('Amount_LCY'))['Amount_LCY__sum'] or 0
            posted_total_balance = posted_receipts.aggregate(Sum('Balance_LCY'))['Balance_LCY__sum'] or 0
            posted_receipts_serializer = PostedReceiptSerializer(posted_receipts, many=True)
        
        if cancelled_receipts:
            cancelled_total_amount = cancelled_receipts.aggregate(Sum('Amount_LCY'))['Amount_LCY__sum'] or 0
            cancelled_total_balance = cancelled_receipts.aggregate(Sum('Balance_LCY'))['Balance_LCY__sum'] or 0          
            cancelled_receipts_serializer = CancelledReceiptSerializer(cancelled_receipts, many=True)

        total_receipts_count = (posted_receipts.count() if posted_receipts else 0) + (cancelled_receipts.count() if cancelled_receipts else 0)

        # Build response message based on receipt type
        if receipt_type == 'Posted':
            message = f"Posted receipts report for region {region} between {start_date} and {end_date}."
        elif receipt_type == 'Cancelled':
            message = f"Cancelled receipts report for region {region} between {start_date} and {end_date}."
        else:
            message = f"MIB from receipts report for region {region} between {start_date} and {end_date}."

        data = {
            'message': message,
            'posted_receipts_count': posted_receipts.count() if posted_receipts else 0,
            'cancelled_receipts_count': cancelled_receipts.count() if cancelled_receipts else 0,
            'total_receipts_count': total_receipts_count,
            'posted_total_amount': posted_total_amount,
            'posted_total_balance': posted_total_balance,
            'cancelled_total_amount': cancelled_total_amount,
            'cancelled_total_balance': cancelled_total_balance,

            'posted_receipts': posted_receipts_serializer.data if posted_receipts_serializer else [],
            'cancelled_receipts': cancelled_receipts_serializer.data if cancelled_receipts_serializer else [],
            'filters_applied': {
                'region': region,
                'start_date': start_date,
                'end_date': end_date,
                'receipt_type': receipt_type if receipt_type else '',
                'marketer': Marketer if Marketer else None,
                'team': Teams if Teams else None,
                'start_cancellation_date': start_cancellation_date if start_cancellation_date else None,
                'end_cancellation_date': end_cancellation_date if end_cancellation_date else None,
            }
        }
        return Response(data)