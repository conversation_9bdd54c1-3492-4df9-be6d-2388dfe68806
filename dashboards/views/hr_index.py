from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from rest_framework.pagination import PageNumberPagination
from rest_framework.decorators import action
from rest_framework import status
import MySQLdb.cursors
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from django.conf import settings

# Configure logging
logger = logging.getLogger(__name__)

"""
HR DASHBOARD VIEWS:
===================

This module provides comprehensive HR dashboard functionality including:
1. Office Cards View - Shows office performance cards
2. Office Periods View - Shows periods for a specific office
3. Period Marketers View - Shows marketers for a specific period

Key Features:
- Secure parameterized queries
- Comprehensive search and filtering
- Pagination for large datasets
- Progress tracking and calculations
- Error handling and logging
"""

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    
    Args:
        tags (list): List of tags to apply to the ViewSet methods
        
    Returns:
        function: Decorator function
    """
    def decorator(cls):
        methods = ['list', 'create', 'retrieve', 'update', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                if hasattr(method, '_swagger_auto_schema'):
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

class DatabaseHelper:
    """
    Helper class for secure database operations
    """
    
    @staticmethod
    def execute_query(conn, query: str, params: List = None, fetch_one: bool = False, dict_cursor: bool = True) -> Optional[Dict]:
        """
        Execute a parameterized query safely
        
        Args:
            conn: Database connection
            query (str): SQL query to execute
            params (List, optional): Query parameters
            fetch_one (bool, optional): Whether to fetch one result
            dict_cursor (bool, optional): Whether to use dict cursor
            
        Returns:
            Optional[Dict]: Query results
            
        Raises:
            Exception: If query execution fails
        """
        try:
            cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
            with conn.connection.cursor(cursor_class) as cur:
                if params:
                    logger.debug(f"Executing query: {query} with params: {params}")
                    cur.execute(query, params)
                else:
                    logger.debug(f"Executing query: {query}")
                    cur.execute(query)
                
                if fetch_one:
                    return cur.fetchone()
                else:
                    return cur.fetchall()
                    
        except Exception as e:
            logger.error(f"Database query failed: {str(e)}")
            logger.error(f"Query: {query}")
            logger.error(f"Params: {params}")
            raise Exception(f"Database query failed: {str(e)}")
    
    @staticmethod
    def validate_pagination_params(page: str, page_size: str) -> Tuple[int, int, int]:
        """
        Validate and sanitize pagination parameters
        
        Args:
            page (str): Page number
            page_size (str): Items per page
            
        Returns:
            Tuple[int, int, int]: Validated page, page_size, and offset
        """
        try:
            page = max(1, int(page or 1))
            page_size = min(max(1, int(page_size or 10)), 100)  # Cap at 100
            offset = (page - 1) * page_size
            return page, page_size, offset
        except (ValueError, TypeError):
            return 1, 10, 0
    
    @staticmethod
    def sanitize_search_term(search: str) -> str:
        """
        Sanitize search term to prevent injection
        
        Args:
            search (str): Search term to sanitize
            
        Returns:
            str: Sanitized search term
        """
        if not search:
            return ""
        search = str(search).strip()[:100]
        return search

class CustomPagination(PageNumberPagination):
    """
    Custom pagination class for consistent pagination across all views
    """
    page_size = 10
    page_size_query_param = 'page_size'
    max_page_size = 100

@swagger_tags(['HR-Dashboard'])
class OfficeCardsView(ViewSet):
    """
    View for office cards showing current period performance
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['HR-Dashboard'],
        operation_description="Returns office cards with current period performance",
        responses={
            200: openapi.Response(
                description="Successful response",
                examples={
                    "application/json": {
                        "offices": [
                            {
                                "office": "HQ",
                                "total_marketers": 25,
                                "current_period": {
                                    "period_name": "January 2024",
                                    "start_date": "2024-01-01",
                                    "end_date": "2024-01-31",
                                    "target": 5000000,
                                    "achieved": 4250000,
                                    "progress": 85.0
                                }
                            }
                        ]
                    }
                }
            )
        }
    )
    def list(self, request):
        """
        Get office cards with current period performance
        
        Returns:
            Response: Office cards data
        """
        conn = connections["reports"]
        conn.ensure_connection()

        try:
            # First, get actual marketer counts from users table
            marketer_counts_sql = """
                SELECT 
                    office,
                    COUNT(*) as actual_marketers
                FROM users_user 
                WHERE is_marketer = TRUE 
                AND office IS NOT NULL 
                AND office != ''
                GROUP BY office
            """
            
            marketer_counts = DatabaseHelper.execute_query(conn, marketer_counts_sql)
            marketer_dict = {row['office']: row['actual_marketers'] for row in marketer_counts or []}

            # Get current period for each office - with better error handling
            office_cards_sql = """
                WITH office_list AS (
                    SELECT DISTINCT office
                    FROM users_user 
                    WHERE office IS NOT NULL AND office != ''
                ),
                current_period AS (
                    SELECT 
                        u.office,
                        mt.period_start_date,
                        mt.period_end_date,
                        SUM(COALESCE(mt.monthly_target, 0)) as monthly_target,
                        SUM(COALESCE(mt.MIB_achieved, 0)) as MIB_achieved,
                        CASE 
                            WHEN SUM(mt.monthly_target) > 0 
                            THEN (SUM(mt.MIB_achieved) / SUM(mt.monthly_target)) * 100 
                            ELSE 0 
                        END as progress,
                        COUNT(DISTINCT u.employee_no) as total_marketers
                    FROM marketer_targets mt
                    JOIN users_user u ON u.employee_no = mt.marketer_no_id AND u.is_marketer = TRUE
                    WHERE mt.period_start_date = (
                        SELECT MAX(period_start_date) 
                        FROM marketer_targets
                    )
                    GROUP BY u.office, mt.period_start_date, mt.period_end_date
                )
                SELECT 
                    u.office,
                    COALESCE(cp.total_marketers, 0) as total_marketers,
                    cp.period_start_date,
                    cp.period_end_date,
                    COALESCE(cp.monthly_target, 0) as monthly_target,
                    COALESCE(cp.MIB_achieved, 0) as MIB_achieved,
                    COALESCE(cp.progress, 0) as progress
                FROM (
                    SELECT DISTINCT office 
                    FROM users_user 
                    WHERE office IS NOT NULL AND office != ''
                ) u
                LEFT JOIN current_period cp ON u.office = cp.office
                ORDER BY u.office
            """
            
            offices = DatabaseHelper.execute_query(conn, office_cards_sql)
            
            processed_offices = []
            for office in offices or []:
                # Use actual marketer count from users table
                actual_marketers = marketer_dict.get(office['office'], 0)
                
                # Handle cases where there might be no current period data
                if office['period_start_date'] and office['period_end_date']:
                    current_period = {
                        "period_name": f"{office['period_start_date'].strftime('%B %Y')}",
                        "start_date": office['period_start_date'].strftime('%Y-%m-%d'),
                        "end_date": office['period_end_date'].strftime('%Y-%m-%d'),
                        "target": float(office['monthly_target'] or 0),
                        "achieved": float(office['MIB_achieved'] or 0),
                        "progress": round(float(office['progress'] or 0), 2)
                    }
                else:
                    # No current period data available
                    current_period = {
                        "period_name": "No Period Data",
                        "start_date": None,
                        "end_date": None,
                        "target": 0,
                        "achieved": 0,
                        "progress": 0
                    }
                
                processed_offices.append({
                    "office": office['office'],
                    "total_marketers": actual_marketers,
                    "current_period": current_period
                })

            return Response({
                "offices": processed_offices
            })

        except Exception as e:
            logger.error(f"OfficeCardsView error: {str(e)}")
            return Response({"error": "Failed to fetch office cards data"}, status=500)

@swagger_tags(['HR-Dashboard'])
class OfficePeriodsView(ViewSet):
    """
    View for office periods showing target vs achieved for each period
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['HR-Dashboard'],
        operation_description="Returns periods for a specific office",
        manual_parameters=[
            openapi.Parameter('office', openapi.IN_QUERY, description="Office name", type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        """
        Get periods for a specific office
        
        Args:
            request: HTTP request object
            
        Returns:
            Response: Office periods data
        """
        conn = connections["reports"]
        conn.ensure_connection()

        office = request.query_params.get('office')
        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )

        try:
            # Get periods for specific office
            periods_sql = """
                SELECT 
                    mt.period_start_date,
                    mt.period_end_date,
                    SUM(mt.monthly_target) as monthly_target,
                    SUM(mt.MIB_achieved) as MIB_achieved,
                    (SUM(mt.MIB_achieved) / SUM(mt.monthly_target)) * 100 as progress,
                    COUNT(DISTINCT u.employee_no) as total_marketers
                FROM marketer_targets mt
                JOIN users_user u ON u.employee_no = mt.marketer_no_id AND u.is_marketer = TRUE
                WHERE u.office = %s
                GROUP BY mt.period_start_date, mt.period_end_date
                ORDER BY mt.period_start_date DESC
                LIMIT %s OFFSET %s
            """
            
            periods = DatabaseHelper.execute_query(conn, periods_sql, [office, page_size, offset])

            # Get total count
            count_sql = """
                SELECT COUNT(DISTINCT mt.period_start_date) as total
                FROM marketer_targets mt
                JOIN users_user u ON u.employee_no = mt.marketer_no_id
                WHERE u.office = %s
            """
            count_result = DatabaseHelper.execute_query(conn, count_sql, [office], fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            processed_periods = []
            for period in periods or []:
                processed_periods.append({
                    "period_name": f"{period['period_start_date'].strftime('%B %Y')}",
                    "start_date": period['period_start_date'].strftime('%Y-%m-%d'),
                    "end_date": period['period_end_date'].strftime('%Y-%m-%d'),
                    "target": float(period['monthly_target'] or 0),
                    "achieved": float(period['MIB_achieved'] or 0),
                    "progress": round(float(period['progress'] or 0), 2),
                    "total_marketers": period['total_marketers'] or 0
                })

            return Response({
                "count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": processed_periods
            })

        except Exception as e:
            logger.error(f"OfficePeriodsView error: {str(e)}")
            return Response({"error": "Failed to fetch office periods data"}, status=500)

@swagger_tags(['HR-Dashboard'])
class PeriodMarketersView(ViewSet):
    """
    View for marketers in a specific period
    """
    queryset = None
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    pagination_class = CustomPagination

    @swagger_auto_schema(
        tags=['HR-Dashboard'],
        operation_description="Returns marketers for a specific period",
        manual_parameters=[
            openapi.Parameter('office', openapi.IN_QUERY, description="Office name", type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('start_date', openapi.IN_QUERY, description="Period start date", type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('end_date', openapi.IN_QUERY, description="Period end date", type=openapi.TYPE_STRING, required=True),
            openapi.Parameter('search', openapi.IN_QUERY, description="Search marketer", type=openapi.TYPE_STRING, required=False),
            openapi.Parameter('page', openapi.IN_QUERY, description="Page number", type=openapi.TYPE_INTEGER, required=False),
            openapi.Parameter('page_size', openapi.IN_QUERY, description="Items per page", type=openapi.TYPE_INTEGER, required=False)
        ]
    )
    def list(self, request):
        """
        Get marketers for a specific period
        
        Args:
            request: HTTP request object
            
        Returns:
            Response: Period marketers data
        """
        conn = connections["reports"]
        conn.ensure_connection()

        office = request.query_params.get('office')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        search = DatabaseHelper.sanitize_search_term(request.query_params.get('search', ''))
        page, page_size, offset = DatabaseHelper.validate_pagination_params(
            request.query_params.get('page'),
            request.query_params.get('page_size')
        )

        try:
            base_query = """
                SELECT 
                    u.fullnames,
                    u.employee_no,
                    COALESCE(t.team, u.team_id) as team,
                    mt.monthly_target,
                    mt.MIB_achieved,
                    (mt.MIB_achieved / mt.monthly_target) * 100 as progress
                FROM users_user u
                JOIN marketer_targets mt ON mt.marketer_no_id = u.employee_no
                LEFT JOIN users_teams t ON t.tl_name = u.team_id
                WHERE u.office = %s
                AND u.is_marketer = TRUE
                AND mt.period_start_date = %s
                AND mt.period_end_date = %s
            """
            params = [office, start_date, end_date]

            if search:
                base_query += " AND (u.fullnames LIKE %s OR t.team LIKE %s OR u.team_id LIKE %s)"
                search_param = f"%{search}%"
                params.extend([search_param] * 3)

            # Get total count
            count_query = f"SELECT COUNT(*) as total FROM ({base_query}) as subquery"
            count_result = DatabaseHelper.execute_query(conn, count_query, params, fetch_one=True)
            total_count = count_result['total'] if count_result else 0

            # Add pagination and ordering
            base_query += " ORDER BY progress DESC LIMIT %s OFFSET %s"
            params.extend([page_size, offset])

            marketers = DatabaseHelper.execute_query(conn, base_query, params)

            processed_marketers = []
            for marketer in marketers or []:
                processed_marketers.append({
                    "fullnames": marketer['fullnames'],
                    "employee_no": marketer['employee_no'],
                    "team": marketer['team'],
                    "target": float(marketer['monthly_target'] or 0),
                    "achieved": float(marketer['MIB_achieved'] or 0),
                    "progress": round(float(marketer['progress'] or 0), 2)
                })

            return Response({
                "count": total_count,
                "total_pages": (total_count + page_size - 1) // page_size,
                "current_page": page,
                "page_size": page_size,
                "results": processed_marketers
            })

        except Exception as e:
            logger.error(f"PeriodMarketersView error: {str(e)}")
            return Response({"error": "Failed to fetch period marketers data"}, status=500) 