services:
  # erp:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.dev
  #   container_name: erp_container
  #   restart: always
  #   ports:
  #     - "8001:80"  # Expose port 80 of the container to port 8000 on the host
  #   volumes:
  #     - .:/var/www/html  # Mount the current directory to /var/www/html in the container
  #   depends_on:
  #     - db
  #     - minio
  #     - phpmyadmin

  db:
    image: mysql:latest
    container_name: erp_mysql_db
    restart: always
    ports:
      - 3306:3306
    volumes:
      - db-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD=0000
      - MYSQL_DATABASE=crm
      - MYSQL_USER=crm_user
      - MYSQL_PASSWORD=crm_password

  

  # minio:
  #   image: minio/minio:latest
  #   container_name: crmminio
  #   environment:
  #     - MINIO_ROOT_USER=ghost      # Set your MinIO credentials
  #     - MINIO_ROOT_PASSWORD=password123
  #   command: server /data --console-address ":9001"
  #   ports:
  #     - "9000:9000"    # MinIO API port
  #     - "9001:9001"    # MinIO web console port
  #   volumes:
  #     - ./minio_data:/data  # Persistent storage for MinIO in development

  # minio_client:
  #   image: minio/mc:latest
  #   container_name: crm_minio_client
  #   depends_on:
  #     - minio

  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin_container_erp
    restart: always
    ports:
      - "8889:80"
    environment:
      PMA_HOST: db
      PMA_PORT: 3306
    depends_on:
      - db

networks:
  my_network:
    driver: bridge

volumes:
  db-data:
  # minio_data:
  