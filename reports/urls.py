from django.urls import path, include
from rest_framework.routers import <PERSON>faultRouter
from reports.views.customer_reports import (CustomersByCategoryView,CustomerHighInvestCustomersByNumberOfLeadFilesReportView,CustomerHighInvestCustomersBySumOfTotalReportView,
                                            CustomerRatingReportView,CustomerView)
from reports.views.sales_reports import NewSalesReportsView,InstallmentsDueTodayReportsView,OverdueInstallmentsReportsView,BelowThresholdReportsView,InstallmentDueReportsView,OverdueBelowThresholdReportsView
from reports.views.sales_reports import OngoingSalesReportsView,CompletedSalesReportsView,CashonCashReportsView,MIBReportsView,SalesView
from reports.views.prospects_reports import AllProspectsView,ProspectView
from reports.views.performance_reports import MarketingPeriodsView,MarketersPerformanceView,OverallMarketersPerformancePerPeriodView,OverallTeamsPerformanceView,OverallHQKarenPerformanceView
from reports.views.performance_reports import  CommissionHeadersReportView,CommissionLinesReportView,TeamMarketerTargetsView
from reports.views.logistics_reports import (
    SiteVisitsDashboardView,ApprovedSiteVisitsView,SiteVisitsSummaryView,MostBookedSitesView,ChauffeurItineraryView,MarketersFeedbackView)
from reports.views.inventory_reports import AllbookingsView
from reports.views.teleteam_reports import (
    TeleteamDailyReportsView, TeleteamDetailedDailyReportsView, TeleteamSalesReportsView)

router = DefaultRouter()

router.register(r'New-Sales', NewSalesReportsView, basename='new-sales')
router.register(r'Installmets-due-today',InstallmentsDueTodayReportsView, basename='installments-due-today')
router.register(r'overdue-collections', OverdueInstallmentsReportsView, basename='overdue-collections')
router.register(r'cash-on-cash-sales', CashonCashReportsView, basename='cash-on-cash-sales')
router.register(r'MIB-report', MIBReportsView, basename='MIB-report')
router.register(r'installment-due', InstallmentDueReportsView, basename='installment-due')
router.register(r'below-threshold', BelowThresholdReportsView, basename='below-threshold')
router.register(r'overdue-below-threshold', OverdueBelowThresholdReportsView, basename='overdue-below-threshold')
router.register(r'customer-high-invest_no_of_leadfiles',CustomerHighInvestCustomersByNumberOfLeadFilesReportView,basename='customer-high-invest_no_of_leadfiles')
router.register(r'customer-high-invest_sum_of_total',CustomerHighInvestCustomersBySumOfTotalReportView,basename='customer-high-invest_sum_of_total')
router.register(r'customer-rating', CustomerRatingReportView, basename='customer-rating')
# router.register(r'customer-view', CustomerView, basename='customer-view')
router.register(r'all-prospects-view', AllProspectsView, basename='all-prospects-view')
router.register(r'marketing-period', MarketingPeriodsView, basename='marketing-period')
router.register(r'marketers-performance', MarketersPerformanceView, basename='marketers-performance')
router.register(r'overall-marketer-performance-in-a-period', OverallMarketersPerformancePerPeriodView, basename='overall-marketer-performance-in-a-period')
router.register(r'overall-teams-performance', OverallTeamsPerformanceView, basename='overall-teams-performance')
router.register(r'overall-hq-karen-performance', OverallHQKarenPerformanceView, basename='overall-hq-karen-performance')
router.register(r'commission-headers-report', CommissionHeadersReportView, basename='commission-headers-report')
router.register(r'commission-lines-report', CommissionLinesReportView, basename='commission-lines-report')
router.register(r'team-marketer_targets',TeamMarketerTargetsView, basename='team-marketer-targets')

# Logistics Reports
router.register(r'site-visits-dashboard', SiteVisitsDashboardView, basename='site-visits-dashboard')
router.register(r'approved-site-visits', ApprovedSiteVisitsView, basename='approved-site-visits')
router.register(r'site-visits-summary', SiteVisitsSummaryView, basename='site-visits-summary')
router.register(r'most-booked-sites', MostBookedSitesView, basename='most-booked-sites')
router.register(r'chauffeur-itinerary', ChauffeurItineraryView, basename='chauffeur-itinerary')
router.register(r'marketers-feedback', MarketersFeedbackView, basename='marketers-feedback')


#
# Inventory Reports
router.register(r'all-bookings', AllbookingsView, basename='all-bookings')

# Telemarketing Team Reports
router.register(r'teleteam-daily', TeleteamDailyReportsView, basename='teleteam-daily')
router.register(r'teleteam-daily-detailed', TeleteamDetailedDailyReportsView, basename='teleteam-daily-detailed')
router.register(r'teleteam-sales-performance', TeleteamSalesReportsView, basename='teleteam-sales-performance')

urlpatterns = [
    path('', include(router.urls)),
] 