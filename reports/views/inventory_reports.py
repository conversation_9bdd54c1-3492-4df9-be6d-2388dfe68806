from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime,os
import MySQLdb.cursors
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")

        


@swagger_tags(['Inventory Reports'])
class AllbookingsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema(
        operation_description="Returns all bookings filters.",
        manual_parameters=[
            openapi.Parameter(name='BOOKING_TYPE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=[ 'MPESA', 'SPECIAL', 'OTHER', 'DIASPORA','ALL'],
                description='Filter by booking type.',required=True,default='ALL',
            ),
            openapi.Parameter(name='STATUS',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=[ 'OPEN', 'OLD', 'TIMED', 'DONE', 'WAITING', 'SUSPENDED', 'REJECTED', 'REVERTED','ALL'],
                description='Filter by status.',required=True,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_NAME',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='PLOT_NUMBER',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by plot number. If not provided, all plots are included.', required=False,default='ALL',
            ),
            openapi.Parameter(
                name='start_date',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='Filter bookings created on or after this date (YYYY-MM-DD).',
                required=False,
            ),
            openapi.Parameter(
                name='end_date',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='Filter bookings created on or before this date (YYYY-MM-DD).',
                required=False,
            ),
            openapi.Parameter(name='search',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Search term to filter prospects by name, phone, or email.',
                required=False,
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
                
        
    def list(self, request):

        # validate and parse date parameters]
        booking_type = request.query_params.get("BOOKING_TYPE", "ALL")
        status = request.query_params.get("STATUS", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        marketer_name = request.query_params.get("MARKETER_NAME", "ALL")
        plot_number = request.query_params.get("PLOT_NUMBER", "ALL")
        start_date = request.query_params.get("start_date", None)
        end_date = request.query_params.get("end_date", None)
        search = request.query_params.get("search", None)
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 20)
    
       
        # Build dynamic WHERE clauses based on filters
        m_where_clauses = ""
        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL"
        p_stat=""
        
        
        # Apply filters based on validated parameters

        if marketer_name and marketer_name != "ALL":
            where_clauses += " AND uu.fullnames LIKE %s"
            params.append(f"%{marketer_name}%")
            stat = f"Marketer {marketer_name}"

        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses += " AND ipb.marketer_id = %s"
            params.append(marketer_employee_no)
            stat = f"Marketer {marketer_employee_no}"

        if booking_type and booking_type != "ALL":
            m_where_clauses += (" AND " if m_where_clauses else "") + "ipb.booking_type = %s"
            params.append(booking_type)
            p_stat += f" {booking_type}"

        if status and status != "ALL":
            m_where_clauses += (" AND " if m_where_clauses else "") + "ipb.status = %s"
            params.append(status)
            p_stat += f" {status}"
            
        if plot_number and plot_number != "ALL":
            m_where_clauses += (" AND " if m_where_clauses else "") + "ipb.plots LIKE %s"
            params.append(f"%{plot_number}%")
            p_stat += f" Plot {plot_number}"

        if search:
            m_where_clauses += (" AND " if m_where_clauses else "") + "(ipb.customer_id LIKE %s OR cc.phone LIKE %s OR cc.customer_name LIKE %s OR cc.primary_email LIKE %s OR ipb.lead_id LIKE %s )"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param, search_param])
            p_stat += " (search)"
            
        # Date filtering
        if start_date:
            if not end_date:
                end_date = datetime.datetime.now().strftime("%Y-%m-%d")
            try:
                datetime.datetime.strptime(start_date, "%Y-%m-%d")
                m_where_clauses += (" AND " if m_where_clauses else "") + "DATE(ipb.creation_date) >= %s"
                params.append(start_date)
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        if end_date:
            if not start_date:
                return Response({"error": "start_date is required when end_date is provided."}, status=400)
            try:
                datetime.datetime.strptime(end_date, "%Y-%m-%d")
                m_where_clauses += (" AND " if m_where_clauses else "") + "DATE(ipb.creation_date) <= %s"
                params.append(end_date)
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)

        
        
        
        base_sql = f"""SELECT ipb.plots, ipb.booking_type,ipb.status, ipb.amount, ipb.creation_date, ipb.customer_id, ipb.lead_id, ipb.marketer_id,
        uu.fullnames as marketer_name, lp.name as lead_name, cc.customer_name as customer_name FROM inventory_plotbooking ipb LEFT JOIN customers_customer cc ON ipb.customer_id = cc.customer_no
        LEFT JOIN leads_prospects lp ON ipb.lead_id = lp.id LEFT JOIN users_user uu ON ipb.marketer_id = uu.employee_no WHERE {where_clauses} """
        if m_where_clauses !="":
            base_sql += f" AND {m_where_clauses} "
        base_sql += " ORDER BY ipb.creation_date DESC"

        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " All {} bookings for {}" .format(p_stat,stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })



@swagger_tags(['Inventory Reports'])
class ProjectsPortfolioView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Returns comprehensive projects portfolio report with computed fields including total plots, sales metrics, receivables, and financial summaries.",
        manual_parameters=[
            openapi.Parameter(
                name='project_id',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Filter by specific project ID. If not provided, all projects are included.',
                required=False,
                default='ALL'
            ),
            openapi.Parameter(
                name='project_name',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Filter by project name (partial match). If not provided, all projects are included.',
                required=False,
                default='ALL'
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,
                required=False,
                default=1
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                description='Number of results per page.',
                type=openapi.TYPE_INTEGER,
                required=False,
                default=20
            ),
        ],
    )
    def list(self, request):
        # Get filter parameters
        project_id = request.query_params.get("project_id", "ALL")
        project_name = request.query_params.get("project_name", "ALL")
        page = request.query_params.get("page", 1)
        page_size = request.query_params.get("page_size", 20)
        
        # Build dynamic WHERE clauses based on filters
        where_clauses = "1=1"
        params = []
        filter_description = "for ALL projects"
        
        if project_id and project_id != "ALL":
            where_clauses += " AND p.projectId = %s"
            params.append(project_id)
            filter_description = f"for Project ID {project_id}"
        
        if project_name and project_name != "ALL":
            where_clauses += " AND p.name LIKE %s"
            params.append(f"%{project_name}%")
            filter_description = f"for projects matching '{project_name}'"
        
        # Main SQL query for projects portfolio
        portfolio_sql = f"""
        SELECT 
            p.projectId AS project_id,
            p.name AS project_name,
            p.initials,
            p.description,
            
            -- Total Plots
            COALESCE(plot_stats.total_plots, 0) AS total_plots,
            
            -- Plots Open (Status = 'Open')
            COALESCE(plot_stats.plots_open, 0) AS plots_open,
            
            -- Plots Fully Sold (from lead_files where lead_file_status_dropped = 'No' and balance(LCY) <= 0)
            COALESCE(sales_stats.plots_fully_sold, 0) AS plots_fully_sold,
            
            -- Plots on Receivables (from lead_files where lead_file_status_dropped = 'No' and balance(LCY) > 0)
            COALESCE(sales_stats.plots_on_receivables, 0) AS plots_on_receivables,
            
            -- Not For Sale (calculated: total_plots - (plots_open + plots_fully_sold + plots_on_receivables))
            COALESCE(plot_stats.total_plots, 0) - 
            (COALESCE(plot_stats.plots_open, 0) + 
             COALESCE(sales_stats.plots_fully_sold, 0) + 
             COALESCE(sales_stats.plots_on_receivables, 0)) AS not_for_sale,
            
            -- Total Received MIB (sum of total_paid from lead_files where lead_file_status_dropped = 'No')
            COALESCE(sales_stats.total_received_mib, 0.00) AS total_received_mib,
            
            -- Outstanding Balance (sum of balance(LCY) from lead_files where lead_file_status_dropped = 'No' and balance(LCY) >= 0)
            COALESCE(sales_stats.outstanding_balance, 0.00) AS outstanding_balance,
            
            -- Value of Unsold Plots (sum of cash_price from plots where status = 'Open')
            COALESCE(plot_stats.value_unsold_plots, 0.00) AS value_unsold_plots,
            
            -- Total Expected Receivables (value_unsold_plots + outstanding_balance)
            COALESCE(plot_stats.value_unsold_plots, 0.00) + 
            COALESCE(sales_stats.outstanding_balance, 0.00) AS total_expected_receivables
            
        FROM 
            inventory_project p
            
        LEFT JOIN (
            -- Plot statistics subquery
            SELECT 
                pl.project_id,
                COUNT(*) AS total_plots,
                SUM(CASE WHEN pl.plot_status = 'Open' THEN 1 ELSE 0 END) AS plots_open,
                SUM(CASE WHEN pl.plot_status = 'Open' THEN pl.cash_price ELSE 0 END) AS value_unsold_plots
            FROM 
                inventory_plot pl
            GROUP BY 
                pl.project_id
        ) plot_stats ON p.projectId = plot_stats.project_id
        
        LEFT JOIN (
            -- Sales statistics subquery
            SELECT 
                lf.project_id,
                COUNT(CASE WHEN lf.lead_file_status_dropped = 0 AND lf.balance_lcy <= 0 THEN 1 END) AS plots_fully_sold,
                COUNT(CASE WHEN lf.lead_file_status_dropped = 0 AND lf.balance_lcy > 0 THEN 1 END) AS plots_on_receivables,
                SUM(CASE WHEN lf.lead_file_status_dropped = 0 THEN lf.total_paid ELSE 0 END) AS total_received_mib,
                SUM(CASE WHEN lf.lead_file_status_dropped = 0 AND lf.balance_lcy >= 0 THEN lf.balance_lcy ELSE 0 END) AS outstanding_balance
            FROM 
                sales_leadfile lf
            GROUP BY 
                lf.project_id
        ) sales_stats ON p.projectId = sales_stats.project_id
        
        WHERE {where_clauses}
        ORDER BY p.priority ASC, p.name ASC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(portfolio_sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": f"Database query failed: {str(e)}"}, status=500)
        
        if not data:
            return Response({
                "message": f"No projects portfolio data found {filter_description}.",
                "Total Results": 0,
                "count": 0,
                "num_pages": 0,
                "current_page": 1,
                "results": []
            }, status=200)
        
        # Process data to ensure proper formatting of decimal fields
        for item in data:
            # Convert decimal fields to float for JSON serialization
            decimal_fields = [
                'total_received_mib', 'outstanding_balance', 
                'value_unsold_plots', 'total_expected_receivables'
            ]
            for field in decimal_fields:
                if item.get(field) is not None:
                    item[field] = float(item[field])
        
        # Close all connections
        for c in connections.all():
            c.close()
        
        # Pagination
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
            
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            "Title": f"Projects Portfolio Report {filter_description}",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list,
            "summary": {
                "total_projects": paginator.count,
                "grand_total_plots": sum(item.get('total_plots', 0) for item in data),
                "grand_total_open": sum(item.get('plots_open', 0) for item in data),
                "grand_total_sold": sum(item.get('plots_fully_sold', 0) for item in data),
                "grand_total_receivables": sum(item.get('plots_on_receivables', 0) for item in data),
                "grand_total_not_for_sale": sum(item.get('not_for_sale', 0) for item in data),
                "grand_total_received_mib": sum(item.get('total_received_mib', 0) for item in data),
                "grand_outstanding_balance": sum(item.get('outstanding_balance', 0) for item in data),
                "grand_value_unsold": sum(item.get('value_unsold_plots', 0) for item in data),
                "grand_expected_receivables": sum(item.get('total_expected_receivables', 0) for item in data)
            }
        })
        
        
    