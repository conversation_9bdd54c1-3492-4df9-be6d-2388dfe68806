from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime, os
import MySQLdb.cursors
import logging

# Set up logging
logger = logging.getLogger(__name__)

# Department ID Constants - IMPORTANT: Digital Marketing is EXCLUDED
TELEMARKETING_DEPT_ID = 21      # Updated for test database
CUSTOMER_SERVICE_DEPT_ID = 9   # Updated for test database  
DIGITAL_MARKETING_DEPT_ID = 10  # EXCLUDED from telemarketing reports

# Allowed digital team departments (excludes Telemarketing and Customer Service)
ALLOWED_DIGITAL_TEAM_DEPT_IDS = [DIGITAL_MARKETING_DEPT_ID]

# Removed swagger_tags decorator due to conflict with existing swagger_auto_schema decorators
class DigitalTeamDailyReportsView(ViewSet):
    """
    Digital Team Reports - Count of prospects handled by Digital Marketing team members with flexible date range filtering
    """
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['Digital Team Reports'],
        operation_description="Returns count of prospects handled by digital marketing team members with flexible date range filtering (daily, weekly, monthly, or custom).",
        manual_parameters=[
            openapi.Parameter(
                name='DEPARTMENT_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by department ID. Digital Marketing (ID: 10) is allowed.',
                required=False,
                default=10,
            ),
            openapi.Parameter(
                name='START_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='Start date for date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='END_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='End date for date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='DEPARTMENT_MEMBER_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Filter by specific department member employee number. Use "ALL" to include all members.',
                required=False,
                default='ALL',
            ),
            openapi.Parameter(
                name='LEAD_SOURCE_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by specific lead source ID. Use 0 to include all lead sources.',
                required=False,
                default=0,
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,
                required=False,
                default=1,
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                description='Number of results per page.',
                type=openapi.TYPE_INTEGER,
                required=False,
                default=50,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Daily digital marketing team report data",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Daily prospects report for Digital Marketing department on 2025-09-22",
                        "data": [
                            {
                                "department_member_id": "EMP123",
                                "department_member_name": "John Doe",
                                "prospects_handled": 15,
                                "report_date": "2025-09-22",
                                "department_name": "Digital Marketing"
                            }
                        ],
                        "pagination": {
                            "page": 1,
                            "page_size": 50,
                            "total_count": 10,
                            "total_pages": 1
                        }
                    }
                }
            ),
            400: openapi.Response(description="Bad request - Invalid parameters"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def list(self, request):
        """
        Get count of prospects handled by digital marketing team members for specified date range
        """
        try:
            # Get and validate parameters (compatible with both DRF and Django requests)
            query_params = getattr(request, 'query_params', request.GET)
            department_id = query_params.get("DEPARTMENT_ID", 10)
            start_date = query_params.get("START_DATE", datetime.date.today().isoformat())
            end_date = query_params.get("END_DATE", datetime.date.today().isoformat())
            department_member_id = query_params.get("DEPARTMENT_MEMBER_ID", "ALL")
            lead_source_id = query_params.get("LEAD_SOURCE_ID", 0)
            
            page = int(query_params.get("page", 1))
            page_size = int(query_params.get("page_size", 50))
            
            # Validate date formats
            try:
                start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"error": "Invalid date format. Use YYYY-MM-DD for both START_DATE and END_DATE."}, 
                    status=400
                )
            
            # Validate date range
            if start_date_obj > end_date_obj:
                return Response(
                    {"error": "START_DATE cannot be later than END_DATE."}, 
                    status=400
                )
            
            # Validate department_id
            try:
                department_id = int(department_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid department_id. Must be an integer."}, 
                    status=400
                )
            
        
            
            # Validate that department is in allowed digital team departments
            if department_id not in ALLOWED_DIGITAL_TEAM_DEPT_IDS:
                return Response(
                    {"error": f" Department ID {department_id} is not a digital team department. Allowed department: Digital Marketing (ID: {DIGITAL_MARKETING_DEPT_ID})."}, 
                    status=400
                )
            
            # Validate lead_source_id
            try:
                lead_source_id = int(lead_source_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid lead_source_id. Must be an integer."}, 
                    status=400
                )
            
            # Build dynamic WHERE clauses and parameters
            where_clauses = "lp.department_id = %s AND DATE(lp.date) BETWEEN %s AND %s"
            params = [department_id, start_date, end_date]
            
            # Add optional filters
            if department_member_id and department_member_id != "ALL":
                where_clauses += " AND lp.department_member_id = %s"
                params.append(department_member_id)
            
            if lead_source_id and lead_source_id != 0:
                where_clauses += " AND lp.lead_source_id = %s" 
                params.append(lead_source_id)
            
            # Base SQL query (enhanced for date ranges)
            base_sql = f"""
            SELECT 
                lp.department_member_id,
                uu.fullnames AS department_member_name,
                COUNT(*) AS prospects_handled,
                MIN(DATE(lp.date)) AS start_date,
                MAX(DATE(lp.date)) AS end_date,
                ud.dp_name AS department_name
            FROM leads_prospects lp
            LEFT JOIN users_user uu ON lp.department_member_id = uu.employee_no
            LEFT JOIN users_departments ud ON lp.department_id = ud.dp_id
            WHERE {where_clauses}
            GROUP BY lp.department_member_id, ud.dp_name, uu.fullnames
            ORDER BY prospects_handled DESC, uu.fullnames ASC
            """
            
            # Execute query
            with connections["reports"].cursor() as cursor:
                cursor.execute(base_sql, params)
                cols = [c[0] for c in cursor.description]
                results = [dict(zip(cols, row)) for row in cursor.fetchall()]
            
            # Convert date objects to strings for JSON serialization
            for result in results:
                if result.get('start_date'):
                    result['start_date'] = str(result['start_date'])
                if result.get('end_date'):
                    result['end_date'] = str(result['end_date'])
            
            # Apply pagination
            paginator = Paginator(results, page_size)
            total_count = len(results)
            total_pages = paginator.num_pages
            
            try:
                paginated_results = paginator.page(page).object_list
            except PageNotAnInteger:
                paginated_results = paginator.page(1).object_list
                page = 1
            except EmptyPage:
                paginated_results = paginator.page(paginator.num_pages).object_list
                page = paginator.num_pages
            
            # Build response message
            dept_filter = f"department {department_id}"
            member_filter = f" for member {department_member_id}" if department_member_id != "ALL" else ""
            source_filter = f" from lead source {lead_source_id}" if lead_source_id != 0 else ""
            
            # Determine the period description
            if start_date == end_date:
                period_desc = f"on {start_date}"
            else:
                # Calculate period type for user-friendly description
                date_diff = (end_date_obj - start_date_obj).days + 1
                if date_diff == 7:
                    period_desc = f"for week ({start_date} to {end_date})"
                elif date_diff in [28, 29, 30, 31]:
                    period_desc = f"for month ({start_date} to {end_date})"
                else:
                    period_desc = f"from {start_date} to {end_date}"
            
            message = f" Digital Marketing prospects report for {dept_filter} {period_desc}{member_filter}{source_filter}"
            
            response_data = {
                "status": "success",
                "message": message,
                "data": paginated_results,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages
                },
                "filters_applied": {
                    "department_id": department_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "date_range_days": (end_date_obj - start_date_obj).days + 1,
                    "department_member_id": department_member_id if department_member_id != "ALL" else None,
                    "lead_source_id": lead_source_id if lead_source_id != 0 else None
                }
            }
            
            return Response(response_data, status=200)
            
        except Exception as e:
            logger.error(f"Error in DigitalMarketing DailyReportsView: {str(e)}")
            return Response(
                {"error": f"Internal server error: {str(e)}"}, 
                status=500
            )


class DigitalTeamDetailedDailyReportsView(ViewSet):
    """
    Detailed Digital Team Reports - Count with prospect categories and status breakdown for Digital Marketing with flexible date range filtering
    """
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['Digital Team Reports'],
        operation_description="Returns detailed count of prospects handled by digital marketing team members with category and status breakdowns, flexible date range filtering (daily, weekly, monthly, or custom).",
        manual_parameters=[
            openapi.Parameter(
                name='DEPARTMENT_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by department ID. Use 21 for Digital Marketing or 9 for Customer Service. Digital Marketing (ID: 10) is NOT allowed.',
                required=False,
                default=10,
            ),
            openapi.Parameter(
                name='START_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='Start date for date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='END_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='End date for date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='DEPARTMENT_MEMBER_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Filter by specific department member employee number. Use "ALL" to include all members.',
                required=False,
                default='ALL',
            ),
            openapi.Parameter(
                name='LEAD_SOURCE_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by specific lead source ID. Use 0 to include all lead sources.',
                required=False,
                default=0,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Detailed daily digital marketing team report data",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Detailed daily prospects report for Digital Marketing department on 2025-09-22",
                        "data": [
                            {
                                "department_member_id": "EMP123",
                                "department_member_name": "John Doe",
                                "member_office": "HQ",
                                "prospects_handled": 15,
                                "report_date": "2025-09-22",
                                "department_name": "Digital Marketing",
                                "lead_source_name": "Website Inquiry",
                                "hot_prospects": 5,
                                "warm_prospects": 8,
                                "cold_prospects": 2,
                                "active_prospects": 13,
                                "dormant_prospects": 2
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(description="Bad request - Invalid parameters"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def list(self, request):
        """
        Get detailed count of prospects handled by digital marketing and customer service team members with breakdowns for specified date range
        """
        try:
            # Get and validate parameters (compatible with both DRF and Django requests)
            query_params = getattr(request, 'query_params', request.GET)
            department_id = query_params.get("DEPARTMENT_ID", 10)
            start_date = query_params.get("START_DATE", datetime.date.today().isoformat())
            end_date = query_params.get("END_DATE", datetime.date.today().isoformat())
            department_member_id = query_params.get("DEPARTMENT_MEMBER_ID", "ALL")
            lead_source_id = query_params.get("LEAD_SOURCE_ID", 0)
            
            # Validate date formats
            try:
                start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"error": "Invalid date format. Use YYYY-MM-DD for both START_DATE and END_DATE."}, 
                    status=400
                )
            
            # Validate date range
            if start_date_obj > end_date_obj:
                return Response(
                    {"error": "START_DATE cannot be later than END_DATE."}, 
                    status=400
                )
            
            # Validate department_id and lead_source_id
            try:
                department_id = int(department_id)
                lead_source_id = int(lead_source_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid department_id or lead_source_id. Must be integers."}, 
                    status=400
                )
            
           
            # Validate that department is in allowed digital team departments
            if department_id not in ALLOWED_DIGITAL_TEAM_DEPT_IDS:
                return Response(
                    {"error": f" Department ID {department_id} is not a digital team department. Allowed department: Digital Marketing (ID: {DIGITAL_MARKETING_DEPT_ID})."}, 
                    status=400
                )
            
            # Build dynamic WHERE clauses and parameters
            where_clauses = "lp.department_id = %s AND DATE(lp.date) BETWEEN %s AND %s"
            params = [department_id, start_date, end_date]
            
            # Add optional filters
            if department_member_id and department_member_id != "ALL":
                where_clauses += " AND lp.department_member_id = %s"
                params.append(department_member_id)
            
            if lead_source_id and lead_source_id != 0:
                where_clauses += " AND lp.lead_source_id = %s" 
                params.append(lead_source_id)
            
            # Detailed SQL query with breakdowns (enhanced for date ranges)
            base_sql = f"""
            SELECT 
                lp.department_member_id,
                uu.fullnames AS department_member_name,
                uu.office AS member_office,
                COUNT(*) AS prospects_handled,
                MIN(DATE(lp.date)) AS start_date,
                MAX(DATE(lp.date)) AS end_date,
                ud.dp_name AS department_name,
                ls.name AS lead_source_name,
                SUM(CASE WHEN lp.category = 'Hot' THEN 1 ELSE 0 END) AS hot_prospects,
                SUM(CASE WHEN lp.category = 'Warm' THEN 1 ELSE 0 END) AS warm_prospects,
                SUM(CASE WHEN lp.category = 'Cold' THEN 1 ELSE 0 END) AS cold_prospects,
                SUM(CASE WHEN lp.status = 'Active' THEN 1 ELSE 0 END) AS active_prospects,
                SUM(CASE WHEN lp.status = 'Dormant' THEN 1 ELSE 0 END) AS dormant_prospects
            FROM leads_prospects lp
            LEFT JOIN users_user uu ON lp.department_member_id = uu.employee_no
            LEFT JOIN users_departments ud ON lp.department_id = ud.dp_id  
            LEFT JOIN leads_leadsource ls ON lp.lead_source_id = ls.leadsource_id
            WHERE {where_clauses}
            GROUP BY lp.department_member_id, ud.dp_name, uu.fullnames, uu.office, ls.name
            ORDER BY prospects_handled DESC, uu.fullnames ASC
            LIMIT 500
            """
            
            # Execute query
            with connections["reports"].cursor() as cursor:
                cursor.execute(base_sql, params)
                cols = [c[0] for c in cursor.description]
                results = [dict(zip(cols, row)) for row in cursor.fetchall()]
            
            # Convert date objects to strings for JSON serialization
            for result in results:
                if result.get('start_date'):
                    result['start_date'] = str(result['start_date'])
                if result.get('end_date'):
                    result['end_date'] = str(result['end_date'])
            
            # Build response message
            dept_filter = f"department {department_id}"
            member_filter = f" for member {department_member_id}" if department_member_id != "ALL" else ""
            source_filter = f" from lead source {lead_source_id}" if lead_source_id != 0 else ""
            
            # Determine the period description
            if start_date == end_date:
                period_desc = f"on {start_date}"
            else:
                # Calculate period type for user-friendly description
                date_diff = (end_date_obj - start_date_obj).days + 1
                if date_diff == 7:
                    period_desc = f"for week ({start_date} to {end_date})"
                elif date_diff in [28, 29, 30, 31]:
                    period_desc = f"for month ({start_date} to {end_date})"
                else:
                    period_desc = f"from {start_date} to {end_date}"
            
            message = f"Detailed digital marketing team prospects report for {dept_filter} {period_desc}{member_filter}{source_filter}"
            
            response_data = {
                "status": "success",
                "message": message,
                "data": results,
                "total_records": len(results),
                "filters_applied": {
                    "department_id": department_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "date_range_days": (end_date_obj - start_date_obj).days + 1,
                    "department_member_id": department_member_id if department_member_id != "ALL" else None,
                    "lead_source_id": lead_source_id if lead_source_id != 0 else None
                }
            }
            
            return Response(response_data, status=200)
            
        except Exception as e:
            logger.error(f"Error in Digital Marketing Team Detailed DailyReportsView: {str(e)}")
            return Response(
                {"error": f"Internal server error: {str(e)}"}, 
                status=500
            )


class DigitalTeamSalesReportsView(ViewSet):
    """
    Digital Team Sales Performance Reports - Combining prospect handling with actual sales generated by digital team members
    """
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['Digital Team Reports'],
        operation_description="Returns sales performance of digital team members with prospect-to-sale conversion metrics for specified date range. Legacy lead files are completely excluded.",
        manual_parameters=[
            openapi.Parameter(
                name='DEPARTMENT_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by department ID. Use 21 for Digital Marketing or 9 for Customer Service. Digital Marketing (ID: 10) is NOT allowed.',
                required=False,
                default=10,
            ),
            openapi.Parameter(
                name='START_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='Start date for sales date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='END_DATE',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                format='date',
                description='End date for sales date range filtering (YYYY-MM-DD). Defaults to today.',
                required=False,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(
                name='DEPARTMENT_MEMBER_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description='Filter by specific department member employee number. Use "ALL" to include all members.',
                required=False,
                default='ALL',
            ),
            openapi.Parameter(
                name='LEAD_SOURCE_ID',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Filter by lead source ID. Use 0 to include all lead sources.',
                required=False,
                default=0,
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Page number for pagination.',
                required=False,
                default=1,
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_INTEGER,
                description='Number of results per page.',
                required=False,
                default=50,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Successful response with digital team sales performance data (legacy lead files completely excluded)",
                examples={
                    "application/json": {
                        "status": "success",
                        "message": "Digital team sales performance report for department 21 from 2025-09-21 to 2025-09-28",
                        "data": [
                            {
                                "department_member_id": "TM001",
                                "department_member_name": "John Doe",
                                "department_name": "DIGITAL MARKETING", 
                                "prospects_handled": 45,
                                "sales_generated": 3,
                                "conversion_rate": 6.67,
                                "total_sales_value": 4500000.00,
                                "total_paid": 1350000.00,
                                "balance_remaining": 3150000.00,
                                "average_sale_value": 1500000.00,
                                "start_date": "2025-09-21",
                                "end_date": "2025-09-28"
                            }
                        ]
                    }
                }
            ),
            400: openapi.Response(description="Bad request - Invalid parameters"),
            500: openapi.Response(description="Internal server error")
        }
    )
    def list(self, request):
        """
        Get sales performance of digital marketing and customer service team members for specified date range
        """
        try:
            # Get and validate parameters (compatible with both DRF and Django requests)
            query_params = getattr(request, 'query_params', request.GET)
            department_id = query_params.get("DEPARTMENT_ID", 10)
            start_date = query_params.get("START_DATE", datetime.date.today().isoformat())
            end_date = query_params.get("END_DATE", datetime.date.today().isoformat())
            department_member_id = query_params.get("DEPARTMENT_MEMBER_ID", "ALL")
            lead_source_id = query_params.get("LEAD_SOURCE_ID", 0)
            
            page = int(query_params.get("page", 1))
            page_size = int(query_params.get("page_size", 50))
            
            # Validate date formats
            try:
                start_date_obj = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
                end_date_obj = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"error": "Invalid date format. Use YYYY-MM-DD for both START_DATE and END_DATE."}, 
                    status=400
                )
            
            # Validate date range
            if start_date_obj > end_date_obj:
                return Response(
                    {"error": "START_DATE cannot be later than END_DATE."}, 
                    status=400
                )
            
            # Validate department_id
            try:
                department_id = int(department_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid department_id. Must be an integer."}, 
                    status=400
                )
            
            # Validate lead_source_id
            try:
                lead_source_id = int(lead_source_id)
            except (ValueError, TypeError):
                return Response(
                    {"error": "Invalid lead_source_id. Must be an integer."}, 
                    status=400
                )
            
            
            
            # Validate that department is in allowed digital team departments
            if department_id not in ALLOWED_DIGITAL_TEAM_DEPT_IDS:
                return Response(
                    {"error": f"Department ID {department_id} is not a digital team department. Allowed department: Digital Marketing (ID: {DIGITAL_MARKETING_DEPT_ID})."}, 
                    status=400
                )
            
            # Build dynamic WHERE clauses and parameters for both prospects and sales
            where_clauses_prospects = "lp.department_id = %s AND DATE(lp.date) BETWEEN %s AND %s"
            where_clauses_sales = "lp.department_id = %s AND lf.booking_date BETWEEN %s AND %s"
            params_prospects = [department_id, start_date, end_date]
            params_sales = [department_id, start_date, end_date]
            
            # Add optional filters
            if department_member_id and department_member_id != "ALL":
                where_clauses_prospects += " AND lp.department_member_id = %s"
                where_clauses_sales += " AND lp.department_member_id = %s"
                params_prospects.append(department_member_id)
                params_sales.append(department_member_id)
            
            if lead_source_id and lead_source_id != 0:
                where_clauses_prospects += " AND lp.lead_source_id = %s"
                where_clauses_sales += " AND lp.lead_source_id = %s"
                params_prospects.append(lead_source_id)
                params_sales.append(lead_source_id)
            
            # Hard-code exclusion of legacy lead files - now handled explicitly in SQL
            # where_clauses_sales += " AND lf.lead_type = %s"
            # params_sales.append("new")
            
            # Simplified SQL query - teleteam sales performance with prospect handling metrics  
            # Direct LEFT JOIN approach for better reliability and performance
            combined_sql = f"""
            SELECT 
                lp.department_member_id AS department_member_id,
                uu.fullnames AS department_member_name,
                ud.dp_name AS department_name,
                COUNT(DISTINCT lp.id) AS prospects_handled,
                COUNT(DISTINCT lf.lead_file_no) AS sales_generated,
                CASE 
                    WHEN COUNT(DISTINCT lp.id) > 0 THEN 
                        ROUND((COUNT(DISTINCT lf.lead_file_no) * 100.0 / COUNT(DISTINCT lp.id)), 2)
                    ELSE 0 
                END AS conversion_rate,
                IFNULL(SUM(lf.selling_price), 0) AS total_sales_value,
                IFNULL(SUM(lf.total_paid), 0) AS total_paid,
                IFNULL(SUM(lf.balance_lcy), 0) AS balance_remaining,
                CASE 
                    WHEN COUNT(DISTINCT lf.lead_file_no) > 0 THEN 
                        ROUND(SUM(lf.selling_price) / COUNT(DISTINCT lf.lead_file_no), 2)
                    ELSE 0 
                END AS average_sale_value,
                %s AS start_date,
                %s AS end_date
            FROM leads_prospects lp
            LEFT JOIN users_user uu ON lp.department_member_id = uu.employee_no
            LEFT JOIN users_departments ud ON lp.department_id = ud.dp_id
            LEFT JOIN sales_leadfile lf ON lf.lead_file_no = lp.leadfiles 
                AND lf.booking_date BETWEEN %s AND %s 
                AND lf.lead_type IS NULL
            WHERE {where_clauses_prospects}
            GROUP BY lp.department_member_id, uu.fullnames, ud.dp_name
            ORDER BY 
                sales_generated DESC,
                prospects_handled DESC,
                lp.department_member_id ASC
            """
            
            # Add parameters for the simplified query
            params_combined = ([start_date, end_date] +  # Date parameters for SELECT clause
                              [start_date, end_date] +  # Date parameters for LEFT JOIN ON clause
                              params_prospects)         # WHERE clause parameters
            
            # Execute query
            with connections["reports"].cursor() as cursor:
                cursor.execute(combined_sql, params_combined)
                cols = [c[0] for c in cursor.description]
                results = [dict(zip(cols, row)) for row in cursor.fetchall()]
            
            # Convert decimal and date objects to strings for JSON serialization
            for result in results:
                for key, value in result.items():
                    if hasattr(value, '_proxy____cast'):  # Decimal field
                        result[key] = float(value) if value is not None else 0.0
                    elif hasattr(value, 'isoformat'):  # Date field
                        result[key] = str(value)
            
            # Implement pagination
            total_count = len(results)
            total_pages = (total_count + page_size - 1) // page_size
            
            try:
                paginator = Paginator(results, page_size)
                paginated_results = paginator.page(page).object_list
            except PageNotAnInteger:
                paginated_results = paginator.page(1).object_list
                page = 1
            except EmptyPage:
                paginated_results = paginator.page(paginator.num_pages).object_list
                page = paginator.num_pages
            
            # Build response message
            dept_filter = f"department {department_id}"
            member_filter = f" for member {department_member_id}" if department_member_id != "ALL" else ""
            source_filter = f" from lead source {lead_source_id}" if lead_source_id != 0 else ""
            
            # Determine the period description
            if start_date == end_date:
                period_desc = f"on {start_date}"
            else:
                # Calculate period type for user-friendly description
                date_diff = (end_date_obj - start_date_obj).days + 1
                if date_diff == 7:
                    period_desc = f"for week ({start_date} to {end_date})"
                elif date_diff in [28, 29, 30, 31]:
                    period_desc = f"for month ({start_date} to {end_date})"
                else:
                    period_desc = f"from {start_date} to {end_date}"
            
            message = f"Digital Marketing sales performance report for {dept_filter} {period_desc}{member_filter}{source_filter}"
            
            response_data = {
                "status": "success",
                "message": message,
                "data": paginated_results,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_count": total_count,
                    "total_pages": total_pages
                },
                "filters_applied": {
                    "department_id": department_id,
                    "start_date": start_date,
                    "end_date": end_date,
                    "date_range_days": (end_date_obj - start_date_obj).days + 1,
                    "department_member_id": department_member_id if department_member_id != "ALL" else None,
                    "lead_source_id": lead_source_id if lead_source_id != 0 else None
                },
                "summary": {
                    "total_members": len(results),
                    "total_prospects_handled": sum(r.get('prospects_handled', 0) for r in results),
                    "total_sales_generated": sum(r.get('sales_generated', 0) for r in results),
                    "total_sales_value": sum(r.get('total_sales_value', 0) for r in results),
                    "overall_conversion_rate": round(
                        (sum(r.get('sales_generated', 0) for r in results) * 100.0 / 
                         max(sum(r.get('prospects_handled', 0) for r in results), 1)), 2
                    ) if results else 0
                }
            }
            
            return Response(response_data, status=200)
            
        except Exception as e:
            logger.error(f"Error in DigitalMarketingSalesReportsView: {str(e)}")
            return Response(
                {"error": f"Internal server error: {str(e)}"}, 
                status=500
            )
