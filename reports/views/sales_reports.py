from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime,os
import MySQLdb.cursors
import logging




def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")


@swagger_tags(['Sales Reports'])
class NewSalesReportsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all new sales based on a start date and end date.",
        manual_parameters=[
            openapi.Parameter(name='start_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=(datetime.date.today() - datetime.timedelta(days=7)).isoformat(),
            ),
            openapi.Parameter(name='end_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(name='OFFICE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by office. If not provided, all offices are included.',
                enum=['HQ','KAREN','ALL'],required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='LEAD_SOURCE'
                ,in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by lead source ID. If not provided, all lead sources are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='DIASPORA_REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by diaspora region. If not provided, all regions are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='ORGANIZATION_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date") 
        office = request.query_params.get("OFFICE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        lead_source_id = request.query_params.get("LEAD_SOURCE", "ALL")
        diaspora_region = request.query_params.get("DIASPORA_REGION", "ALL")
        marketer_team = request.query_params.get("MARKETER_TEAM", "ALL")
        organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
    
        if not start_date:
            start_date = (datetime.date.today() - datetime.timedelta(days=7)).isoformat()
        
        if not end_date:
            end_date = datetime.date.today().isoformat()
        try:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build dynamic WHERE clauses based on filters
        join_clause = f""
        where_clauses = ""
        params=[]
        stat=f"for ALL"
        if office and office != "ALL":
            join_clause=f" JOIN `users_user` uu on sl.`marketer_id`= uu.`employee_no` "
            where_clauses= f" uu.`office` = %s"
            params = [office]
            stat=f"for {office}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= " `marketer_id` = %s"
            params=[marketer_employee_no]
            stat=f"for Marketer {marketer_employee_no}"
        if lead_source_id != "ALL":
            where_clauses=f" `customer_lead_source_id` = %s"
            params=[lead_source_id]
            stat=f"for lead source {lead_source_id}"
        if diaspora_region and diaspora_region != "ALL":
            where_clauses =f""" `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
            `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s))"""
            params=[diaspora_region]
            stat=f"for Diaspora region {diaspora_region}"
        if marketer_team and marketer_team != "ALL":
            join_clause=f" JOIN `users_user` uu on sl.`marketer_id`= uu.`employee_no` "
            where_clauses=f" uu.`team_id` = %s"
            params=[marketer_team]
            stat=f"for Team {marketer_team}"
        # if organization_team and organization_team != "ALL":
        #     where_clauses_list.append(f"`ORGANIZATION_TEAM` = %s")
        #     params.append(organization_team)
        
        # params for start_date and end_date should be at the end
        if where_clauses != "":
            where_clauses= where_clauses + "AND"
        params.extend([start_date, end_date])  
        base_sql = f""" SELECT sl.`lead_file_no`,sl.`customer_name`,sl.`plot_id`,sl.`selling_price`,sl.`total_paid`,sl.`balance_lcy`,sl.`marketer_id` FROM `sales_leadfile` sl {join_clause} WHERE {where_clauses} `booking_date` BETWEEN %s AND %s ORDER BY `lead_file_no` DESC LIMIT 1000 """
        
        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "New Sales Reports from {} to {}  for {}" .format(start_date, end_date, stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

@swagger_tags(['Sales Reports'])
class InstallmentsDueTodayReportsView(ViewSet):
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    queryset = None
    @swagger_auto_schema(
        operation_id="Installments Reports",
        operation_description="Returns all installments that are due today",
        
      manual_parameters=[            
            openapi.Parameter(name='project_id',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by project ID. If not provided, all projects are included.',
                 default='ALL'),
            openapi.Parameter(name='marketer_no',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by marketer employee number. If not provided, all marketers are included.',
                 default='ALL'),
            openapi.Parameter(name='team',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by team ID. If not provided, all teams are included.',
                 default='ALL'),
            openapi.Parameter(name='credit_officer',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by credit officer employee number. If not provided, all credit officers are included.',
                 default='ALL'),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False,
                description='Page number for pagination.', default=1),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False,
                description='Number of results per page.', default=20),
        ],
    )
    def list(self, request):
        currentDate = datetime.date.today().isoformat()
        # validate and parse query 
        currentDate = datetime.date.today().isoformat()
        # validate and parse query 
        project_id = request.query_params.get("project_id", "ALL")
        marketer_no = request.query_params.get("marketer_no", "ALL")
        team = request.query_params.get("team", "ALL")
        credit_officer = request.query_params.get("credit_officer", "ALL")
        # SQL query to fetch installments due today dynamically based on the parameters
         # Always filter by today's date 
        where_clauses =""
        params = []
        stat = "for Installments Due Today"
        join_clause = f""
        if project_id and project_id != "ALL":
            where_clauses = "lf.project_id = %s"
            params = [project_id]
            join_clause = ""  # No join needed
            stat = f"for Project {project_id}"
            # Marketer filter
        if marketer_no and marketer_no != "ALL":
            where_clauses = "i.marketer_no = %s"
            join_clause = "JOIN `users_user` u ON i.marketer_no = u.employee_no"
            params = [marketer_no]
            stat = f"for Marketer {marketer_no}"
            # Team filter
        if team and team != "ALL":
            where_clauses = "u.team_id = %s"
            join_clause = "JOIN `users_user` u ON i.marketer_no = u.employee_no"
            params = [team]
            stat = f"for Team {team}"
            # Credit Officer filter
        if credit_officer and credit_officer != "ALL":
            where_clauses = " lf.credit_officer_id = %s"
            join_clause = "JOIN `users_user` u ON lf.credit_officer_id = u.erp_user_id"
            params = [credit_officer]
            stat = f"for Credit Officer {credit_officer}"


        # Build WHERE clause
        if where_clauses != "":
            where_clauses= where_clauses + "AND"
        # Final SQL query
        base_sql = f"""
            SELECT 
            i.`no`, 
            i.`due_date`,
            i.`customer_number`,
            i.`total_to_collect`,
            i.`total_previously_collected`,
            i.`installments_due`,
            i.`current_balance`,
            i.`overdue_collections`,
            i.`penalties_accrued`,
            i.`completion_date`,
            i.`plot_name`,
            lf.`lead_file_no`,
            lf.`customer_name`, 
            lf.`project_id`, 
            lf.`marketer_id`,
            uu.`fullnames` 
            FROM `portfolio_lines` i
            JOIN `sales_leadfile` lf ON i.lead_file_no = lf.lead_file_no
            JOIN `users_user` uu ON lf.marketer_id = uu.employee_no
            {join_clause}
            WHERE
            {where_clauses}
            installments_due > 0 AND i.due_date = CURDATE()
            ORDER BY i.lead_file_no DESC
            LIMIT 1000;
        """
        sql=  base_sql 
        print("SQL:", sql)
        print("Params:", params)
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)
        
        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Installment Due Report from {}  {}".format(currentDate, stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Sales Reports'])
class OverdueInstallmentsReportsView(ViewSet):
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]  
    queryset = None  

    @swagger_auto_schema(
        operation_id="Overdue Installments Reports",
        operation_description="Returns all overdue installments",
        manual_parameters=[
            openapi.Parameter(name='start_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=(datetime.date.today() - datetime.timedelta(days=7)).isoformat(),
            ),
            openapi.Parameter(name='end_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(name='project_id',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by project ID. If not provided, all projects are included.',
                 default='ALL'),
            openapi.Parameter(name='marketer_no',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by marketer employee number. If not provided, all marketers are included.',
                 default='ALL'),
            openapi.Parameter(name='team',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by team ID. If not provided, all teams are included.',
                 default='ALL'),
            openapi.Parameter(name='credit_officer',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by credit officer employee number. If not provided, all credit officers are included.',
                 default='ALL'),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False,
                description='Page number for pagination.', default=1),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False,
                description='Number of results per page.', default=20),
        ],
    )
    def list(self, request):
        # validate and parse query parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date") 
        project_id = request.query_params.get("project_id", "ALL")
        marketer_no = request.query_params.get("marketer_no", "ALL")
        team = request.query_params.get("team", "ALL")
        credit_officer = request.query_params.get("credit_officer", "ALL")
        if not start_date:
            start_date = (datetime.date.today() - datetime.timedelta(days=7)).isoformat()
        
        if not end_date:
            end_date = datetime.date.today().isoformat()
        try:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # SQL query to fetch overdue installments dynamically based on the parameters
        where_clauses = "" 
        params = [start_date, end_date]
        join_clause = f""
        stat = "for ALL"

        # Filter by Project ID
        # Filter by Project ID
        if project_id and project_id != "ALL":
            where_clauses = " lf.project_id = %s"
            params = [project_id]
            join_clause = ""  # No join needed
            stat = f"for Project {project_id}"

        # Filter by Marketer Number (overrides project if both exist)
        # Filter by Marketer Number (overrides project if both exist)
        if marketer_no and marketer_no != "ALL":
            join_clause = "JOIN `users_user` u ON i.marketer_no = u.employee_no"
            where_clauses = "i.marketer_no = %s"
            params = [marketer_no]
            stat = f"for Marketer {marketer_no}"
        # Filter by Team (overrides project if both exist)
        if team and team != "ALL":
            join_clause= "JOIN `users_user` u ON i.marketer_no = u.employee_no"
            where_clauses = " u.team_id = %s"
            params = [team]
            stat = f"for Team {team}"
        # Filter by Credit Officer (overrides project if both exist)
        if credit_officer and credit_officer != "ALL":
            join_clause = "JOIN `users_user` u ON lf.credit_officer_id = u.erp_user_id"
            params = [credit_officer]
            where_clauses = "lf.credit_officer_id = %s"  
            stat = f"for Credit Officer {credit_officer}"

        # Build WHERE clause
        if where_clauses != "":
            where_clauses = where_clauses + "AND"
            params.extend([start_date, end_date])  
        # Build the SQL query
        base_sql =f"""
            SELECT 
                i.`no`, 
                i.`due_date`,
                i.`customer_number`,
                i.`total_to_collect`,
                i.`total_previously_collected`,
                i.`installments_due`,
                i.`current_balance`,
                i.`overdue_collections`,
                i.`penalties_accrued`,
                i.`completion_date`,
                i.`plot_name`,
                lf.`lead_file_no`,
                lf.`customer_name`, 
                lf.`project_id`, 
                lf.`marketer_id`,
                uu.`fullnames`
            FROM `portfolio_lines` i
            JOIN `sales_leadfile` lf ON i.lead_file_no = lf.lead_file_no
            JOIN users_user uu ON i.marketer_no = uu.employee_no
            {join_clause}
            WHERE
            {where_clauses}
            due_date BETWEEN %s AND %s
            AND overdue_collections > 0
            ORDER BY i.lead_file_no DESC
            LIMIT 1000;
        """
        print("SQL:", base_sql)
        print("Params:", params)
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": f"Overdue Installments Report from {start_date} to {end_date}  {stat}",
            "Title": f"Overdue Installments Report from {start_date} to {end_date}  {stat}",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
        

@swagger_tags(['Sales Reports'])
class OngoingSalesReportsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all ongoing sales ,ie balance>0 and not dropped",
          manual_parameters=[
            openapi.Parameter(name='project_id',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by project ID. If not provided, all projects are included.',
                 default='ALL'),
            
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='LEAD_SOURCE'
                ,in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by lead source ID. If not provided, all lead sources are included.',required=False,default='ALL',
            ),
           
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):
        
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        lead_source_id = request.query_params.get("LEAD_SOURCE", "ALL")
        project_id = request.query_params.get("project_id", "ALL")
        
        
        
        where_clauses = " 1=1"
        params = []
        stat = "for ALL"
        if project_id and project_id != "ALL":
            where_clauses ="`project_id` = %s"
            params=[project_id]
            getmonth_sql = "SELECT `name` FROM `inventory_project` WHERE `projectId`=%s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(project_id,), dict_cursor=True)
            project_name = row['name'] if row and 'name' in row else None
            stat = f"for Project {project_name}"

        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses ="`marketer_id` = %s"
            params=[marketer_employee_no]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat = f"for Marketer {marketer_fullname}"
            
        if lead_source_id and lead_source_id != "ALL":
            where_clauses ="`customer_lead_source_id` = %s"
            params=[lead_source_id]
            stat = f"for Marketer {lead_source_id}"

        
        
        # Build WHERE clause
        where_clause_str = f"""WHERE `lead_file_status_dropped` = 0 AND `balance_lcy` > 0 AND {where_clauses}"""

       
     
        base_sql = f"""SELECT `lead_file_no`,`customer_name`,`plot_id`,`selling_price`,`total_paid`,`balance_lcy`,`marketer_id`,uu.`fullnames`,`project_id`
        FROM `sales_leadfile`lf JOIN `users_user`uu on lf.`marketer_id` = uu.`employee_no` {where_clause_str} """
        print (base_sql)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql,params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        
        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Ongoning sales {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })


@swagger_tags(['Sales Reports'])
class CompletedSalesReportsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all ongoing sales ,ie balance>0 and not dropped",
          manual_parameters=[
            openapi.Parameter(name='project_id',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Filter by project ID. If not provided, all projects are included.',
                 default='ALL'),
            
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='LEAD_SOURCE'
                ,in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by lead source ID. If not provided, all lead sources are included.',required=False,default='ALL',
            ),
           
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):
        
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        lead_source_id = request.query_params.get("LEAD_SOURCE", "ALL")
        project_id = request.query_params.get("project_id", "ALL")
        
        
        
        where_clauses = " 1=1"
        params = []
        stat = "for ALL"
        if project_id and project_id != "ALL":
            where_clauses ="`project_id` = %s"
            params=[project_id]
            getmonth_sql = "SELECT `name` FROM `inventory_project` WHERE `projectId`=%s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(project_id,), dict_cursor=True)
            project_name = row['name'] if row and 'name' in row else None
            stat = f"for Project {project_name}"

        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses ="`marketer_id` = %s"
            params=[marketer_employee_no]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat = f"for Marketer {marketer_fullname}"
            
        if lead_source_id and lead_source_id != "ALL":
            where_clauses ="`customer_lead_source_id` = %s"
            params=[lead_source_id]
            stat = f"for Marketer {lead_source_id}"

        
        
        # Build WHERE clause
        where_clause_str = f"""WHERE `lead_file_status_dropped` = 0 AND `balance_lcy` <= 0 AND {where_clauses}"""

       
     
        base_sql = f"""SELECT `lead_file_no`,`customer_name`,`plot_id`,`selling_price`,`total_paid`,`balance_lcy`,`marketer_id`,uu.`fullnames`,`project_id`
        FROM `sales_leadfile`lf JOIN `users_user`uu on lf.`marketer_id` = uu.`employee_no` {where_clause_str} """
        print (base_sql)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql,params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        
        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Completed sales {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
        
@swagger_tags(['Sales Reports'])
class CashonCashReportsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all cash on cash bases on  start date, end date (posted dates) or period start date eg 2023-06-21",
        manual_parameters=[
            openapi.Parameter(name='start_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=(datetime.date.today() - datetime.timedelta(days=7)).isoformat(),
            ),
            openapi.Parameter(name='end_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(name='period_start_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='eg 2023-06-21 for period starting 2023-06-21 to 2023-07-21',
                required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date") 
        period_start_date = request.query_params.get("period_start_date", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
    
        if not start_date:
            start_date = (datetime.date.today() - datetime.timedelta(days=7)).isoformat()
        
        if not end_date:
            end_date = datetime.date.today().isoformat()
        try:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build dynamic WHERE clauses based on filters
        join_clause = f""
        where_clauses = ""
        params=[]
        stat=f"for ALL"
       
            
        if period_start_date and period_start_date != "ALL":
            where_clauses ="`Period` = %s"
            params=[period_start_date]
            start_date = period_start_date
            stat = f"for Marketing period started on {period_start_date}"    
         
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses ="`marketer_id` = %s"
            params=[marketer_employee_no]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat = f" Marketer {marketer_fullname}"
        
        # params for start_date and end_date should be at the end
        if where_clauses != "":
            where_clauses= where_clauses + "AND"
        params.extend([start_date, end_date])  
        base_sql = f""" SELECT * FROM `cash_on_cash` WHERE {where_clauses} `Transaction_date` BETWEEN %s AND %s ORDER BY `Transaction_date` DESC LIMIT 1000 """
        
        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Cash on cash Reports from {} to {}  for {}" .format(start_date, end_date, stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })


@swagger_tags(['Sales Reports'])
class MIBReportsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all MIB based on a start date and end date.",
        manual_parameters=[
            openapi.Parameter(name='start_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=(datetime.date.today() - datetime.timedelta(days=7)).isoformat(),
            ),
            openapi.Parameter(name='end_date',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,required=True,
                default=datetime.date.today().isoformat(),
            ),
            openapi.Parameter(name='OFFICE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by office. If not provided, all offices are included.',
                enum=['HQ','KAREN','ALL'],required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='LEAD_SOURCE'
                ,in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by lead source ID. If not provided, all lead sources are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='DIASPORA_REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by diaspora region. If not provided, all regions are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            # openapi.Parameter(name='ORGANIZATION_TEAM',
            #     in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
            #     description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            # ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date") 
        office = request.query_params.get("OFFICE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        lead_source_id = request.query_params.get("LEAD_SOURCE", "ALL")
        diaspora_region = request.query_params.get("DIASPORA_REGION", "ALL")
        marketer_team = request.query_params.get("MARKETER_TEAM", "ALL")
        # organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
    
        if not start_date:
            start_date = (datetime.date.today() - datetime.timedelta(days=7)).isoformat()
        
        if not end_date:
            end_date = datetime.date.today().isoformat()
        try:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build dynamic WHERE clauses based on filters
        
        where_clause = """Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND 1=%s))"""
        where_clause_2= """ `Lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 ))"""
        p=1
        params=[]
        params2=[]
        stat=f"for ALL"
        if office and office != "ALL":
            where_clause = """
                 Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`=%s))"""
            p=office   
            where_clause_2= """ `Lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` IN ( SELECT `employee_no` FROM `users_user` WHERE `is_marketer`= 1 AND `office`=%s))"""
            params2=[office]
            stat=f" {office}"
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clause = """
                 Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` =%s)"""
            p=marketer_employee_no
            where_clause_2= """ `Lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `marketer_id` =%s)"""
            params2=[marketer_employee_no]
            
            
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat = f" Marketer {marketer_fullname}"    
            
        if lead_source_id != "ALL":
            where_clause = """
                 Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id` =%s)"""
            p=lead_source_id
            where_clause_2= """ `Lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id` =%s)"""
            params2=[lead_source_id]
            stat=f" lead source {lead_source_id}"
        if diaspora_region and diaspora_region != "ALL":
            where_clause = """
                 Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s)))"""
            p=diaspora_region
            where_clause_2= """ `Lead_file_no` IN 
                (SELECT `lead_file_no` FROM `sales_leadfile` WHERE `lead_file_status_dropped`= 0 AND
                `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s)))"""
            params2=[diaspora_region]
            
            stat=f" Diaspora region {diaspora_region}"
        if marketer_team and marketer_team != "ALL":
            where_clause = """
                 Pay_mode NOT IN ('Voucher', 'Supplier', 'Commission on Referral')
                AND Pay_mode IS NOT NULL AND `Teams` = %s """
            p=marketer_team
            where_clause_2= """ `Teams` = %s"""
            params2=[marketer_team]
            
            stat=f" Team {marketer_team}"
 
        # params for start_date and end_date should be at the end
            
        time_clause = f"""WHERE {where_clause} AND POSTED_DATE1  BETWEEN %s AND %s """    
        transaction_clause = f"""{time_clause} AND Transaction_type NOT IN ('Overpayment', 'Final Transfer Cost', 'Part of Transfer Cost', 'Transfer Cost')"""  
        base_sql = f"""SELECT 
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_postedreceipt {transaction_clause} ), 0) AS posted_total,
                    COALESCE((
                        SELECT SUM(Amount_LCY)
                        FROM receipts_cancelledreceipt {transaction_clause} ), 0) AS canceled_total;"""
        # print ("SQL:", base_sql)                
        params = [p,start_date, end_date,p,start_date, end_date]    
        row = fetch_one(base_sql, "Error fetching Daily MIB",params=(params), dict_cursor=True)
        Total_mib = row['posted_total'] - row['canceled_total'] if row else 0
          
        base_sql_2=f"""SELECT `Receipt_No`,`Lead_file_no`,`Marketer`,`Plot_NO`,`Customer_Id`,`Customer_Name`,`Amount_LCY` 
        FROM `receipts_postedreceipt` WHERE {where_clause_2} AND POSTED_DATE1  BETWEEN %s AND %s AND `Transaction_type` IN ('Deposit','Additional Deposit')
        """
        params2.extend([start_date, end_date])
          

        # print("SQL:", base_sql_2)
            
        sql=  base_sql_2 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params2)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        

        # --- Close all connections
        for c in connections.all():
                c.close()
                
        if not data:
            return Response({"message": "No data fetched."}, status=200)
        else:
            # Pagination
            page = request.query_params.get('page', 1)
            page_size = request.query_params.get('page_size', 20)
            try:
                page = int(page)
            except ValueError:
                page = 1
            try:
                page_size = int(page_size)
            except ValueError:
                page_size = 20
            paginator = Paginator(data, page_size)
            try:
                paged_data = paginator.page(page)
            except PageNotAnInteger:
                paged_data = paginator.page(1)
            except EmptyPage:
                paged_data = paginator.page(paginator.num_pages)

            return Response({
                "Title": "MIB Report from {} to {}  for {}" .format(start_date, end_date, stat),
                "Total MIB": Total_mib,
                "Total Results": paginator.count,
                "count": paginator.count,
                "num_pages": paginator.num_pages,
                "current_page": paged_data.number,
                "results": paged_data.object_list
            })

@swagger_tags(['Sales Reports'])
class BelowThresholdReportsView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Returns all sales below a certain threshold as per today's date.That is 14 days after booking date, total paid < deposit threshold and not dropped.",
        manual_parameters=[ 
            openapi.Parameter(name='project_id', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',description='Filter by project ID. If not provided, all projects are included.'),
            openapi.Parameter(name='marketer_no', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',description='Filter by marketer. If not provided, all marketers are included.'),
            openapi.Parameter(name='team', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',description='Filter by team. If not provided, all teams are included.'),
            openapi.Parameter(name='credit_officer', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',description='Filter by credit officer. If not provided, all credit officers are included.'),
            openapi.Parameter(name='page', in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, default=1,description='Page number for pagination.'),
            openapi.Parameter(name='page_size', in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER, default=20, description='Number of results per page.'),
        ],
    )
    def list(self, request):
        project_id = request.query_params.get("project_id", "ALL")
        marketer_no = request.query_params.get("marketer_no", "ALL")
        team = request.query_params.get("team", "ALL")
        credit_officer = request.query_params.get("credit_officer", "ALL")
        # Default date for the report
        today = datetime.date.today().isoformat()
        join_clause =f""
        where_clauses =""
        params = []
        stat = "for ALL"

        # Enforce only the LAST applicable filter (highest priority: credit_officer)
        if project_id != "ALL":
            where_clauses ="lf.project_id = %s"
            params =[project_id]
            stat = f"for Project {project_id}"

        if marketer_no != "ALL":
            join_clause ="JOIN users_user lf_user ON lf.marketer_id = lf_user.employee_no"
            where_clauses="lf.marketer_id = %s"
            params= [marketer_no]
            stat = f"for Marketer {marketer_no}"

        if team != "ALL":
            join_clause ="JOIN users_user team_user ON lf.marketer_id = team_user.employee_no"
            where_clauses ="team_user.team_id = %s"
            params=[team]
            stat = f"for Team {team}"

        if credit_officer != "ALL":
            join_clause ="JOIN users_user credit_user ON lf.credit_officer_id = credit_user.erp_user_id"
            where_clauses ="lf.credit_officer_id = %s"
            params=[credit_officer]
            stat = f"for Credit Officer {credit_officer}"

        # Final SQL
        if where_clauses != "":
            where_clauses= where_clauses + "AND"
            params.extend([today])
            
        base_sql = f"""
            SELECT 
                lf.lead_file_no,
                lf.booking_date, 
                lf.customer_name,
                lf.additional_deposit_date,
                lf.plot_no,
                lf.purchase_price,
                lf.selling_price,
                lf.deposit_threshold,
                lf.total_paid,
                lf.balance_lcy,
                lf.marketer_id
            FROM sales_leadfile lf
            {join_clause}
            WHERE 
            {where_clauses}
            lf.total_paid < lf.deposit_threshold
            AND lf.lead_file_status_dropped = 0
            ORDER BY lf.lead_file_no
            LIMIT 1000;
        """
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        return Response({
            "Title": f"Sales Below Threshold Report {stat}",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Sales Reports'])

class InstallmentDueReportsView(ViewSet):
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_id="Installment Due Reports",
        operation_description="Returns all installment due reports",
        manual_parameters=[
            openapi.Parameter("start_date", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=True, description="Start date in YYYY-MM-DD", default=(datetime.date.today() - datetime.timedelta(days=7)).isoformat()),
            openapi.Parameter("end_date", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=True, description="End date in YYYY-MM-DD", default=datetime.date.today().isoformat()),
            openapi.Parameter("project_id", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False, default="ALL"),
            openapi.Parameter("marketer_no", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False, default="ALL"),
            openapi.Parameter("team", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False, default="ALL"),
            openapi.Parameter("credit_officer", openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False, default="ALL"),
            openapi.Parameter("page", openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False, default=1),
            openapi.Parameter("page_size", openapi.IN_QUERY, type=openapi.TYPE_INTEGER, required=False, default=20),
        ]
    )
    def list(self, request):
        # Step 1: Parse query params
        start_date = request.query_params.get("start_date")
        end_date = request.query_params.get("end_date")
        project_id = request.query_params.get("project_id", "ALL")
        marketer_no = request.query_params.get("marketer_no", "ALL")
        team = request.query_params.get("team", "ALL")
        credit_officer = request.query_params.get("credit_officer", "ALL")

        if not start_date:
            start_date = (datetime.date.today() - datetime.timedelta(days=7)).isoformat()
        if not end_date:
            end_date = datetime.date.today().isoformat()

        try:
            start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            end_date = datetime.datetime.strptime(end_date, "%Y-%m-%d").date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)

        # Step 2: Build WHERE and JOIN clauses
        base_conditions =""
        params = [start_date, end_date]
        join_clause = f""
        stat = "ALL Installments"

        if project_id != "ALL":
            base_conditions = f"lf.project_id = %s"
            params = [project_id]
            stat = f"Project {project_id}"

        if marketer_no != "ALL":
            join_clause = "JOIN `users_user` u ON lf.marketer_id = u.employee_no"
            base_conditions = f"lf.marketer_id = %s"
            params = [marketer_no]
            stat = f"Marketer {marketer_no}"

        if team != "ALL":
            join_clause = "JOIN `users_user` u ON i.marketer_no = u.employee_no"
            base_conditions = "u.team_id = %s"
            params = [team]
            stat = f"Team {team}"

        if credit_officer != "ALL":
            base_conditions = f" lf.credit_officer_id = %s "
            join_clause = "JOIN `users_user` u ON lf.credit_officer_id = u.erp_user_id"
            params = [credit_officer]
            stat = f"Credit Officer {credit_officer}"
    
        if base_conditions != "":
            base_conditions= base_conditions + "AND"
            params.extend([start_date, end_date])  
        # Step 3: SQL Query
        base_sql = f"""
            SELECT 
                i.`no`, 
                i.`due_date`,
                i.`customer_number`,
                i.`total_to_collect`,
                i.`total_previously_collected`,
                i.`installments_due`,
                i.`current_balance`,
                i.`overdue_collections`,
                i.`penalties_accrued`,
                i.`completion_date`,
                i.`plot_name`,
                lf.`lead_file_no`,
                lf.`customer_name`, 
                lf.`project_id`, 
                lf.`marketer_id`,
                uu.`fullnames` AS marketer_fullname
            FROM `portfolio_lines` i
            JOIN `sales_leadfile` lf ON i.lead_file_no = lf.lead_file_no
            JOIN `users_user` uu ON lf.marketer_id = uu.employee_no
            {join_clause}
            WHERE
            {base_conditions}
            due_date BETWEEN %s AND %s
            AND i.installments_due > 0
            AND lf.lead_file_status_dropped = 0
            ORDER BY i.lead_file_no DESC
            LIMIT 1000;
        """
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": f"Installment Due Reports from {start_date} to {end_date} for {stat}",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list,
        })

@swagger_tags(['Sales Reports'])
class OverdueBelowThresholdReportsView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Returns all overdue sales below a certain threshold",
        manual_parameters=[
            openapi.Parameter(name='project_id', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING,
                            description="Filter by project ID. If not provided, all projects are included.",
                            default='ALL'),
            openapi.Parameter(name='marketer_no', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',
                            description="Filter by marketer number. If not provided, all marketers are included."
                            ),
            openapi.Parameter(name='team', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',
                              description="Filter by team. If not provided, all teams are included."
                              ),
            openapi.Parameter(name='credit_officer', in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, default='ALL',
                                description="Filter by credit officer. If not provided, all credit officers are included."
                                ),
            openapi.Parameter(name='page', in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                              description="Page number for pagination. Default is 1.",
                              default=1),
            openapi.Parameter(name='page_size', in_=openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                              description="Number of results per page. Default is 20.",
                              default=20),
        ],
    )   
    def list(self, request):
        project_id = request.query_params.get("project_id", "ALL")
        marketer_no = request.query_params.get("marketer_no", "ALL")
        team = request.query_params.get("team", "ALL")
        credit_officer = request.query_params.get("credit_officer", "ALL")

        join_clause = f""
        where_clauses = ""
        params = []
        stat = "for ALL"
        
        if project_id != "ALL":
            where_clauses = f"lf.project_id = %s"
            params =[project_id]
            stat = f"for Project {project_id}"

        if marketer_no != "ALL":
            join_clause = "JOIN `users_user` u ON lf.marketer_id = u.employee_no"
            where_clauses = f"lf.marketer_id = %s"
            params =[marketer_no]
            stat = f"for Marketer {marketer_no}"

        if team != "ALL":
            join_clause = "JOIN users_user team_user ON lf.marketer_id = team_user.employee_no"
            where_clauses = f"team_user.team_id = %s"
            params = [team]
            stat = f"for Team {team}"
        if credit_officer != "ALL":
            join_clause = "JOIN users_user credit_user ON lf.credit_officer_id = credit_user.erp_user_id"
            where_clauses =  f"lf.credit_officer_id = %s"
            params = [credit_officer]
            stat = f"for Credit Officer {credit_officer}"

        # Final SQL
        if where_clauses != "":
            where_clauses= where_clauses + "AND"
            
        base_sql = f"""
            SELECT 
                lf.lead_file_no,
                lf.booking_date, 
                lf.customer_name, 
                lf.additional_deposit_date,
                lf.plot_no,
                lf.purchase_price,
                lf.selling_price,
                lf.deposit_threshold,
                lf.total_paid,
                lf.balance_lcy,
                lf.marketer_id
            FROM sales_leadfile lf
            {join_clause}
            WHERE {where_clauses}
            lf.total_paid < lf.deposit_threshold
            AND lf.additional_deposit_date <= CURRENT_DATE
            AND lf.lead_file_status_dropped = 0
            ORDER BY lf.lead_file_no
            LIMIT 1000;
        """
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        return Response({
            "Title": f"Sales Below Threshold Report {stat}",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

@swagger_tags(['Sales Reports'])
class SalesView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all new sales based on a start date and end date.",
        manual_parameters=[
            openapi.Parameter(name='OFFICE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by office. If not provided, all offices are included.',
                enum=['HQ','KAREN','ALL'],required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='DIASPORA_REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by diaspora region. If not provided, all regions are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='ORGANIZATION_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['DIASPORA','DIGITAL','TELEMARKETING','OTHER',],
                description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='search',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Search by customer name, lead file number, or plot id.',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        office = request.query_params.get("OFFICE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        diaspora_region = request.query_params.get("DIASPORA_REGION", "ALL")
        organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
        search = request.query_params.get("search", None)

        # Build dynamic WHERE clauses based on filters
        join_clause=f" JOIN `users_user` uu on sl.`marketer_id`= uu.`employee_no` "
        where_clauses = "1=1"
        params=[]
        stat=f"for ALL"
        if office and office != "ALL":
            where_clauses= f"1=1 AND uu.`office` = %s"
            params = [office]
            stat=f"for {office}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= "1=1 AND `marketer_id` = %s"
            params=[marketer_employee_no]
            stat=f"for Marketer {marketer_employee_no}"
        
        if diaspora_region and diaspora_region != "ALL":
            where_clauses =f"""1=1 AND `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
            `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s))"""
            params=[diaspora_region]
            stat=f"for Diaspora region {diaspora_region}"
        
        if organization_team and organization_team != "ALL":

            if organization_team == "DIASPORA":
                where_clauses =f"""1=1 AND `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id ` = %s))"""
                params=['3']
                stat=f"for Organization team {organization_team}"
                
            if organization_team == "DIGITAL":
                where_clauses =f"""1=1 AND `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Digital']
                stat=f"for Organization team {organization_team}"

            if organization_team == "TELEMARKETING":
                where_clauses =f"""1=1 AND `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Telemarketing']
                stat=f"for Organization team {organization_team}"

            if organization_team == "OTHER":
                where_clauses =f"""1=1 AND `customer_lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Other']
                stat=f"for Organization team {organization_team}"
        
        if search and search != "":
            where_clauses += " AND (sl.`customer_name` LIKE %s OR sl.`lead_file_no` LIKE %s OR sl.`plot_no` LIKE %s)"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param])

        # params for start_date and end_date should be at the end
        
        base_sql = f""" SELECT sl.`lead_file_no`,sl.`customer_name`,sl.`plot_id`,sl.`plot_no`,sl.`selling_price`,sl.`total_paid`,sl.`balance_lcy`,sl.`marketer_id`,uu.`fullnames`  FROM `sales_leadfile` sl {join_clause} 
        WHERE {where_clauses}  ORDER BY `lead_file_no` DESC LIMIT 1000 """
        
        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Sales for {}" .format( stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

        
      
    
 