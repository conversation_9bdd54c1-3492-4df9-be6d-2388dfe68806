from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsA<PERSON>enticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from django.db import connections
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.core.exceptions import ValidationError
from django.conf import settings
import logging
import traceback
import datetime,os
import MySQLdb.cursors

# Set up logging
logger = logging.getLogger(__name__)


class LogisticsReportsException(Exception):
    """Custom exception for logistics reports errors"""
    def __init__(self, message, error_code=None, status_code=400):
        self.message = message
        self.error_code = error_code
        self.status_code = status_code
        super().__init__(self.message)


def handle_reports_error(func):
    """Decorator to handle logistics reports errors consistently"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except LogisticsReportsException as e:
            logger.error(f"Logistics Reports Error in {func.__name__}: {e.message}")
            return Response(
                {
                    'error': e.message,
                    'error_code': e.error_code,
                    'timestamp': timezone.now().isoformat()
                },
                status=e.status_code
            )
        except ValidationError as e:
            logger.error(f"Validation Error in {func.__name__}: {str(e)}")
            return Response(
                {
                    'error': 'Validation failed',
                    'details': str(e),
                    'timestamp': timezone.now().isoformat()
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        except Exception as e:
            logger.error(f"Unexpected Error in {func.__name__}: {str(e)}\n{traceback.format_exc()}")
            return Response(
                {
                    'error': 'An unexpected error occurred',
                    'details': str(e) if settings.DEBUG else 'Please contact support',
                    'timestamp': timezone.now().isoformat()
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    return wrapper


def fetch_reports_data(query, params=None, dict_cursor=True):
    """
    Utility function to fetch data from reports database
    """
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            if dict_cursor:
                return cur.fetchall()
            else:
                cols = [c[0] for c in cur.description]
                data = cur.fetchall()
                return [dict(zip(cols, row)) for row in data]
    except Exception as e:
        logger.error(f"Database query error: {str(e)}\nQuery: {query}\nParams: {params}")
        raise LogisticsReportsException(
            f"Failed to fetch data from database: {str(e)}",
            error_code="DATABASE_ERROR",
            status_code=500
        )
    finally:
        # Close connections
        for c in connections.all():
            c.close()


def validate_date_range(start_date_str, end_date_str):
    """
    Validate and parse date range parameters
    """
    try:
        if not start_date_str:
            start_date_str = (datetime.date.today() - datetime.timedelta(days=30)).isoformat()
        if not end_date_str:
            end_date_str = datetime.date.today().isoformat()
            
        start_date = datetime.datetime.strptime(start_date_str, "%Y-%m-%d").date()
        end_date = datetime.datetime.strptime(end_date_str, "%Y-%m-%d").date()
        
        if start_date > end_date:
            raise LogisticsReportsException(
                "Start date must be before end date",
                error_code="INVALID_DATE_RANGE"
            )
            
        return start_date, end_date
    except ValueError:
        raise LogisticsReportsException(
            "Invalid date format. Use YYYY-MM-DD",
            error_code="INVALID_DATE_FORMAT"
        )


def get_common_date_parameters():
    """
    Get common date range parameters for swagger documentation
    """
    return [
        openapi.Parameter(
            'start_date',
            openapi.IN_QUERY,
            description="Start date for the report period (YYYY-MM-DD)",
            type=openapi.TYPE_STRING,
            required=False,
            default=(datetime.date.today() - datetime.timedelta(days=30)).isoformat()
        ),
        openapi.Parameter(
            'end_date',
            openapi.IN_QUERY,
            description="End date for the report period (YYYY-MM-DD)",
            type=openapi.TYPE_STRING,
            required=False,
            default=datetime.date.today().isoformat()
        ),
        openapi.Parameter(
            'page',
            openapi.IN_QUERY,
            description="Page number for pagination",
            type=openapi.TYPE_INTEGER,
            required=False,
            default=1
        ),
        openapi.Parameter(
            'page_size',
            openapi.IN_QUERY,
            description="Number of results per page",
            type=openapi.TYPE_INTEGER,
            required=False,
            default=20
        ),
    ]


def paginate_data(data, request):
    """
    Paginate data based on request parameters
    """
    page = request.query_params.get('page', 1)
    page_size = request.query_params.get('page_size', 20)
    
    try:
        page = int(page)
    except ValueError:
        page = 1
    try:
        page_size = int(page_size)
    except ValueError:
        page_size = 20
        
    paginator = Paginator(data, page_size)
    try:
        paged_data = paginator.page(page)
    except PageNotAnInteger:
        paged_data = paginator.page(1)
    except EmptyPage:
        paged_data = paginator.page(paginator.num_pages)
        
    return {
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': paged_data.number,
        'results': paged_data.object_list
    }


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        methods = ['list']
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                if hasattr(method, '_swagger_auto_schema'):
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        return cls
    return decorator


class BaseLogisticsReportsViewSet(viewsets.ViewSet):
    """
    Base ViewSet for logistics reports with common functionality
    """
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    
    def get_date_range(self, request):
        """
        Get validated date range from request parameters
        """
        start_date_str = request.query_params.get("start_date")
        end_date_str = request.query_params.get("end_date")
        return validate_date_range(start_date_str, end_date_str)


@swagger_tags(['Logistics Reports'])
class SiteVisitsDashboardView(BaseLogisticsReportsViewSet):
    """
    Site Visits Dashboard providing summary statistics with date range filtering
    """
    
    @swagger_auto_schema(
        operation_summary="Site Visits Dashboard",
        operation_description="Get site visits statistics dashboard with breakdown by status",
        manual_parameters=[
            openapi.Parameter(
                'date_range',
                openapi.IN_QUERY,
                description="Predefined date range",
                type=openapi.TYPE_STRING,
                enum=['today', 'this_week', 'this_month', 'this_quarter', 'this_year'],
                required=False,
                default='this_week'
            ),
            openapi.Parameter(
                'start_date',
                openapi.IN_QUERY,
                description="Custom start date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'end_date',
                openapi.IN_QUERY,
                description="Custom end date (YYYY-MM-DD)",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={
            200: openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'title': openapi.Schema(type=openapi.TYPE_STRING),
                    'period': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'summary': openapi.Schema(type=openapi.TYPE_OBJECT),
                    'status_breakdown': openapi.Schema(type=openapi.TYPE_OBJECT),
                }
            ),
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get site visits dashboard statistics
        """
        date_range = request.query_params.get("date_range", "this_week")
        
        # Calculate date range
        today = datetime.date.today()
        if date_range == 'today':
            start_date = today
            end_date = today
        elif date_range == 'this_week':
            start_date = today - datetime.timedelta(days=today.weekday())
            end_date = start_date + datetime.timedelta(days=6)
        elif date_range == 'this_month':
            start_date = today.replace(day=1)
            if start_date.month == 12:
                end_date = start_date.replace(year=start_date.year + 1, month=1) - datetime.timedelta(days=1)
            else:
                end_date = start_date.replace(month=start_date.month + 1) - datetime.timedelta(days=1)
        elif date_range == 'this_quarter':
            quarter_start_month = ((today.month - 1) // 3) * 3 + 1
            start_date = today.replace(month=quarter_start_month, day=1)
            end_date = start_date.replace(month=start_date.month + 2)
            end_date = end_date.replace(day=1) + datetime.timedelta(days=32)
            end_date = end_date.replace(day=1) - datetime.timedelta(days=1)
        elif date_range == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today.replace(month=12, day=31)
        else:
            # Use custom date range if provided
            start_date, end_date = self.get_date_range(request)
        
        # Get current period statistics
        current_stats_sql = """
            SELECT 
                COUNT(*) as all_site_visits,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_site_visits,
                COUNT(CASE WHEN status = 'Approved' THEN 1 END) as approved_site_visits,
                COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as enroute_site_visits,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_site_visits,
                COUNT(CASE WHEN status = 'Rejected' THEN 1 END) as rejected_site_visits,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_site_visits
            FROM logistics_sitevisit 
            WHERE pickup_date BETWEEN %s AND %s
        """
        
        # Get previous period for comparison (same duration)
        period_days = (end_date - start_date).days + 1
        prev_start_date = start_date - datetime.timedelta(days=period_days)
        prev_end_date = start_date - datetime.timedelta(days=1)
        
        prev_stats_sql = """
            SELECT 
                COUNT(*) as prev_all_site_visits,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as prev_pending_site_visits,
                COUNT(CASE WHEN status = 'Approved' THEN 1 END) as prev_approved_site_visits,
                COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as prev_enroute_site_visits,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as prev_completed_site_visits,
                COUNT(CASE WHEN status = 'Rejected' THEN 1 END) as prev_rejected_site_visits,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as prev_cancelled_site_visits
            FROM logistics_sitevisit 
            WHERE pickup_date BETWEEN %s AND %s
        """
        
        current_stats = fetch_reports_data(current_stats_sql, [start_date, end_date])[0]
        prev_stats = fetch_reports_data(prev_stats_sql, [prev_start_date, prev_end_date])[0]
        
        # Calculate percentage changes
        def calculate_percentage_change(current, previous):
            if previous == 0:
                return 100 if current > 0 else 0
            return round(((current - previous) / previous) * 100, 1)
        
        status_breakdown = {
            'all_site_visits': {
                'count': current_stats['all_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['all_site_visits'], 
                    prev_stats['prev_all_site_visits']
                ),
                'period': date_range
            },
            'pending_site_visits': {
                'count': current_stats['pending_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['pending_site_visits'], 
                    prev_stats['prev_pending_site_visits']
                ),
                'period': date_range
            },
            'approved_site_visits': {
                'count': current_stats['approved_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['approved_site_visits'], 
                    prev_stats['prev_approved_site_visits']
                ),
                'period': date_range
            },
            'enroute_site_visits': {
                'count': current_stats['enroute_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['enroute_site_visits'], 
                    prev_stats['prev_enroute_site_visits']
                ),
                'period': date_range
            },
            'completed_site_visits': {
                'count': current_stats['completed_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['completed_site_visits'], 
                    prev_stats['prev_completed_site_visits']
                ),
                'period': date_range
            },
            'rejected_site_visits': {
                'count': current_stats['rejected_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['rejected_site_visits'], 
                    prev_stats['prev_rejected_site_visits']
                ),
                'period': date_range
            }
        }
        
        return Response({
            'title': f'Site Visits Dashboard - {date_range.replace("_", " ").title()}',
            'period': {
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'range': date_range,
                'days': period_days
            },
            'summary': {
                'total_site_visits': current_stats['all_site_visits'],
                'completion_rate': round(
                    (current_stats['completed_site_visits'] * 100.0 / 
                     max(current_stats['all_site_visits'], 1)), 2
                ),
                'approval_rate': round(
                    (current_stats['approved_site_visits'] * 100.0 / 
                     max(current_stats['all_site_visits'], 1)), 2
                )
            },
            'status_breakdown': status_breakdown
        })


@swagger_tags(['Logistics Reports'])
class ApprovedSiteVisitsView(BaseLogisticsReportsViewSet):
    """
    Approved Site Visits Report showing all approved site visits with details
    """
    
    @swagger_auto_schema(
        operation_summary="Approved Site Visits Report",
        operation_description="Get all approved site visits with marketer and project details",
        manual_parameters=[
            *get_common_date_parameters(),
            openapi.Parameter(
                'marketer_employee_no',
                openapi.IN_QUERY,
                description="Filter by marketer employee number",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'project_id',
                openapi.IN_QUERY,
                description="Filter by project ID",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={
            200: "Approved site visits data",
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get approved site visits report
        """
        start_date, end_date = self.get_date_range(request)
        marketer_employee_no = request.query_params.get("marketer_employee_no")
        project_id = request.query_params.get("project_id")
        
        # Build WHERE clauses
        where_clauses = ["sv.status = 'Approved'"]
        params = [start_date, end_date]
        
        if marketer_employee_no:
            where_clauses.append("sv.marketer_id = %s")
            params.append(marketer_employee_no)
            
        if project_id:
            where_clauses.append("sv.project_id = %s")
            params.append(project_id)
        
        sql = f"""
            SELECT 
                sv.id as site_visit_id,
                sv.pickup_date,
                sv.pickup_time,
                sv.pickup_location,
                sv.status,
                sv.remarks,
                sv.created_at,
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                p.projectId as project_id,
                v.vehicle_registration,
                v.make as vehicle_make,
                v.model as vehicle_model,
                ud.fullnames as driver_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                COUNT(svc.id) as client_count
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_vehicle v ON sv.vehicle_id = v.id
            LEFT JOIN users_user ud ON sv.driver_id = ud.employee_no
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            WHERE {' AND '.join(where_clauses)} 
                AND sv.pickup_date BETWEEN %s AND %s
            GROUP BY sv.id, sv.pickup_date, sv.pickup_time, sv.pickup_location, sv.status, 
                     sv.remarks, sv.created_at, u.fullnames, sv.marketer_id, p.name, p.projectId,
                     v.vehicle_registration, v.make, v.model, ud.fullnames
            ORDER BY sv.pickup_date DESC, sv.pickup_time DESC
        """
        
        data = fetch_reports_data(sql, params)
        
        if not data:
            return Response({
                "title": f"Approved Site Visits Report from {start_date} to {end_date}",
                "message": "No approved site visits found for the specified period",
                "count": 0,
                "results": []
            })
        
        paginated_result = paginate_data(data, request)
        
        return Response({
            "title": f"Approved Site Visits Report from {start_date} to {end_date}",
            **paginated_result
        })


@swagger_tags(['Logistics Reports'])
class SiteVisitsSummaryView(BaseLogisticsReportsViewSet):
    """
    Site Visits Summary Report providing aggregated statistics by marketer and project
    """
    
    @swagger_auto_schema(
        operation_summary="Site Visits Summary Report",
        operation_description="Get aggregated site visits statistics by marketer and project",
        manual_parameters=get_common_date_parameters(),
        responses={
            200: "Site visits summary data",
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get site visits summary report
        """
        start_date, end_date = self.get_date_range(request)
        
        sql = """
            SELECT 
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                COUNT(sv.id) as total_site_visits,
                COUNT(CASE WHEN sv.status = 'Pending' THEN 1 END) as pending_visits,
                COUNT(CASE WHEN sv.status = 'Approved' THEN 1 END) as approved_visits,
                COUNT(CASE WHEN sv.status = 'In Progress' THEN 1 END) as enroute_visits,
                COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) as completed_visits,
                COUNT(CASE WHEN sv.status = 'Rejected' THEN 1 END) as rejected_visits,
                COUNT(CASE WHEN sv.status = 'Cancelled' THEN 1 END) as cancelled_visits,
                ROUND(
                    (COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(sv.id), 0)), 2
                ) as completion_rate,
                ROUND(
                    (COUNT(CASE WHEN sv.status = 'Approved' THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(sv.id), 0)), 2
                ) as approval_rate
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            WHERE sv.pickup_date BETWEEN %s AND %s
            GROUP BY sv.marketer_id, u.fullnames, p.name
            ORDER BY total_site_visits DESC, completion_rate DESC
        """
        
        data = fetch_reports_data(sql, [start_date, end_date])
        
        if not data:
            return Response({
                "title": f"Site Visits Summary Report from {start_date} to {end_date}",
                "message": "No site visits found for the specified period",
                "count": 0,
                "results": []
            })
        
        paginated_result = paginate_data(data, request)
        
        return Response({
            "title": f"Site Visits Summary Report from {start_date} to {end_date}",
            **paginated_result
        })


@swagger_tags(['Logistics Reports'])
class MostBookedSitesView(BaseLogisticsReportsViewSet):
    """
    Most Booked Sites Report showing sites with highest booking success rates
    """
    
    @swagger_auto_schema(
        operation_summary="Most Booked Sites Report",
        operation_description="Get sites with highest booking rates from completed site visits",
        manual_parameters=get_common_date_parameters(),
        responses={
            200: "Most booked sites data",
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get most booked sites report
        """
        start_date, end_date = self.get_date_range(request)
        
        # First, let's try a simpler query to debug
        try:
            # Test if basic site visit data exists
            test_sql = """
                SELECT COUNT(*) as total_count
                FROM logistics_sitevisit sv
                WHERE sv.pickup_date BETWEEN %s AND %s
                    AND sv.status = 'Completed'
            """
            test_data = fetch_reports_data(test_sql, [start_date, end_date])
            logger.info(f"Test query result: {test_data}")
            
            # Main query with better error handling
            sql = """
                SELECT 
                    COALESCE(p.name, 'Unknown Project') as project_name,
                    COALESCE(p.projectId, 'N/A') as project_id,
                    COUNT(sv.id) as total_site_visits,
                    COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) as completed_visits,
                    COUNT(CASE WHEN svs.booked = 1 THEN 1 END) as successful_bookings,
                    COUNT(CASE WHEN svs.visited = 1 THEN 1 END) as actual_visits,
                    ROUND(
                        (COUNT(CASE WHEN svs.booked = 1 THEN 1 END) * 100.0 / 
                         NULLIF(COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END), 0)), 2
                    ) as booking_conversion_rate,
                    COALESCE(SUM(CASE WHEN svs.amount_reserved IS NOT NULL THEN svs.amount_reserved ELSE 0 END), 0) as total_reserved_amount,
                    COALESCE(AVG(CASE WHEN svs.amount_reserved IS NOT NULL THEN svs.amount_reserved ELSE 0 END), 0) as avg_reserved_amount
                FROM logistics_sitevisit sv
                LEFT JOIN inventory_project p ON sv.project_id = p.projectId
                LEFT JOIN logistics_sitevisitsurvey svs ON sv.id = svs.site_visit_id
                WHERE sv.pickup_date BETWEEN %s AND %s
                    AND sv.status = 'Completed'
                GROUP BY p.projectId, p.name
                HAVING successful_bookings > 0
                ORDER BY booking_conversion_rate DESC, successful_bookings DESC, total_reserved_amount DESC
            """
            
            data = fetch_reports_data(sql, [start_date, end_date])
            logger.info(f"Main query returned {len(data) if data else 0} records")
            
        except Exception as e:
            logger.error(f"Error in most booked sites query: {str(e)}")
            # Try an even simpler fallback query
            fallback_sql = """
                SELECT 
                    'Project Data' as project_name,
                    'N/A' as project_id,
                    COUNT(sv.id) as total_site_visits,
                    COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) as completed_visits,
                    0 as successful_bookings,
                    0 as actual_visits,
                    0 as booking_conversion_rate,
                    0 as total_reserved_amount,
                    0 as avg_reserved_amount
                FROM logistics_sitevisit sv
                WHERE sv.pickup_date BETWEEN %s AND %s
                    AND sv.status = 'Completed'
            """
            data = fetch_reports_data(fallback_sql, [start_date, end_date])
        
        if not data:
            return Response({
                "title": f"Most Booked Sites Report from {start_date} to {end_date}",
                "message": "No booking data found for the specified period",
                "count": 0,
                "results": []
            })
        
        paginated_result = paginate_data(data, request)
        
        return Response({
            "title": f"Most Booked Sites Report from {start_date} to {end_date}",
            **paginated_result
        })


@swagger_tags(['Logistics Reports'])
class ChauffeurItineraryView(BaseLogisticsReportsViewSet):
    """
    Chauffeur Itinerary Report showing driver schedules and assignments
    """
    
    @swagger_auto_schema(
        operation_summary="Chauffeur Itinerary Report",
        operation_description="Get driver itinerary and schedule information",
        manual_parameters=[
            *get_common_date_parameters(),
            openapi.Parameter(
                'driver_employee_no',
                openapi.IN_QUERY,
                description="Filter by specific driver employee number",
                type=openapi.TYPE_STRING,
                required=False
            ),
        ],
        responses={
            200: "Chauffeur itinerary data",
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get chauffeur itinerary report
        """
        start_date, end_date = self.get_date_range(request)
        driver_employee_no = request.query_params.get("driver_employee_no")
        
        # Build WHERE clauses
        where_clauses = []
        params = [start_date, end_date]
        
        if driver_employee_no:
            where_clauses.append("sv.driver_id = %s")
            params.append(driver_employee_no)
        
        where_clause = " AND " + " AND ".join(where_clauses) if where_clauses else ""
        
        sql = f"""
            SELECT 
                ud.fullnames as driver_name,
                sv.driver_id,
                sv.pickup_date,
                sv.pickup_time,
                sv.pickup_location,
                sv.status,
                v.vehicle_registration,
                v.make as vehicle_make,
                v.model as vehicle_model,
                u.fullnames as marketer_name,
                p.name as project_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                COUNT(svc.id) as client_count,
                sv.remarks,
                CASE 
                    WHEN sv.pickup_time < '12:00:00' THEN 'Morning'
                    WHEN sv.pickup_time < '18:00:00' THEN 'Afternoon'
                    ELSE 'Evening'
                END as time_slot
            FROM logistics_sitevisit sv
            LEFT JOIN users_user ud ON sv.driver_id = ud.employee_no
            LEFT JOIN logistics_vehicle v ON sv.vehicle_id = v.id
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            WHERE sv.pickup_date BETWEEN %s AND %s{where_clause}
            GROUP BY sv.id, ud.fullnames, sv.driver_id, sv.pickup_date, sv.pickup_time, 
                     sv.pickup_location, sv.status, v.vehicle_registration, v.make, v.model,
                     u.fullnames, p.name, sv.remarks
            ORDER BY sv.pickup_date ASC, sv.pickup_time ASC, ud.fullnames ASC
        """
        
        data = fetch_reports_data(sql, params)
        
        if not data:
            return Response({
                "title": f"Chauffeur Itinerary Report from {start_date} to {end_date}",
                "message": "No driver assignments found for the specified period",
                "count": 0,
                "results": []
            })
        
        paginated_result = paginate_data(data, request)
        
        return Response({
            "title": f"Chauffeur Itinerary Report from {start_date} to {end_date}",
            **paginated_result
        })


@swagger_tags(['Logistics Reports'])
class MarketersFeedbackView(BaseLogisticsReportsViewSet):
    """
    Marketers' Feedback Report showing site visit outcomes and feedback
    """
    
    @swagger_auto_schema(
        operation_summary="Marketers' Feedback Report",
        operation_description="Get marketers' feedback from completed site visits including booking outcomes",
        manual_parameters=[
            *get_common_date_parameters(),
            openapi.Parameter(
                'marketer_employee_no',
                openapi.IN_QUERY,
                description="Filter by marketer employee number",
                type=openapi.TYPE_STRING,
                required=False
            ),
            openapi.Parameter(
                'feedback_status',
                openapi.IN_QUERY,
                description="Filter by feedback status",
                type=openapi.TYPE_STRING,
                enum=['booked', 'not_booked', 'visited', 'not_visited', 'all'],
                required=False,
                default='all'
            ),
        ],
        responses={
            200: "Marketers' feedback data",
            400: "Bad Request",
            500: "Internal Server Error"
        }
    )
    @handle_reports_error
    def list(self, request):
        """
        Get marketers' feedback report
        """
        start_date, end_date = self.get_date_range(request)
        marketer_employee_no = request.query_params.get("marketer_employee_no")
        feedback_status = request.query_params.get("feedback_status", "all")
        
        # Build WHERE clauses
        where_clauses = ["sv.status = 'Completed'"]
        params = [start_date, end_date]
        
        if marketer_employee_no:
            where_clauses.append("sv.marketer_id = %s")
            params.append(marketer_employee_no)
        
        if feedback_status != "all":
            if feedback_status == "booked":
                where_clauses.append("svs.booked = 1")
            elif feedback_status == "not_booked":
                where_clauses.append("svs.booked = 0")
            elif feedback_status == "visited":
                where_clauses.append("svs.visited = 1")
            elif feedback_status == "not_visited":
                where_clauses.append("svs.visited = 0")
        
        sql = f"""
            SELECT 
                sv.id as site_visit_id,
                sv.pickup_date,
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                svs.booked,
                svs.visited,
                svs.amount_reserved,
                svs.plot_details,
                svs.reason_not_visited,
                svs.reason_not_booked,
                sv.remarks as visit_remarks,
                CASE 
                    WHEN svs.booked = 1 THEN 'Successfully Booked'
                    WHEN svs.visited = 1 AND svs.booked = 0 THEN 'Visited but Not Booked'
                    WHEN svs.visited = 0 THEN 'Did Not Visit'
                    ELSE 'No Feedback'
                END as outcome_summary
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            LEFT JOIN logistics_sitevisitsurvey svs ON sv.id = svs.site_visit_id
            WHERE {' AND '.join(where_clauses)} 
                AND sv.pickup_date BETWEEN %s AND %s
            GROUP BY sv.id, sv.pickup_date, u.fullnames, sv.marketer_id, p.name,
                     svs.booked, svs.visited, svs.amount_reserved, svs.plot_details,
                     svs.reason_not_visited, svs.reason_not_booked, sv.remarks
            ORDER BY sv.pickup_date DESC
        """
        
        data = fetch_reports_data(sql, params)
        
        if not data:
            return Response({
                "title": f"Marketers' Feedback Report from {start_date} to {end_date}",
                "message": "No feedback data found for the specified period",
                "count": 0,
                "results": []
            })
        
        paginated_result = paginate_data(data, request)
        
        return Response({
            "title": f"Marketers' Feedback Report from {start_date} to {end_date}",
            **paginated_result
        }) 