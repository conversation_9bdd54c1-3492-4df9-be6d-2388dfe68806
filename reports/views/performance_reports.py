from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import MySQLdb.cursors,os
from datetime import date, datetime



def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")



@swagger_tags(['Performance Reports'])
class MarketingPeriodsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]  
    
    def list(self, request):
        base_sql = f"""SELECT DISTINCT `period_start_date`,`period_end_date` FROM `marketer_targets` ORDER BY `marketer_targets`.`period_end_date` DESC"""
        
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql)
                cols = [c[0] for c in cur.description]
                raw_data = [dict(zip(cols, row)) for row in cur.fetchall()]
                # Convert date/datetime objects to strings for JSON serialization
                from datetime import date, datetime
                def serialize_dates(obj):
                    for k, v in obj.items():
                        if isinstance(v, (date, datetime)):
                            obj[k] = v.isoformat()
                    return obj
                data = [serialize_dates(item) for item in raw_data]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " All Marketing Periods",
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })    


@swagger_tags(['Performance Reports'])
class MarketersPerformanceView(ViewSet):
    queryset = None   # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    
     # --- Dynamically fetch enum options from the database ---
        # Example: Fetch lead types from the database
        # try:
        #     with connections["reports"].cursor() as cur:
        #         cur.execute("SELECT DISTINCT `period_start_date` FROM `marketer_targets`")
        #         m_periods = [row[0] for row in cur.fetchall()]
        #         if not m_periods:
        #             m_periods = ['N/A']
        # except Exception:
        #     m_periods = ['N/A']
    @swagger_auto_schema(
        operation_description="Returns Individual Marketers performance plus lines in a marketing Period.",
        manual_parameters = [
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=True,default='ALL',
            ),
            # openapi.Parameter(
            #     name='MARKETING_PERIOD',
            #     in_=openapi.IN_QUERY,
            #     type=openapi.TYPE_STRING,
            #     enum=m_periods + ['ALL'] if 'ALL' not in m_periods else m_periods,
            #     description='Filter PERIOD.',
            #     required=True,
            #     default='ALL',
            # ),
            
        ])
    def list(self, request):
       
        # period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        marketer = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL"

        # if period_start_date and period_start_date != "ALL":
        #     where_clauses= " `period_start_date` = %s"
        #     params=[period_start_date]
        #     stat=f"for period  started on {period_start_date}"

        if marketer and marketer != "ALL":
            where_clauses=f" m.`marketer_no_id` = %s"
            params= [marketer]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat=f"for marketer {marketer_fullname}"

        base_sql = f"""SELECT m.*,u.fullnames FROM `marketer_targets` m JOIN `users_user` u ON m.`marketer_no_id`= u.`employee_no` WHERE {where_clauses} ORDER BY m.`period_end_date` DESC"""

        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " MIB performance  for {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })


@swagger_tags(['Performance Reports'])
class OverallMarketersPerformancePerPeriodView(ViewSet):
    queryset = None   # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    
    @swagger_auto_schema(
        operation_description="Returns all performance plus lines in a marketing Period.",
        manual_parameters = [
            openapi.Parameter(name='MARKETING_PERIOD',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by period. If not provided, all periods are included.', required=True,default='ALL',
            ),
            openapi.Parameter(
                name='MARKETER_EMPLOYEE_NO',in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter marketer.',required=False,default='ALL',
            ),
            
        ])
    def list(self, request):
      
        period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        marketer = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL"

        if period_start_date and period_start_date != "ALL":
            where_clauses= " m.`period_start_date` = %s"
            params=[period_start_date]
            stat=f"for period  started on {period_start_date}"

        if marketer and marketer != "ALL":
            where_clauses= where_clauses + f" AND m.`marketer_no_id` = %s"
            params= params + [marketer]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat= stat + f"for marketer {marketer_fullname}"

        base_sql = f"""SELECT m.*,u.fullnames FROM `marketer_targets` m JOIN `users_user` u ON m.`marketer_no_id`= u.`employee_no` WHERE {where_clauses} ORDER BY m.`period_end_date` DESC"""

        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " MIB performance  for {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
    
@swagger_tags(['Performance Reports'])
class OverallTeamsPerformanceView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Returns all teams performance filter by region and period.",
        manual_parameters = [
            openapi.Parameter(name='REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by region. If not provided, all regions are included.',
                enum=['ATLANTIC','PACIFIC','ALL'],required=False,default= 'ALL',
            ),
             openapi.Parameter(name='MARKETING_PERIOD',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by period. If not provided, all periods are included.', required=False,default='ALL',
            ), ]
            
       )
    def list(self, request):
      
        period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        region = request.query_params.get("REGION", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL "

        if period_start_date and period_start_date != "ALL":
            where_clauses= "  `period_start_date` = %s"
            params=[period_start_date]
            stat=f"for period  started on {period_start_date}  "

        if region and region != "ALL":
            where_clauses= where_clauses + f" AND `team` IN (SELECT `team` FROM `users_teams` WHERE `office` LIKE %s AND `inactive`=0)"
            params= params + [f"%{region}%"]
            stat= stat + f"for {region} "

        base_sql = f"""SELECT * FROM `teams_targets` WHERE {where_clauses} ORDER BY `teams_targets`.`period_start_date` DESC"""

        print(base_sql)
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error a": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Team MIB performance {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
    
    
@swagger_tags(['Performance Reports'])
class OverallRegionsPerformanceView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Returns all teams performance filter by region and period.",
        manual_parameters = [
            openapi.Parameter(name='REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by region. If not provided, all regions are included.',
                enum=['ATLANTIC','PACIFIC','ALL'],required=False,default= 'ALL',
            ),
             openapi.Parameter(name='MARKETING_PERIOD',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by period. If not provided, all periods are included.', required=False,default='ALL',
            ), ]
            
       )
    def list(self, request):
      
        period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        region = request.query_params.get("REGION", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL "

        if period_start_date and period_start_date != "ALL":
            where_clauses= "  `period_start_date` = %s"
            params=[period_start_date]
            stat=f"for period  started on {period_start_date}"

        if region and region != "ALL":
            if region == "ATLANTIC":
                where_clauses=  """`title` LIKE 'Hos'"""
            else:
                where_clauses=  """`title` LIKE 'GM'"""
            stat= stat + f"for {region} "

        base_sql = f"""SELECT * FROM `hos_gm_targets` WHERE {where_clauses} ORDER BY `hos_gm_targets`.`period_start_date` DESC"""

        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Team MIB performance {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
    
@swagger_tags(['Performance Reports'])
class CommissionHeadersReportView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(
        operation_description="Returns all commission headers filter by region and period.",
        manual_parameters=[
            openapi.Parameter(name='ROLE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by region. If not provided, all regions are included.',
                enum=['ATLANTIC','PACIFIC','TEAM-LEADERS','MARKETER'],required=True,default= 'ALL',
            ),openapi.Parameter(name='MARKETING_PERIOD',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by period. If not provided, all periods are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=True,default='ALL',
            ),]
            
       )
    def list(self, request):
      
        period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        role = request.query_params.get("ROLE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL"

        if period_start_date and period_start_date != "ALL":
            where_clauses= "`period_start_date` = %s"
            params=[period_start_date]
            stat=f"for period  started on {period_start_date}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= where_clauses + f" AND `emp_no_id` = %s"
            params= params + [marketer_employee_no]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat= stat + f"for marketer {marketer_fullname}"   
            

        if role and role != "ALL":
            if role == "ATLANTIC":
                ck='HoS'
            elif role == "PACIFIC":
                ck='HoS'
            elif role == "TEAM-LEADERS":
                ck='Leader'
            else:
                ck='Member'
            where_clauses= where_clauses + f""" AND `role` LIKE %s"""
            params= params + [f"%{ck}%"]
            stat= stat + f"for {role}"

        base_sql = f"""SELECT * FROM `commission_headers` WHERE {where_clauses} ORDER BY `commission_headers`.`period_start_date` DESC"""

        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " Commission Headers Report {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

@swagger_tags(['Performance Reports'])
class CommissionLinesReportView(ViewSet):
    queryset = None
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    
    @swagger_auto_schema(
        operation_description="Returns all commission lines filter by office and period.",
        manual_parameters=[
            openapi.Parameter(name='MARKETING_PERIOD',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by period. If not provided, all periods are included.', required=True,default='ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=True,default='ALL',
            ),]
            
       )
    def list(self, request):
      
        period_start_date = request.query_params.get("MARKETING_PERIOD", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")

        where_clauses = "1=1 "
        params=[]
        stat=f"for ALL"

        if period_start_date and period_start_date != "ALL":
            where_clauses= "`period_start_date` = %s"
            params=[period_start_date]
            stat=f"for period  started on {period_start_date}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= where_clauses + f" AND `marketer_no_id` = %s"
            params= params + [marketer_employee_no]
            getmonth_sql = "SELECT `fullnames` FROM `users_user` WHERE `employee_no` = %s"
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(marketer_employee_no,), dict_cursor=True)
            marketer_fullname = row['fullnames'] if row and 'fullnames' in row else None
            stat= stat + f"for marketer {marketer_fullname}"


        base_sql = f"""SELECT * FROM `commission_lines` WHERE {where_clauses} ORDER BY `commission_lines`.`period_start_date` DESC"""

        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        for c in connections.all():
            c.close()
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " Commission Lines Report {}" .format(stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

@swagger_tags(['Performance Reports'])
class TeamMarketerTargetsView(ViewSet):
    queryset = None
    # if os.environ.get('DEBUG', 'false').lower() == 'true':
    #     permission_classes = [IsAuthenticated]
    # else:
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        operation_description="Retrieve team targets with team leader and members + their targets in the current marketing period.",
        manual_parameters=[
            openapi.Parameter(
                name="TEAM_LEADER_EMPLOYEE_NO",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter by team leader employee number. Use 'ALL' to include all team leaders.",
                required=False,
                default="ALL",
            ),
            openapi.Parameter(
                name="MARKETING_PERIOD",
                in_=openapi.IN_QUERY,
                type=openapi.TYPE_STRING,
                description="Filter by marketing period start date. Use 'ALL' to include all periods.",
                required=False,
                default="ALL",
            ),
           
        ],
    )
    def list(self, request):
        team_leader_employee_no = request.query_params.get("TEAM_LEADER_EMPLOYEE_NO", "ALL")
        marketing_period = request.query_params.get("MARKETING_PERIOD", "ALL")
        params = []

        # Step 1: Determine how to handle period
        where_period = ""
        if marketing_period and marketing_period != "ALL":
            # Specific period
            where_period = "tt.period_start_date = %s"
            params.append(marketing_period)
        elif marketing_period == "ALL":
            # No extra filter → all periods
            where_period = "1=1"
        else:
            # Default to latest
            with connections["reports"].cursor() as cur:
                cur.execute("SELECT MAX(period_start_date) FROM teams_targets")
                latest_period = cur.fetchone()[0]

            if not latest_period:
                return Response([], status=200)

            where_period = "tt.period_start_date = %s"
            params.append(latest_period)

        # Step 2: team leader filter
        where_clauses = "1=1"
        if team_leader_employee_no and team_leader_employee_no != "ALL":
            where_clauses = " t.tl_code = %s "
            params.append(team_leader_employee_no)

        # Step 3: Base SQL
        base_sql = f"""
            SELECT 
                tt.line_no,
                tt.team,
                tt.period_start_date,
                tt.period_end_date,
                tt.monthly_target,
                tt.daily_target,
                tt.MIB_achieved,
                tt.MIB_Perfomance,
                u.employee_no AS leader_employee_no,
                u.fullnames AS leader_fullnames
            FROM teams_targets tt
            JOIN users_teams t ON t.team = tt.team
            LEFT JOIN users_user u ON u.employee_no = t.tl_code
            WHERE {where_period}
            AND {where_clauses} AND t.inactive = 0
            ORDER BY tt.period_start_date DESC, tt.team ASC
        """

        try:
            with connections["reports"].cursor() as cur:
                cur.execute(base_sql, params)
                cols = [c[0] for c in cur.description]
                team_data = [dict(zip(cols, row)) for row in cur.fetchall()]

            # Step 4: Fetch members for each team/period
            for team in team_data:
                team_code = team["team"]
                period_start_date = team["period_start_date"]

                with connections["reports"].cursor() as cur:
                    cur.execute(
                        """
                        SELECT uu.employee_no, uu.fullnames,
                            mt.period_start_date, mt.period_end_date,
                            mt.monthly_target, mt.daily_target,
                            mt.MIB_achieved, mt.MIB_Perfomance
                        FROM users_user uu
                        LEFT JOIN marketer_targets mt 
                            ON uu.employee_no = mt.marketer_no_id
                        WHERE mt.period_start_date = %s AND (uu.team_id = %s AND uu.is_marketer = 1 AND uu.is_active = 1)
                          ORDER BY uu.fullnames ASC
                        """,
                        [period_start_date, team_code],
                    )
                    member_cols = [c[0] for c in cur.description]
                    members = [dict(zip(member_cols, row)) for row in cur.fetchall()]

                team["members"] = members

            return Response(team_data, status=200)

        except Exception as e:
            return Response({"error": str(e)}, status=500)


