from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime,os
import MySQLdb.cursors


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")

        


@swagger_tags(['Prospects Reports'])
class AllProspectsView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all prospects filters.",
        manual_parameters=[
            openapi.Parameter(name='LEAD_TYPE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['Personal','Allocated','ALL'],
                description='Filter by lead type.',required=True,default='ALL',
            ),
            openapi.Parameter(name='STATUS',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['Active','Dormant','ALL'],
                description='Filter by Status. active are warm and hot, dormant are cold',required=True,default='ALL',
            ),
            openapi.Parameter(name='CONVERSION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['Converted','Open','ALL'],
                description='Filter bY if its converted to sale or not.',required=True,default='ALL',
            ),
            
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='ORGANIZATION_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['DIGITAL','TELEMARKETING'],
                description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='LEAD_SOURCE'
                ,in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by lead source ID. If not provided, all lead sources are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters]
        lead_type = request.query_params.get("LEAD_TYPE", "ALL")
        lead_status = request.query_params.get("STATUS", "ALL")
        conversion = request.query_params.get("CONVERSION", "ALL")
        
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        lead_source_id = request.query_params.get("LEAD_SOURCE", "ALL")
        organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
    
       
        # Build dynamic WHERE clauses based on filters
        
        where_clauses = "1=1 "
        m_where_clauses= ""
        params=[]
        stat=f"for ALL"
        p_stat=""
        
        
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= " `marketer_id` = %s"
            params=[marketer_employee_no]
            stat=f"for Marketer {marketer_employee_no}"
        
        
        if organization_team and organization_team != "ALL":
            if organization_team == "TELEMARKETING":
                team_id=21
            else: 
                team_id=10
            where_clauses=f" `department_id` = %s"
            params=[team_id]
            stat=f"for Team {organization_team}"
        if lead_source_id and lead_source_id != "ALL":
            where_clauses=f" `lead_source_id` = %s"
            params=[lead_source_id]
            stat=f"for lead source {lead_source_id}"
        # params for start_date and end_date should be at the end
        
        if lead_type and lead_type != "ALL":
            if m_where_clauses =="":
                m_where_clauses= "`lead_type` = %s"
            else:
                m_where_clauses= m_where_clauses + " AND `lead_type` = %s"
            params.extend([lead_type]) 
            p_stat= p_stat + f" {lead_type}"
        if lead_status and lead_status != "ALL":
            if m_where_clauses =="":
                m_where_clauses= "`status` = %s"
            else:
                m_where_clauses= m_where_clauses + " AND `status` = %s"
            params.extend([lead_status])
            p_stat= p_stat + f" {lead_status}"
        
        if conversion and conversion != "ALL":
            if conversion=="Converted":
                conv= 1
            else:
                conv=0
              
            if m_where_clauses =="":
                m_where_clauses= " `is_converted` = %s"
            else:
                m_where_clauses= m_where_clauses + " AND `is_converted` = %s"
            params.extend([conv])
            p_stat= p_stat + f" {conversion}"
        
        
        
        
        base_sql = f"""SELECT `name`,`phone`,`email`,`marketer_id`,`lead_source_id`,`project_id`,`category` FROM `leads_prospects` WHERE {where_clauses} AND  {m_where_clauses}"""
        
        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": " All {} prospects  for {}" .format(p_stat,stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

@swagger_tags(['Prospects Reports'])       
class ProspectView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all prospects based on provided parameters.",
        manual_parameters=[
            openapi.Parameter(name='OFFICE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by office. If not provided, all offices are included.',
                enum=['HQ','KAREN','ALL'],required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='DIASPORA_REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by diaspora region. If not provided, all regions are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='ORGANIZATION_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['DIASPORA','DIGITAL','TELEMARKETING','OTHER',],
                description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='search',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Search by customer name, lead file number, or plot id.',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        office = request.query_params.get("OFFICE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        diaspora_region = request.query_params.get("DIASPORA_REGION", "ALL")
        organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
        search = request.query_params.get("search", None)

        # Build dynamic WHERE clauses based on filters
        join_clause=f" JOIN `users_user` uu on lp.`marketer_id`= uu.`employee_no` JOIN `leads_leadsource` ls ON lp.`lead_source_id` = ls.`id`"
        where_clauses = "1=1"
        params=[]
        stat=f"for ALL"
        if office and office != "ALL":
            
            where_clauses= f"1=1 AND uu.`office` = %s"
            params = [office]
            stat=f"for {office}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= "1=1 AND `marketer_id` = %s"
            params=[marketer_employee_no]
            stat=f"for Marketer {marketer_employee_no}"
        
        if diaspora_region and diaspora_region != "ALL":
            where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
            `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s))"""
            params=[diaspora_region]
            stat=f"for Diaspora region {diaspora_region}"
        
        if organization_team and organization_team != "ALL":

            if organization_team == "DIASPORA":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id ` = %s))"""
                params=['3']
                stat=f"for Organization team {organization_team}"
                
            if organization_team == "DIGITAL":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Digital']
                stat=f"for Organization team {organization_team}"

            if organization_team == "TELEMARKETING":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Telemarketing']
                stat=f"for Organization team {organization_team}"

            if organization_team == "OTHER":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Other']
                stat=f"for Organization team {organization_team}"
        
        if search and search != "":
            where_clauses += " AND (lp.`name` LIKE %s OR lp.`phone` LIKE %s OR lp.`email` LIKE %s OR uu.`fullnames` LIKE %s)"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param])

        # params for start_date and end_date should be at the end

        base_sql = f""" SELECT lp.`name`,lp.`phone`,lp.`email`,lp.`marketer_id`,uu.`fullnames`,lp.`lead_source_id` FROM `leads_prospects` lp {join_clause} 
        WHERE {where_clauses}  ORDER BY lp.`name` DESC LIMIT 1000 """

        print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Customers for {}" .format( stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
