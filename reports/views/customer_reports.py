from rest_framework.views import APIView
from rest_framework.response import Response
from django.db import connections
from drf_spectacular.utils import extend_schema, OpenApiParameter, OpenApiExample
from drf_yasg.utils import swagger_auto_schema
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.viewsets import ViewSet
from drf_yasg import openapi
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
import datetime,os


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


@swagger_tags(['Customer'])
class CustomersByCategoryView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        tags=['Customer'],
        operation_description="Returns customers based on categorization: ACTIVE, COMPLETED, DROPPED.",
        manual_parameters=[
            openapi.Parameter(
                name='category',in_=openapi.IN_QUERY,description='Returns customers based on categorization: ACTIVE, COMPLETED, DROPPED.',
                type=openapi.TYPE_STRING,enum=['ACTIVE', 'COMPLETED', 'DROPPED'],required=False,default='ACTIVE',
            ),
            openapi.Parameter(
                name='page',in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(
                name='page_size',in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):
        Category = request.query_params.get("category", "ACTIVE")
        if not Category:
            Category = "ACTIVE"
        if Category not in ["ACTIVE", "COMPLETED", "DROPPED"]:
            return Response({"error": "Invalid category. Must be one of: ACTIVE, COMPLETED, DROPPED."}, status=400)
        if Category == "ACTIVE":
            v = 0
        elif Category == "DROPPED":
            v = 1
        elif Category == "COMPLETED":
            v = 0
        base_sql = """
            SELECT
            l.customer_id_id,
            MAX(c.customer_name) AS customer_name,
            MAX(c.customer_no) AS customer_no,
            MAX(c.customer_type) AS customer_type,
            MAX(c.phone) AS phone,
            MAX(c.primary_email)  AS email,
            MAX(u.fullnames)  AS Marketer,
            SUM(l.balance_lcy)   AS total_balance
            FROM sales_leadfile l
            JOIN customers_customer c ON c.customer_no = l.customer_id_id
            JOIN users_user u ON c.marketer_id = u.employee_no
            WHERE l.lead_file_status_dropped = %s
            GROUP BY l.customer_id_id
        """
        params = [v]
        if Category == "COMPLETED":  # COMPLETED
            base_sql += " HAVING SUM(l.balance_lcy) >= %s"
            params.append(0)
        sql = base_sql + " ORDER BY c.customer_no DESC;"
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Customer'])
class CustomerHighInvestCustomersByNumberOfLeadFilesReportView(ViewSet):
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=['Customer'],
        operation_description="Returns customers with high investments.",
        manual_parameters= [
           openapi.Parameter(
                name='page', in_=openapi.IN_QUERY, description='Page number for pagination.',
                type=openapi.TYPE_INTEGER, required=False, default=1
            ),
            openapi.Parameter(
                name='page_size', in_=openapi.IN_QUERY, description='Number of results per page.',
                type=openapi.TYPE_INTEGER, required=False, default=20
            ),
        ]          
    )
    def list (self, request):
        base_sql = """
        SELECT 
        c.customer_no,
        MAX(c.customer_name) AS customer_name,
        MAX(c.customer_type) AS customer_type,
        MAX(c.phone) AS phone,
        MAX(c.primary_email) AS primary_email,
        COUNT(l.lead_file_no) AS total_leadfiles
        FROM customers_customer c
        JOIN sales_leadfile l ON c.customer_no = l.customer_id_id
        WHERE l.lead_file_status_dropped = 0
        GROUP BY c.customer_no
        ORDER BY total_leadfiles DESC;

        """
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Customer High Investment Report by Number of Lead Files",
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Customer'])
class CustomerHighInvestCustomersBySumOfTotalReportView(ViewSet):

    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=['Customer'],
        operation_description="Returns customers with high investments based on sum of total.",
        manual_parameters=[
            openapi.Parameter(
                name='page', in_=openapi.IN_QUERY, description='Page number for pagination.',
                type=openapi.TYPE_INTEGER, required=False, default=1
            ),
            openapi.Parameter(
                name='page_size', in_=openapi.IN_QUERY, description='Number of results per page.',
                type=openapi.TYPE_INTEGER, required=False, default=20
            ),
        ]
    )
    def list(self, request):
        base_sql = """
            SELECT 
            c.customer_no,
            MAX(c.customer_name) AS customer_name,
            MAX(c.customer_type) AS customer_type,
            MAX(c.phone) AS phone,
            MAX(c.primary_email) AS primary_email,
            SUM(l.total_paid) AS total_investment
            FROM customers_customer c
            JOIN sales_leadfile l ON c.customer_no = l.customer_id_id
            WHERE l.lead_file_status_dropped = 0
            GROUP BY c.customer_no
            HAVING SUM(l.total_paid) > 0
            ORDER BY total_investment DESC;

        """
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        return Response({
            "Title": "Customer High Investment Report by Sum of Total",
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Customer'])    
class CustomerRatingReportView(ViewSet):
    """
    ViewSet for generating customer rating reports.
    """
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=['Customer'],
        operation_description="Returns customer ratings based on various criteria.",
        manual_parameters=[
           openapi.Parameter(name='customer_no', in_=openapi.IN_QUERY, description='Filter by customer number.',
                type=openapi.TYPE_STRING, required=False, default='ALL'),
            
            openapi.Parameter(
                name='page', in_=openapi.IN_QUERY, description='Page number for pagination.',
                type=openapi.TYPE_INTEGER, required=False, default=1
            ),
            openapi.Parameter(
                name='page_size', in_=openapi.IN_QUERY, description='Number of results per page.',
                type=openapi.TYPE_INTEGER, required=False, default=20
            ),
        ]
    )
    def list(self, request):
        # Generate customer rating report based on various criteria.
        customer_no = request.query_params.get("customer_no","ALL")

        where_clauses =" "
        params = []
        stat = "for ALL"
        if customer_no != "ALL":
            where_clauses =f"c.customer_no = %s"
            params =[customer_no]
            stat = f"for customer {customer_no}"

        if where_clauses != " ":
            where_clauses= where_clauses + "AND"

        base_sql =f"""
        SELECT 
        c.customer_no,
        MAX(c.customer_name) AS customer_name,
        MAX(c.customer_type) AS customer_type,
        MAX(c.phone) AS phone,
        MAX(c.primary_email) AS primary_email,
        COUNT(l.lead_file_no) AS total_leadfiles,
        SUM(l.total_paid) AS total_investment,
        CASE
        WHEN SUM(l.total_paid) BETWEEN 0 AND 500000 THEN 'Sapphire'
        WHEN SUM(l.total_paid) BETWEEN 500001 AND 1500000 THEN 'Bronze'
        WHEN SUM(l.total_paid) BETWEEN 1500001 AND 3500000 THEN 'Silver'
        WHEN SUM(l.total_paid) BETWEEN 3500001 AND 5000000 THEN 'Gold'
        WHEN SUM(l.total_paid) BETWEEN 5000001 AND 10000000 THEN 'Platinum'
        WHEN SUM(l.total_paid) > 10000000 THEN 'Diamond'
        ELSE 'Unrated'
        END AS customer_rating
        FROM customers_customer c
        JOIN sales_leadfile l ON c.customer_no = l.customer_id_id
        WHERE {where_clauses} 
        l.lead_file_status_dropped = 0
        GROUP BY c.customer_no
        ORDER BY total_investment DESC, total_leadfiles DESC;
        """
        sql = base_sql
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": f"Customer Rating Report {stat}",
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })
@swagger_tags(['Customer'])       
class CustomerView(ViewSet):
    queryset = None  # Not used, but required for ViewSet
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    @swagger_auto_schema(
        operation_description="Returns all customers based on provided parameters.",
        manual_parameters=[
            openapi.Parameter(name='OFFICE',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,description='Filter by office. If not provided, all offices are included.',
                enum=['HQ','KAREN','ALL'],required=False,default= 'ALL',
            ),
            openapi.Parameter(name='MARKETER_EMPLOYEE_NO',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by marketer. If not provided, all marketers are included.', required=False,default='ALL',
            ),
            openapi.Parameter(name='DIASPORA_REGION',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,
                description='Filter by diaspora region. If not provided, all regions are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='ORGANIZATION_TEAM',
                in_=openapi.IN_QUERY,type=openapi.TYPE_STRING,enum=['DIASPORA','DIGITAL','TELEMARKETING','OTHER',],
                description='Filter by organization team. If not provided, all teams are included.',required=False,default='ALL',
            ),
            openapi.Parameter(name='search',
                in_=openapi.IN_QUERY, type=openapi.TYPE_STRING, required=False,
                description='Search by customer name, lead file number, or plot id.',
            ),
            openapi.Parameter(name='page',
                in_=openapi.IN_QUERY,description='Page number for pagination.',
                type=openapi.TYPE_INTEGER,required=False,default=1,
            ),
            openapi.Parameter(name='page_size',
                in_=openapi.IN_QUERY,description='Number of results per page.',
                type=openapi.TYPE_INTEGER,required=False,default=20,
            ),
        ],
    )
    def list(self, request):

        # validate and parse date parameters
        office = request.query_params.get("OFFICE", "ALL")
        marketer_employee_no = request.query_params.get("MARKETER_EMPLOYEE_NO", "ALL")
        diaspora_region = request.query_params.get("DIASPORA_REGION", "ALL")
        organization_team = request.query_params.get("ORGANIZATION_TEAM", "ALL")
        search = request.query_params.get("search", None)

        # Build dynamic WHERE clauses based on filters
        join_clause=f" JOIN `users_user` uu on cc.`marketer_id`= uu.`employee_no` "
        where_clauses = "1=1"
        params=[]
        stat=f"for ALL"
        if office and office != "ALL":
            
            where_clauses= f"1=1 AND uu.`office` = %s"
            params = [office]
            stat=f"for {office}"
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses= "1=1 AND `marketer_id` = %s"
            params=[marketer_employee_no]
            stat=f"for Marketer {marketer_employee_no}"
        
        if diaspora_region and diaspora_region != "ALL":
            where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
            `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `name` = %s))"""
            params=[diaspora_region]
            stat=f"for Diaspora region {diaspora_region}"
        
        if organization_team and organization_team != "ALL":

            if organization_team == "DIASPORA":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `lead_source_subcategory_id` IN (SELECT `id` FROM `leads_leadsourcesubcategory` WHERE `lead_source_category_id ` = %s))"""
                params=['3']
                stat=f"for Organization team {organization_team}"
                
            if organization_team == "DIGITAL":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Digital']
                stat=f"for Organization team {organization_team}"

            if organization_team == "TELEMARKETING":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Telemarketing']
                stat=f"for Organization team {organization_team}"

            if organization_team == "OTHER":
                where_clauses =f"""1=1 AND `lead_source_id` IN (SELECT name FROM `leads_leadsource` WHERE
                `managing_team` = %s)"""
                params=['Other']
                stat=f"for Organization team {organization_team}"
        
        if search and search != "":
            where_clauses += " AND (cc.`customer_name` LIKE %s OR cc.`phone` LIKE %s OR cc.`primary_email` LIKE %s OR uu.`fullnames` LIKE %s)"
            search_param = f"%{search}%"
            params.extend([search_param, search_param, search_param, search_param])

        # params for start_date and end_date should be at the end

        base_sql = f""" SELECT cc.`customer_no`,cc.`customer_name`,cc.`primary_email`,cc.`marketer_id`,uu.`fullnames` FROM `customers_customer` cc {join_clause} 
        WHERE {where_clauses}  ORDER BY cc.`customer_no` DESC LIMIT 1000 """
        
        # print("SQL:", base_sql)
        # print("Params:", params)
            
        sql=  base_sql 
        sql = sql.replace("\n", " ").strip()
        if not sql.endswith(";"):
            sql += ";"
        # Execute the SQL query
        if not sql:
            return Response({"error": "No SQL query generated."}, status=500)
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            return Response({"error": str(e)}, status=500)
        if not data:
            return Response({"message": "No data fetched."}, status=200)

        # --- Close all connections
        for c in connections.all():
                c.close()
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        return Response({
            "Title": "Customers for {}" .format( stat),
            "Total Results": paginator.count,
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": paged_data.object_list
        })

    