# Comprehensive Grouping Examples - Customers & Leads

## Overview
Both Customer and Leads APIs now support powerful grouping functionality for data analysis and reporting.

## Customer Grouping Examples

### Available Customer Group Fields
- `lead_source` - Group by lead source name
- `marketer` - Group by marketer full name  
- `customer_type` - Group by Individual/Group
- `country_of_residence` - Group by country
- `date_of_registration` - Group by registration date

### 1. Customer Type Distribution
```bash
GET /api/customers/filter/?group_by=customer_type&group_count=true
```

**Response:**
```json
{
  "group_by": "customer_type",
  "group_count": true,
  "total_records": 500,
  "statistics": {
    "total": 500,
    "individual": 450,
    "group_customers": 50,
    "groups": 2
  },
  "groups": [
    {
      "group_value": "Individual",
      "count": 450,
      "percentage": 90.0
    },
    {
      "group_value": "Group",
      "count": 50,
      "percentage": 10.0
    }
  ]
}
```

### 2. Geographic Distribution Analysis
```bash
GET /api/customers/filter/?group_by=country_of_residence&group_count=true&customer_type=Individual
```

### 3. Marketer Performance (Customer Acquisition)
```bash
GET /api/customers/filter/?group_by=marketer&group_count=true&date_of_registration_from=2024-01-01
```

### 4. Lead Source Effectiveness (Conversions)
```bash
GET /api/customers/filter/?group_by=lead_source&group_count=true&date_of_registration_from=2024-10-01
```

## Leads Grouping Examples

### Available Leads Group Fields
- `lead_source` - Group by lead source name
- `marketer` - Group by marketer full name
- `department` - Group by department name
- `status` - Group by Active/Dormant
- `category` - Group by Hot/Warm/Cold
- `pipeline_level` - Group by pipeline stage
- `country` - Group by country
- `city` - Group by city
- `lead_type` - Group by personal/allocated
- `is_verified` - Group by verification status
- `is_converted` - Group by conversion status

### 5. Pipeline Health Analysis
```bash
GET /api/leads/filter/?group_by=pipeline_level&group_count=true&status=Active
```

**Response:**
```json
{
  "group_by": "pipeline_level",
  "group_count": true,
  "total_records": 200,
  "statistics": {
    "total": 200,
    "active": 200,
    "verified": 150,
    "converted": 45,
    "groups": 6
  },
  "groups": [
    {
      "group_value": "New",
      "count": 80,
      "percentage": 40.0
    },
    {
      "group_value": "Qualified Leads",
      "count": 45,
      "percentage": 22.5
    },
    {
      "group_value": "Nurturing", 
      "count": 35,
      "percentage": 17.5
    },
    {
      "group_value": "Site Visits",
      "count": 25,
      "percentage": 12.5
    },
    {
      "group_value": "Booking",
      "count": 15,
      "percentage": 7.5
    }
  ]
}
```

### 6. Team Performance Analysis
```bash
GET /api/leads/filter/?group_by=department&group_count=true&status=Active&category=Hot,Warm
```

### 7. Lead Quality Assessment
```bash
GET /api/leads/filter/?group_by=category&group_count=true&is_verified=true
```

### 8. Conversion Funnel by Source
```bash
GET /api/leads/filter/?group_by=is_converted&lead_source=123,456&group_count=true
```

## Business Intelligence Use Cases

### 9. Regional Market Analysis (Leads vs Customers)
```bash
# Analyze lead distribution
GET /api/leads/filter/?group_by=country&group_count=true&status=Active

# Compare with customer distribution  
GET /api/customers/filter/?group_by=country_of_residence&group_count=true
```

### 10. Sales Team Performance Comparison
```bash
# Lead generation performance
GET /api/leads/filter/?group_by=marketer&group_count=true&date_from_date=2024-10-01

# Customer conversion performance
GET /api/customers/filter/?group_by=marketer&group_count=true&date_of_registration_from=2024-10-01
```

### 11. Lead Source ROI Analysis
```bash
# Total leads by source
GET /api/leads/filter/?group_by=lead_source&group_count=true&date_from_date=2024-01-01

# Converted customers by source
GET /api/customers/filter/?group_by=lead_source&group_count=true&date_of_registration_from=2024-01-01
```

### 12. Department Efficiency Analysis
```bash
# Leads allocated to departments
GET /api/leads/filter/?group_by=department&lead_type=allocated&group_count=true

# Department conversion rates
GET /api/leads/filter/?group_by=department&is_converted=true&group_count=true
```

## Advanced Filtering + Grouping Combinations

### 13. Hot Leads by Marketer (Detailed)
```bash
GET /api/leads/filter/?category=Hot&status=Active&group_by=marketer
```

**Response Structure:**
```json
{
  "group_by": "marketer", 
  "group_count": false,
  "total_records": 50,
  "groups": {
    "John Smith": {
      "count": 12,
      "records": [
        {
          "id": 1,
          "name": "Jane Doe",
          "category": "Hot",
          "status": "Active",
          "pipeline_level": "Site Visits",
          ...
        }
      ]
    },
    "Mary Johnson": {
      "count": 8,
      "records": [...]
    }
  }
}
```

### 14. Geographic Customer Analysis (Detailed)
```bash
GET /api/customers/filter/?customer_type=Individual&group_by=country_of_residence
```

### 15. Monthly Performance Tracking
```bash
# This month's leads by category
GET /api/leads/filter/?date_from_date=2024-10-01&date_to_date=2024-10-31&group_by=category&group_count=true

# This month's customer registrations by type
GET /api/customers/filter/?date_of_registration_from=2024-10-01&date_of_registration_to=2024-10-31&group_by=customer_type&group_count=true
```

## Comparative Analysis Examples

### 16. Lead Source Performance Comparison
```bash
# Lead volume by source
GET /api/leads/filter/?lead_source=123,456,789&group_by=lead_source&group_count=true

# Conversion rate by source
GET /api/customers/filter/?lead_source=123,456,789&group_by=lead_source&group_count=true
```

### 17. Marketer Efficiency Metrics
```bash
# Total leads assigned
GET /api/leads/filter/?marketer=EMP001,EMP002,EMP003&group_by=marketer&group_count=true

# Successful conversions
GET /api/customers/filter/?marketer=EMP001,EMP002,EMP003&group_by=marketer&group_count=true
```

### 18. Quality Control Analysis
```bash
# Verification rates by department
GET /api/leads/filter/?group_by=department&is_verified=true&group_count=true

# Conversion rates by verification status
GET /api/leads/filter/?group_by=is_verified&is_converted=true&group_count=true
```

## Dashboard-Ready Queries

### 19. Executive Summary Dashboard
```bash
# Overall lead distribution
GET /api/leads/filter/?group_by=status&group_count=true

# Customer type breakdown
GET /api/customers/filter/?group_by=customer_type&group_count=true

# Pipeline health
GET /api/leads/filter/?group_by=pipeline_level&status=Active&group_count=true
```

### 20. Sales Manager Dashboard
```bash
# Team performance
GET /api/leads/filter/?group_by=marketer&status=Active&group_count=true

# Hot prospects by team member
GET /api/leads/filter/?category=Hot&group_by=marketer&group_count=true

# Monthly conversions
GET /api/customers/filter/?date_of_registration_from=2024-10-01&group_by=marketer&group_count=true
```

## Performance Optimization Tips

### For Large Datasets
1. **Use Count-Only Grouping**: `group_count=true` for better performance
2. **Apply Date Filters**: Limit scope with date ranges
3. **Filter Before Grouping**: Apply status/category filters first

### For Real-Time Dashboards
1. **Cache Results**: Consider caching grouped data for frequently accessed reports
2. **Limit Group Size**: Some groupings like `marketer` may have many values
3. **Use Pagination**: For detailed grouping results

### Best Practices
- Combine filters to reduce dataset size before grouping
- Use appropriate group fields based on your analysis needs
- Consider using both leads and customer grouping for conversion analysis
- Apply time-based filters for trend analysis

## Error Handling

### Invalid Group Field
```json
{
  "error": "Invalid group_by field. Available options: [list of valid fields]"
}
```

### Empty Results
When no records match filters, returns empty groups array but maintains response structure.

## Integration Benefits

1. **Unified API**: Same grouping syntax across customers and leads
2. **Flexible Analysis**: Combine any filter with any grouping field
3. **Business Intelligence**: Ready-to-use data for reporting and analytics
4. **Performance**: Optimized database queries for large datasets
5. **Consistency**: Standardized response format for easy frontend integration

The grouping functionality transforms both APIs into powerful business intelligence tools while maintaining the flexibility and ease of use of the existing filtering system.