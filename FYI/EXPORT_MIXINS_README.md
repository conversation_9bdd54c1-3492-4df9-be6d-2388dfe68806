# Export Mixins Documentation

## Overview

The export mixins provide reusable functionality for ViewSets to export filtered data as PDF and Excel files. Two mixins are available:

1. **ReportExportMixin** - Basic PDF and Excel export functionality
2. **AdvancedReportExportMixin** - Extended functionality with statistics and multiple Excel sheets

## Features

### PDF Export
- Custom HTML templates with CSS styling
- Professional report layout with headers, metadata, and summaries
- Applied filters documentation
- Responsive design for print optimization
- Company branding and footer information

### Excel Export
- Multi-sheet workbooks
- Professional formatting with colors and borders
- Auto-adjusted column widths
- Metadata sheet with export information
- Statistics sheet (advanced mixin only)
- Proper data type handling for complex objects

### Advanced Features
- Permission-based filtering integration
- Custom statistics generation
- Flexible filename generation with timestamps
- Error handling and validation
- Swagger/OpenAPI documentation integration

## Implementation

### Basic Setup

1. **Import the mixins in your views.py:**
```python
from utils.export_mixins import ReportExportMixin, AdvancedReportExportMixin
```

2. **Add mixin to your ViewSet:**
```python
class MyFilterView(AdvancedReportExportMixin, viewsets.ViewSet):
    # Export configuration
    export_template_name = 'exports/my_custom_report.html'
    export_filename_prefix = 'my_report'
    export_title = 'My Data Report'
    
    # Your existing ViewSet code...
```

### Configuration Options

Configure the mixin behavior by setting these class attributes:

```python
class MyViewSet(AdvancedReportExportMixin, viewsets.ViewSet):
    # Required: Template for PDF generation
    export_template_name = 'exports/custom_report.html'
    
    # Optional: Filename prefix (default: 'report')
    export_filename_prefix = 'customer_data'
    
    # Optional: Report title (default: 'Data Report')
    export_title = 'Customer Analysis Report'
```

### Custom Methods

Override these methods to customize behavior:

#### 1. get_export_queryset(request)
```python
def get_export_queryset(self, request):
    """Customize which data gets exported"""
    queryset = self.get_queryset()
    # Apply additional filtering for exports
    return queryset.filter(is_active=True)
```

#### 2. get_export_statistics(queryset, request)
```python
def get_export_statistics(self, queryset, request):
    """Generate custom statistics for advanced exports"""
    return {
        'total_count': queryset.count(),
        'active_count': queryset.filter(status='active').count(),
        'export_date': timezone.now(),
        'custom_metric': calculate_custom_metric(queryset),
    }
```

#### 3. get_applied_filters(request)
```python
def get_applied_filters(self, request):
    """Extract relevant filter parameters"""
    filters = {}
    for param in ['status', 'category', 'date_from', 'date_to']:
        if request.query_params.get(param):
            filters[param] = request.query_params.get(param)
    return filters
```

## API Endpoints

The mixins automatically add these endpoints to your ViewSet:

### 1. PDF Export
```
GET /api/your-endpoint/export_pdf/
```

**Query Parameters:**
- `template`: Optional custom template name
- All filter parameters from your main list view

**Response:** PDF file download

### 2. Excel Export
```
GET /api/your-endpoint/export_excel/
```

**Query Parameters:**
- `sheet_name`: Custom sheet name (default: 'Data')
- `include_metadata`: Include metadata sheet (default: true)
- All filter parameters from your main list view

**Response:** Excel file download

### 3. Advanced Excel Export (AdvancedReportExportMixin only)
```
GET /api/your-endpoint/export_advanced_excel/
```

**Features:**
- Multiple sheets (Data, Statistics, Metadata)
- Enhanced formatting and charts
- Comprehensive statistics

## Template Creation

### Basic Template Structure

Create HTML templates in `templates/exports/`:

```html
<!DOCTYPE html>
<html>
<head>
    <title>{{ title }}</title>
    <style>
        /* Your custom CSS */
        @page { 
            size: A4; 
            margin: 2cm; 
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
        }
        .data-table { 
            width: 100%; 
            border-collapse: collapse; 
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{ title }}</h1>
        <p>Generated: {{ timestamp|date:"F d, Y g:i A" }}</p>
    </div>
    
    {% if filters_applied %}
    <div class="filters">
        <h3>Applied Filters:</h3>
        {% for key, value in filters_applied.items %}
            <p><strong>{{ key|title }}:</strong> {{ value }}</p>
        {% endfor %}
    </div>
    {% endif %}
    
    {% if data %}
    <table class="data-table">
        <thead>
            <tr>
                {% for key in data.0.keys %}
                    <th>{{ key|title }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {% for item in data %}
                <tr>
                    {% for value in item.values %}
                        <td>{{ value|default:"-" }}</td>
                    {% endfor %}
                </tr>
            {% endfor %}
        </tbody>
    </table>
    {% endif %}
</body>
</html>
```

### Available Template Context

Templates receive these context variables:

- `title`: Report title
- `data`: Serialized data array
- `timestamp`: Generation timestamp
- `user`: Current user (if authenticated)
- `total_records`: Total record count
- `filters_applied`: Dictionary of applied filters
- `export_type`: 'pdf' or 'excel'

## Examples

### Customer Export Implementation

```python
class CustomerExportView(AdvancedReportExportMixin, viewsets.ViewSet):
    export_template_name = 'exports/customer_report.html'
    export_filename_prefix = 'customer_report'
    export_title = 'Customer Data Export'
    
    def get_queryset(self):
        return Customer.objects.all()
    
    def get_export_statistics(self, queryset, request):
        return {
            'total_customers': queryset.count(),
            'individual_customers': queryset.filter(type='Individual').count(),
            'group_customers': queryset.filter(type='Group').count(),
            'active_customers': queryset.filter(status='Active').count(),
        }
    
    def get_applied_filters(self, request):
        filters = {}
        for param in ['customer_type', 'status', 'country']:
            if request.query_params.get(param):
                filters[param] = request.query_params.get(param)
        return filters
```

### Usage in Frontend

```javascript
// PDF Export
window.open('/api/customers/filter/export_pdf/?status=active&customer_type=Individual');

// Excel Export
window.open('/api/customers/filter/export_excel/?sheet_name=Active Customers&include_metadata=true');

// Advanced Excel Export
window.open('/api/customers/filter/export_advanced_excel/?status=active');
```

## Error Handling

The mixins include comprehensive error handling:

```python
{
    "error": "PDF generation failed",
    "detail": "Template not found: custom_template.html"
}
```

Common errors:
- Template not found
- Permission denied
- Invalid filter parameters
- Excel generation errors

## Performance Considerations

1. **Large Datasets**: Consider pagination or filtering for exports
2. **Template Complexity**: Simple templates render faster
3. **Image Assets**: Use base64 encoding for embedded images
4. **Memory Usage**: Excel exports consume more memory than PDF

## Security

- Exports respect the same permissions as list views
- User context is included in templates
- Files are generated in memory (not stored on disk)
- Proper content-type headers for downloads

## Dependencies

Required packages (already included in requirements.txt):
- `weasyprint`: PDF generation
- `xlsxwriter`: Excel file creation
- `django`: Template rendering
- `djangorestframework`: API framework

## Troubleshooting

### Common Issues

1. **Template Not Found**
   - Ensure template exists in `templates/exports/`
   - Check `export_template_name` setting

2. **Permission Denied**
   - Verify user has access to underlying data
   - Check ViewSet permissions

3. **Large File Timeouts**
   - Implement filtering to reduce dataset size
   - Consider background task processing for very large exports

4. **CSS Not Rendering in PDF**
   - Use inline styles or ensure CSS is in `<style>` tags
   - Avoid external CSS files

### Debug Mode

Enable debug information in templates:
```html
<!-- Debug info (remove in production) -->
{% if debug %}
<div style="font-size: 10px; color: gray;">
    Records: {{ data|length }}, Filters: {{ filters_applied|length }}
</div>
{% endif %}
```

## Best Practices

1. **Template Design**
   - Use print-friendly CSS
   - Include page breaks for long content
   - Test with various data sizes

2. **Performance**
   - Filter data before export
   - Use select_related() for foreign keys
   - Consider async processing for large exports

3. **User Experience**
   - Provide export progress indicators
   - Include helpful filenames with timestamps
   - Show applied filters in reports

4. **Security**
   - Validate export parameters
   - Log export activities
   - Respect data privacy regulations

## Future Enhancements

Potential improvements:
- Background task support for large exports
- Chart generation in Excel
- Email delivery of reports
- Custom styling options
- Export scheduling
- Data compression options