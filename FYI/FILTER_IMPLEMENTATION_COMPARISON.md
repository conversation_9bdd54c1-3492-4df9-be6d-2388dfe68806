# Customer vs Leads Filter Implementation Comparison

## Overview
Both Customer and Leads modules now have comprehensive filtering capabilities with multiple value support and extensive API documentation.

## Filter Endpoints

### Customer Filters
- **Standard View**: `/api/customers/all-customers/` - Uses CustomerFilter
- **Dedicated Filter**: `/api/customers/filter/` - Uses CustomerfilterView

### Leads Filters  
- **Standard View**: `/api/leads/prospects/` - Uses ProspectsFilter
- **Dedicated Filter**: `/api/leads/filter/` - Uses ProspectsFilterView

## Common Filter Features

### Multiple Value Support
Both implementations support comma-separated values for:
- **Lead Sources**: `?lead_source=123,456,789`
- **Marketers**: `?marketer=EMP001,EMP002,EMP003`

### Date Range Filtering
- **Customers**: `date_of_registration_from` / `date_of_registration_to`
- **Leads**: `date_from_date` / `date_to_date`

### Text Filters (Partial Match)
- **Name/Contact**: Both support name, phone, email filtering
- **Location**: Both support city/country filtering

### Choice Filters
- **Customer Type** vs **Lead Type**: Different choice sets
- **Status**: Both have status filtering with different options

## Customer-Specific Filters

| Filter | Type | Description | Example |
|--------|------|-------------|---------|
| `customer_type` | Choice | Individual/Group | `?customer_type=Individual` |
| `country_of_residence` | Text | Country (partial) | `?country_of_residence=Kenya` |
| `date_of_registration_from` | Date | Registration from date | `?date_of_registration_from=2024-01-01` |
| `date_of_registration_to` | Date | Registration to date | `?date_of_registration_to=2024-12-31` |
| `national_id` | Text | National ID (exact) | `?national_id=12345678` |
| `kra_pin` | Text | KRA PIN (exact) | `?kra_pin=A123456789Z` |
| `passport_no` | Text | Passport number | `?passport_no=AB123456` |

## Leads-Specific Filters

| Filter | Type | Description | Example |
|--------|------|-------------|---------|
| `lead_source_category` | Integer | Lead source category ID | `?lead_source_category=5` |
| `lead_source_subcategory` | Integer | Lead source subcategory ID | `?lead_source_subcategory=10` |
| `department` | Multiple Text | Department names | `?department=Digital,Telemarketing` |
| `department_member` | Multiple Text | Allocator employee numbers | `?department_member=EMP001,EMP002` |
| `lead_type` | Choice | personal/allocated | `?lead_type=personal` |
| `status` | Choice | Active/Dormant | `?status=Active` |
| `category` | Choice | Hot/Warm/Cold | `?category=Hot` |
| `pipeline_level` | Choice | New/Qualified/etc. | `?pipeline_level=New` |
| `is_verified` | Boolean | Verification status | `?is_verified=true` |
| `is_converted` | Boolean | Conversion status | `?is_converted=false` |
| `project` | Integer | Project ID | `?project=5` |
| `alternate_phone` | Text | Alternate phone | `?alternate_phone=254` |
| `comment` | Text | Comments (partial) | `?comment=follow` |

## Technical Implementation

### Filter Classes Used

#### Customer Filters
```python
class MultipleLeadSourceFilter(BaseInFilter, NumberFilter)
class MultipleMarketerFilter(BaseInFilter, CharFilter)
```

#### Leads Filters  
```python
class MultipleLeadSourceFilter(BaseInFilter, NumberFilter)
class MultipleMarketerFilter(BaseInFilter, CharFilter)  
class MultipleDepartmentFilter(BaseInFilter, CharFilter)
```

### API Documentation

Both implementations include:
- **Comprehensive Swagger Documentation**: Parameter descriptions, examples, response schemas
- **Filter Examples**: Practical usage examples in docstrings
- **Error Handling**: Proper error responses and validation
- **Filter Summary**: Response includes applied filters summary

### Response Format

Both return paginated responses with filter summary:
```json
{
  "count": 150,
  "next": "...",
  "previous": null,
  "results": [...],
  "filter_summary": {
    "applied_filters": {
      "lead_source": "123,456",
      "marketer": "EMP001,EMP002"
    },
    "total_without_pagination": 150
  }
}
```

## Permission Integration

### Customer Permissions
```python
queryset = customers_permission_filters(self.request.user, base_queryset)
```

### Leads Permissions
```python
queryset = leads_permission_filters(self.request.user, base_queryset)
```

Both implementations respect existing permission systems.

## Usage Examples Comparison

### Customer Examples
```bash
# Multiple lead sources and customer type
GET /api/customers/filter/?lead_source=123,456&customer_type=Individual&country_of_residence=Kenya

# Date range with marketer filter
GET /api/customers/filter/?date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31&marketer=EMP001,EMP002
```

### Leads Examples  
```bash
# Multiple lead sources with pipeline and category
GET /api/leads/filter/?lead_source=123,456&pipeline_level=New&category=Hot&status=Active

# Department and verification filtering
GET /api/leads/filter/?department=Digital,Telemarketing&is_verified=true&lead_type=allocated
```

## Key Differences

### 1. Data Model Complexity
- **Customers**: Simpler model focused on customer information
- **Leads**: More complex with pipeline management, departments, and lead lifecycle

### 2. Filter Granularity
- **Customers**: Basic demographic and registration filtering
- **Leads**: Detailed lead management and sales pipeline filtering

### 3. Business Process Alignment
- **Customers**: Post-conversion customer management
- **Leads**: Pre-conversion lead management and sales tracking

### 4. Hierarchical Filtering
- **Customers**: Flat filtering structure
- **Leads**: Hierarchical lead source filtering (category → subcategory → source)

## Best Practices Applied

1. **Backward Compatibility**: Single values still work alongside multiple values
2. **Performance**: Efficient SQL IN clauses for multiple value filters
3. **Validation**: Proper type validation and error handling
4. **Documentation**: Comprehensive API documentation with examples
5. **Pagination**: Configurable pagination with reasonable defaults
6. **Search Integration**: Combined filtering with search and ordering
7. **Permission Respect**: Full integration with existing permission systems

Both implementations provide powerful, flexible filtering capabilities while maintaining consistency in API design and user experience.