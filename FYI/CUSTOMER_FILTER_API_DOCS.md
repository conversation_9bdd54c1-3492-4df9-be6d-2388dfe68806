# Customer Filter API Documentation

## Overview
The `CustomerfilterView` provides advanced filtering capabilities for the Customer model with comprehensive filter options and proper API documentation.

## Endpoint
```
GET /api/customers/filter/
```

## Available Filters

### 1. Lead Source Filter
- **Parameter**: `lead_source`
- **Type**: String (comma-separated integers)
- **Description**: Filter by one or more lead source IDs
- **Examples**: 
  - `/api/customers/filter/?lead_source=123` - Single lead source
  - `/api/customers/filter/?lead_source=123,456,789` - Multiple lead sources

### 2. Marketer Filter
- **Parameter**: `marketer`
- **Type**: String (comma-separated employee numbers)
- **Description**: Filter by one or more marketer employee numbers
- **Examples**: 
  - `/api/customers/filter/?marketer=EMP001` - Single marketer
  - `/api/customers/filter/?marketer=EMP001,EMP002,EMP003` - Multiple marketers

### 3. Date of Registration Filters
- **Parameters**: 
  - `date_of_registration_from` (Date from)
  - `date_of_registration_to` (Date to)
- **Type**: Date (YYYY-MM-DD format)
- **Description**: Filter customers registered within a date range
- **Examples**: 
  - `/api/customers/filter/?date_of_registration_from=2024-01-01`
  - `/api/customers/filter/?date_of_registration_to=2024-12-31`
  - `/api/customers/filter/?date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31`

### 4. Customer Type Filter
- **Parameter**: `customer_type`
- **Type**: String
- **Options**: `Individual`, `Group`
- **Description**: Filter by customer type
- **Example**: `/api/customers/filter/?customer_type=Individual`

### 5. Country of Residence Filter
- **Parameter**: `country_of_residence`
- **Type**: String
- **Description**: Filter by country of residence (partial match, case insensitive)
- **Example**: `/api/customers/filter/?country_of_residence=Kenya`

### 6. Additional Filters
- **customer_name**: Partial match, case insensitive
- **phone**: Partial match for phone numbers

## Pagination
- **page**: Page number for pagination
- **page_size**: Number of results per page (max 100, default 20)

## Ordering
- **Parameter**: `ordering`
- **Available fields**: `customer_no`, `customer_name`, `date_of_registration`, `customer_type`
- **Descending order**: Prefix with `-`
- **Example**: `/api/customers/filter/?ordering=-date_of_registration`

## Complex Filter Examples

### Example 1: Filter Individual customers from Kenya registered in 2024
```
/api/customers/filter/?customer_type=Individual&country_of_residence=Kenya&date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31
```

### Example 2: Filter customers by multiple marketers and lead sources
```
/api/customers/filter/?marketer=EMP001,EMP002&lead_source=123,456&ordering=-date_of_registration
```

### Example 3: Filter Individual customers from Kenya with multiple lead sources
```
/api/customers/filter/?customer_type=Individual&country_of_residence=Kenya&lead_source=123,456,789&date_of_registration_from=2024-01-01
```

### Example 4: Search with pagination and multiple filters
```
/api/customers/filter/?customer_name=John&customer_type=Individual&marketer=EMP001,EMP002,EMP003&page=1&page_size=50&ordering=-date_of_registration
```

## Response Format

### Success Response (200)
```json
{
  "count": 150,
  "next": "http://api/customers/filter/?page=2",
  "previous": null,
  "results": [
    {
      "customer_no": "CUST001",
      "customer_name": "John Doe",
      "customer_type": "Individual",
      "country_of_residence": "Kenya",
      "date_of_registration": "2024-01-15",
      "lead_source": {
        "leadsource_id": 123,
        "name": "Website"
      },
      "marketer": {
        "employee_no": "EMP001",
        "fullnames": "Jane Smith"
      },
      "phone": "+254712345678",
      "primary_email": "<EMAIL>"
    }
  ],
  "filter_summary": {
    "applied_filters": {
      "customer_type": "Individual",
      "country_of_residence": "Kenya"
    },
    "total_without_pagination": 150
  }
}
```

### Error Responses
- **400**: Bad request - Invalid filter parameters
- **403**: Permission denied - User lacks access rights

## API Description in Swagger/OpenAPI

The API includes comprehensive Swagger documentation with:

1. **Parameter Descriptions**: Each filter parameter is documented with type, format, and usage examples
2. **Response Schema**: Complete response structure with example data
3. **Error Handling**: Documented error responses with status codes
4. **Filter Examples**: Practical usage examples in the operation description

## Integration with Existing Customer Views

The enhanced filtering is also available on the existing `CustomerView` through the updated `CustomerFilter` class, providing consistent filtering across all customer endpoints.

## Permission Handling

The view respects the existing permission system using `customers_permission_filters()` to ensure users only see customers they have access to based on their roles and permissions.