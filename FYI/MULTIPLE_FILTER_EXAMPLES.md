# Multiple Lead Source Filtering - Test Examples

## Basic Usage Examples

### 1. Single Lead Source (Backward Compatible)
```
GET /api/customers/filter/?lead_source=123
```
This will filter customers where lead_source__leadsource_id = 123

### 2. Multiple Lead Sources
```
GET /api/customers/filter/?lead_source=123,456,789
```
This will filter customers where lead_source__leadsource_id IN (123, 456, 789)

### 3. Single Marketer (Backward Compatible)
```
GET /api/customers/filter/?marketer=EMP001
```
This will filter customers where marketer__employee_no = 'EMP001'

### 4. Multiple Marketers
```
GET /api/customers/filter/?marketer=EMP001,EMP002,EMP003
```
This will filter customers where marketer__employee_no IN ('EMP001', 'EMP002', 'EMP003')

## Advanced Combinations

### 5. Multiple Lead Sources + Multiple Marketers
```
GET /api/customers/filter/?lead_source=123,456&marketer=EMP001,EMP002&customer_type=Individual
```

### 6. Multiple Lead Sources + Date Range
```
GET /api/customers/filter/?lead_source=123,456,789&date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31
```

### 7. Complex Filter with All Options
```
GET /api/customers/filter/?lead_source=123,456&marketer=EMP001,EMP002&customer_type=Individual&country_of_residence=Kenya&date_of_registration_from=2024-01-01&ordering=-date_of_registration&page_size=50
```

## Technical Implementation Details

### Filter Classes Used:
1. **MultipleLeadSourceFilter**: Inherits from `BaseInFilter` and `NumberFilter`
   - Converts comma-separated integers to a list
   - Uses `lookup_expr='in'` for SQL IN clause
   - Field: `lead_source__leadsource_id`

2. **MultipleMarketerFilter**: Inherits from `BaseInFilter` and `CharFilter`
   - Converts comma-separated strings to a list
   - Uses `lookup_expr='in'` for SQL IN clause
   - Field: `marketer__employee_no`

### SQL Query Examples:
- Single: `WHERE lead_source.leadsource_id = 123`
- Multiple: `WHERE lead_source.leadsource_id IN (123, 456, 789)`

### URL Parameter Format:
- Values separated by commas (no spaces)
- Example: `?lead_source=123,456,789`
- Example: `?marketer=EMP001,EMP002,EMP003`

## Error Handling

### Invalid Lead Source IDs:
If non-numeric values are provided for lead_source, the filter will ignore invalid values.

### Empty Values:
Empty strings or malformed comma lists will be handled gracefully by the BaseInFilter.

### Response Format:
The response includes a `filter_summary` showing which filters were applied:

```json
{
  "filter_summary": {
    "applied_filters": {
      "lead_source": "123,456,789",
      "marketer": "EMP001,EMP002",
      "customer_type": "Individual"
    },
    "total_without_pagination": 45
  }
}
```