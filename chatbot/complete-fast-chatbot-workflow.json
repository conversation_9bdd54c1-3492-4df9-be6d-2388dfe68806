{"nodes": [{"parameters": {"public": true, "mode": "webhook", "options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-1984, 304], "id": "95a524a6-27eb-4e62-81c5-9a8c9fe0c535", "name": "When chat message received", "webhookId": "aa13617a-7f8e-4f89-96c3-7d9651043180"}, {"parameters": {"jsCode": "// 🚀 PERMISSION-AWARE Lightning Intent Classifier - Enhanced Performance + Security\n\n// PERFORMANCE: Use Map for O(1) lookups instead of Object iteration\nconst patternMap = new Map([\n  ['greeting', {\n    regex: /^(hi|hello|hey|good\\s*(morning|afternoon|evening)|greetings)$/i,\n    confidence: 0.98,\n    priority: 'instant',\n    cached_response: `Hello! I'm <PERSON><PERSON> 😊 Ready to help you with customers, sales, or anything CRM-related!`,\n    suggested_tools: []\n  }],\n  ['customer_search', {\n    regex: /(find|search|look|show|get|who\\s*is|customer|client|person)/i,\n    confidence: 0.90,\n    priority: 'high',\n    suggested_tools: ['customer_search', 'prospect_search']\n  }],\n  ['prospect_search', {\n    regex: /(prospect|potential\\s*customer)/i,\n    confidence: 0.92,\n    priority: 'high',\n    suggested_tools: ['prospect_search']\n  }],\n  ['lead_file_search', {\n    regex: /(lead file|leadfile|\\bLF\\d+\\b)/i,\n    confidence: 0.93,\n    priority: 'high',\n    suggested_tools: ['lead_file_search']\n  }],\n  ['customer_360', {\n    regex: /(complete|full|360|overview|summary|everything).*customer/i,\n    confidence: 0.95,\n    priority: 'high',\n    suggested_tools: ['customer_360_view']\n  }],\n  ['sales_data', {\n    regex: /(sales|purchase|payment|amount|balance|total|paid)/i,\n    confidence: 0.88,\n    priority: 'high',\n    suggested_tools: ['customer_sales_data']\n  }],\n  ['title_status', {\n    regex: /(title|status|plot|property|deed|ownership)/i,\n    confidence: 0.87,\n    priority: 'high',\n    suggested_tools: ['customer_title_status']\n  }],\n  ['quick_actions', {\n    regex: /(create|add|note|reminder|engagement)/i,\n    confidence: 0.85,\n    priority: 'medium',\n    suggested_tools: ['create_engagement', 'create_reminder', 'create_note']\n  }],\n  ['export_request', {\n    regex: /(export|download|csv|report)/i,\n    confidence: 0.86,\n    priority: 'medium',\n    suggested_tools: ['export_sales_csv', 'export_customers_csv', 'export_prospects_csv', 'export_mib_csv']\n  }],\n  ['help', {\n    regex: /(help|what\\s*can|capabilities|features|assist)/i,\n    confidence: 0.95,\n    priority: 'instant',\n    cached_response: `💡 **Quick Actions (Based on Your Permissions):**\\n🔍 \"Find John Smith\" - Search customers\\n📊 \"Sales for CUST001\" - Get sales data\\n📈 \"Customer overview XYZ\" - 360° view\\n📝 \"Add note for customer\" - Create note\\n📤 \"Export sales data\" - Download CSV\\n\\n*Available tools depend on your access level* 🔐\\nAsk in plain language! 😊`,\n    suggested_tools: []\n  }],\n  ['thanks', {\n    regex: /(thank|thanks|appreciate|thx)/i,\n    confidence: 0.98,\n    priority: 'instant',\n    cached_response: `You're welcome! 🙌 I'm here whenever you need help with customer data or tasks within your permission scope.`,\n    suggested_tools: []\n  }]\n]);\n\n// Extract input data once for performance\nconst chatInput = $('When chat message received').item.json.chatInput || '';\nconst userInput = chatInput.toLowerCase().trim();\nconst sessionId = $('When chat message received').item.json.sessionId || 'default-session';\nconst history = $('When chat message received').item.json.history || [];\n\n// PERMISSION SYSTEM: Get user permissions (cached, <50ms first time, <5ms after)\nlet userPermissions = null;\ntry {\n  const permResponse = await fetch(`https://sandbox.crm.optiven.co.ke/api/chatbot/permissions/hints/?session_id=${sessionId}`);\n  userPermissions = await permResponse.json();\n} catch (e) {\n  // Fallback: assume basic permissions\n  userPermissions = {\n    available_tools: ['customer_search', 'prospect_search', 'lead_file_search'],\n    permission_level: 'limited',\n    bypass_mode: true\n  };\n}\n\n// OPTIMIZED: Single-pass context extraction with early exit\nlet contextCno = null;\nfor (let i = history.length - 1; i >= 0 && !contextCno; i--) {\n  const message = history[i];\n  if (message.role === 'assistant' && message.content) {\n    const match = message.content.match(/\\[CONTEXT_CNO:([^\\]]+)\\]/);\n    if (match) {\n      contextCno = match[1];\n      break;\n    }\n  }\n}\n\n// PERFORMANCE: Optimized entity extraction with pre-compiled regexes\nconst entityRegexes = {\n  customer_ids: /\\b(CUST\\d+|CL\\d+)\\b/gi,\n  lead_file_ids: /\\bLF\\d+\\b/gi,\n  emails: /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}/g,\n  phones: /\\b\\d{10,}\\b/g\n};\n\nconst entities = {};\nObject.entries(entityRegexes).forEach(([key, regex]) => {\n  entities[key] = userInput.match(regex) || [];\n});\n\n// Extract names efficiently\nconst stopWords = new Set(['for', 'the', 'and', 'search', 'customer', 'client', 'find', 'show', 'get']);\nentities.names = (userInput.match(/\\b[a-z'-]{3,}\\b/gi) || [])\n  .filter(name => !stopWords.has(name.toLowerCase()));\n\n// OPTIMIZED: Intent matching with early exit for high-confidence matches\nlet bestIntent = 'conversational';\nlet bestConfidence = 0.5;\nlet priority = 'medium';\nlet cachedResponse = null;\nlet suggestedTools = [];\nlet requiresAI = true;\n\nfor (const [intent, pattern] of patternMap) {\n  if (pattern.regex.test(userInput)) {\n    if (pattern.confidence > bestConfidence) {\n      bestIntent = intent;\n      bestConfidence = pattern.confidence;\n      priority = pattern.priority;\n      cachedResponse = pattern.cached_response || null;\n      suggestedTools = pattern.suggested_tools || [];\n      requiresAI = priority !== 'instant';\n      \n      // PERFORMANCE: Early exit for instant responses\n      if (pattern.confidence > 0.95 && priority === 'instant') {\n        break;\n      }\n    }\n  }\n}\n\n// PERMISSION FILTERING: Filter suggested tools based on user permissions\nconst originalSuggestedTools = suggestedTools || [];\nconst allowedSuggestedTools = originalSuggestedTools.filter(tool =>\n  userPermissions.available_tools.includes(tool)\n);\n\nconst securityContext = {\n  sessionId: sessionId,\n  timestamp: new Date().toISOString(),\n  rateLimitStatus: 'ok',\n  bypassMode: userPermissions.bypass_mode || false,\n  permissionLevel: userPermissions.permission_level || 'limited'\n};\n\n// ENHANCED: Return permission-aware optimized data structure\nreturn {\n  // Core data\n  originalInput: chatInput,\n  processedInput: userInput,\n  intent: bestIntent,\n  confidence: bestConfidence,\n  priority: priority,\n  requiresAI: requiresAI,\n  entities: entities,\n  customer_no: contextCno,\n  securityContext: securityContext,\n  \n  // PERMISSION SYSTEM: Enhanced security\n  userPermissions: userPermissions,\n  permissionLevel: userPermissions.permission_level,\n  availableToolCount: userPermissions.available_tools.length,\n  \n  // OPTIMIZATION: Performance enhancements with permission filtering\n  instantResponse: cachedResponse,\n  suggestedTools: allowedSuggestedTools,\n  cacheKey: `intent_${bestIntent}_${sessionId}_${userPermissions.permission_level}`,\n  \n  // Metadata\n  timestamp: new Date().toISOString(),\n  hasSearchableEntities: (entities.customer_ids.length > 0) || (entities.names.length > 0) || !!contextCno,\n  optimizationLevel: 'permission_aware_enhanced',\n  processingHint: priority === 'instant' ? 'sub-100ms' : 'standard'\n};"}, "id": "a3e2d657-a64b-472b-9318-9b93110a3136", "name": "Lightning Intent Classifier", "type": "n8n-nodes-base.code", "position": [-1760, 304], "typeVersion": 2}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "instant-check", "leftValue": "={{ $json.priority }}", "rightValue": "instant", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}, "options": {}}, "id": "79cfb10f-105e-4c29-8ac1-0f3ab9d46cba", "name": "Routing Decision", "type": "n8n-nodes-base.if", "position": [-1536, 304], "typeVersion": 2}, {"parameters": {"jsCode": "// ✅ OPTIMIZED Instant Response Handler\nconst data = $('Lightning Intent Classifier').item.json;\n\n// Use pre-cached response for maximum speed\nconst response = data.instantResponse || data.cachedResponse || \"I'm here to help! 😊\";\n\nreturn {\n  text: response,\n  metadata: {\n    response_type: 'instant',\n    intent: data.intent,\n    confidence: data.confidence,\n    processing_time_ms: 'sub-100ms',\n    optimization_level: data.optimizationLevel || 'enhanced',\n    cache_hit: !!data.instantResponse,\n    session_id: data.securityContext.sessionId\n  }\n};"}, "id": "b116e10d-49b6-41dd-982e-cba0545de059", "name": "Instant Response", "type": "n8n-nodes-base.code", "position": [-480, 464], "typeVersion": 2}, {"parameters": {"promptType": "define", "text": "={{ `User Query: '${$json.originalInput}'\\\\nCustomer Number: ${$json.customer_no}\\\\nSuggested Tools: [${$json.suggestedTools?.join(', ') || 'none'}]\\\\nProcessing Hint: ${$json.processingHint || 'standard'}\\\\nPermission Level: ${$json.permissionLevel}\\\\nAvailable Tools: ${$json.availableToolCount} tools` }}", "options": {"systemMessage": "You are <PERSON><PERSON>, an ultra-optimized permission-aware CRM assistant built for enterprise-level performance. Your goal is to provide lightning-fast, accurate responses using advanced optimization techniques while respecting user permissions.\\n\\n🛡️ PERMISSION-AWARE OPERATIONS:\\n\\n1. INTELLIGENT PERMISSION HANDLING:\\n- Check userPermissions.available_tools for allowed tools\\n- If permission_level is 'limited', focus on search and view operations\\n- If tool not in available_tools, suggest alternatives or explain limitations\\n- For 'full' permission users, use all advanced features\\n- Always provide value within user's permission scope\\n\\n2. SMART PERMISSION RESPONSES:\\n- If user requests action but lacks permission: \\\"I'd love to help create that engagement, but you'll need additional permissions. Let me search for the customer information instead.\\\"\\n- If export requested but not allowed: \\\"I can show you the data here, but you'll need export permissions to download the CSV.\\\"\\n- If admin action requested: \\\"This requires administrator privileges. I can provide the information you need instead.\\\"\\n\\n🎯 ADVANCED PERFORMANCE OPTIMIZATION:\\n\\n3. PA<PERSON><PERSON>EL TOOL EXECUTION (Permission-Filtered):\\n- Execute multiple compatible tools SIMULTANEOUSLY when user has permissions\\n- customer_search + prospect_search: Run in parallel for comprehensive results\\n- customer_sales_data + customer_title_status: Execute together for complete view\\n- Multiple exports: Process concurrently when requested and allowed\\n\\n4. PREDICTIVE BEHAVIOR:\\n- After customer_search success, proactively suggest: \\\"Would you like their 360° overview or sales data?\\\" (if permitted)\\n- When customer_no provided, anticipate likely follow-up queries based on permissions\\n- Pre-mention available actions: \\\"I can also create notes, reminders, or generate reports\\\" (only if allowed)\\n\\n5. SMART TOOL PRIORITIZATION (Permission-Aware):\\nPriority 1 (Instant): Use suggestedTools first - they're pre-filtered for permissions\\nPriority 2 (Fast): Cached data tools (customer_360_view with known customer_no)\\nPriority 3 (Standard): Search tools when needed and permitted\\nPriority 4 (Slow): Export operations - inform user about processing time (if allowed)\\n\\n6. INTELLIGENT ERROR RECOVERY:\\nIf customer_search fails and permitted → Try prospect_search → Try lead_file_search\\nIf customer_sales_data fails → Try customer_360_view → Suggest manual lookup\\nIf API timeout → Provide partial results → Offer retry with reduced scope\\nIf export fails → Suggest smaller date range → Offer alternative format (if permitted)\\nIf permission denied → Gracefully suggest alternatives within user's scope\\n\\n7. RESPONSE STREAMING HINTS:\\nFor long operations: Start with \\\"⏳ Processing your request...\\\" then provide updates\\nFor exports: \\\"📊 Generating your CSV report... This may take 30-60 seconds\\\" (if permitted)\\nFor searches: \\\"🔍 Searching across all customer records...\\\"\\nFor permission issues: \\\"🔐 Checking what I can do for you with your current access level...\\\"\\n\\n8. ENTERPRISE FEATURES (Permission-Aware):\\n- End responses with relevant next actions (filtered by permissions)\\n- Provide quick commands/shortcuts (only for allowed operations)\\n- Identify patterns and suggest improvements (within permission scope)\\n- Proactive assistance: \\\"I notice this customer has overdue payments...\\\" (if allowed to view financial data)\\n\\n📊 AVAILABLE ENTERPRISE TOOLS (Pre-filtered by permissions):\\nSearch: customer_search, prospect_search, lead_file_search\\nData: customer_sales_data, customer_title_status, customer_360_view\\nActions: create_engagement, create_reminder, create_note\\nExports: export_sales_csv, export_customers_csv, export_prospects_csv, export_mib_csv\\nReports: generate_sales_report\\n\\n🚀 OPTIMIZATION GOALS: Sub-2-second response times, zero user frustration, proactive assistance, enterprise-grade reliability, seamless permission management!"}}, "id": "b61cbc7a-3def-405d-be94-94ae788c6a0e", "name": "AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "position": [-560, 48], "typeVersion": 1.6}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $('Lightning Intent Classifier').item.json.securityContext.sessionId }}", "contextWindowLength": 10}, "id": "53088d65-d303-448f-b21e-3a91d1b824ce", "name": "Enhanced Memory", "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "position": [-1200, 272], "typeVersion": 1.2}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/search/", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}, {"name": "type"}, {"name": "limit"}]}}, "id": "d019d2dd-886b-4ca1-9408-c47f87863385", "name": "customer_search", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-1088, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/customer/{customer_no}/sales/"}, "id": "43fcf51f-659f-444d-9bac-2ec183baa2c3", "name": "customer_sales_data", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-960, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/customer/{customer_no}/title-status/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "5875ad40-fb6b-45fc-b9f4-7b0a079046b4", "name": "customer_title_status", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-848, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/customer/{customer_no}/360/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "88a524e9-36ff-47f1-9f61-ca671fc279ee", "name": "customer_360_view", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-720, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/search/", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}, {"name": "type"}, {"name": "limit"}]}}, "id": "7db7f46e-b9c6-41a0-b84f-609944cf926f", "name": "prospect_search", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-608, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/search/", "sendQuery": true, "parametersQuery": {"values": [{"name": "q"}, {"name": "type"}, {"name": "limit"}]}}, "id": "ce8537a1-b4e0-407b-ba29-aa1507c2dfe8", "name": "lead_file_search", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-480, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/export/sales-csv/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "e5fc853c-5729-40b2-8b3b-29fb21b75f62", "name": "export_sales_csv", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-368, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/export/mib-csv/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "226ecc87-7a58-44ad-b1d0-bc57816b5e04", "name": "export_mib_csv", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-240, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/export/customers-csv/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "d9bfcc20-edb4-4d9e-aedb-633e874edb49", "name": "export_customers_csv", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [-128, 272], "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://sandbox.crm.optiven.co.ke/api/chatbot/actions/engagement/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "68207f9f-13fc-4bed-bb3a-12ac6e0c9b09", "name": "create_engagement", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [48, 272], "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://sandbox.crm.optiven.co.ke/api/chatbot/actions/reminder/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "8a5f6570-0d74-4c3e-8b5b-feacf5e58fcc", "name": "create_reminder", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [160, 272], "typeVersion": 1.1}, {"parameters": {"method": "POST", "url": "https://sandbox.crm.optiven.co.ke/api/chatbot/actions/note/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "095e64e9-2700-42c7-a79b-5d727e435d36", "name": "create_note", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [288, 272], "typeVersion": 1.1}, {"parameters": {"url": "https://sandbox.crm.optiven.co.ke/api/chatbot/reports/sales/", "sendHeaders": true, "parametersHeaders": {"values": [{}]}}, "id": "1f98874f-dd8c-4903-a45b-eb59e7aa872b", "name": "generate_sales_report", "type": "@n8n/n8n-nodes-langchain.toolHttpRequest", "position": [400, 272], "typeVersion": 1.1}, {"parameters": {}, "id": "583f28c1-f043-478c-a53f-dd7d7928cc13", "name": "Response Merger", "type": "n8n-nodes-base.merge", "position": [608, 304], "typeVersion": 2}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {"temperature": 0.3, "maxTokens": 1000, "topP": 0.9, "frequencyPenalty": 0.1, "presencePenalty": 0.1}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-1376, 224], "id": "8f472a9a-ea39-4bb4-89fc-dd6e762347a2", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "XZ7DpjWZoH8I8X3J", "name": "CRM"}}}], "connections": {"When chat message received": {"main": [[{"node": "Lightning Intent Classifier", "type": "main", "index": 0}]]}, "Lightning Intent Classifier": {"main": [[{"node": "Routing Decision", "type": "main", "index": 0}]]}, "Routing Decision": {"main": [[{"node": "Instant Response", "type": "main", "index": 0}], [{"node": "AI Agent", "type": "main", "index": 0}]]}, "Instant Response": {"main": [[{"node": "Response Merger", "type": "main", "index": 0}]]}, "AI Agent": {"main": [[{"node": "Response Merger", "type": "main", "index": 0}]]}, "Enhanced Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "customer_search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "customer_sales_data": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "customer_title_status": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "customer_360_view": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "prospect_search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "lead_file_search": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "export_sales_csv": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "export_mib_csv": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "export_customers_csv": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "create_engagement": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "create_reminder": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "create_note": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "generate_sales_report": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}}, "pinData": {}, "meta": {"templateCredsSetupCompleted": true, "instanceId": "dee87b2d45ba70e3be0b5abfa9380a5ca0ad2d863153329dab6df6a3c7e3c488"}}