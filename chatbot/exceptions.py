"""
Chatbot Exception Handling
==========================

Standardized exceptions and error handling for the chatbot module.
"""

from django.http import JsonResponse
from rest_framework import status
from typing import Dict, Any, Optional
import logging
import traceback

from .config import ChatbotConfig

logger = logging.getLogger(__name__)


class ChatbotException(Exception):
    """Base exception for chatbot operations"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or 'CHATBOT_ERROR'
        self.details = details or {}
        super().__init__(self.message)


class ChatbotPermissionError(ChatbotException):
    """Permission-related errors"""
    
    def __init__(self, message: str = "Insufficient permissions", details: Dict = None):
        super().__init__(message, 'PERMISSION_DENIED', details)


class ChatbotSearchError(ChatbotException):
    """Search operation errors"""
    
    def __init__(self, message: str = "Search operation failed", details: Dict = None):
        super().__init__(message, 'SEARCH_ERROR', details)


class ChatbotRateLimitError(ChatbotException):
    """Rate limiting errors"""
    
    def __init__(self, message: str = "Rate limit exceeded", details: Dict = None):
        super().__init__(message, 'RATE_LIMIT_EXCEEDED', details)


class ChatbotValidationError(ChatbotException):
    """Input validation errors"""
    
    def __init__(self, message: str = "Invalid input", details: Dict = None):
        super().__init__(message, 'VALIDATION_ERROR', details)


class ChatbotResponseFormatter:
    """Standardized response formatting for chatbot API"""
    
    @staticmethod
    def success_response(data: Any, message: str = "Success", metadata: Dict = None) -> Dict:
        """Format successful response"""
        response = {
            'success': True,
            'message': message,
            'data': data
        }
        
        if metadata:
            response['metadata'] = metadata
            
        return response
    
    @staticmethod
    def error_response(error: Exception, request_id: str = None) -> Dict:
        """Format error response"""
        if isinstance(error, ChatbotException):
            response = {
                'success': False,
                'error': {
                    'code': error.error_code,
                    'message': error.message,
                    'details': error.details
                }
            }
        else:
            response = {
                'success': False,
                'error': {
                    'code': 'INTERNAL_ERROR',
                    'message': 'An internal error occurred',
                    'details': {}
                }
            }
            
            # Add detailed error info in debug mode
            if ChatbotConfig.DEBUG_MODE:
                response['error']['details'] = {
                    'exception_type': type(error).__name__,
                    'exception_message': str(error),
                    'traceback': traceback.format_exc()
                }
        
        if request_id:
            response['request_id'] = request_id
            
        return response
    
    @staticmethod
    def paginated_response(data: list, total_count: int, page: int = 1, 
                          page_size: int = 10, metadata: Dict = None) -> Dict:
        """Format paginated response"""
        total_pages = (total_count + page_size - 1) // page_size
        
        response = {
            'success': True,
            'data': data,
            'pagination': {
                'current_page': page,
                'page_size': page_size,
                'total_pages': total_pages,
                'total_count': total_count,
                'has_next': page < total_pages,
                'has_previous': page > 1
            }
        }
        
        if metadata:
            response['metadata'] = metadata
            
        return response


class ChatbotErrorHandler:
    """Global error handler for chatbot operations"""
    
    @staticmethod
    def handle_exception(func):
        """Decorator for consistent error handling"""
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except ChatbotException as e:
                logger.warning(f"Chatbot operation failed: {e.message}", extra={'details': e.details})
                return JsonResponse(
                    ChatbotResponseFormatter.error_response(e),
                    status=ChatbotErrorHandler._get_status_code(e)
                )
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}: {str(e)}", exc_info=True)
                return JsonResponse(
                    ChatbotResponseFormatter.error_response(e),
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
        return wrapper
    
    @staticmethod
    def _get_status_code(error: ChatbotException) -> int:
        """Get appropriate HTTP status code for chatbot exception"""
        status_mapping = {
            'PERMISSION_DENIED': status.HTTP_403_FORBIDDEN,
            'VALIDATION_ERROR': status.HTTP_400_BAD_REQUEST,
            'RATE_LIMIT_EXCEEDED': status.HTTP_429_TOO_MANY_REQUESTS,
            'SEARCH_ERROR': status.HTTP_500_INTERNAL_SERVER_ERROR,
            'CHATBOT_ERROR': status.HTTP_500_INTERNAL_SERVER_ERROR
        }
        
        return status_mapping.get(error.error_code, status.HTTP_500_INTERNAL_SERVER_ERROR) 