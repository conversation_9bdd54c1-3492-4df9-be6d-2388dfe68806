"""
Chatbot Performance Monitoring - Optimized Async Version
========================================================

High-performance async monitoring with intelligent sampling.
Minimizes monitoring overhead while maintaining visibility.
"""

from django.utils import timezone
from django.core.cache import cache
from django.db import connections
from typing import Dict, List, Any, Optional
import time
import logging
import json
import asyncio
import random
from functools import wraps
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from asgiref.sync import sync_to_async

from .config import ChatbotConfig

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Data class for performance metrics"""
    operation: str
    user_id: str
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    success: bool
    error_message: Optional[str] = None
    metadata: Optional[Dict] = None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        data = asdict(self)
        data['start_time'] = self.start_time.isoformat()
        data['end_time'] = self.end_time.isoformat()
        return data


class ChatbotPerformanceMonitor:
    """
    Optimized async performance monitoring with intelligent sampling.
    
    Features:
    - 10% sampling rate by default (configurable)
    - Async metric storage to prevent blocking
    - Intelligent sampling based on operation importance
    - Minimal performance impact
    """
    
    CACHE_KEY_PREFIX = "chatbot_perf_"
    METRICS_RETENTION_HOURS = 24
    
    # Sampling configuration
    DEFAULT_SAMPLE_RATE = 0.10  # 10% sampling
    CRITICAL_SAMPLE_RATE = 1.0  # Always monitor critical operations
    
    # Critical operations that should always be monitored
    CRITICAL_OPERATIONS = {
        'search', 'permission_check', 'authentication', 'error'
    }
    
    @staticmethod
    def track_operation(operation_name: str, sample_rate: Optional[float] = None):
        """
        Optimized decorator with intelligent sampling.
        
        Args:
            operation_name: Name of the operation to track
            sample_rate: Override default sampling rate (0.0-1.0)
        """
        def decorator(func):
            @wraps(func)
            def wrapper(*args, **kwargs):
                # Determine if we should monitor this call
                effective_sample_rate = sample_rate or ChatbotPerformanceMonitor._get_sample_rate(operation_name)
                should_monitor = ChatbotPerformanceMonitor._should_monitor(operation_name, effective_sample_rate)
                
                if not should_monitor:
                    # Skip monitoring for this call
                    return func(*args, **kwargs)
                
                # Monitoring active for this call
                start_time = timezone.now()
                user_id = "unknown"
                success = True
                error_message = None
                
                # Try to extract user from arguments (fast extraction)
                user_id = ChatbotPerformanceMonitor._extract_user_id(args)
                
                try:
                    result = func(*args, **kwargs)
                    return result
                except Exception as e:
                    success = False
                    error_message = str(e)
                    raise
                finally:
                    if should_monitor:  # Double-check in case of exceptions
                        end_time = timezone.now()
                        duration = (end_time - start_time).total_seconds()
                        
                        # Create metric
                        metric = PerformanceMetric(
                            operation=operation_name,
                            user_id=user_id,
                            start_time=start_time,
                            end_time=end_time,
                            duration_seconds=duration,
                            success=success,
                            error_message=error_message,
                            metadata={
                                'function_name': func.__name__,
                                'sampled': True,
                                'sample_rate': effective_sample_rate
                            }
                        )
                        
                        # Store metric asynchronously if possible
                        ChatbotPerformanceMonitor._store_metric_async(metric)
                        
                        # Log slow operations (always log regardless of sampling)
                        if duration > 3.0:  # Reduced threshold for better alerting
                            logger.warning(
                                f"Slow operation: {operation_name} took {duration:.3f}s "
                                f"for user {user_id}"
                            )
            
            return wrapper
        return decorator
    
    @staticmethod
    def _get_sample_rate(operation_name: str) -> float:
        """Get appropriate sample rate for operation"""
        if operation_name in ChatbotPerformanceMonitor.CRITICAL_OPERATIONS:
            return ChatbotPerformanceMonitor.CRITICAL_SAMPLE_RATE
        
        # Check if performance monitoring is enabled
        if not getattr(ChatbotConfig, 'ENABLE_PERFORMANCE_MONITORING', False):
            return 0.0  # Disabled
        
        return ChatbotPerformanceMonitor.DEFAULT_SAMPLE_RATE
    
    @staticmethod
    def _should_monitor(operation_name: str, sample_rate: float) -> bool:
        """Determine if this operation call should be monitored"""
        if sample_rate >= 1.0:
            return True
        if sample_rate <= 0.0:
            return False
        
        return random.random() < sample_rate
    
    @staticmethod
    def _extract_user_id(args: tuple) -> str:
        """Fast user ID extraction from function arguments"""
        for arg in args:
            if hasattr(arg, 'user') and hasattr(arg.user, 'employee_no'):
                return arg.user.employee_no
            elif hasattr(arg, 'employee_no'):
                return arg.employee_no
        return "unknown"
    
    @staticmethod
    def _store_metric_async(metric: PerformanceMetric):
        """Store metric asynchronously to avoid blocking main thread"""
        try:
            # Try async storage if event loop is available
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # Schedule async storage
                loop.create_task(ChatbotPerformanceMonitor._async_store_metric(metric))
            else:
                # Fallback to sync storage
                ChatbotPerformanceMonitor.store_metric(metric)
        except RuntimeError:
            # No event loop, use sync storage
            ChatbotPerformanceMonitor.store_metric(metric)
    
    @staticmethod
    async def _async_store_metric(metric: PerformanceMetric):
        """Async metric storage implementation"""
        try:
            await sync_to_async(ChatbotPerformanceMonitor.store_metric)(metric)
        except Exception as e:
            logger.error(f"Failed to store metric asynchronously: {e}")
    
    @staticmethod
    def store_metric(metric: PerformanceMetric):
        """Store performance metric in cache"""
        try:
            # Create cache key with timestamp
            timestamp = int(metric.start_time.timestamp())
            cache_key = f"{ChatbotPerformanceMonitor.CACHE_KEY_PREFIX}{metric.operation}_{timestamp}"
            
            # Store with retention period
            cache.set(
                cache_key, 
                metric.to_dict(), 
                ChatbotPerformanceMonitor.METRICS_RETENTION_HOURS * 3600
            )
            
            # Also add to operation-specific list
            operation_key = f"{ChatbotPerformanceMonitor.CACHE_KEY_PREFIX}list_{metric.operation}"
            operation_metrics = cache.get(operation_key, [])
            operation_metrics.append(cache_key)
            
            # Keep only recent metrics (last 100 operations)
            if len(operation_metrics) > 100:
                old_key = operation_metrics.pop(0)
                cache.delete(old_key)
            
            cache.set(
                operation_key, 
                operation_metrics, 
                ChatbotPerformanceMonitor.METRICS_RETENTION_HOURS * 3600
            )
            
        except Exception as e:
            logger.error(f"Failed to store performance metric: {str(e)}")
    
    @staticmethod
    def get_operation_metrics(operation: str, hours_back: int = 1) -> List[Dict]:
        """Get metrics for a specific operation"""
        try:
            operation_key = f"{ChatbotPerformanceMonitor.CACHE_KEY_PREFIX}list_{operation}"
            metric_keys = cache.get(operation_key, [])
            
            cutoff_time = timezone.now() - timedelta(hours=hours_back)
            recent_metrics = []
            
            for key in metric_keys:
                metric_data = cache.get(key)
                if metric_data:
                    metric_time = datetime.fromisoformat(metric_data['start_time'].replace('Z', '+00:00'))
                    if metric_time >= cutoff_time:
                        recent_metrics.append(metric_data)
            
            return recent_metrics
        except Exception as e:
            logger.error(f"Failed to get operation metrics: {str(e)}")
            return []
    
    @staticmethod
    def get_performance_summary(hours_back: int = 1) -> Dict[str, Any]:
        """Get performance summary for all operations"""
        operations = ['search', 'create_engagement', 'create_reminder', 'create_note', 'export_csv']
        summary = {
            'period_hours': hours_back,
            'generated_at': timezone.now().isoformat(),
            'operations': {}
        }
        
        for operation in operations:
            metrics = ChatbotPerformanceMonitor.get_operation_metrics(operation, hours_back)
            if metrics:
                durations = [m['duration_seconds'] for m in metrics]
                successes = [m for m in metrics if m['success']]
                errors = [m for m in metrics if not m['success']]
                
                summary['operations'][operation] = {
                    'total_requests': len(metrics),
                    'successful_requests': len(successes),
                    'failed_requests': len(errors),
                    'success_rate': len(successes) / len(metrics) * 100 if metrics else 0,
                    'avg_duration_seconds': sum(durations) / len(durations) if durations else 0,
                    'max_duration_seconds': max(durations) if durations else 0,
                    'min_duration_seconds': min(durations) if durations else 0,
                    'slow_requests': len([d for d in durations if d > 2.0]),  # > 2 seconds
                    'recent_errors': [e['error_message'] for e in errors[-5:]]  # Last 5 errors
                }
        
        return summary
    
    @staticmethod
    def get_database_performance() -> Dict[str, Any]:
        """Get database performance metrics"""
        try:
            db_metrics = {}
            
            # Test query performance for each connection
            for db_alias in ['default', 'reports']:
                if db_alias in connections.databases:
                    start_time = time.time()
                    try:
                        with connections[db_alias].cursor() as cursor:
                            cursor.execute("SELECT 1")
                            cursor.fetchone()
                        
                        duration = time.time() - start_time
                        db_metrics[db_alias] = {
                            'status': 'healthy',
                            'response_time_seconds': round(duration, 4)
                        }
                    except Exception as e:
                        db_metrics[db_alias] = {
                            'status': 'error',
                            'error': str(e)
                        }
            
            return db_metrics
        except Exception as e:
            logger.error(f"Failed to get database metrics: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def get_cache_performance() -> Dict[str, Any]:
        """Get cache performance metrics"""
        try:
            start_time = time.time()
            test_key = f"chatbot_cache_test_{int(start_time)}"
            
            # Test cache write
            cache.set(test_key, 'test_value', 60)
            
            # Test cache read
            value = cache.get(test_key)
            
            # Cleanup
            cache.delete(test_key)
            
            duration = time.time() - start_time
            
            return {
                'status': 'healthy' if value == 'test_value' else 'error',
                'response_time_seconds': round(duration, 4),
                'read_write_success': value == 'test_value'
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    @staticmethod
    def check_performance_alerts() -> List[Dict[str, Any]]:
        """Check for performance issues and return alerts"""
        alerts = []
        
        try:
            # Check recent performance
            summary = ChatbotPerformanceMonitor.get_performance_summary(1)  # Last hour
            
            for operation, metrics in summary['operations'].items():
                # Alert on high error rate
                if metrics['success_rate'] < 95 and metrics['total_requests'] > 5:
                    alerts.append({
                        'type': 'high_error_rate',
                        'operation': operation,
                        'severity': 'warning',
                        'message': f"{operation} has {metrics['success_rate']:.1f}% success rate",
                        'details': metrics
                    })
                
                # Alert on slow responses
                if metrics['avg_duration_seconds'] > 3.0:
                    alerts.append({
                        'type': 'slow_response',
                        'operation': operation,
                        'severity': 'warning',
                        'message': f"{operation} average response time is {metrics['avg_duration_seconds']:.2f}s",
                        'details': metrics
                    })
            
            # Check database performance
            db_metrics = ChatbotPerformanceMonitor.get_database_performance()
            for db_alias, metrics in db_metrics.items():
                if metrics.get('status') == 'error':
                    alerts.append({
                        'type': 'database_error',
                        'operation': f'database_{db_alias}',
                        'severity': 'critical',
                        'message': f"Database {db_alias} is not responding",
                        'details': metrics
                    })
                elif metrics.get('response_time_seconds', 0) > 1.0:
                    alerts.append({
                        'type': 'slow_database',
                        'operation': f'database_{db_alias}',
                        'severity': 'warning',
                        'message': f"Database {db_alias} response time is {metrics['response_time_seconds']:.2f}s",
                        'details': metrics
                    })
            
            # Check cache performance
            cache_metrics = ChatbotPerformanceMonitor.get_cache_performance()
            if cache_metrics.get('status') == 'error':
                alerts.append({
                    'type': 'cache_error',
                    'operation': 'cache',
                    'severity': 'warning',
                    'message': "Cache is not responding properly",
                    'details': cache_metrics
                })
            
        except Exception as e:
            alerts.append({
                'type': 'monitoring_error',
                'operation': 'performance_check',
                'severity': 'warning',
                'message': f"Failed to check performance: {str(e)}",
                'details': {'error': str(e)}
            })
        
        return alerts


# Optimized convenience decorators with appropriate sampling
search_performance = ChatbotPerformanceMonitor.track_operation('search')  # Always monitored (critical)
action_performance = ChatbotPerformanceMonitor.track_operation('action', sample_rate=0.2)  # 20% sampling
export_performance = ChatbotPerformanceMonitor.track_operation('export', sample_rate=0.5)  # 50% sampling (important)
permission_performance = ChatbotPerformanceMonitor.track_operation('permission_check')  # Always monitored (critical)

# Additional optimized decorators
api_performance = ChatbotPerformanceMonitor.track_operation('api', sample_rate=0.1)  # 10% sampling
cache_performance = ChatbotPerformanceMonitor.track_operation('cache', sample_rate=0.05)  # 5% sampling 