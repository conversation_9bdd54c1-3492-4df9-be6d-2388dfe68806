"""
Production Permission Configuration Management
============================================

Secure configuration management for enabling/disabling permission system
with proper environment variable support and security logging.

Author: CRM Optimization Team
Version: 1.0
"""

import os
import logging
from typing import Dict, Any, Optional, List
from django.conf import settings
from django.core.cache import cache

logger = logging.getLogger(__name__)


class PermissionConfigManager:
    """
    Centralized configuration management for chatbot permissions.
    Handles environment variables, security logging, and configuration validation.
    """
    
    # Configuration keys
    BYPASS_PERMISSIONS_KEY = 'CHATBOT_BYPASS_PERMISSIONS'
    PERMISSION_CACHE_TTL_KEY = 'CHATBOT_PERMISSION_CACHE_TTL'
    PERMISSION_API_TIMEOUT_KEY = 'CHATBOT_PERMISSION_TIMEOUT'
    PERMISSION_FALLBACK_MODE_KEY = 'CHATBOT_PERMISSION_FALLBACK'
    
    # Default values
    DEFAULTS = {
        'bypass_permissions': True,  # Safe default for development
        'permission_cache_ttl': 1800,  # 30 minutes
        'permission_api_timeout': 5,  # 5 seconds
        'enable_fallback_mode': True,  # Graceful degradation
        'log_permission_denials': True,  # Security logging
        'strict_mode': False,  # Strict permission enforcement
    }
    
    @classmethod
    def get_permission_config(cls) -> Dict[str, Any]:
        """
        Get comprehensive permission configuration with environment variable support.
        
        Returns:
            Dict containing all permission-related configuration
        """
        config = {
            # Core permission settings
            'bypass_permissions': cls._get_bool_env(
                cls.BYPASS_PERMISSIONS_KEY, 
                cls.DEFAULTS['bypass_permissions']
            ),
            
            # Performance settings
            'permission_cache_ttl': cls._get_int_env(
                cls.PERMISSION_CACHE_TTL_KEY,
                cls.DEFAULTS['permission_cache_ttl']
            ),
            
            'permission_api_timeout': cls._get_int_env(
                cls.PERMISSION_API_TIMEOUT_KEY,
                cls.DEFAULTS['permission_api_timeout']
            ),
            
            # Fallback and error handling
            'enable_fallback_mode': cls._get_bool_env(
                cls.PERMISSION_FALLBACK_MODE_KEY,
                cls.DEFAULTS['enable_fallback_mode']
            ),
            
            # Security and logging
            'log_permission_denials': cls._get_bool_env(
                'CHATBOT_LOG_PERMISSION_DENIALS',
                cls.DEFAULTS['log_permission_denials']
            ),
            
            'strict_mode': cls._get_bool_env(
                'CHATBOT_PERMISSION_STRICT_MODE',
                cls.DEFAULTS['strict_mode']
            ),
            
            # Environment detection
            'environment': cls._detect_environment(),
            'debug_mode': settings.DEBUG,
            
            # Security context
            'security_headers_enabled': True,
            'audit_logging_enabled': not cls._get_bool_env(cls.BYPASS_PERMISSIONS_KEY, True),
        }
        
        # Log configuration changes
        cls._log_configuration_status(config)
        
        return config
    
    @classmethod
    def is_permission_system_enabled(cls) -> bool:
        """Check if permission system is enabled (not in bypass mode)"""
        return not cls._get_bool_env(cls.BYPASS_PERMISSIONS_KEY, True)
    
    @classmethod
    def enable_permissions(cls, cache_ttl: int = 1800) -> Dict[str, str]:
        """
        Enable permission system with proper configuration.
        
        Args:
            cache_ttl: Cache timeout in seconds
            
        Returns:
            Dict with status and instructions for environment variables
        """
        instructions = {
            'status': 'instructions_provided',
            'message': 'Permission system configuration instructions',
            'environment_variables': {
                cls.BYPASS_PERMISSIONS_KEY: 'False',
                cls.PERMISSION_CACHE_TTL_KEY: str(cache_ttl),
                cls.PERMISSION_API_TIMEOUT_KEY: '5',
                cls.PERMISSION_FALLBACK_MODE_KEY: 'True'
            },
            'django_settings': {
                'file': 'chatbot/config.py',
                'changes': [
                    'BYPASS_ALL_PERMISSIONS = False',
                    'FORCE_UNRESTRICTED_ACCESS = False'
                ]
            },
            'security_notes': [
                'Test permission system in staging environment first',
                'Monitor permission performance metrics',
                'Ensure proper user authentication is configured',
                'Review permission analytics regularly'
            ]
        }
        
        logger.info("Permission system enablement instructions generated")
        return instructions
    
    @classmethod
    def disable_permissions(cls) -> Dict[str, str]:
        """
        Disable permission system (enable bypass mode).
        
        Returns:
            Dict with status and instructions
        """
        instructions = {
            'status': 'instructions_provided',
            'message': 'Permission system disabled (bypass mode enabled)',
            'environment_variables': {
                cls.BYPASS_PERMISSIONS_KEY: 'True'
            },
            'django_settings': {
                'file': 'chatbot/config.py',
                'changes': [
                    'BYPASS_ALL_PERMISSIONS = True',
                    'FORCE_UNRESTRICTED_ACCESS = True'
                ]
            },
            'warning': 'This disables all permission checks - use only for development/testing'
        }
        
        logger.warning("Permission system disabled (bypass mode enabled)")
        return instructions
    
    @classmethod
    def get_configuration_status(cls) -> Dict[str, Any]:
        """Get current permission system configuration status"""
        config = cls.get_permission_config()
        
        status = {
            'permission_system_enabled': not config['bypass_permissions'],
            'current_mode': 'production' if not config['bypass_permissions'] else 'bypass',
            'environment': config['environment'],
            'configuration': config,
            'health_checks': cls._run_health_checks(config),
            'recommendations': cls._get_recommendations(config)
        }
        
        return status
    
    @classmethod
    def _get_bool_env(cls, key: str, default: bool) -> bool:
        """Get boolean environment variable with proper parsing"""
        value = os.environ.get(key, str(default)).lower()
        return value in ('true', '1', 'yes', 'on')
    
    @classmethod
    def _get_int_env(cls, key: str, default: int) -> int:
        """Get integer environment variable with validation"""
        try:
            return int(os.environ.get(key, str(default)))
        except ValueError:
            logger.warning(f"Invalid integer value for {key}, using default: {default}")
            return default
    
    @classmethod
    def _detect_environment(cls) -> str:
        """Detect current environment (production, staging, development)"""
        if os.environ.get('DJANGO_ENV') == 'production':
            return 'production'
        elif os.environ.get('DJANGO_ENV') == 'staging':
            return 'staging'
        elif settings.DEBUG:
            return 'development'
        else:
            return 'unknown'
    
    @classmethod
    def _log_configuration_status(cls, config: Dict[str, Any]):
        """Log current configuration for security audit"""
        mode = 'BYPASS' if config['bypass_permissions'] else 'ENABLED'
        environment = config['environment'].upper()
        
        if config['bypass_permissions']:
            logger.warning(f"SECURITY: Permission system in BYPASS mode ({environment})")
        else:
            logger.info(f"SECURITY: Permission system ENABLED ({environment})")
        
        # Log configuration details in debug mode
        if config['debug_mode']:
            logger.debug(f"Permission configuration: {config}")
    
    @classmethod
    def _run_health_checks(cls, config: Dict[str, Any]) -> Dict[str, Any]:
        """Run health checks on permission configuration"""
        health = {
            'environment_appropriate': True,
            'security_compliant': True,
            'performance_optimal': True,
            'issues': []
        }
        
        # Check environment appropriateness
        if config['environment'] == 'production' and config['bypass_permissions']:
            health['environment_appropriate'] = False
            health['issues'].append('Permission bypass enabled in production environment')
        
        # Check security compliance
        if not config['audit_logging_enabled']:
            health['security_compliant'] = False
            health['issues'].append('Audit logging disabled')
        
        # Check performance configuration
        if config['permission_cache_ttl'] < 300:  # Less than 5 minutes
            health['performance_optimal'] = False
            health['issues'].append('Permission cache TTL is very short, may impact performance')
        
        return health
    
    @classmethod
    def _get_recommendations(cls, config: Dict[str, Any]) -> List[str]:
        """Get configuration recommendations"""
        recommendations = []
        
        if config['environment'] == 'production':
            if config['bypass_permissions']:
                recommendations.append('Enable permission system for production use')
            if not config['strict_mode']:
                recommendations.append('Consider enabling strict mode for production')
        
        if config['environment'] == 'development':
            if not config['bypass_permissions']:
                recommendations.append('Consider bypass mode for faster development')
        
        if config['permission_cache_ttl'] > 3600:  # More than 1 hour
            recommendations.append('Consider shorter cache TTL for more responsive permission changes')
        
        return recommendations


# Configuration access functions (for easy import)

def get_permission_config() -> Dict[str, Any]:
    """Get current permission configuration"""
    return PermissionConfigManager.get_permission_config()

def is_permissions_enabled() -> bool:
    """Check if permission system is enabled"""
    return PermissionConfigManager.is_permission_system_enabled()

def get_config_status() -> Dict[str, Any]:
    """Get comprehensive configuration status"""
    return PermissionConfigManager.get_configuration_status()


# Django management command helpers

class PermissionConfigCommand:
    """Helper for Django management commands"""
    
    @staticmethod
    def enable_permissions_command():
        """Enable permissions via management command"""
        instructions = PermissionConfigManager.enable_permissions()
        
        print("Enabling Chatbot Permission System")
        print("=" * 50)
        print(f"Status: {instructions['status']}")
        print(f"Message: {instructions['message']}")
        print()
        
        print("📋 Environment Variables to Set:")
        for key, value in instructions['environment_variables'].items():
            print(f"  export {key}={value}")
        print()
        
        print("📝 Django Settings Changes:")
        print(f"  File: {instructions['django_settings']['file']}")
        for change in instructions['django_settings']['changes']:
            print(f"  {change}")
        print()
        
        print("⚠️  Security Notes:")
        for note in instructions['security_notes']:
            print(f"  - {note}")
        print()
        
        return instructions
    
    @staticmethod
    def disable_permissions_command():
        """Disable permissions via management command"""
        instructions = PermissionConfigManager.disable_permissions()
        
        print("Disabling Chatbot Permission System")
        print("=" * 50)
        print(f"Status: {instructions['status']}")
        print(f"Message: {instructions['message']}")
        print()
        
        if 'warning' in instructions:
            print(f"⚠️  WARNING: {instructions['warning']}")
            print()
        
        print("📋 Environment Variables to Set:")
        for key, value in instructions['environment_variables'].items():
            print(f"  export {key}={value}")
        print()
        
        return instructions
    
    @staticmethod
    def status_command():
        """Show permission system status"""
        status = PermissionConfigManager.get_configuration_status()
        
        print("Chatbot Permission System Status")
        print("=" * 50)
        print(f"System Enabled: {'Yes' if status['permission_system_enabled'] else 'No (Bypass Mode)'}")
        print(f"Current Mode: {status['current_mode'].upper()}")
        print(f"Environment: {status['environment'].upper()}")
        print()
        
        # Health checks
        health = status['health_checks']
        print("Health Checks:")
        print(f"  Environment Appropriate: {'Yes' if health['environment_appropriate'] else 'No'}")
        print(f"  Security Compliant: {'Yes' if health['security_compliant'] else 'No'}")
        print(f"  Performance Optimal: {'Yes' if health['performance_optimal'] else 'No'}")
        
        if health['issues']:
            print("\nIssues Found:")
            for issue in health['issues']:
                print(f"  - {issue}")
        
        # Recommendations
        if status['recommendations']:
            print("\nRecommendations:")
            for rec in status['recommendations']:
                print(f"  - {rec}")
        
        print()
        return status
