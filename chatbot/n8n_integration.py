"""
N8N Integration Utilities
========================

Enhanced integration utilities for N8N workflows.
Provides session analytics, performance monitoring, and workflow optimization.
"""

from django.utils import timezone
from django.core.cache import cache
from typing import Dict, List, Any, Optional
import json
import logging
import hashlib

from .config import ChatbotConfig
from .monitoring import ChatbotPerformanceMonitor
from .services.lightweight_permissions import UnifiedChatbotPermissionSystem

logger = logging.getLogger(__name__)


class N8NSessionManager:
    """
    Manages N8N session analytics without storing chat content.
    Tracks user interaction patterns for workflow optimization.
    """
    
    @staticmethod
    def track_session_event(session_id: str, event_type: str, metadata: Dict = None) -> Dict:
        """Track session events for workflow analytics"""
        try:
            cache_key = f"n8n_session_{session_id}"
            session_data = cache.get(cache_key, {
                'session_id': session_id,
                'start_time': timezone.now().isoformat(),
                'events': [],
                'metrics': {'total_queries': 0, 'ai_tool_calls': 0}
            })
            
            event = {
                'timestamp': timezone.now().isoformat(),
                'event_type': event_type,
                'metadata': metadata or {}
            }
            
            session_data['events'].append(event)
            
            if event_type == 'query':
                session_data['metrics']['total_queries'] += 1
            elif event_type == 'tool_called':
                session_data['metrics']['ai_tool_calls'] += 1
            
            cache.set(cache_key, session_data, 7200)  # 2 hours
            
            return {'success': True, 'session_id': session_id}
            
        except Exception as e:
            logger.error(f"Failed to track session event: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def get_session_analytics(session_id: str) -> Dict:
        """Get analytics for a specific session"""
        try:
            cache_key = f"n8n_session_{session_id}"
            session_data = cache.get(cache_key)
            
            if not session_data:
                return {'error': 'Session not found'}
            
            # Calculate analytics
            events = session_data['events']
            metrics = session_data['metrics']
            
            # Calculate session duration
            start_time = timezone.datetime.fromisoformat(session_data['start_time'].replace('Z', '+00:00'))
            duration_minutes = (timezone.now() - start_time).total_seconds() / 60
            
            # Calculate average response time
            response_times = metrics.get('response_times', [])
            avg_response_time = sum(response_times) / len(response_times) if response_times else 0
            
            analytics = {
                'session_id': session_id,
                'duration_minutes': round(duration_minutes, 2),
                'total_events': len(events),
                'total_queries': metrics['total_queries'],
                'ai_tool_calls': metrics['ai_tool_calls'],
                'avg_response_time_ms': round(avg_response_time, 2),
                'events_timeline': [
                    {
                        'timestamp': e['timestamp'],
                        'event_type': e['event_type'],
                        'metadata': e.get('metadata', {})
                    }
                    for e in events[-10:]  # Last 10 events
                ]
            }
            
            return analytics
            
        except Exception as e:
            logger.error(f"Failed to get session analytics: {str(e)}")
            return {'error': str(e)}


class N8NPerformanceTracker:
    """
    Tracks performance metrics for N8N workflow optimization.
    """
    
    @staticmethod
    def track_intent_classification(session_id: str, intent: str, processing_time_ms: float) -> None:
        """Track intent classification performance"""
        try:
            metric_data = {
                'session_id': session_id,
                'intent': intent,
                'processing_time_ms': processing_time_ms,
                'timestamp': timezone.now().isoformat()
            }
            
            cache_key = f"n8n_intent_{int(timezone.now().timestamp())}"
            cache.set(cache_key, metric_data, 3600)
            
            logger.info(f"Intent classification tracked: {intent} - {processing_time_ms}ms")
            
        except Exception as e:
            logger.error(f"Failed to track intent classification: {str(e)}")
    
    @staticmethod
    def track_tool_performance(tool_name: str, execution_time_ms: float, 
                             success: bool, error_message: str = None) -> None:
        """Track AI tool performance"""
        try:
            metric_data = {
                'tool_name': tool_name,
                'execution_time_ms': execution_time_ms,
                'success': success,
                'error_message': error_message,
                'timestamp': timezone.now().isoformat()
            }
            
            # Cache for analytics
            cache_key = f"n8n_tool_{tool_name}_{int(timezone.now().timestamp())}"
            cache.set(cache_key, metric_data, 3600)
            
            logger.info(f"N8N tool performance tracked: {tool_name} - {execution_time_ms}ms")
            
        except Exception as e:
            logger.error(f"Failed to track tool performance: {str(e)}")
    
    @staticmethod
    def get_workflow_analytics(hours_back: int = 1) -> Dict:
        """Get workflow performance analytics"""
        try:
            summary = ChatbotPerformanceMonitor.get_performance_summary(hours_back)
            
            # Add N8N-specific metrics
            workflow_metrics = {
                'period_hours': hours_back,
                'generated_at': timezone.now().isoformat(),
                'workflow_performance': summary,
                'recommendations': []
            }
            
            # Generate optimization recommendations
            operations = summary.get('operations', {})
            
            for operation, metrics in operations.items():
                if metrics['avg_duration_seconds'] > 2.0:
                    workflow_metrics['recommendations'].append({
                        'type': 'performance',
                        'operation': operation,
                        'issue': 'Slow response time',
                        'suggestion': f'Consider optimizing {operation} - current avg: {metrics["avg_duration_seconds"]:.2f}s'
                    })
                
                if metrics['success_rate'] < 95:
                    workflow_metrics['recommendations'].append({
                        'type': 'reliability',
                        'operation': operation,
                        'issue': 'Low success rate',
                        'suggestion': f'Investigate {operation} failures - current rate: {metrics["success_rate"]:.1f}%'
                    })
            
            return workflow_metrics
            
        except Exception as e:
            logger.error(f"Failed to get workflow analytics: {str(e)}")
            return {'error': str(e)}


class N8NPermissionIntegrator:
    """
    Provides permission-aware support for N8N workflows.
    Ensures workflows respect user permissions and provide relevant suggestions.
    """
    
    @staticmethod
    def get_user_capabilities(user) -> Dict:
        """Get user capabilities for N8N workflow routing"""
        try:
            user_perms = UnifiedChatbotPermissionSystem.get_user_permissions_comprehensive(user)
            summary = user_perms['permission_summary']
            
            # Build N8N-friendly capability map
            capabilities = {
                'search_capabilities': {
                    'can_search_customers': summary['can_view_customers'],
                    'can_search_prospects': summary['can_view_prospects'],
                    'can_search_leadfiles': summary['can_view_leadfiles'],
                    'search_scope': 'own' if user.is_marketer else 'all'
                },
                'action_capabilities': {
                    'can_create_notes': summary['can_create_notes'],
                    'can_create_engagements': summary.get('can_create_engagements', False),
                    'can_set_reminders': summary.get('can_set_reminders', False)
                },
                'export_capabilities': {
                    'can_export_data': summary['can_export_data'],
                    'available_exports': []
                },
                'user_context': {
                    'employee_no': user.employee_no,
                    'is_marketer': user.is_marketer,
                    'office': user.office,
                    'role_type': 'marketer' if user.is_marketer else 'admin'
                }
            }
            
            # Determine available exports based on permissions
            if summary['can_export_data']:
                if summary['can_view_customers']:
                    capabilities['export_capabilities']['available_exports'].append('customers')
                if summary['can_view_prospects']:
                    capabilities['export_capabilities']['available_exports'].append('prospects')
                if summary['can_view_leadfiles']:
                    capabilities['export_capabilities']['available_exports'].append('leadfiles')
            
            return {
                'success': True,
                'capabilities': capabilities,
                'generated_at': timezone.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get user capabilities: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    @staticmethod
    def validate_tool_access(user, tool_name: str) -> Dict:
        """Validate if user can access a specific N8N tool"""
        try:
            # Map N8N tools to required permissions
            tool_permissions = {
                'CRM Search Tool': ['can_view_customers', 'can_view_prospects', 'can_view_leadfiles'],
                'Create Engagement Tool': ['can_create_engagements'],
                'Add Note Tool': ['can_create_notes'],
                'Set Reminder Tool': ['can_set_reminders'],
                'Export Sales CSV Tool': ['can_export_data', 'can_view_leadfiles'],
                'Export MIB CSV Tool': ['can_export_data', 'can_view_reports'],
                'Export Customers CSV Tool': ['can_export_data', 'can_view_customers'],
                'Export Prospects CSV Tool': ['can_export_data', 'can_view_prospects']
            }
            
            required_perms = tool_permissions.get(tool_name, [])
            
            if not required_perms:
                return {
                    'tool_name': tool_name,
                    'access_allowed': True,
                    'reason': 'No specific permissions required'
                }
            
            user_perms = UnifiedChatbotPermissionSystem.get_user_permissions_comprehensive(user)
            effective_perms = user_perms['effective_permissions']
            
            # Check if user has any of the required permissions
            has_access = any(effective_perms.get(perm, False) for perm in required_perms)
            
            return {
                'tool_name': tool_name,
                'access_allowed': has_access,
                'required_permissions': required_perms,
                'user_permissions': {perm: effective_perms.get(perm, False) for perm in required_perms},
                'reason': 'Sufficient permissions' if has_access else 'Insufficient permissions'
            }
            
        except Exception as e:
            logger.error(f"Tool access validation failed: {str(e)}")
            return {
                'tool_name': tool_name,
                'access_allowed': False,
                'reason': f'Validation error: {str(e)}'
            }
    
    @staticmethod
    def generate_workflow_suggestions(user) -> Dict:
        """Generate workflow routing suggestions based on user permissions"""
        try:
            capabilities = N8NPermissionIntegrator.get_user_capabilities(user)
            
            if not capabilities['success']:
                return capabilities
            
            caps = capabilities['capabilities']
            suggestions = {
                'routing_suggestions': [],
                'available_tools': [],
                'blocked_tools': [],
                'user_experience_tips': []
            }
            
            # Generate routing suggestions
            if caps['search_capabilities']['can_search_customers']:
                suggestions['routing_suggestions'].append({
                    'condition': 'user asks about customers',
                    'route_to': 'CRM Search Tool',
                    'parameters': {'type': 'customers'}
                })
                suggestions['available_tools'].append('CRM Search Tool')
            
            if caps['action_capabilities']['can_create_notes']:
                suggestions['routing_suggestions'].append({
                    'condition': 'user wants to create note',
                    'route_to': 'Add Note Tool',
                    'parameters': {}
                })
                suggestions['available_tools'].append('Add Note Tool')
            
            # Add user experience tips
            if user.is_marketer:
                suggestions['user_experience_tips'].extend([
                    "As a marketer, you can only access your own customers and prospects",
                    "Use 'my customers' or 'my prospects' for more relevant results",
                    "You can create notes and reminders for your leads"
                ])
            
            if caps['export_capabilities']['can_export_data']:
                suggestions['user_experience_tips'].append(
                    f"You can export data for: {', '.join(caps['export_capabilities']['available_exports'])}"
                )
            
            return {
                'success': True,
                'suggestions': suggestions,
                'user_context': caps['user_context']
            }
            
        except Exception as e:
            logger.error(f"Failed to generate workflow suggestions: {str(e)}")
            return {'success': False, 'error': str(e)}


class N8NWorkflowOptimizer:
    """
    Provides optimization suggestions for N8N workflows.
    """
    
    @staticmethod
    def analyze_intent_patterns(hours_back: int = 24) -> Dict:
        """Analyze intent classification patterns for optimization"""
        try:
            # This would analyze cached intent data
            patterns = {
                'most_common_intents': [],
                'low_confidence_patterns': [],
                'optimization_suggestions': []
            }
            
            # Add intelligent suggestions based on patterns
            patterns['optimization_suggestions'].extend([
                {
                    'type': 'caching',
                    'suggestion': 'Cache common query responses to improve speed',
                    'impact': 'High',
                    'implementation': 'Add caching layer in N8N workflow before AI agent'
                },
                {
                    'type': 'routing',
                    'suggestion': 'Optimize intent classification routing for frequently used patterns',
                    'impact': 'Medium', 
                    'implementation': 'Update Lightning Intent Classifier patterns'
                }
            ])
            
            return patterns
            
        except Exception as e:
            logger.error(f"Failed to analyze intent patterns: {str(e)}")
            return {'error': str(e)}
    
    @staticmethod
    def generate_n8n_enhancements() -> Dict:
        """Generate specific N8N workflow enhancement recommendations"""
        return {
            'performance_enhancements': [
                {
                    'component': 'Lightning Intent Classifier',
                    'enhancement': 'Add response time tracking',
                    'code_snippet': '''
// Add to your Lightning Intent Classifier
const startTime = Date.now();
// ... existing logic ...
const processingTime = Date.now() - startTime;

// Send to session analytics
fetch('https://sandbox.crm.optiven.co.ke/api/chatbot/session/analytics/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        session_id: sessionId,
        event_type: 'intent_classified',
        metadata: {
            intent: intent,
            processing_time_ms: processingTime,
            requires_ai: requiresAI
        }
    })
});
                    '''
                },
                {
                    'component': 'AI Agent',
                    'enhancement': 'Add tool performance tracking',
                    'code_snippet': '''
// Add before tool execution
const toolStartTime = Date.now();

// Add after tool execution  
const toolExecutionTime = Date.now() - toolStartTime;

// Track tool performance
fetch('https://sandbox.crm.optiven.co.ke/api/chatbot/monitoring/performance/', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        tool_name: toolName,
        execution_time_ms: toolExecutionTime,
        success: true
    })
});
                    '''
                }
            ],
            'monitoring_enhancements': [
                {
                    'feature': 'Real-time Performance Dashboard',
                    'endpoint': '/api/chatbot/monitoring/performance/',
                    'description': 'Call this endpoint to get real-time performance metrics'
                },
                {
                    'feature': 'Session Analytics',
                    'endpoint': '/api/chatbot/session/analytics/',
                    'description': 'Track user session patterns without storing chat content'
                }
            ]
        } 