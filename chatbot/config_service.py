"""
Optimized Configuration Service
==============================

Advanced configuration management with caching, hot-reloading,
and environment variable optimization.

NOTE: This file is currently UNUSED. The main configuration is in config.py.
This file is kept for potential future use if hot-reload functionality is needed.

To use this service, update imports throughout the codebase from:
    from ..config import ChatbotConfig
to:
    from ..config_service import OptimizedChatbotConfig as ChatbotConfig
"""

import os
import time
import threading
import logging
from typing import Dict, Any, Optional, Union, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


@dataclass 
class ConfigEntry:
    """Configuration entry with metadata"""
    key: str
    value: Any
    cached_at: datetime = field(default_factory=datetime.now)
    ttl: int = 300  # 5 minutes default
    converter: Optional[Callable] = None
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        return datetime.now() > (self.cached_at + timedelta(seconds=self.ttl))


class OptimizedConfigService:
    """
    High-performance configuration service with intelligent caching.
    
    Features:
    - Environment variable caching with TTL
    - Hot-reloading for development
    - Type conversion with validation
    - Thread-safe operations
    - Zero-downtime config updates
    
    NOTE: Currently unused. See config.py for active configuration.
    """
    
    _instance = None
    _lock = threading.RLock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized') or not self._initialized:
            with self._lock:
                if not self._initialized:
                    self._cache: Dict[str, ConfigEntry] = {}
                    self._watchers: Dict[str, datetime] = {}
                    self._hot_reload_enabled = False
                    self._last_reload = datetime.now()
                    self._initialized = True
    
    def get_bool(self, key: str, default: bool = False, ttl: int = 300) -> bool:
        """Get boolean configuration value with caching"""
        return self._get_cached_value(key, default, ttl, self._convert_bool)
    
    def get_int(self, key: str, default: int = 0, ttl: int = 300) -> int:
        """Get integer configuration value with caching"""
        return self._get_cached_value(key, default, ttl, self._convert_int)
    
    def get_float(self, key: str, default: float = 0.0, ttl: int = 300) -> float:
        """Get float configuration value with caching"""
        return self._get_cached_value(key, default, ttl, self._convert_float)
    
    def get_str(self, key: str, default: str = "", ttl: int = 300) -> str:
        """Get string configuration value with caching"""
        return self._get_cached_value(key, default, ttl, str)
    
    def _get_cached_value(self, key: str, default: Any, ttl: int, converter: Optional[Callable]) -> Any:
        """Get cached configuration value with type conversion"""
        with self._lock:
            # Check cache first
            if key in self._cache:
                entry = self._cache[key]
                if not entry.is_expired:
                    return entry.value
                else:
                    # Cache expired, remove entry
                    del self._cache[key]
            
            # Get fresh value from environment
            raw_value = os.environ.get(key)
            if raw_value is None:
                value = default
            else:
                try:
                    value = converter(raw_value) if converter else raw_value
                except (ValueError, TypeError) as e:
                    logger.warning(f"Failed to convert config {key}='{raw_value}': {e}, using default")
                    value = default
            
            # Cache the value
            self._cache[key] = ConfigEntry(
                key=key,
                value=value,
                ttl=ttl,
                converter=converter
            )
            
            return value
    
    def enable_hot_reload(self, check_interval: int = 30):
        """
        Enable hot-reloading of configuration values.
        
        Args:
            check_interval: How often to check for changes (seconds)
        """
        self._hot_reload_enabled = True
        self._start_hot_reload_thread(check_interval)
        logger.info(f"Hot-reload enabled with {check_interval}s interval")
    
    def disable_hot_reload(self):
        """Disable hot-reloading"""
        self._hot_reload_enabled = False
        logger.info("Hot-reload disabled")
    
    def invalidate_cache(self, key: Optional[str] = None):
        """
        Invalidate cached configuration values.
        
        Args:
            key: Specific key to invalidate, or None for all keys
        """
        with self._lock:
            if key:
                self._cache.pop(key, None)
                logger.debug(f"Invalidated config cache for: {key}")
            else:
                self._cache.clear()
                logger.debug("Invalidated entire config cache")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            total_entries = len(self._cache)
            expired_entries = sum(1 for entry in self._cache.values() if entry.is_expired)
            
            return {
                'total_entries': total_entries,
                'active_entries': total_entries - expired_entries,
                'expired_entries': expired_entries,
                'cache_keys': list(self._cache.keys()),
                'hot_reload_enabled': self._hot_reload_enabled,
                'last_reload': self._last_reload.isoformat()
            }
    
    def _start_hot_reload_thread(self, check_interval: int):
        """Start background thread for hot-reloading"""
        def reload_worker():
            while self._hot_reload_enabled:
                try:
                    time.sleep(check_interval)
                    if self._hot_reload_enabled:
                        self._check_for_changes()
                except Exception as e:
                    logger.error(f"Hot-reload error: {e}")
        
        thread = threading.Thread(target=reload_worker, daemon=True)
        thread.start()
    
    def _check_for_changes(self):
        """Check for configuration changes and reload if needed"""
        with self._lock:
            changed_keys = []
            
            for key, entry in list(self._cache.items()):
                current_raw = os.environ.get(key)
                
                # Convert current raw value using same converter
                try:
                    if current_raw is None:
                        current_value = None
                    else:
                        current_value = entry.converter(current_raw) if entry.converter else current_raw
                    
                    # Compare values
                    if current_value != entry.value:
                        changed_keys.append(key)
                        # Update cache with new value
                        entry.value = current_value
                        entry.cached_at = datetime.now()
                        
                except Exception as e:
                    logger.error(f"Error checking config change for {key}: {e}")
            
            if changed_keys:
                self._last_reload = datetime.now()
                logger.info(f"Hot-reloaded config keys: {changed_keys}")
    
    @staticmethod
    def _convert_bool(value: str) -> bool:
        """Convert string to boolean"""
        if isinstance(value, bool):
            return value
        return str(value).lower() in ('true', '1', 'yes', 'on', 'enabled')
    
    @staticmethod
    def _convert_int(value: str) -> int:
        """Convert string to integer with validation"""
        return int(value)
    
    @staticmethod
    def _convert_float(value: str) -> float:
        """Convert string to float with validation"""
        return float(value)


# Global config service instance
config_service = OptimizedConfigService()


class OptimizedChatbotConfig:
    """
    Optimized ChatbotConfig using the configuration service.
    Replaces the original ChatbotConfig with cached, hot-reloadable values.
    
    NOTE: Currently unused. See config.py for active configuration.
    """
    
    # Cache timeouts for different types of config
    FAST_CACHE_TTL = 60      # 1 minute for frequently accessed
    STANDARD_CACHE_TTL = 300  # 5 minutes for standard config
    SLOW_CACHE_TTL = 900     # 15 minutes for rarely changed
    
    @classmethod
    def get_testing_mode(cls) -> bool:
        """Get testing mode with caching"""
        return config_service.get_bool('CHATBOT_TESTING_MODE', False, cls.FAST_CACHE_TTL)
    
    @classmethod 
    def get_bypass_permissions(cls) -> bool:
        """Get bypass permissions flag with caching"""
        return config_service.get_bool('CHATBOT_BYPASS_PERMISSIONS', True, cls.FAST_CACHE_TTL)
    
    @classmethod
    def get_cache_timeout(cls) -> int:
        """Get cache timeout with caching"""
        return config_service.get_int('CHATBOT_CACHE_TIMEOUT', 900, cls.STANDARD_CACHE_TTL)
    
    @classmethod
    def get_permission_cache_timeout(cls) -> int:
        """Get permission cache timeout with caching"""
        return config_service.get_int('CHATBOT_PERMISSION_CACHE_TIMEOUT', 1800, cls.STANDARD_CACHE_TTL)
    
    @classmethod
    def get_max_search_results(cls) -> int:
        """Get max search results with caching"""
        return config_service.get_int('CHATBOT_MAX_RESULTS', 100, cls.SLOW_CACHE_TTL)
    
    @classmethod
    def get_default_search_limit(cls) -> int:
        """Get default search limit with caching"""
        return config_service.get_int('CHATBOT_DEFAULT_LIMIT', 20, cls.SLOW_CACHE_TTL)
    
    @classmethod
    def get_rate_limit_requests(cls) -> int:
        """Get rate limit requests with caching"""
        return config_service.get_int('CHATBOT_RATE_LIMIT', 100, cls.STANDARD_CACHE_TTL)
    
    @classmethod
    def get_enable_performance_monitoring(cls) -> bool:
        """Get performance monitoring flag with caching"""
        return config_service.get_bool('CHATBOT_ENABLE_PERF_MONITORING', False, cls.FAST_CACHE_TTL)
    
    @classmethod
    def get_log_search_queries(cls) -> bool:
        """Get log search queries flag with caching"""
        return config_service.get_bool('CHATBOT_LOG_QUERIES', False, cls.FAST_CACHE_TTL)
    
    @classmethod
    def invalidate_cache(cls):
        """Invalidate all cached configuration"""
        config_service.invalidate_cache()
    
    @classmethod
    def enable_hot_reload(cls):
        """Enable hot-reloading in development"""
        config_service.enable_hot_reload(30)  # Check every 30 seconds
    
    @classmethod
    def get_cache_stats(cls) -> Dict[str, Any]:
        """Get configuration cache statistics"""
        return config_service.get_cache_stats()


# For development environments, enable hot-reload
# NOTE: Disabled by default since this service is unused
# if os.environ.get('DJANGO_DEBUG', 'False').lower() == 'true':
#     OptimizedChatbotConfig.enable_hot_reload()
#     logger.info("Configuration hot-reload enabled for development")
