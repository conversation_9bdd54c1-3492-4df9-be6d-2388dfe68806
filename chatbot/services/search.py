"""
Optimized Chatbot Search Views
============================

High-performance search implementation with async/await patterns,
optimized caching, and connection pooling.
"""

import logging
import json
import re
import hashlib
import asyncio
from dataclasses import dataclass, asdict
from typing import Dict, List, Any, Tuple, Optional, Set

from django.utils import timezone
from django.db import connections
from django.core.cache import cache
from asgiref.sync import sync_to_async, async_to_sync

from ..config import ChatbotConfig
from .lightweight_permissions import UnifiedChatbotPermissionSystem
from ..monitoring import search_performance

logger = logging.getLogger(__name__)


@dataclass
class SearchQuery:
    """Structured search query with optimization hints"""
    query_text: str
    normalized_query: str
    query_tokens: List[str]
    entity_types: List[str]
    filters: Dict[str, Any]
    limit: int
    offset: int
    include_fuzzy: bool
    min_score: float


class SearchIndexManager:
    """Manages search indexes for MySQL"""
    
    INDEX_CREATION_SQL = {
        'customers_fulltext': """
            ALTER TABLE customers_customer ADD FULLTEXT INDEX idx_customers_fulltext (customer_name, customer_no, phone, primary_email)
        """,
        'prospects_fulltext': """
            ALTER TABLE leads_prospects ADD FULLTEXT INDEX idx_prospects_fulltext (name, phone, email, city)
        """,
        'leadfiles_fulltext': """
            ALTER TABLE sales_leadfile ADD FULLTEXT INDEX idx_leadfiles_fulltext (lead_file_no, customer_name, plot_no)
        """,
        'users_fulltext': """
            ALTER TABLE users_user ADD FULLTEXT INDEX idx_users_fulltext (fullnames, employee_no, email)
        """
    }
    
    @classmethod
    def ensure_indexes_exist(cls):
        """Create MySQL FULLTEXT indexes if they don't exist"""
        try:
            with connections['default'].cursor() as cursor:
                for index_name, sql in cls.INDEX_CREATION_SQL.items():
                    try:
                        table_name = sql.split('ADD FULLTEXT INDEX')[0].split('ALTER TABLE')[1].strip()
                        cursor.execute(f"SHOW INDEX FROM {table_name} WHERE Key_name = '{index_name}'")
                        if cursor.fetchone():
                            logger.info(f"FULLTEXT index '{index_name}' already exists.")
                        else:
                            cursor.execute(sql)
                            logger.info(f"FULLTEXT index '{index_name}' created.")
                    except Exception as e:
                        if hasattr(e, 'args') and e.args and e.args[0] == 1061:
                            logger.info(f"FULLTEXT index '{index_name}' already exists (caught duplicate error).")
                        else:
                            logger.warning(f"Could not create index '{index_name}': {str(e)}")
        except Exception as e:
            logger.error(f"Failed to ensure search indexes for MySQL: {str(e)}")


class OptimizedCacheManager:
    """
    Simplified single-level caching with cache-aside pattern
    Consolidates previous multi-level approach for better performance
    """
    
    CACHE_PREFIX = 'chatbot_search_'
    CACHE_TIMEOUT = 900  # 15 minutes - optimal balance
    
    @classmethod
    def get_cache_key(cls, query: SearchQuery, user_id: str) -> str:
        """Generate optimized cache key using hash for performance"""
        key_data = f"{query.normalized_query}_{user_id}_{sorted(query.entity_types)}_{query.limit}_{query.offset}"
        key_hash = hashlib.sha256(key_data.encode()).hexdigest()[:24]
        return f"{cls.CACHE_PREFIX}{key_hash}"
    
    @classmethod
    async def get_cached_results(cls, query: SearchQuery, user_id: str) -> Optional[Dict]:
        """Async cache-aside pattern: check cache first"""
        cache_key = cls.get_cache_key(query, user_id)
        
        try:
            cached_data = await sync_to_async(cache.get)(cache_key)
            if cached_data:
                cached_data['query_info']['cache_hit'] = True
                cached_data['query_info']['cached_at'] = cached_data.get('cached_at')
                logger.debug(f"Cache HIT for query: {query.normalized_query}")
                return cached_data
        except Exception as e:
            logger.error(f"Cache read error: {str(e)}")
        
        return None
    
    @classmethod
    async def cache_results(cls, query: SearchQuery, user_id: str, results: Dict):
        """Async cache-aside pattern: store results after computation"""
        cache_key = cls.get_cache_key(query, user_id)
        
        # Add cache metadata
        results['cached_at'] = timezone.now().isoformat()
        results['query_info']['cache_hit'] = False
        
        try:
            await sync_to_async(cache.set)(cache_key, results, cls.CACHE_TIMEOUT)
            logger.debug(f"Cached results for query: {query.normalized_query}")
        except Exception as e:
            logger.error(f"Cache write error: {str(e)}")
    
    @classmethod
    def invalidate_pattern(cls, pattern: str):
        """Invalidate cache entries matching pattern"""
        try:
            # This would require Redis for pattern-based invalidation
            # For now, we'll rely on TTL-based expiration
            logger.info(f"Cache invalidation pattern: {pattern}")
        except Exception as e:
            logger.error(f"Cache invalidation error: {str(e)}")


class ChatbotSearchService:
    """
    Optimized search service with async/await and simplified caching
    """
    
    def __init__(self):
        self.cache_timeout = ChatbotConfig.CACHE_TIMEOUT
        self.logger = logging.getLogger(__name__)
        self.cache_manager = OptimizedCacheManager()
        
        # Ensure database indexes exist for optimal performance
        SearchIndexManager.ensure_indexes_exist()
    
    def _normalize_query(self, query: str) -> Tuple[str, List[str]]:
        """Normalize and tokenize search query"""
        normalized = re.sub(r'[^\w\s@.-]', ' ', query.lower().strip())
        normalized = re.sub(r'\s+', ' ', normalized)
        
        tokens = [w.strip() for w in normalized.split() if len(w.strip()) >= 2]
        
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        emails = re.findall(email_pattern, query, re.IGNORECASE)
        tokens.extend(emails)
        
        phone_pattern = r'\b[\d\+\-\(\)\s]{7,15}\b'
        phones = re.findall(phone_pattern, query)
        tokens.extend([re.sub(r'[\+\-\(\)\s]', '', p) for p in phones])
        
        emp_patterns = [r'OL[\/\-]?HR[\/\-]?\d+', r'OL[\/\-]?\d+']
        for pattern in emp_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            tokens.extend([m.upper().replace('-', '/') for m in matches])
        
        return normalized, list(set(tokens))
    
    def _build_search_query(self, query: str, entity_types: List[str], limit: int, 
                           user, filter_params: Optional[Dict] = None, **kwargs) -> SearchQuery:
        """Build structured search query with permission filters"""
        normalized_query, tokens = self._normalize_query(query)
        
        # Get department filters (legacy system)
        dept_filters = UnifiedChatbotPermissionSystem.get_department_filter(user)
        
        # Merge with new permission filter params
        if filter_params:
            dept_filters.update(filter_params)
        
        return SearchQuery(
            query_text=query,
            normalized_query=normalized_query,
            query_tokens=tokens,
            entity_types=entity_types,
            filters=dept_filters,
            limit=min(limit, ChatbotConfig.MAX_SEARCH_RESULTS),
            offset=kwargs.get('offset', 0),
            include_fuzzy=kwargs.get('include_fuzzy', True),
            min_score=kwargs.get('min_score', 0.1)
        )
    
    @search_performance
    async def _execute_optimized_query_async(self, entity_type: str, search_query: SearchQuery, 
                                            connection_name: str = 'default') -> List[Dict]:
        """Execute optimized MySQL FULLTEXT query asynchronously"""
        query_builders = {
            'customers': self._build_customer_query,
            'prospects': self._build_prospect_query, 
            'leadfiles': self._build_leadfile_query,
            'users': self._build_user_query,
            'plots': self._build_plot_query,
            'projects': self._build_project_query
        }
        
        if entity_type not in query_builders:
            return []
        
        sql, params = query_builders[entity_type](search_query)
        
        try:
            # Execute database query in async manner
            def execute_query():
                with connections[connection_name].cursor() as cursor:
                    cursor.execute(sql, params)
                    columns = [col[0] for col in cursor.description]
                    return [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            results = await sync_to_async(execute_query)()
            return results
        
        except Exception as e:
            self.logger.error(f"MySQL query execution failed for {entity_type}: {str(e)}")
            return []
    
    def _get_boolean_query(self, search_query: SearchQuery) -> str:
        """Creates a boolean mode query string from tokens."""
        if not search_query.query_tokens:
            return ""
        return ' '.join(f'+{token}*' for token in search_query.query_tokens)
    
    def _build_customer_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized customer search query for MySQL FULLTEXT search"""
        boolean_query = self._get_boolean_query(search_query)

        base_sql = """
            SELECT 
                c.customer_no, c.customer_name, c.phone, c.primary_email,
                c.alternative_phone, c.customer_type, c.lead_source_id,
                c.marketer_id, u.fullnames as marketer_name,
                u.employee_no as marketer_employee_no, t.team as marketer_team,
                ls.name as lead_source,
                MATCH(c.customer_name, c.customer_no, c.phone, c.primary_email) AGAINST(%s IN BOOLEAN MODE) as score
            FROM customers_customer c
            LEFT JOIN users_user u ON c.marketer_id = u.employee_no
            LEFT JOIN users_teams t ON u.team_id = t.team
            LEFT JOIN leads_leadsource ls ON c.lead_source_id = ls.leadsource_id
            WHERE 
                MATCH(c.customer_name, c.customer_no, c.phone, c.primary_email) AGAINST(%s IN BOOLEAN MODE) > 0
        """
        params = [boolean_query, boolean_query]

        if search_query.filters.get('marketer_filter'):
            base_sql += " AND u.employee_no = %s"
            params.append(search_query.filters['marketer_id'])
        
        if search_query.filters.get('office_filter'):
            base_sql += " AND u.office = %s"
            params.append(search_query.filters['office'])
        
        base_sql += " ORDER BY score DESC, c.customer_name LIMIT %s OFFSET %s"
        params.extend([search_query.limit, search_query.offset])
        
        return base_sql, params
    
    def _build_prospect_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized prospect search query for MySQL"""
        boolean_query = self._get_boolean_query(search_query)
        base_sql = """
        SELECT 
            p.name, p.phone, p.email, p.city, p.country,
            p.marketer_id, u.fullnames as marketer_name,
            ls.name as lead_source_name,
                MATCH(p.name, p.phone, p.email, p.city) AGAINST(%s IN BOOLEAN MODE) as score
        FROM leads_prospects p
        LEFT JOIN users_user u ON p.marketer_id = u.employee_no
        LEFT JOIN leads_leadsource ls ON p.lead_source_id = ls.leadsource_id
            WHERE
                MATCH(p.name, p.phone, p.email, p.city) AGAINST(%s IN BOOLEAN MODE) > 0
            ORDER BY score DESC, p.name
        LIMIT %s OFFSET %s
        """
        params = [boolean_query, boolean_query, search_query.limit, search_query.offset]
        return base_sql, params
    
    def _build_leadfile_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized leadfile search query for MySQL - includes both active and dropped lead files"""
        boolean_query = self._get_boolean_query(search_query)
        base_sql = """
    SELECT 
        lf.lead_file_no, lf.customer_name, lf.purchase_type,
        lf.purchase_price, lf.selling_price, lf.balance_lcy,
        lf.marketer_id, u.fullnames as marketer_name,
        lf.plot_no, pr.name as project_name,
        lf.lead_file_status_dropped,
        CASE 
            WHEN lf.lead_file_status_dropped = 0 THEN 'Active'
            WHEN lf.lead_file_status_dropped = 1 THEN 'Dropped'
            ELSE 'Unknown'
        END as status,
            MATCH(lf.lead_file_no, lf.customer_name, lf.plot_no) AGAINST(%s IN BOOLEAN MODE) as score
    FROM sales_leadfile lf
    LEFT JOIN users_user u ON lf.marketer_id = u.employee_no
    LEFT JOIN inventory_project pr ON lf.project_id = pr.projectId
        WHERE
            MATCH(lf.lead_file_no, lf.customer_name, lf.plot_no) AGAINST(%s IN BOOLEAN MODE) > 0
        ORDER BY lf.lead_file_status_dropped ASC, score DESC, lf.lead_file_no
    LIMIT %s OFFSET %s
    """
        params = [boolean_query, boolean_query, search_query.limit, search_query.offset]
        return base_sql, params
    
    def _build_user_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized user search query for MySQL"""
        boolean_query = self._get_boolean_query(search_query)
        base_sql = """
        SELECT 
            u.employee_no, u.fullnames, u.email, u.phone_number,
            u.office, u.status, u.is_marketer,
            t.team as team_name, t.tl_name as team_leader,
                MATCH(u.fullnames, u.employee_no, u.email) AGAINST(%s IN BOOLEAN MODE) as score
        FROM users_user u
        LEFT JOIN users_teams t ON u.team_id = t.team
            WHERE u.is_active = true AND
                MATCH(u.fullnames, u.employee_no, u.email) AGAINST(%s IN BOOLEAN MODE) > 0
            ORDER BY score DESC, u.fullnames 
        LIMIT %s OFFSET %s
        """
        params = [boolean_query, boolean_query, search_query.limit, search_query.offset]
        return base_sql, params
    
    def _build_plot_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized plot search query"""
        like_query = f"%{search_query.normalized_query}%"
        
        base_sql = """
        SELECT 
            p.plotId, p.plot_no, p.plot_size, p.plot_type, p.plot_status, 
            p.location, p.cash_price, p.threshold_price, p.lr_no, p.view,
            proj.name as project_name, proj.initials as project_initials,
            proj.tier as project_tier, proj.priority as project_priority,
            CASE 
                WHEN p.plot_no LIKE %s THEN 3.0
                WHEN p.location LIKE %s THEN 2.0
                WHEN proj.name LIKE %s THEN 1.5
                ELSE 1.0
            END as score
        FROM inventory_plot p
        JOIN inventory_project proj ON p.project_id = proj.projectId
        WHERE (
            p.plot_no LIKE %s OR 
            p.location LIKE %s OR 
            proj.name LIKE %s OR
            proj.initials LIKE %s OR
            p.plot_type LIKE %s
        )
        ORDER BY score DESC, p.plot_no ASC
        LIMIT %s OFFSET %s
        """
        
        params = [
            like_query, like_query, like_query,  # for score calculation
            like_query, like_query, like_query, like_query, like_query,  # for WHERE clause
            search_query.limit, search_query.offset
        ]
        return base_sql, params
    
    def _build_project_query(self, search_query: SearchQuery) -> Tuple[str, List]:
        """Build optimized project search query"""
        like_query = f"%{search_query.normalized_query}%"
        
        base_sql = """
        SELECT 
            projectId, name, description, initials, link, priority, tier, 
            visibiliy, bank, account_no, website_link,
            (SELECT COUNT(*) FROM inventory_plot WHERE project_id = projectId AND plot_status = 'Open') as open_plots,
            (SELECT COUNT(*) FROM inventory_plot WHERE project_id = projectId AND plot_status = 'Sold') as sold_plots,
            (SELECT COUNT(*) FROM inventory_plot WHERE project_id = projectId AND plot_status = 'Reserved') as reserved_plots,
            (SELECT COUNT(*) FROM inventory_plot WHERE project_id = projectId) as total_plots,
            CASE 
                WHEN name LIKE %s THEN 3.0
                WHEN initials LIKE %s THEN 2.5
                WHEN description LIKE %s THEN 1.5
                ELSE 1.0
            END as score
        FROM inventory_project
        WHERE (
            name LIKE %s OR 
            initials LIKE %s OR 
            description LIKE %s OR
            tier LIKE %s
        ) AND visibiliy = 'SHOW'
        ORDER BY score DESC, priority ASC, name ASC
        LIMIT %s OFFSET %s
        """
        
        params = [
            like_query, like_query, like_query,  # for score calculation
            like_query, like_query, like_query, like_query,  # for WHERE clause
            search_query.limit, search_query.offset
        ]
        return base_sql, params
    
    async def search_async(self, query: str, user, entity_types: List[str] = None, 
                          limit: int = None, filter_params: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """Optimized async search with concurrent queries and caching"""
        start_time = timezone.now()
        
        if not entity_types:
            entity_types = ['customers', 'prospects', 'leadfiles', 'users', 'plots', 'projects']
        
        if limit is None:
            limit = ChatbotConfig.DEFAULT_SEARCH_LIMIT
        
        search_query = self._build_search_query(query, entity_types, limit, user, filter_params, **kwargs)
        
        # Check cache first (cache-aside pattern)
        cached_results = await self.cache_manager.get_cached_results(search_query, str(user.id))
        if cached_results:
            return cached_results
        
        # Skip permission checks - authenticated users have full search access
        # All logged-in users can search seamlessly without barriers
        
        # Execute all queries concurrently using asyncio.gather
        tasks = [
            self._execute_optimized_query_async(entity_type, search_query)
            for entity_type in entity_types
        ]
        
        try:
            # Run all database queries concurrently
            raw_results_list = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Process results
            results = {}
            total_results = 0
            
            for i, raw_results in enumerate(raw_results_list):
                entity_type = entity_types[i]
                if isinstance(raw_results, Exception):
                    self.logger.error(f"Search failed for {entity_type}: {str(raw_results)}")
                    results[entity_type] = []
                else:
                    results[entity_type] = raw_results
                    total_results += len(raw_results)
            
        except Exception as e:
            self.logger.error(f"Concurrent search execution failed: {str(e)}")
            results = {entity_type: [] for entity_type in entity_types}
            total_results = 0
        
        duration = (timezone.now() - start_time).total_seconds()
        
        response = {
            'results': results,
            'query_info': {
                'original_query': query,
                'normalized_query': search_query.normalized_query,
                'query_tokens': search_query.query_tokens,
                'entity_types': entity_types,
                'total_results': total_results,
                'search_duration_seconds': round(duration, 3),
                'cache_hit': False,
                'bypass_mode': True,  # Always bypass for authenticated users
                'optimization_level': 'seamless_authenticated_access'
            },
            'metadata': {
                'search_performed_at': timezone.now().isoformat(),
                'search_performed_by': user.employee_no if hasattr(user, 'employee_no') else 'unknown',
                'permissions_bypassed': True  # Seamless access for authenticated users
            }
        }
        
        # Cache results asynchronously
        await self.cache_manager.cache_results(search_query, str(user.id), response)
        self._log_search_analytics(user, search_query, response, duration)
        
        return response
    
    @search_performance
    def search(self, query: str, user, entity_types: List[str] = None, 
               limit: int = None, filter_params: Optional[Dict] = None, **kwargs) -> Dict[str, Any]:
        """Synchronous wrapper for backward compatibility"""
        return async_to_sync(self.search_async)(query, user, entity_types, limit, filter_params, **kwargs)
    
    def _log_search_analytics(self, user, search_query: SearchQuery, 
                             response: Dict, duration: float):
        """Log comprehensive search analytics"""
        if not ChatbotConfig.LOG_SEARCH_QUERIES:
            return
        
        analytics_data = {
            'timestamp': timezone.now().isoformat(),
            'user_id': user.employee_no if hasattr(user, 'employee_no') else 'unknown',
            'search_query': asdict(search_query),
            'results_summary': {
                'total_count': response['query_info']['total_results'],
                'duration_seconds': duration
            },
            'cache_hit': response['query_info']['cache_hit']
        }
        
        self.logger.info(f"Search Analytics: {json.dumps(analytics_data)}")
    
    def unified_search(self, query: str, user, search_type: str = 'all', limit: int = None) -> Dict:
        """Legacy unified search method for backward compatibility"""
        entity_types = []
        
        if search_type == 'all':
            entity_types = ['customers', 'prospects', 'leadfiles', 'users']
        else:
            entity_types = [search_type] if search_type in ['customers', 'prospects', 'leadfiles', 'users'] else []
        
        return self.search(query, user, entity_types, limit) 