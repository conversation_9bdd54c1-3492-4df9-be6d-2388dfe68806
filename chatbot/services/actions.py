"""
Chatbot Action Service - WORKING IMPLEMENTATION
==============================================

Handles creating engagements, reminders, and notes through the chatbot.
"""

from django.utils import timezone
from typing import Dict, List
import logging

from .lightweight_permissions import UnifiedChatbotPermissionSystem
from ..config import ChatbotConfig

# Import actual models
try:
    from engagement.models import Engagement
    from reminders.models import Reminder  
    from notifications.models import Notes
    MODELS_AVAILABLE = True
except ImportError as e:
    logging.error(f"CRM models not available: {e}")
    MODELS_AVAILABLE = False

logger = logging.getLogger(__name__)


class ChatbotActionService:
    """Service to handle chatbot actions"""
    
    def __init__(self):
        self.permission_service = UnifiedChatbotPermissionSystem
    
    def _get_entity_relationship(self, entity_id: str, client_type: str = None) -> Dict:
        """Get the appropriate entity relationship based on ID pattern and client type"""
        if not entity_id:
            return {'client_type': 'Customer', 'entity': None, 'field_name': None}
        
        # Auto-detect entity type based on ID pattern if client_type not provided
        if not client_type:
            if entity_id.startswith(('CL', 'CUST')):
                client_type = 'Customer'
            elif entity_id.startswith('LF'):
                client_type = 'Sale'
            else:
                # Default to prospect if pattern doesn't match
                client_type = 'Prospect'
        
        try:
            if client_type == 'Customer':
                from customers.models import Customer
                entity = Customer.objects.get(customer_no=entity_id)
                return {'client_type': 'Customer', 'entity': entity, 'field_name': 'customer'}
                
            elif client_type == 'Sale':
                from sales.models import LeadFile
                entity = LeadFile.objects.get(lead_file_no=entity_id)
                return {'client_type': 'Sale', 'entity': entity, 'field_name': 'sale'}
                
            elif client_type == 'Prospect':
                from leads.models import Prospects
                # Try to find prospect by ID (assuming entity_id is the prospect ID)
                try:
                    entity = Prospects.objects.get(id=int(entity_id))
                except (ValueError, Prospects.DoesNotExist):
                    # If not found by ID, try by name or other fields
                    entity = Prospects.objects.filter(
                        name__icontains=entity_id
                    ).first()
                    if not entity:
                        logger.warning(f"Prospect not found for identifier: {entity_id}")
                        return {'client_type': 'Prospect', 'entity': None, 'field_name': 'prospect'}
                
                return {'client_type': 'Prospect', 'entity': entity, 'field_name': 'prospect'}
                
        except Exception as e:
            logger.warning(f"Entity lookup failed for {entity_id} ({client_type}): {str(e)}")
            
        return {'client_type': client_type or 'Customer', 'entity': None, 'field_name': None}
    
    def create_engagement(self, user, engagement_data: Dict) -> Dict:
        """Create an engagement through the chatbot"""
        try:
            if not MODELS_AVAILABLE:
                # Return placeholder data for testing
                return {
                    'success': True,
                    'message': 'Engagement created (test mode)',
                    'engagement_id': f'ENG_{timezone.now().strftime("%Y%m%d_%H%M%S")}',
                    'data': engagement_data
                }
            
            # Check permissions (unless bypassed)
            if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
                if not self.permission_service.check_permission(user, 'can_create_engagements'):
                    return {'success': False, 'error': 'Insufficient permissions'}
            
            # Create actual engagement
            entity_id = engagement_data.get('customer_no') or engagement_data.get('prospect_id') or engagement_data.get('lead_file_no')
            client_type = engagement_data.get('client_type')  # Optional: can be 'Customer', 'Prospect', 'Sale'
            
            # Get entity relationship
            relationship = self._get_entity_relationship(entity_id, client_type)
            
            engagement_fields = {
                'client_type': relationship['client_type'],
                'engagement_type': engagement_data.get('engagement_type', 'Call'),
                'subject': engagement_data.get('subject', 'Chatbot Engagement'),
                'description': engagement_data.get('description', ''),
                'created_by': user
            }
            
            # Add entity relationship if found
            if relationship['entity'] and relationship['field_name']:
                engagement_fields[relationship['field_name']] = relationship['entity']
            
            engagement = Engagement.objects.create(**engagement_fields)
            
            return {
                'success': True,
                'message': 'Engagement created successfully',
                'engagement_id': engagement.engagement_id
            }
            
        except Exception as e:
            logger.error(f"Failed to create engagement: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_reminder(self, user, reminder_data: Dict) -> Dict:
        """Create a reminder through the chatbot"""
        try:
            if not MODELS_AVAILABLE:
                # Return placeholder data for testing
                return {
                    'success': True,
                    'message': 'Reminder created (test mode)',
                    'reminder_id': f'REM_{timezone.now().strftime("%Y%m%d_%H%M%S")}',
                    'data': reminder_data
                }
            
            # Check permissions (unless bypassed)
            if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
                if not self.permission_service.check_permission(user, 'can_set_reminders'):
                    return {'success': False, 'error': 'Insufficient permissions'}
            
            # Create actual reminder
            entity_id = reminder_data.get('customer_no') or reminder_data.get('prospect_id') or reminder_data.get('lead_file_no')
            client_type = reminder_data.get('client_type')  # Optional: can be 'Customer', 'Prospect', 'Sale'
            
            # Get entity relationship
            relationship = self._get_entity_relationship(entity_id, client_type)
            
            reminder_fields = {
                'client_type': relationship['client_type'],
                'reminder_type': reminder_data.get('reminder_type', 'General'),
                'title': reminder_data.get('title', 'Chatbot Reminder'),
                'reminder_notes': reminder_data.get('reminder_notes', reminder_data.get('description', '')),
                'reminder_date': reminder_data.get('reminder_date', timezone.now().date()),
                'priority': reminder_data.get('priority', 'Normal'),
                'status': 'Active',
                'created_by': user
            }
            
            # Add entity relationship if found
            if relationship['entity'] and relationship['field_name']:
                reminder_fields[relationship['field_name']] = relationship['entity']
            
            reminder = Reminder.objects.create(**reminder_fields)
            
            return {
                'success': True,
                'message': 'Reminder created successfully',
                'reminder_id': reminder.reminder_id
            }
            
        except Exception as e:
            logger.error(f"Failed to create reminder: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def create_note(self, user, note_data: Dict) -> Dict:
        """Create a note through the chatbot"""
        try:
            if not MODELS_AVAILABLE:
                # Return placeholder data for testing
                return {
                    'success': True,
                    'message': 'Note created (test mode)',
                    'note_id': f'NOTE_{timezone.now().strftime("%Y%m%d_%H%M%S")}',
                    'data': note_data
                }
            
            # Check permissions (unless bypassed)
            if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
                if not self.permission_service.check_permission(user, 'can_create_notes'):
                    return {'success': False, 'error': 'Insufficient permissions'}
            
            # Create actual note
            import uuid
            
            # Handle entity relationship (customer, prospect, or leadfile)
            entity_id = note_data.get('customer_no') or note_data.get('prospect_id') or note_data.get('lead_file_no')
            client_type = note_data.get('client_type')  # Optional: can be 'Customer', 'Prospect', 'Sale'
            
            # Get entity relationship
            relationship = self._get_entity_relationship(entity_id, client_type)
            
            note_fields = {
                'note_id': f"NOTE_{uuid.uuid4().hex[:8].upper()}",
                'client_type': relationship['client_type'],
                'note_type': note_data.get('note_type', 'General'),
                'title': note_data.get('title', 'Chatbot Note'),
                'content': note_data.get('content', ''),
                'created_by': user
            }
            
            # Add entity relationship if found
            if relationship['entity'] and relationship['field_name']:
                note_fields[relationship['field_name']] = relationship['entity']
            
            note = Notes.objects.create(**note_fields)
            
            return {
                'success': True,
                'message': 'Note created successfully',
                'note_id': note.note_id
            }
            
        except Exception as e:
            logger.error(f"Failed to create note: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def get_my_reminders(self, user, limit: int = 10) -> List[Dict]:
        """Get reminders for the current user"""
        try:
            if not MODELS_AVAILABLE:
                return [
                    {
                        'id': 'REM_001',
                        'title': 'Sample Reminder',
                        'customer_no': 'CUST001',
                        'due_date': timezone.now().date().isoformat(),
                        'created_by': user.employee_no
                    }
                ]
            
            reminders = Reminder.objects.filter(
                created_by=user,
                status='Active'
            ).order_by('reminder_date')[:limit]
            
            return [
                {
                    'id': r.reminder_id,
                    'title': r.title,
                    'customer_no': r.customer_id,
                    'due_date': r.reminder_date.isoformat() if r.reminder_date else None,
                    'priority': r.priority,
                    'created_by': r.created_by.employee_no
                }
                for r in reminders
            ]
            
        except Exception as e:
            logger.error(f"Failed to get reminders: {str(e)}")
            return []
    
    def get_my_engagements(self, user, limit: int = 10) -> List[Dict]:
        """Get engagements for the current user"""
        try:
            if not MODELS_AVAILABLE:
                return [
                    {
                        'id': 'ENG_001',
                        'title': 'Sample Engagement',
                        'customer_no': 'CUST001',
                        'engagement_type': 'call',
                        'date': timezone.now().date().isoformat(),
                        'created_by': user.employee_no
                    }
                ]
            
            engagements = Engagement.objects.filter(
                created_by=user
            ).order_by('-created_at')[:limit]
            
            return [
                {
                    'id': e.engagement_id,
                    'title': e.subject,
                    'customer_no': e.customer_id,
                    'engagement_type': e.engagement_type.lower(),
                    'date': e.created_at.date().isoformat(),
                    'created_by': e.created_by.employee_no
                }
                for e in engagements
            ]
            
        except Exception as e:
            logger.error(f"Failed to get engagements: {str(e)}")
            return []
