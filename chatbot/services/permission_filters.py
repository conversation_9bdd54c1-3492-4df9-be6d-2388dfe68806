"""
Permission Filter Service
=========================

Provides filtering infrastructure for permission-based access control.
Filters search results based on user's team, office, and marketer assignments.

When BYPASS_ALL_PERMISSIONS is True, all filters are skipped.
When False, filters are applied based on user permissions.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from django.core.cache import cache
from django.utils import timezone
from django.db.models import Q, QuerySet

from ..config import ChatbotConfig
from users.models import User

logger = logging.getLogger(__name__)


class PermissionFilterService:
    """
    Service for generating and applying permission-based filters to database queries.
    """
    
    # Cache configuration
    USER_CONTEXT_CACHE_KEY = "chatbot_user_context_{}"
    USER_CONTEXT_CACHE_TTL = 1800  # 30 minutes
    
    @classmethod
    def generate_filters_from_permissions(
        cls, 
        user: User, 
        permissions_list: List[str]
    ) -> Dict[str, Any]:
        """
        Generate filter parameters based on user permissions.
        Uses permission ID system (same as other apps).
        
        Args:
            user: Django User object
            permissions_list: List of permission names (e.g., ['can_view_own_customers'])
            
        Returns:
            Dictionary of filter parameters
        """
        from config.permissions_utils import has_user_custom_permission
        
        # Convert permissions list to set for faster lookup
        permissions_set = set(permissions_list)
        
        filters = {
            'user_id': user.employee_no,
            'restrict_to_team': False,
            'restrict_to_office': False,
            'restrict_to_marketer': False,
            'team_id': None,
            'office': None,
            'marketer_employee_no': None,
            'permissions': permissions_list,
            'generated_at': timezone.now().isoformat()
        }
        
        # Check permission IDs directly (same as other apps)
        # Sales/Lead Files permissions (1000s)
        sales_permission_ids = ["1001", "1002", "1003", "1004", "1006", "1007", "1008", "1011"]
        user_sales_perm = next((perm_id for perm_id in sales_permission_ids if has_user_custom_permission(user, perm_id)), None)
        
        # Customers permissions (2000s)
        customer_permission_ids = ["2001", "2002", "2003", "2004", "2006", "2007", "2008", "2011"]
        user_customer_perm = next((perm_id for perm_id in customer_permission_ids if has_user_custom_permission(user, perm_id)), None)
        
        # Prospects permissions (3000s)
        prospect_permission_ids = ["3001", "3002", "3003", "3004", "3006", "3007", "3008", "3011"]
        user_prospect_perm = next((perm_id for perm_id in prospect_permission_ids if has_user_custom_permission(user, perm_id)), None)
        
        # Apply filters based on permission IDs (same logic as config/perm_filters.py)
        
        # Sales/Lead Files filters
        if user_sales_perm == "1004":  # VIEW_SALES_OWN_MARKETER - own records only
            filters['restrict_to_marketer'] = True
            filters['marketer_employee_no'] = user.employee_no
        elif user_sales_perm == "1001":  # VIEW_SALES_HQ - HQ office only
            filters['restrict_to_office'] = True
            filters['office'] = 'HQ'
        elif user_sales_perm == "1002":  # VIEW_SALES_KAREN - KAREN office only
            filters['restrict_to_office'] = True
            filters['office'] = 'KAREN'
        elif user_sales_perm == "1003":  # VIEW_SALES_ALL_OFFICES - all records (no filter)
            pass  # No restriction
        elif user_sales_perm == "1007":  # Digital team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        elif user_sales_perm == "1008":  # Telemarketing team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        
        # Customers filters
        if user_customer_perm == "2004":  # VIEW_CUSTOMER_OWN_MARKETER - own records only
            filters['restrict_to_marketer'] = True
            filters['marketer_employee_no'] = user.employee_no
        elif user_customer_perm == "2001":  # VIEW_CUSTOMER_HQ - HQ office only
            filters['restrict_to_office'] = True
            filters['office'] = 'HQ'
        elif user_customer_perm == "2002":  # VIEW_CUSTOMER_KAREN - KAREN office only
            filters['restrict_to_office'] = True
            filters['office'] = 'KAREN'
        elif user_customer_perm == "2003":  # VIEW_CUSTOMER_ALL_OFFICES - all records (no filter)
            pass  # No restriction
        elif user_customer_perm == "2007":  # Digital team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        elif user_customer_perm == "2008":  # Telemarketing team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        
        # Prospects filters
        if user_prospect_perm == "3004":  # VIEW_PROSPECT_OWN_MARKETER - own records only
            filters['restrict_to_marketer'] = True
            filters['marketer_employee_no'] = user.employee_no
        elif user_prospect_perm == "3001":  # VIEW_PROSPECT_HQ - HQ office only
            filters['restrict_to_office'] = True
            filters['office'] = 'HQ'
        elif user_prospect_perm == "3002":  # VIEW_PROSPECT_KAREN - KAREN office only
            filters['restrict_to_office'] = True
            filters['office'] = 'KAREN'
        elif user_prospect_perm == "3003":  # VIEW_PROSPECT_ALL_OFFICES - all records (no filter)
            pass  # No restriction
        elif user_prospect_perm == "3007":  # Digital team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        elif user_prospect_perm == "3008":  # Telemarketing team
            if user.team:
                filters['restrict_to_team'] = True
                filters['team_id'] = user.team.id
        
        # Fallback: Check for view-all permissions (for backward compatibility)
        has_view_all_customers = 'can_view_customers' in permissions_set or 'can_view_all_customers' in permissions_set
        has_view_all_prospects = 'can_view_prospects' in permissions_set or 'can_view_all_prospects' in permissions_set
        has_view_all_leadfiles = 'can_view_leadfiles' in permissions_set or 'can_view_all_leadfiles' in permissions_set
        
        # If no permission ID found, use permission names (backward compatibility)
        if not user_sales_perm and not user_customer_perm and not user_prospect_perm:
            # Determine marketer restriction from permission names
            if not has_view_all_customers or not has_view_all_prospects:
                # User can only view their own records
                if 'can_view_own_customers' in permissions_set or 'can_view_own_prospects' in permissions_set:
                    filters['restrict_to_marketer'] = True
                    filters['marketer_employee_no'] = user.employee_no
        
        logger.info(f"Generated filters for user {user.employee_no} using permission IDs: {json.dumps(filters, default=str)}")
        return filters
    
    @classmethod
    def apply_customer_filters(
        cls, 
        queryset: QuerySet, 
        filter_params: Dict[str, Any]
    ) -> QuerySet:
        """
        Apply permission filters to Customer queryset.
        
        Args:
            queryset: Customer QuerySet to filter
            filter_params: Filter parameters dictionary
            
        Returns:
            Filtered QuerySet
        """
        if not filter_params or cls.should_skip_filters():
            return queryset
        
        q_filter = Q()
        
        if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            q_filter &= Q(marketer__employee_no=filter_params['marketer_employee_no'])
        
        if filter_params.get('restrict_to_team') and filter_params.get('team_id'):
            q_filter &= Q(marketer__team_id=filter_params['team_id'])
        
        if filter_params.get('restrict_to_office') and filter_params.get('office'):
            q_filter &= Q(marketer__office=filter_params['office'])
        
        if q_filter:
            queryset = queryset.filter(q_filter)
            logger.debug(f"Applied customer filters: {q_filter}")
        
        return queryset
    
    @classmethod
    def apply_prospect_filters(
        cls, 
        queryset: QuerySet, 
        filter_params: Dict[str, Any]
    ) -> QuerySet:
        """
        Apply permission filters to Prospects queryset.
        
        Args:
            queryset: Prospects QuerySet to filter
            filter_params: Filter parameters dictionary
            
        Returns:
            Filtered QuerySet
        """
        if not filter_params or cls.should_skip_filters():
            return queryset
        
        q_filter = Q()
        
        if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            q_filter &= Q(marketer__employee_no=filter_params['marketer_employee_no'])
        
        if filter_params.get('restrict_to_team') and filter_params.get('team_id'):
            q_filter &= Q(marketer__team_id=filter_params['team_id'])
        
        if filter_params.get('restrict_to_office') and filter_params.get('office'):
            q_filter &= Q(marketer__office=filter_params['office'])
        
        if q_filter:
            queryset = queryset.filter(q_filter)
            logger.debug(f"Applied prospect filters: {q_filter}")
        
        return queryset
    
    @classmethod
    def apply_leadfile_filters(
        cls, 
        queryset: QuerySet, 
        filter_params: Dict[str, Any]
    ) -> QuerySet:
        """
        Apply permission filters to LeadFile queryset.
        
        Args:
            queryset: LeadFile QuerySet to filter
            filter_params: Filter parameters dictionary
            
        Returns:
            Filtered QuerySet
        """
        if not filter_params or cls.should_skip_filters():
            return queryset
        
        q_filter = Q()
        
        if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            q_filter &= Q(marketer__employee_no=filter_params['marketer_employee_no'])
        
        if filter_params.get('restrict_to_team') and filter_params.get('team_id'):
            q_filter &= Q(marketer__team_id=filter_params['team_id'])
        
        if filter_params.get('restrict_to_office') and filter_params.get('office'):
            q_filter &= Q(marketer__office=filter_params['office'])
        
        if q_filter:
            queryset = queryset.filter(q_filter)
            logger.debug(f"Applied leadfile filters: {q_filter}")
        
        return queryset
    
    @classmethod
    def apply_plot_filters(
        cls, 
        queryset: QuerySet, 
        filter_params: Dict[str, Any]
    ) -> QuerySet:
        """
        Apply permission filters to Plot queryset.
        Filters plots by customer ownership based on marketer access.
        
        Args:
            queryset: Plot QuerySet to filter
            filter_params: Filter parameters dictionary
            
        Returns:
            Filtered QuerySet
        """
        if not filter_params or cls.should_skip_filters():
            return queryset
        
        q_filter = Q()
        
        # Filter plots by customer's marketer
        if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            q_filter &= Q(lead_file__marketer__employee_no=filter_params['marketer_employee_no'])
        
        if filter_params.get('restrict_to_team') and filter_params.get('team_id'):
            q_filter &= Q(lead_file__marketer__team_id=filter_params['team_id'])
        
        if filter_params.get('restrict_to_office') and filter_params.get('office'):
            q_filter &= Q(lead_file__marketer__office=filter_params['office'])
        
        if q_filter:
            queryset = queryset.filter(q_filter).distinct()
            logger.debug(f"Applied plot filters: {q_filter}")
        
        return queryset
    
    @classmethod
    def should_skip_filters(cls) -> bool:
        """
        Check if filters should be skipped.
        
        IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering.
        Bypass mode only bypasses permission checks, not data filtering.
        Filters are always applied based on permission IDs to ensure users only see
        what they have permissions for.
        
        Returns:
            False (always apply filters based on permission IDs)
        """
        # Always apply filters based on permission IDs (even in bypass mode)
        # This ensures users only see what they have permissions for
        return False
    
    @classmethod
    def can_access_customer(
        cls, 
        user: User, 
        customer_no: str, 
        filter_params: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Check if user can access a specific customer.
        
        Args:
            user: Django User object
            customer_no: Customer number to check
            filter_params: Optional filter parameters (generated if not provided)
            
        Returns:
            True if user can access the customer, False otherwise
        """
        # Always allow in bypass mode
        if cls.should_skip_filters():
            return True
        
        try:
            from customers.models import Customer
            
            # Get the customer
            customer = Customer.objects.select_related('marketer', 'marketer__team').get(
                customer_no=customer_no
            )
            
            # Generate filters if not provided
            if not filter_params:
                # Get user permissions
                from .lightweight_permissions import UnifiedChatbotPermissionSystem
                user_perms = UnifiedChatbotPermissionSystem.get_user_permissions(user)
                permission_names = [perm for perm, has_it in user_perms.items() if has_it]
                filter_params = cls.generate_filters_from_permissions(user, permission_names)
            
            # Check marketer restriction
            if filter_params.get('restrict_to_marketer'):
                if not customer.marketer or customer.marketer.employee_no != filter_params.get('marketer_employee_no'):
                    logger.info(f"Access denied: User {user.employee_no} cannot access customer {customer_no} (marketer restriction)")
                    return False
            
            # Check team restriction
            if filter_params.get('restrict_to_team'):
                if not customer.marketer or not customer.marketer.team or \
                   customer.marketer.team.id != filter_params.get('team_id'):
                    logger.info(f"Access denied: User {user.employee_no} cannot access customer {customer_no} (team restriction)")
                    return False
            
            # Check office restriction
            if filter_params.get('restrict_to_office'):
                if not customer.marketer or customer.marketer.office != filter_params.get('office'):
                    logger.info(f"Access denied: User {user.employee_no} cannot access customer {customer_no} (office restriction)")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error checking customer access for {customer_no}: {str(e)}")
            return False
    
    @classmethod
    def cache_user_context(
        cls, 
        session_id: str, 
        user_context: Dict[str, Any]
    ) -> None:
        """
        Cache user context for quick retrieval.
        
        Args:
            session_id: Session identifier
            user_context: User context data including filter parameters
        """
        cache_key = cls.USER_CONTEXT_CACHE_KEY.format(session_id)
        cache.set(cache_key, user_context, cls.USER_CONTEXT_CACHE_TTL)
        logger.debug(f"Cached user context for session {session_id}")
    
    @classmethod
    def get_cached_user_context(cls, session_id: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve cached user context.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Cached user context or None if not found
        """
        cache_key = cls.USER_CONTEXT_CACHE_KEY.format(session_id)
        context = cache.get(cache_key)
        
        if context:
            logger.debug(f"Cache HIT for user context session {session_id}")
        else:
            logger.debug(f"Cache MISS for user context session {session_id}")
        
        return context
    
    @classmethod
    def invalidate_user_context_cache(cls, session_id: str) -> None:
        """
        Invalidate cached user context when permissions change.
        
        Args:
            session_id: Session identifier
        """
        cache_key = cls.USER_CONTEXT_CACHE_KEY.format(session_id)
        cache.delete(cache_key)
        logger.info(f"Invalidated user context cache for session {session_id}")


def extract_filter_params_from_request(request) -> Optional[Dict[str, Any]]:
    """
    Extract filter parameters from request.
    Checks cached user context first, then falls back to authenticated user.
    
    IMPORTANT: Even in bypass mode, we extract filters based on permission IDs
    to ensure users only see what they have permissions for. Bypass mode only
    bypasses permission checks, not data filtering.
    
    Args:
        request: Django request object
        
    Returns:
        Filter parameters dictionary or None
    """
    # Always extract filters based on permission IDs (even in bypass mode)
    # This ensures users only see what they have permissions for
    
    # Try to get session_id from request data or query params
    session_id = None
    if hasattr(request, 'data') and isinstance(request.data, dict):
        session_id = request.data.get('session_id') or request.data.get('sessionId')
    
    if not session_id:
        session_id = request.GET.get('session_id') or request.GET.get('sessionId')
    
    # Check cached user context if session_id provided
    if session_id:
        cached_context = PermissionFilterService.get_cached_user_context(session_id)
        if cached_context and 'filter_params' in cached_context:
            logger.debug(f"Using cached filter params for session {session_id}")
            return cached_context['filter_params']
    
    # Fall back to generating filters from authenticated user
    if hasattr(request, 'user') and request.user.is_authenticated:
        from .lightweight_permissions import UnifiedChatbotPermissionSystem
        
        user = request.user
        user_perms = UnifiedChatbotPermissionSystem.get_user_permissions(user)
        permission_names = [perm for perm, has_it in user_perms.items() if has_it]
        
        filter_params = PermissionFilterService.generate_filters_from_permissions(
            user, permission_names
        )
        
        logger.debug(f"Generated filter params from authenticated user {user.employee_no}")
        return filter_params
    
    logger.warning("Could not extract filter parameters - no session or authenticated user")
    return None

