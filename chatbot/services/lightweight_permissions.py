"""
Comprehensive Chatbot Permission System
=======================================

Complete permission system combining:
- UnifiedChatbotPermissionSystem: Core Django database permission queries
- LightweightPermissionService: Ultra-fast caching and N8N integration

Features:
- Enterprise-grade caching (30min sessions)
- Tool-to-permission mapping
- Permission-aware N8N workflow integration
- <50ms permission resolution
- Graceful fallback and error handling

Author: CRM Optimization Team
Version: 2.0 (Combined System)
"""

from django.core.cache import cache
from django.utils import timezone
from django.conf import settings
from django.core.exceptions import ValidationError
from typing import Dict, List, Optional, Set, Tuple, Union, Any
import json
import logging
import time
import hashlib
from dataclasses import dataclass, asdict
from enum import Enum

from ..config import ChatbotConfig
from users.models import (
    User, UserPermissions, TeamsPermissions, UserGroupPermissions,
    User_2_UserPermissions, Teams_2_TeamsPermissions, 
    UserGroup_2_UserGroupPermissions, Teams, UserGroups
)

logger = logging.getLogger(__name__)


class PermissionLevel(Enum):
    """Permission levels for chatbot users"""
    NONE = "none"
    READ_ONLY = "read_only"  
    READ_PLUS = "read_plus"
    STANDARD = "standard"
    FULL = "full"
    ADMIN = "admin"


@dataclass
class ToolPermission:
    """Data class for tool permission mapping"""
    tool_name: str
    required_permissions: List[str]
    category: str
    description: str
    fallback_tools: List[str] = None
    
    def __post_init__(self):
        if self.fallback_tools is None:
            self.fallback_tools = []


@dataclass
class SessionPermissions:
    """Data class for session permission data"""
    user_id: str
    session_id: str
    available_tools: List[str]
    permission_level: PermissionLevel
    restrictions: Dict[str, str]
    tool_categories: Dict[str, List[str]]
    cache_info: Dict[str, Union[str, bool, float]]
    expires_at: str
    bypass_mode: bool = False
    error: Optional[str] = None
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for JSON serialization"""
        result = asdict(self)
        result['permission_level'] = self.permission_level.value
        return result


class UnifiedChatbotPermissionSystem:
    """
    Single, unified permission system for the chatbot.
    Handles all permission checking, caching, and access control.
    Uses the same permission ID system as other apps (1004, 2004, 3004, etc.)
    """
    
    # Cache keys with optimized timeout
    PERMISSIONS_CACHE_KEY = "chatbot_all_permissions"
    USER_PERMS_CACHE_KEY = "chatbot_user_perms_{}"
    USER_CAPABILITIES_CACHE_KEY = "chatbot_capabilities_{}"
    
    # Optimized cache timeouts
    CACHE_TIMEOUT_LONG = 1800   # 30 minutes for user permissions
    CACHE_TIMEOUT_SHORT = 900   # 15 minutes for capabilities
    CACHE_TIMEOUT_SESSION = 3600  # 1 hour for session data
    
    # Permission ID to Chatbot Permission Name Mapping
    # Same permission ID system as other apps (config/perm_filters.py)
    PERMISSION_ID_MAPPING = {
        # Sales/Lead Files Permissions (1000s)
        '1001': 'can_view_leadfiles_hq',  # VIEW_SALES_HQ
        '1002': 'can_view_leadfiles_karen',  # VIEW_SALES_KAREN
        '1003': 'can_view_leadfiles',  # VIEW_SALES_ALL_OFFICES (all records)
        '1004': 'can_view_own_leadfiles',  # VIEW_SALES_OWN_MARKETER (own records)
        '1005': 'can_view_leadfiles',  # VIEW_SALES_ALL_MARKETERS (all records)
        '1006': 'can_view_leadfiles_diaspora',  # Diaspora region
        '1007': 'can_view_leadfiles_digital',  # Digital team
        '1008': 'can_view_leadfiles_telemarketing',  # Telemarketing team
        '1011': 'can_view_leadfiles_manager',  # Manager's lead sources
        
        # Customers Permissions (2000s)
        '2001': 'can_view_customers_hq',  # VIEW_CUSTOMER_HQ
        '2002': 'can_view_customers_karen',  # VIEW_CUSTOMER_KAREN
        '2003': 'can_view_customers',  # VIEW_CUSTOMER_ALL_OFFICES (all records)
        '2004': 'can_view_own_customers',  # VIEW_CUSTOMER_OWN_MARKETER (own records)
        '2005': 'can_view_customers',  # VIEW_CUSTOMER_ALL_MARKETERS (all records)
        '2006': 'can_view_customers_diaspora',  # Diaspora region
        '2007': 'can_view_customers_digital',  # Digital team
        '2008': 'can_view_customers_telemarketing',  # Telemarketing team
        '2011': 'can_view_customers_manager',  # Manager's lead sources
        
        # Prospects Permissions (3000s)
        '3001': 'can_view_prospects_hq',  # VIEW_PROSPECT_HQ
        '3002': 'can_view_prospects_karen',  # VIEW_PROSPECT_KAREN
        '3003': 'can_view_prospects',  # VIEW_PROSPECT_ALL_OFFICES (all records)
        '3004': 'can_view_own_prospects',  # VIEW_PROSPECT_OWN_MARKETER (own records)
        '3005': 'can_view_prospects',  # VIEW_PROSPECT_ALL_MARKETERS (all records)
        '3006': 'can_view_prospects_diaspora',  # Diaspora region
        '3007': 'can_view_prospects_digital',  # Digital team
        '3008': 'can_view_prospects_telemarketing',  # Telemarketing team
        '3011': 'can_view_prospects_manager',  # Manager's lead sources
    }
    
    # Permission IDs for each category (same as config/perm_filters.py)
    SALES_PERMISSION_IDS = ["1001", "1002", "1003", "1004", "1006", "1007", "1008", "1011"]
    CUSTOMER_PERMISSION_IDS = ["2001", "2002", "2003", "2004", "2006", "2007", "2008", "2011"]
    PROSPECT_PERMISSION_IDS = ["3001", "3002", "3003", "3004", "3006", "3007", "3008", "3011"]
    
    # Explicitly blocked permissions for security
    ALWAYS_BLOCKED_PERMISSIONS = {
        'can_delete_customers',
        'can_delete_prospects', 
        'can_delete_leadfiles',
        'can_delete_users',
        'can_admin_system',
        'can_manage_finances',
        'can_approve_payments'
    }
    
    # Allowed permissions for chatbot use
    CHATBOT_ALLOWED_PERMISSIONS = {
        # Read-only permissions
        'can_view_users',
        'can_view_all_users', 
        'can_view_customers',
        'can_view_own_customers',
        'can_view_prospects',
        'can_view_own_prospects',
        'can_view_leadfiles',
        'can_view_own_leadfiles',
        'can_view_services',
        'can_view_reports',
        
        # Minimal interaction permissions
        'can_create_notes',
        'can_create_engagements',
        'can_set_reminders',
        'can_add_engagement',
        'can_add_reminder',
        'can_export_data',
        'can_export_reports'
    }
    
    @classmethod
    def _check_category_permissions(cls, user: User, perm_ids: List[str], 
                                     own_perm_id: str, all_perm_id: str, 
                                     all_marketers_perm_id: str,
                                     own_perm_name: str, all_perm_name: str) -> Dict[str, bool]:
        """
        Helper method to check permissions for a category (sales/customers/prospects).
        Reduces code duplication.
        
        Args:
            user: Django User object
            perm_ids: List of permission IDs to check for this category
            own_perm_id: Permission ID for "own only" access (e.g., '1004', '2004', '3004')
            all_perm_id: Permission ID for "all offices" access (e.g., '1003', '2003', '3003')
            all_marketers_perm_id: Permission ID for "all marketers" access (e.g., '1005', '2005', '3005')
            own_perm_name: Chatbot permission name for "own only" (e.g., 'can_view_own_leadfiles')
            all_perm_name: Chatbot permission name for "all" (e.g., 'can_view_leadfiles')
            
        Returns:
            Dictionary of permissions found for this category
        """
        from config.permissions_utils import has_user_custom_permission
        
        permissions = {}
        
        for perm_id in perm_ids:
            if has_user_custom_permission(user, perm_id):
                chatbot_perm = cls.PERMISSION_ID_MAPPING.get(perm_id)
                if chatbot_perm:
                    permissions[chatbot_perm] = True
                    
                    # Set generic permissions based on access level
                    if perm_id == own_perm_id:
                        permissions[own_perm_name] = True
                    elif perm_id == all_perm_id or perm_id == all_marketers_perm_id:
                        permissions[all_perm_name] = True
                        permissions[own_perm_name] = True
                
                break  # User should only have one permission per category
        
        return permissions
    
    @classmethod
    def get_user_permissions_from_ids(cls, user: User) -> Dict[str, bool]:
        """
        Get user permissions by checking permission IDs (same as other apps).
        Maps permission IDs to chatbot permission names.
        
        Uses has_user_custom_permission from config.permissions_utils (same as other apps).
        
        Args:
            user: Django User object
            
        Returns:
            Dictionary of chatbot permission names to boolean values
        """
        permissions = {}
        
        # Check sales/leadfiles permissions (1000s)
        sales_perms = cls._check_category_permissions(
            user, cls.SALES_PERMISSION_IDS,
            '1004', '1003', '1005',
            'can_view_own_leadfiles', 'can_view_leadfiles'
        )
        permissions.update(sales_perms)
        
        # Check customers permissions (2000s)
        customer_perms = cls._check_category_permissions(
            user, cls.CUSTOMER_PERMISSION_IDS,
            '2004', '2003', '2005',
            'can_view_own_customers', 'can_view_customers'
        )
        permissions.update(customer_perms)
        
        # Check prospects permissions (3000s)
        prospect_perms = cls._check_category_permissions(
            user, cls.PROSPECT_PERMISSION_IDS,
            '3004', '3003', '3005',
            'can_view_own_prospects', 'can_view_prospects'
        )
        permissions.update(prospect_perms)
        
        # Set default False for all chatbot permissions
        for perm_name in cls.CHATBOT_ALLOWED_PERMISSIONS:
            if perm_name not in permissions:
                permissions[perm_name] = False
        
        return permissions
    
    @classmethod
    def get_user_permissions(cls, user: User) -> Dict[str, bool]:
        """
        Get user permissions using permission ID system (same as other apps).
        Returns boolean dictionary of permissions.
        
        Uses has_user_custom_permission to check permission IDs (1004, 2004, 3004, etc.)
        and maps them to chatbot permission names.
        
        IMPORTANT: Even in bypass mode, we respect permission IDs to ensure users only
        see what they have permissions for. Bypass mode only bypasses permission checks,
        not the actual data filtering.
        """
        # Use permission ID system (same as other apps)
        # CRITICAL: Always check permission IDs, even in bypass mode
        # Bypass mode only bypasses permission checks, not data filtering
        cache_key = cls.USER_PERMS_CACHE_KEY.format(user.employee_no)
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        # Get permissions from permission IDs (always check, even in bypass mode)
        permissions = cls.get_user_permissions_from_ids(user)
        
        # If bypass mode is enabled, grant additional permissions but keep data filtering
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.info(f"BYPASS MODE: Granting additional permissions to {user.employee_no}, but respecting permission IDs for data filtering")
            # Grant additional permissions for actions, but keep view permissions based on IDs
            permissions.update({
                'can_create_notes': True,
                'can_create_engagements': True,
                'can_set_reminders': True,
                'can_view_reports': True,
                'can_export_data': True,
                'can_add_engagement': True,
                'can_add_reminder': True,
                'can_view_services': True,
            })
            # Keep view permissions based on actual permission IDs (for data filtering)
            # Don't override can_view_own_* or can_view_* permissions - they come from IDs
        
        # Cache for 30 minutes
        cache.set(cache_key, permissions, cls.CACHE_TIMEOUT_LONG)
        
        logger.debug(f"Resolved permissions for {user.employee_no} using permission IDs: {list(permissions.keys())}")
        return permissions
    
    @classmethod
    def get_user_permissions_comprehensive(cls, user: User) -> Dict[str, Any]:
        """
        Get comprehensive permissions for a user from all sources.
        Returns detailed permission breakdown with sources.
        """
        # BYPASS ALL PERMISSIONS - RETURN FULL ACCESS
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            return cls._get_unrestricted_permissions(user)
        
        cache_key = cls.USER_PERMS_CACHE_KEY.format(user.employee_no)
        cached_result = cache.get(cache_key)
        
        if cached_result:
            return cached_result
        
        start_time = timezone.now()
        
        try:
            result = {
                'user_info': {
                    'employee_no': user.employee_no,
                    'fullnames': user.fullnames,
                    'office': user.office,
                    'department': user.department.dp_name if user.department else None,
                    'team': user.team.team if user.team else None,
                    'group': user.group.name if user.group else None,
                    'is_marketer': user.is_marketer
                },
                'permissions': {
                    'user_level': [],
                    'team_level': [],
                    'group_level': [],
                    'auto_granted': [],
                    'blocked': []
                },
                'effective_permissions': {},
                'permission_summary': {
                    'total_permissions': 0,
                    'can_view_customers': False,
                    'can_view_prospects': False,
                    'can_view_leadfiles': False,
                    'can_create_notes': False,
                    'can_export_data': False
                }
            }
        
            # Get user-level permissions
            user_perms = User_2_UserPermissions.objects.filter(
                user=user,
                permission__permission_name__in=cls.CHATBOT_ALLOWED_PERMISSIONS
            ).exclude(
                permission__permission_name__in=cls.ALWAYS_BLOCKED_PERMISSIONS
            ).select_related('permission').values_list('permission__permission_name', flat=True)
            result['permissions']['user_level'] = list(user_perms)
            
            # Get team-level permissions (if user has team)
            if user.team:
                try:
                    team_perms = Teams_2_TeamsPermissions.objects.filter(
                        team=user.team,
                        permission__permission_name__in=cls.CHATBOT_ALLOWED_PERMISSIONS
                    ).exclude(
                        permission__permission_name__in=cls.ALWAYS_BLOCKED_PERMISSIONS
                    ).select_related('permission').values_list('permission__permission_name', flat=True)
                    result['permissions']['team_level'] = list(team_perms)
                except Exception as team_error:
                    logger.warning(f"Team permissions query failed for {user.employee_no}: {str(team_error)}")
                    result['permissions']['team_level'] = []
            
            # Get group-level permissions (if user has group)
            if user.group:
                group_perms = UserGroup_2_UserGroupPermissions.objects.filter(
                    group=user.group,
                    permission__permission_name__in=cls.CHATBOT_ALLOWED_PERMISSIONS
                ).exclude(
                    permission__permission_name__in=cls.ALWAYS_BLOCKED_PERMISSIONS
                ).select_related('permission').values_list('permission__permission_name', flat=True)
                result['permissions']['group_level'] = list(group_perms)
            
            # Auto-granted permissions for marketers
            if user.is_marketer:
                auto_granted = [
                    'can_view_own_customers',
                    'can_view_own_prospects', 
                    'can_view_own_leadfiles',
                    'can_create_notes',
                    'can_create_engagements',
                    'can_set_reminders',
                    'can_view_reports'
                ]
                result['permissions']['auto_granted'] = auto_granted
            
            # Calculate effective permissions (allowed - blocked)
            all_granted = set(
                result['permissions']['user_level'] +
                result['permissions']['team_level'] +
                result['permissions']['group_level'] +
                result['permissions']['auto_granted']
            )
            
            # Remove blocked permissions
            effective = all_granted - cls.ALWAYS_BLOCKED_PERMISSIONS
            
            # Convert to boolean dictionary
            result['effective_permissions'] = {perm: True for perm in effective}
            
            # Calculate summary
            result['permission_summary'].update({
                'total_permissions': len(effective),
                'can_view_customers': any(p in effective for p in ['can_view_customers', 'can_view_own_customers']),
                'can_view_prospects': any(p in effective for p in ['can_view_prospects', 'can_view_own_prospects']),
                'can_view_leadfiles': any(p in effective for p in ['can_view_leadfiles', 'can_view_own_leadfiles']),
                'can_create_notes': 'can_create_notes' in effective,
                'can_export_data': any(p in effective for p in ['can_export_data', 'can_export_reports'])
            })
            
            # Cache for 5 minutes
            cache.set(cache_key, result, 300)
            
            duration = (timezone.now() - start_time).total_seconds()
            logger.info(f"Permission resolution for {user.employee_no}: {duration:.3f}s, {len(effective)} permissions")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive permissions for {user.employee_no}: {str(e)}")
            return cls._get_minimal_permissions(user)
    
    @classmethod
    def can_access_module_cached(cls, user: User, module: str) -> Union[bool, Dict[str, Any]]:
        """
        Optimized module access check with caching
        Avoids repeated permission database queries
        """
        # BYPASS ALL PERMISSIONS - RETURN TRUE
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.debug(f"BYPASS MODE: Granting module access to {user.employee_no} for {module}")
            return True
        
        # Check capabilities cache first
        cache_key = cls.USER_CAPABILITIES_CACHE_KEY.format(f"{user.employee_no}_{module}")
        cached_result = cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Module access cache HIT for {user.employee_no}:{module}")
            return cached_result
        
        # Cache miss - compute access
        permissions = cls.get_user_permissions_cached(user)
        result = cls._check_module_access(permissions, module)
        
        # Cache result for 15 minutes
        cache.set(cache_key, result, cls.CACHE_TIMEOUT_SHORT)
        logger.debug(f"Module access cache MISS for {user.employee_no}:{module}")
        
        return result
    
    @classmethod
    def _check_module_access(cls, permissions: Dict[str, bool], module: str) -> bool:
        """Internal method to check module access from permissions dict"""
        module_permissions_map = {
            'search': ['can_view_customers', 'can_view_prospects', 'can_view_leadfiles'],
            'export': ['can_export_data'],
            'create': ['can_create_notes', 'can_create_engagements', 'can_set_reminders'],
            'reports': ['can_view_reports']
        }
        
        required_perms = module_permissions_map.get(module, [])
        if not required_perms:
            return True  # No specific permissions required
        
        return any(permissions.get(perm, False) for perm in required_perms)
    
    @classmethod 
    def get_user_permissions_cached(cls, user: User) -> Dict[str, bool]:
        """
        Get user permissions with aggressive caching.
        Uses 30-minute cache to avoid repeated database queries.
        
        Note: This method is now redundant as get_user_permissions already handles caching.
        Kept for backward compatibility.
        """
        # get_user_permissions already handles caching, so just call it
        return cls.get_user_permissions(user)

    @classmethod
    def can_access_module(cls, user: User, module: str) -> Union[bool, Dict[str, Any]]:
        """
        Legacy method - now uses cached version for performance
        """
        return cls.can_access_module_cached(user, module)
    
    @classmethod
    def get_module_access_detailed(cls, user: User, module_name: str) -> Dict[str, Any]:
        """
        Get detailed module access information.
        Returns comprehensive access breakdown.
        """
        # BYPASS ALL PERMISSIONS - RETURN FULL ACCESS
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.debug(f"BYPASS MODE: Granting full module access to {user.employee_no} for {module_name}")
            return {
                'module': module_name,
                'user': user.employee_no,
                'access': {
                    'view': True,
                    'create': True,
                    'edit': True,
                    'delete': True,
                    'export': True
                },
                'allowed_actions': ['view', 'create', 'edit', 'delete', 'export'],
                'blocked_actions': [],
                'bypass_mode': True
            }
        
        user_perms = cls.get_user_permissions_comprehensive(user)
        effective_perms = user_perms['effective_permissions']
        
        # Define module access rules
        module_rules = {
            'customers': {
                'view': ['can_view_customers', 'can_view_own_customers'],
                'create': ['can_add_customers'],
                'edit': ['can_change_customers'],
                'delete': ['can_delete_customers'],
                'export': ['can_export_data']
            },
            'prospects': {
                'view': ['can_view_prospects', 'can_view_own_prospects'],
                'create': ['can_add_prospects'],
                'edit': ['can_change_prospects'],
                'delete': ['can_delete_prospects'],
                'export': ['can_export_data']
            },
            'leadfiles': {
                'view': ['can_view_leadfiles', 'can_view_own_leadfiles'],
                'create': ['can_add_leadfiles'],
                'edit': ['can_change_leadfiles'],
                'delete': ['can_delete_leadfiles'],
                'export': ['can_export_data']
            },
            'reports': {
                'view': ['can_view_reports'],
                'export': ['can_export_data', 'can_export_reports']
            },
            'notes': {
                'view': ['can_view_notes'],
                'create': ['can_create_notes']
            },
            'engagements': {
                'view': ['can_view_engagements'],
                'create': ['can_create_engagements']
            }
        }
        
        rules = module_rules.get(module_name, {})
        access_result = {
            'module': module_name,
            'user': user.employee_no,
            'access': {},
            'allowed_actions': [],
            'blocked_actions': []
        }
        
        for action, required_perms in rules.items():
            has_access = any(perm in effective_perms for perm in required_perms)
            access_result['access'][action] = has_access
            
            if has_access:
                access_result['allowed_actions'].append(action)
            else:
                access_result['blocked_actions'].append(action)
        
        return access_result
    
    @classmethod
    def get_department_filter(cls, user: User) -> Dict[str, Any]:
        """Get department-specific filters for queries"""
        # BYPASS ALL PERMISSIONS - RETURN NO FILTERS
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.debug(f"BYPASS MODE: No department filters for {user.employee_no}")
            return {
                'department_filter': False,
                'office_filter': False,
                'marketer_filter': False,
                'department_id': None,
                'office': None,
                'marketer_id': None,
                'team_id': None,
                'organization_team': None,
                'bypass_mode': True
            }
        
        filters = {
            'department_filter': False,
            'office_filter': False,
            'marketer_filter': False,
            'department_id': None,
            'office': None,
            'marketer_id': None,
            'team_id': None,
            'organization_team': None
        }
        
        try:
            # Department filtering
            if user.department:
                filters.update({
                    'department_filter': True,
                    'department_id': user.department.id
                })
            
            # Office filtering
            if user.office:
                filters.update({
                    'office_filter': True,
                    'office': user.office
                })
            
            # Team filtering
            if user.team:
                filters.update({
                    'team_id': user.team.id,
                    'organization_team': user.team.team
                })
            
            # Marketer filtering
            if user.is_marketer:
                filters.update({
                    'marketer_filter': True,
                    'marketer_id': user.employee_no
                })
            
            logger.info(f"Generated filters for {user.employee_no}: {json.dumps(filters)}")
            
        except Exception as e:
            logger.error(f"Error generating filters for {user.employee_no}: {str(e)}")
            
        return filters
    
    @classmethod
    def check_permission(cls, user: User, permission_name: str) -> bool:
        """
        Quick permission check for a specific permission.
        Returns True if user has the permission, False otherwise.
        
        IMPORTANT: Even in bypass mode, we respect permission IDs for view permissions
        to ensure users only see what they have permissions for. Bypass mode only bypasses
        action permission checks (create, edit, etc.), not view permissions.
        """
        try:
            permissions = cls.get_user_permissions(user)
            
            # For view permissions, always check actual permission IDs (even in bypass mode)
            view_permissions = {
                'can_view_customers', 'can_view_own_customers',
                'can_view_prospects', 'can_view_own_prospects',
                'can_view_leadfiles', 'can_view_own_leadfiles',
                'can_view_users', 'can_view_all_users'
            }
            
            if permission_name in view_permissions:
                # Always respect permission IDs for view permissions (even in bypass mode)
                return permissions.get(permission_name, False)
            else:
                # For action permissions, bypass mode can grant access
                if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
                    logger.debug(f"BYPASS MODE: Granting {permission_name} to {user.employee_no}")
                    return True
                return permissions.get(permission_name, False)
        except Exception as e:
            logger.error(f"Permission check failed for {user.employee_no}/{permission_name}: {str(e)}")
            return False
    
    @classmethod
    def get_all_available_permissions(cls) -> Dict[str, Any]:
        """
        Get all available permissions from the database.
        Returns categorized permissions for better organization.
        """
        cache_key = cls.PERMISSIONS_CACHE_KEY
        cached_permissions = cache.get(cache_key)
        
        if cached_permissions:
            return cached_permissions
        
        try:
            # Get all permission types
            user_permissions = list(UserPermissions.objects.values_list('permission_name', flat=True))
            team_permissions = list(TeamsPermissions.objects.values_list('permission_name', flat=True))
            group_permissions = list(UserGroupPermissions.objects.values_list('permission_name', flat=True))
            
            # Categorize permissions by module
            all_permissions = set(user_permissions + team_permissions + group_permissions)
            
            categorized = {
                'customers': [p for p in all_permissions if 'customer' in p.lower()],
                'prospects': [p for p in all_permissions if 'prospect' in p.lower()],
                'leadfiles': [p for p in all_permissions if 'leadfile' in p.lower() or 'lead_file' in p.lower()],
                'sales': [p for p in all_permissions if 'sale' in p.lower()],
                'users': [p for p in all_permissions if 'user' in p.lower() and 'customer' not in p.lower()],
                'reports': [p for p in all_permissions if 'report' in p.lower()],
                'export': [p for p in all_permissions if 'export' in p.lower()],
                'notes': [p for p in all_permissions if 'note' in p.lower()],
                'engagements': [p for p in all_permissions if 'engagement' in p.lower()],
                'reminders': [p for p in all_permissions if 'reminder' in p.lower()],
                'admin': [p for p in all_permissions if 'admin' in p.lower() or 'manage' in p.lower()],
                'other': []
            }
            
            # Add uncategorized permissions to 'other'
            categorized_perms = set()
            for perms in categorized.values():
                categorized_perms.update(perms)
            
            categorized['other'] = list(all_permissions - categorized_perms)
            
            result = {
                'permissions_by_category': categorized,
                'total_permissions': len(all_permissions),
                'generated_at': timezone.now().isoformat(),
                'chatbot_allowed': list(cls.CHATBOT_ALLOWED_PERMISSIONS),
                'always_blocked': list(cls.ALWAYS_BLOCKED_PERMISSIONS),
                'permission_counts': {
                    'user_permissions': len(user_permissions),
                    'team_permissions': len(team_permissions), 
                    'group_permissions': len(group_permissions)
                }
            }
            
            # Cache for 1 hour
            cache.set(cache_key, result, 3600)
            
            logger.info(f"Loaded {len(all_permissions)} permissions across {len(categorized)} categories")
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to load permissions: {str(e)}")
            return {'permissions_by_category': {}, 'total_permissions': 0}
    
    @classmethod
    def invalidate_user_cache(cls, user: User) -> None:
        """Invalidate cached permissions for a user"""
        cache_key = cls.USER_PERMS_CACHE_KEY.format(user.employee_no)
        cache.delete(cache_key)
        logger.info(f"Invalidated permission cache for {user.employee_no}")
    
    @classmethod
    def refresh_all_permissions(cls) -> Dict[str, Any]:
        """Refresh the global permissions cache"""
        cache.delete(cls.PERMISSIONS_CACHE_KEY)
        permissions = cls.get_all_available_permissions()
        
        return {
            'status': 'success',
            'message': 'Permissions cache refreshed',
            'total_permissions': permissions['total_permissions'],
            'categories': list(permissions['permissions_by_category'].keys())
        }
    
    @classmethod
    def log_access_denied(cls, user: User, module: str, reason: str = None):
        """Log access denied events with detailed information"""
        # BYPASS ALL PERMISSIONS - DON'T LOG ACCESS DENIED
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.debug(f"BYPASS MODE: Access granted to {user.employee_no} for {module}")
            return
        
        log_data = {
            'timestamp': timezone.now().isoformat(),
            'event': 'access_denied',
            'user_id': user.employee_no,
            'user_name': user.fullnames,
            'module': module,
            'is_marketer': user.is_marketer,
            'office': user.office,
            'department': user.department.dp_name if user.department else None,
            'team': user.team.team if user.team else None,
            'reason': reason or 'Insufficient permissions'
        }
        
        logger.warning(f"Access Denied: {json.dumps(log_data)}") 
    
    @classmethod
    def _get_unrestricted_permissions(cls, user: User) -> Dict[str, Any]:
        """Return unrestricted permissions for bypass mode"""
        logger.info(f"BYPASS MODE: Granting unrestricted access to {user.employee_no}")
        
        return {
            'user_info': {
                'employee_no': user.employee_no,
                'fullnames': user.fullnames,
                'office': user.office,
                'department': user.department.dp_name if hasattr(user, 'department') and user.department else None,
                'team': user.team.team if hasattr(user, 'team') and user.team else None,
                'group': user.group.name if hasattr(user, 'group') and user.group else None,
                'is_marketer': user.is_marketer
            },
            'permissions': {
                'user_level': ['unrestricted_access'],
                'team_level': ['unrestricted_access'],
                'group_level': ['unrestricted_access'],
                'auto_granted': ['unrestricted_access'],
                'blocked': []
            },
            'effective_permissions': {
                'can_view_customers': True,
                'can_view_own_customers': True,
                'can_view_prospects': True,
                'can_view_own_prospects': True,
                'can_view_leadfiles': True,
                'can_view_own_leadfiles': True,
                'can_view_users': True,
                'can_view_all_users': True,
                'can_create_notes': True,
                'can_create_engagements': True,
                'can_set_reminders': True,
                'can_view_reports': True,
                'can_export_data': True,
                'can_export_reports': True,
                'can_add_customers': True,
                'can_change_customers': True,
                'can_add_prospects': True,
                'can_change_prospects': True,
                'can_add_leadfiles': True,
                'can_change_leadfiles': True,
                'can_view_services': True,
                'can_add_engagement': True,
                'can_add_reminder': True,
                'unrestricted_access': True
            },
            'permission_summary': {
                'total_permissions': 999,
                'can_view_customers': True,
                'can_view_prospects': True,
                'can_view_leadfiles': True,
                'can_create_notes': True,
                'can_export_data': True,
                'bypass_mode': True
            }
        }
    
    @classmethod
    def _get_minimal_permissions(cls, user: User) -> Dict[str, Any]:
        """Fallback minimal permissions in case of errors"""
        return {
            'user_info': {
                'employee_no': user.employee_no,
                'fullnames': user.fullnames,
                'is_marketer': user.is_marketer
            },
            'permissions': {
                'user_level': [],
                'team_level': [],
                'group_level': [],
                'auto_granted': ['can_view_own_customers'] if user.is_marketer else [],
                'blocked': []
            },
            'effective_permissions': {
                'can_view_own_customers': user.is_marketer
            },
            'permission_summary': {
                'total_permissions': 1 if user.is_marketer else 0,
                'can_view_customers': user.is_marketer,
                'can_view_prospects': False,
                'can_view_leadfiles': False,
                'can_create_notes': False,
                'can_export_data': False
            }
        }


class LightweightPermissionService:
    """
    Enterprise-grade permission service for chatbot with:
    - Aggressive caching (30min sessions)
    - Graceful degradation
    - Performance monitoring
    - Security logging
    - Type safety
    """
    
    # Configuration constants
    SESSION_CACHE_TTL = 1800  # 30 minutes
    FALLBACK_CACHE_TTL = 300  # 5 minutes for error conditions
    CACHE_KEY_PREFIX = "chatbot_session_perms"
    
    # Tool permission mappings with fallbacks
    TOOL_PERMISSIONS = {
        # Search tools (lightweight permissions)
        "customer_search": ToolPermission(
            tool_name="customer_search",
            required_permissions=["can_view_customers", "can_view_own_customers"],
            category="search",
            description="Search for customer records",
            fallback_tools=["prospect_search"]
        ),
        "prospect_search": ToolPermission(
            tool_name="prospect_search", 
            required_permissions=["can_view_prospects", "can_view_own_prospects"],
            category="search",
            description="Search for prospect records",
            fallback_tools=["lead_file_search"]
        ),
        "lead_file_search": ToolPermission(
            tool_name="lead_file_search",
            required_permissions=["can_view_leadfiles", "can_view_own_leadfiles"],
            category="search",
            description="Search for lead file records",
            fallback_tools=["customer_search"]
        ),
        
        # Data access tools
        "customer_sales_data": ToolPermission(
            tool_name="customer_sales_data",
            required_permissions=["can_view_customers", "can_view_own_customers"],
            category="data",
            description="Access customer sales information",
            fallback_tools=["customer_360_view"]
        ),
        "customer_title_status": ToolPermission(
            tool_name="customer_title_status",
            required_permissions=["can_view_customers", "can_view_own_customers"],
            category="data", 
            description="Access customer title status",
            fallback_tools=["customer_360_view"]
        ),
        "customer_360_view": ToolPermission(
            tool_name="customer_360_view",
            required_permissions=["can_view_customers", "can_view_own_customers"],
            category="data",
            description="Comprehensive customer overview",
            fallback_tools=["customer_search"]
        ),
        
        # Action tools (require specific permissions)
        "create_engagement": ToolPermission(
            tool_name="create_engagement",
            required_permissions=["can_create_engagements", "can_add_engagement"],
            category="action",
            description="Create customer engagement records"
        ),
        "create_reminder": ToolPermission(
            tool_name="create_reminder",
            required_permissions=["can_set_reminders", "can_add_reminder"],
            category="action", 
            description="Set reminders for follow-ups"
        ),
        "create_note": ToolPermission(
            tool_name="create_note",
            required_permissions=["can_create_notes"],
            category="action",
            description="Add notes to customer records"
        ),
        
        # Export tools (require export permissions)
        "export_sales_csv": ToolPermission(
            tool_name="export_sales_csv",
            required_permissions=["can_export_data", "can_view_reports"],
            category="export",
            description="Export sales data to CSV"
        ),
        "export_customers_csv": ToolPermission(
            tool_name="export_customers_csv", 
            required_permissions=["can_export_data", "can_view_customers"],
            category="export",
            description="Export customer data to CSV"
        ),
        "export_mib_csv": ToolPermission(
            tool_name="export_mib_csv",
            required_permissions=["can_export_data", "can_view_reports"],
            category="export",
            description="Export MIB data to CSV"
        ),
        "export_prospects_csv": ToolPermission(
            tool_name="export_prospects_csv",
            required_permissions=["can_export_data", "can_view_prospects"],
            category="export",
            description="Export prospects data to CSV"
        ),
        
        # Report tools
        "generate_sales_report": ToolPermission(
            tool_name="generate_sales_report",
            required_permissions=["can_view_reports", "can_export_reports"],
            category="report",
            description="Generate comprehensive sales reports"
        )
    }
    
    @classmethod
    def get_session_permissions(
        cls, 
        user, 
        session_id: Optional[str] = None,
        force_refresh: bool = False
    ) -> SessionPermissions:
        """
        Get comprehensive session permissions with aggressive caching.
        
        Args:
            user: Django user object
            session_id: Optional session identifier  
            force_refresh: Force cache refresh
            
        Returns:
            SessionPermissions object with all permission data
            
        Raises:
            ValidationError: If user validation fails
        """
        start_time = time.time()
        
        try:
            # Validate inputs
            user_id = cls._validate_and_extract_user_id(user)
            session_id = session_id or "default"
            
            # Generate cache key
            cache_key = cls._generate_cache_key(user_id, session_id)
            
            # Try cache first (ultra-fast path)
            if not force_refresh:
                cached_perms = cache.get(cache_key)
                if cached_perms:
                    cached_perms = SessionPermissions(**cached_perms)
                    cached_perms.cache_info['cache_hit'] = True
                    cached_perms.cache_info['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
                    
                    logger.debug(f"Cache hit for user {user_id}: {cached_perms.cache_info['response_time_ms']}ms")
                    return cached_perms
            
            # Cache miss - resolve permissions
            session_perms = cls._resolve_user_permissions(user, user_id, session_id, start_time)
            
            # Cache the result
            cache_ttl = cls.FALLBACK_CACHE_TTL if session_perms.error else cls.SESSION_CACHE_TTL
            cache.set(cache_key, session_perms.to_dict(), cache_ttl)
            
            # Log performance metrics
            cls._log_permission_resolution(user_id, session_perms)
            
            return session_perms
            
        except Exception as e:
            logger.error(f"Permission resolution failed for {getattr(user, 'employee_no', 'unknown')}: {e}")
            return cls._create_emergency_fallback_permissions(user, session_id, start_time, str(e))
    
    @classmethod
    def _validate_and_extract_user_id(cls, user) -> str:
        """Validate user object and extract ID"""
        if not user:
            raise ValidationError("User object is required")
            
        if hasattr(user, 'employee_no'):
            return str(user.employee_no)
        elif hasattr(user, 'id'):
            return str(user.id)
        else:
            raise ValidationError("User object missing required ID fields")
    
    @classmethod
    def _generate_cache_key(cls, user_id: str, session_id: str) -> str:
        """Generate secure, collision-free cache key"""
        key_data = f"{cls.CACHE_KEY_PREFIX}_{user_id}_{session_id}_{ChatbotConfig.BYPASS_ALL_PERMISSIONS}"
        return hashlib.md5(key_data.encode()).hexdigest()[:24]
    
    @classmethod  
    def _resolve_user_permissions(
        cls, 
        user, 
        user_id: str, 
        session_id: str, 
        start_time: float
    ) -> SessionPermissions:
        """Resolve user permissions with proper error handling"""
        
        # BYPASS MODE: Full access (development/testing)
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            return cls._create_bypass_permissions(user_id, session_id, start_time)
        
        # PRODUCTION MODE: Real permission resolution
        try:
            # Get user permissions (uses existing caching)
            user_permissions = UnifiedChatbotPermissionSystem.get_user_permissions(user)
            
            # Calculate available tools
            available_tools, tool_categories = cls._calculate_available_tools(user_permissions)
            
            # Determine permission level
            permission_level = cls._calculate_permission_level(available_tools, user_permissions)
            
            # Get user restrictions
            restrictions = cls._calculate_user_restrictions(user_permissions)
            
            return SessionPermissions(
                user_id=user_id,
                session_id=session_id,
                available_tools=available_tools,
                permission_level=permission_level,
                restrictions=restrictions,
                tool_categories=tool_categories,
                cache_info=cls._create_cache_info(start_time, False),
                expires_at=(timezone.now() + timezone.timedelta(seconds=cls.SESSION_CACHE_TTL)).isoformat(),
                bypass_mode=False
            )
            
        except Exception as e:
            logger.error(f"Permission resolution error for {user_id}: {e}")
            return cls._create_fallback_permissions(user_id, session_id, start_time, str(e))
    
    @classmethod
    def _calculate_available_tools(cls, user_permissions: Dict[str, bool]) -> Tuple[List[str], Dict[str, List[str]]]:
        """Calculate available tools and categorize them"""
        available_tools = []
        tool_categories = {
            "search": [],
            "data": [], 
            "action": [],
            "export": [],
            "report": []
        }
        
        for tool_name, tool_config in cls.TOOL_PERMISSIONS.items():
            # Check if user has any required permission
            has_permission = any(
                user_permissions.get(perm, False) 
                for perm in tool_config.required_permissions
            )
            
            if has_permission:
                available_tools.append(tool_name)
                tool_categories[tool_config.category].append(tool_name)
        
        return available_tools, tool_categories
    
    @classmethod
    def _calculate_permission_level(cls, available_tools: List[str], user_permissions: Dict[str, bool]) -> PermissionLevel:
        """Calculate overall permission level based on available tools"""
        tool_count = len(available_tools)
        
        if tool_count == 0:
            return PermissionLevel.NONE
        
        # Check for admin permissions
        admin_perms = ["can_admin_system", "can_manage_finances"]
        if any(user_permissions.get(perm, False) for perm in admin_perms):
            return PermissionLevel.ADMIN
        
        # Count tools by category
        action_tools = [t for t in available_tools if cls.TOOL_PERMISSIONS[t].category == "action"]
        export_tools = [t for t in available_tools if cls.TOOL_PERMISSIONS[t].category == "export"]
        
        if len(action_tools) >= 2 and len(export_tools) >= 2:
            return PermissionLevel.FULL
        elif len(action_tools) >= 1:
            return PermissionLevel.STANDARD
        elif tool_count >= 5:
            return PermissionLevel.READ_PLUS
        else:
            return PermissionLevel.READ_ONLY
    
    @classmethod
    def _calculate_user_restrictions(cls, user_permissions: Dict[str, bool]) -> Dict[str, str]:
        """Calculate user-specific restrictions"""
        restrictions = {}
        
        # Check for "own only" restrictions
        own_only_checks = [
            ("customers", "can_view_own_customers", "can_view_customers"),
            ("prospects", "can_view_own_prospects", "can_view_prospects"),
            ("leadfiles", "can_view_own_leadfiles", "can_view_leadfiles")
        ]
        
        for resource, own_perm, all_perm in own_only_checks:
            if user_permissions.get(own_perm, False) and not user_permissions.get(all_perm, False):
                restrictions[resource] = "own_only"
        
        return restrictions
    
    @classmethod
    def _create_bypass_permissions(cls, user_id: str, session_id: str, start_time: float) -> SessionPermissions:
        """Create full permissions for bypass mode"""
        all_tools = list(cls.TOOL_PERMISSIONS.keys())
        all_categories = {
            "search": [t for t in all_tools if cls.TOOL_PERMISSIONS[t].category == "search"],
            "data": [t for t in all_tools if cls.TOOL_PERMISSIONS[t].category == "data"],
            "action": [t for t in all_tools if cls.TOOL_PERMISSIONS[t].category == "action"],
            "export": [t for t in all_tools if cls.TOOL_PERMISSIONS[t].category == "export"],
            "report": [t for t in all_tools if cls.TOOL_PERMISSIONS[t].category == "report"]
        }
        
        return SessionPermissions(
            user_id=user_id,
            session_id=session_id,
            available_tools=all_tools,
            permission_level=PermissionLevel.FULL,
            restrictions={},
            tool_categories=all_categories,
            cache_info=cls._create_cache_info(start_time, False, "bypass"),
            expires_at=(timezone.now() + timezone.timedelta(seconds=cls.SESSION_CACHE_TTL)).isoformat(),
            bypass_mode=True
        )
    
    @classmethod
    def _create_fallback_permissions(cls, user_id: str, session_id: str, start_time: float, error: str) -> SessionPermissions:
        """Create minimal permissions for error conditions"""
        basic_tools = ["customer_search", "prospect_search", "lead_file_search"]
        basic_categories = {
            "search": basic_tools,
            "data": [],
            "action": [],
            "export": [],
            "report": []
        }
        
        return SessionPermissions(
            user_id=user_id,
            session_id=session_id,
            available_tools=basic_tools,
            permission_level=PermissionLevel.READ_ONLY,
            restrictions={"mode": "fallback"},
            tool_categories=basic_categories,
            cache_info=cls._create_cache_info(start_time, False, "fallback"),
            expires_at=(timezone.now() + timezone.timedelta(seconds=cls.FALLBACK_CACHE_TTL)).isoformat(),
            bypass_mode=False,
            error=f"Permission resolution failed: {error}"
        )
    
    @classmethod
    def _create_emergency_fallback_permissions(cls, user, session_id: str, start_time: float, error: str) -> SessionPermissions:
        """Emergency fallback for catastrophic failures"""
        user_id = getattr(user, 'employee_no', 'emergency')
        
        return SessionPermissions(
            user_id=user_id,
            session_id=session_id or "emergency",
            available_tools=[],
            permission_level=PermissionLevel.NONE,
            restrictions={"mode": "emergency"},
            tool_categories={"search": [], "data": [], "action": [], "export": [], "report": []},
            cache_info=cls._create_cache_info(start_time, False, "emergency"),
            expires_at=(timezone.now() + timezone.timedelta(seconds=300)).isoformat(),  # 5 minutes
            bypass_mode=False,
            error=f"Emergency fallback: {error}"
        )
    
    @classmethod
    def _create_cache_info(cls, start_time: float, cache_hit: bool = False, mode: str = "normal") -> Dict:
        """Create cache information metadata"""
        return {
            "cache_hit": cache_hit,
            "cached_at": timezone.now().isoformat(),
            "response_time_ms": round((time.time() - start_time) * 1000, 2),
            "mode": mode,
            "cache_ttl": cls.SESSION_CACHE_TTL if mode == "normal" else cls.FALLBACK_CACHE_TTL
        }
    
    @classmethod
    def _log_permission_resolution(cls, user_id: str, session_perms: SessionPermissions):
        """Log permission resolution for monitoring"""
        logger.info(
            f"Permission resolution - User: {user_id}, "
            f"Tools: {len(session_perms.available_tools)}, "
            f"Level: {session_perms.permission_level.value}, "
            f"Time: {session_perms.cache_info['response_time_ms']}ms, "
            f"Mode: {session_perms.cache_info['mode']}"
        )
        
        # Log security events
        if session_perms.bypass_mode:
            logger.warning(f"BYPASS MODE: Full permissions granted to {user_id}")
        
        if session_perms.error:
            logger.error(f"Permission error for {user_id}: {session_perms.error}")
    
    @classmethod
    def check_tool_access(cls, user, tool_name: str, session_id: Optional[str] = None) -> bool:
        """
        Lightning-fast tool access check using session cache.
        
        Args:
            user: Django user object
            tool_name: Name of the tool to check
            session_id: Optional session identifier
            
        Returns:
            bool: True if user can access the tool
        """
        try:
            session_perms = cls.get_session_permissions(user, session_id)
            return tool_name in session_perms.available_tools
        except Exception as e:
            logger.error(f"Tool access check failed for {tool_name}: {e}")
            # Fail securely - deny access on error
            return False
    
    @classmethod
    def get_tool_alternatives(cls, tool_name: str, available_tools: List[str]) -> List[str]:
        """Get alternative tools if primary tool is not available"""
        if tool_name not in cls.TOOL_PERMISSIONS:
            return []
        
        tool_config = cls.TOOL_PERMISSIONS[tool_name]
        return [
            alt_tool for alt_tool in tool_config.fallback_tools 
            if alt_tool in available_tools
        ]
    
    @classmethod
    def clear_user_session_cache(cls, user, session_id: Optional[str] = None):
        """Clear cached permissions for a user session"""
        try:
            user_id = cls._validate_and_extract_user_id(user)
            cache_key = cls._generate_cache_key(user_id, session_id or "default")
            cache.delete(cache_key)
            logger.info(f"Cleared permission cache for user {user_id}")
        except Exception as e:
            logger.error(f"Failed to clear cache: {e}")
    
    @classmethod
    def get_permission_analytics(cls) -> Dict:
        """Get permission system analytics for monitoring"""
        # Get cache keys count (safe for all cache backends)
        cache_keys_active = 0
        try:
            # Try to get cache keys if supported (Redis, Memcached)
            if hasattr(cache, 'keys'):
                cache_keys_active = len(cache.keys(f"{cls.CACHE_KEY_PREFIX}*"))
            else:
                # Fallback for LocMemCache and other backends
                cache_keys_active = 0  # Cannot determine for this cache backend
        except Exception as e:
            logger.warning(f"Could not retrieve cache keys count: {e}")
            cache_keys_active = -1  # Indicates error
        
        return {
            "cache_keys_active": cache_keys_active,
            "total_tools_defined": len(cls.TOOL_PERMISSIONS),
            "permission_levels": [level.value for level in PermissionLevel],
            "cache_ttl_normal": cls.SESSION_CACHE_TTL,
            "cache_ttl_fallback": cls.FALLBACK_CACHE_TTL,
            "cache_backend": cache.__class__.__name__,
            "analytics_timestamp": timezone.now().isoformat()
        }


# Maintain backward compatibility with old class names
ChatbotPermissionService = UnifiedChatbotPermissionSystem
ChatbotPermissionManager = UnifiedChatbotPermissionSystem
