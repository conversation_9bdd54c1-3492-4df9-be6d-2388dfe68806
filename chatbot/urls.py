"""
Chatbot URL Configuration
========================

This module defines all the URL patterns for the chatbot application.
URLs are organized into logical groups for better maintainability.

Structure:
- Utility endpoints (health, test, user info)
- Core functionality (search, unified API router)
- User management (reminders, engagements)
- CSV exports (all report types)
- Session proxy (for embedded chatbot)
"""

from django.urls import path
from . import views
from .views import customer_details, permission_hints, n8n_integration, enhanced_actions, reports

app_name = 'chatbot'

urlpatterns = [
    # ==========================================================================
    # UTILITY ENDPOINTS
    # ==========================================================================
    path('test/', views.simple_test, name='simple_test'),
    path('health/', views.health_check, name='health_check'),
    path('user-info/', views.user_info, name='user_info'),
    path('debug/', views.debug_search, name='debug_search'),
    
    # ==========================================================================
    # CORE FUNCTIONALITY (CONSOLIDATED - NO DUPLICATION)
    # ==========================================================================
    path('search/', views.search_unified, name='search_unified'),
    path('api/', views.chatbot_api_router, name='chatbot_api_router'),
    
    # ==========================================================================
    # ACTION ENDPOINTS (FOR N8N INTEGRATION) - Multi-Entity Support
    # ==========================================================================
    path('actions/engagement/', enhanced_actions.create_engagement_endpoint, name='create_engagement_endpoint'),
    path('actions/reminder/', enhanced_actions.create_reminder_endpoint, name='create_reminder_endpoint'),
    path('actions/note/', enhanced_actions.create_note_endpoint, name='create_note_endpoint'),
    
    # ==========================================================================
    # CUSTOMER DETAILS (SALES & TITLE STATUS)
    # ==========================================================================
    path('customer/<str:customer_no>/sales/', customer_details.customer_sales_data, name='customer_sales_data'),
    path('customer/<str:customer_no>/title-status/', customer_details.customer_title_status, name='customer_title_status'),
    path('customer/<str:customer_no>/360/', customer_details.customer_360_view, name='customer_360_view'),
    
    # ==========================================================================
    # N8N-COMPATIBLE CUSTOMER ENDPOINTS (QUERY PARAMETERS)
    # ==========================================================================
    path('customer-sales-data/', customer_details.customer_sales_data_query, name='customer_sales_data_query'),
    path('customer-title-status/', customer_details.customer_title_status_query, name='customer_title_status_query'),
    path('customer-360-view/', customer_details.customer_360_view_query, name='customer_360_view_query'),
    
    # N8N-COMPATIBLE INVENTORY ENDPOINTS (QUERY PARAMETERS)
    path('customer-plots/', views.get_customer_plots_query, name='get_customer_plots_query'),
    path('plot-details/', views.get_plot_details_query, name='get_plot_details_query'),
    
    # ==========================================================================
    # INVENTORY SEARCH ENDPOINTS
    # ==========================================================================
    path('search/plots/', views.search_plots, name='search_plots'),
    path('search/projects/', views.search_projects, name='search_projects'),
    path('customer/<str:customer_no>/plots/', views.get_customer_plots, name='get_customer_plots'),
    path('plot/<str:plot_no>/details/', views.get_plot_details, name='get_plot_details'),
    
    # ==========================================================================
    # USER MANAGEMENT
    # ==========================================================================
    path('reminders/my/', views.my_reminders, name='my_reminders'),
    path('engagements/my/', views.my_engagements, name='my_engagements'),
    
    # ==========================================================================
    # CSV EXPORTS (8 ENDPOINTS)
    # ==========================================================================
    path('export/sales-csv/', views.export_sales_csv, name='export_sales_csv'),
    path('export/mib-csv/', views.export_mib_csv, name='export_mib_csv'),
    path('export/installments-csv/', views.export_installments_csv, name='export_installments_csv'),
    path('export/customers-csv/', views.export_customers_csv, name='export_customers_csv'),
    path('export/site-visits-csv/', views.export_site_visits_csv, name='export_site_visits_csv'),
    path('export/prospects-csv/', views.export_prospects_csv, name='export_prospects_csv'),
    path('export/customer-view-csv/', views.export_customer_view_csv, name='export_customer_view_csv'),
    path('export/bookings-csv/', views.export_bookings_csv, name='export_bookings_csv'),
    
    # ==========================================================================
    # REPORT ENDPOINTS (N8N INTEGRATION)
    # ==========================================================================
    path('reports/sales/', reports.generate_sales_report, name='generate_sales_report'),
    path('reports/new-sales/', reports.new_sales_report, name='new_sales_report'),
    path('reports/mib/', reports.mib_report, name='mib_report'),
    path('reports/installments-due-today/', reports.installments_due_today, name='installments_due_today'),
    path('reports/overdue-collections/', reports.overdue_collections_report, name='overdue_collections_report'),
    path('reports/prospects/', reports.prospects_report, name='prospects_report'),
    
    # ==========================================================================
    # PERMISSION MANAGEMENT (N8N INTEGRATION)
    # ==========================================================================
    path('permissions/hints/', permission_hints.permission_hints, name='permission_hints'),
    path('permissions/clear-cache/', permission_hints.clear_permission_cache, name='clear_permission_cache'),
    path('permissions/analytics/', permission_hints.permission_analytics, name='permission_analytics'),
    path('permissions/health/', permission_hints.health_check, name='permission_health'),
    
    # ==========================================================================
    # N8N OPTIMIZED INTEGRATION ENDPOINTS
    # ==========================================================================
    path('n8n/permissions/', n8n_integration.n8n_permission_check, name='n8n_permission_check'),
    path('n8n/intent/', n8n_integration.n8n_lightweight_intent, name='n8n_lightweight_intent'),
    
    # ==========================================================================
    # SESSION PROXY
    # ==========================================================================
    path('session/', views.chatbot_session_proxy, name='chatbot_session_proxy'),
] 