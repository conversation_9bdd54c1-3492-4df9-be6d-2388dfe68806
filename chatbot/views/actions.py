"""
Chatbot Action Views
===================

Action endpoints for the chatbot system.
Handles creating engagements, reminders, notes, and the main API router.
"""

from django.http import JsonResponse, HttpResponse
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import json
import logging

from ..config import ChatbotConfig
from .utils import get_request_user, get_permission_classes

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def chatbot_api_router(request):
    """
    Main API router for all chatbot actions.
    Routes requests to appropriate action handlers.
    """
    user = get_request_user(request)
    
    # Log bypass mode usage
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        logger.info(f"BYPASS MODE: API accessed by {getattr(user, 'employee_no', 'anonymous')} without authentication")
    
    try:
        data = json.loads(request.body) if request.body else {}
        action = data.get('action')
        
        if not action:
            return Response({
                'error': 'Action is required',
                'available_actions': ['create_engagement', 'create_reminder', 'create_note', 'search'],
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Route to appropriate handler
        if action == 'search':
            return handle_search_action(user, data)
        elif action == 'create_engagement':
            return handle_create_engagement(user, data)
        elif action == 'create_reminder':
            return handle_create_reminder(user, data)
        elif action == 'create_note':
            return handle_create_note(user, data)
        else:
            return Response({
                'error': f'Unknown action: {action}',
                'available_actions': ['create_engagement', 'create_reminder', 'create_note', 'search'],
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
    except json.JSONDecodeError:
        return Response({
            'error': 'Invalid JSON in request body',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"API router error for user {user.employee_no if hasattr(user, 'employee_no') else 'anonymous'}: {str(e)}")
        return Response({
            'error': 'Internal server error',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'An error occurred',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# INDIVIDUAL ACTION ENDPOINTS (FOR N8N INTEGRATION)
# ==========================================================================
# NOTE: These endpoints are LEGACY - only support customers.
# For multi-entity support (customers, prospects, leadfiles), use enhanced_actions.py endpoints.
# URLs currently use enhanced_actions versions (see urls.py lines 40-42).

@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_engagement_endpoint(request):
    """
    Direct endpoint for creating engagements (N8N integration).
    
    LEGACY: This endpoint only supports customers.
    For multi-entity support, use enhanced_actions.create_engagement_endpoint.
    """
    user = get_request_user(request)
    
    try:
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            from ..services.lightweight_permissions import LightweightPermissionService
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has required tool permission
            if 'create_engagement' not in session_perms.available_tools:
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to create engagement without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to create engagements. Required: can_create_engagements or can_add_engagement',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        data = json.loads(request.body) if request.body else {}
        
        # Extract engagement data from request body
        engagement_data = {
            'customer_no': data.get('customer_no'),
            'engagement_type': data.get('engagement_type', 'Call'),
            'description': data.get('description', ''),
            'subject': data.get('subject', data.get('title', 'Chatbot Engagement'))
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_engagement(user, engagement_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create engagement endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create engagement failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_reminder_endpoint(request):
    """Direct endpoint for creating reminders (N8N integration)"""
    user = get_request_user(request)
    
    try:
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            from ..services.lightweight_permissions import LightweightPermissionService
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has required tool permission
            if 'create_reminder' not in session_perms.available_tools:
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to create reminder without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to create reminders. Required: can_set_reminders or can_add_reminder',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        data = json.loads(request.body) if request.body else {}
        
        # Extract reminder data from request body
        reminder_data = {
            'customer_no': data.get('customer_no'),
            'title': data.get('title', 'Chatbot Reminder'),
            'description': data.get('description', ''),
            'reminder_notes': data.get('description', ''),
            'due_date': data.get('due_date'),
            'reminder_date': data.get('due_date'),
            'priority': data.get('priority', 'Normal'),
            'reminder_type': data.get('reminder_type', 'General')
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_reminder(user, reminder_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create reminder endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create reminder failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_note_endpoint(request):
    """Direct endpoint for creating notes (N8N integration)"""
    user = get_request_user(request)
    
    try:
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            from ..services.lightweight_permissions import LightweightPermissionService
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has required tool permission
            if 'create_note' not in session_perms.available_tools:
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to create note without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to create notes. Required: can_create_notes',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        data = json.loads(request.body) if request.body else {}
        
        # Extract note data from request body
        note_data = {
            'customer_no': data.get('customer_no'),
            'title': data.get('title', 'Chatbot Note'),
            'content': data.get('content', ''),
            'note_type': data.get('note_type', 'General')
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_note(user, note_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create note endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create note failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# ACTION HANDLERS (FOR API ROUTER)
# ==========================================================================

def handle_search_action(user, data):
    """Handle search action"""
    try:
        from ..services.search import ChatbotSearchService
        search_service = ChatbotSearchService()
        
        query = data.get('query', '').strip()
        search_type = data.get('type', 'all')
        limit = min(int(data.get('limit', ChatbotConfig.DEFAULT_SEARCH_LIMIT)), 
                   ChatbotConfig.MAX_SEARCH_RESULTS)
        
        if not query:
            return Response({
                'error': 'Query is required for search action',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        results = search_service.unified_search(
            query=query,
            user=user,
            search_type=search_type,
            limit=limit
        )
        
        return Response(results)
        
    except Exception as e:
        logger.error(f"Search action error: {str(e)}")
        return Response({
            'error': 'Search action failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_create_engagement(user, data):
    """Handle create engagement action"""
    try:
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        
        engagement_data = data.get('engagement', {})
        if not engagement_data:
            return Response({
                'error': 'Engagement data is required',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        result = action_service.create_engagement(user, engagement_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create engagement error: {str(e)}")
        return Response({
            'error': 'Create engagement failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_create_reminder(user, data):
    """Handle create reminder action"""
    try:
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        
        reminder_data = data.get('reminder', {})
        if not reminder_data:
            return Response({
                'error': 'Reminder data is required',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        result = action_service.create_reminder(user, reminder_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create reminder error: {str(e)}")
        return Response({
            'error': 'Create reminder failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def handle_create_note(user, data):
    """Handle create note action"""
    try:
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        
        note_data = data.get('note', {})
        if not note_data:
            return Response({
                'error': 'Note data is required',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        result = action_service.create_note(user, note_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create note error: {str(e)}")
        return Response({
            'error': 'Create note failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# USER-SPECIFIC ENDPOINTS
# ==========================================================================

@api_view(['GET'])
@permission_classes(get_permission_classes())
def my_reminders(request):
    """Get current user's reminders"""
    user = get_request_user(request)
    
    # Log bypass mode usage
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        logger.info(f"BYPASS MODE: My reminders accessed by {getattr(user, 'employee_no', 'anonymous')} without authentication")
    
    try:
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        
        limit = min(int(request.GET.get('limit', ChatbotConfig.DEFAULT_SEARCH_LIMIT)), 
                   ChatbotConfig.MAX_SEARCH_RESULTS)
        
        reminders = action_service.get_my_reminders(user, limit)
        
        return Response({
            'reminders': reminders,
            'count': len(reminders),
            'user': user.employee_no if hasattr(user, 'employee_no') else 'anonymous',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        })
        
    except Exception as e:
        logger.error(f"Get reminders error: {str(e)}")
        return Response({
            'error': 'Failed to get reminders',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes(get_permission_classes())
def my_engagements(request):
    """Get current user's engagements"""
    user = get_request_user(request)
    
    # Log bypass mode usage
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        logger.info(f"BYPASS MODE: My engagements accessed by {getattr(user, 'employee_no', 'anonymous')} without authentication")
    
    try:
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        
        limit = min(int(request.GET.get('limit', ChatbotConfig.DEFAULT_SEARCH_LIMIT)), 
                   ChatbotConfig.MAX_SEARCH_RESULTS)
        
        engagements = action_service.get_my_engagements(user, limit)
        
        return Response({
            'engagements': engagements,
            'count': len(engagements),
            'user': user.employee_no if hasattr(user, 'employee_no') else 'anonymous',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        })
        
    except Exception as e:
        logger.error(f"Get engagements error: {str(e)}")
        return Response({
            'error': 'Failed to get engagements',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def chatbot_session_proxy(request):
    """
    Session proxy endpoint for embedded chatbot.
    Provides a secure way to maintain chatbot sessions.
    """
    user = get_request_user(request)
    
    # Log bypass mode usage
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        logger.info(f"BYPASS MODE: Session proxy accessed by {getattr(user, 'employee_no', 'anonymous')} without authentication")
    
    try:
        data = json.loads(request.body) if request.body else {}
        
        # Basic session management
        session_data = {
            'user_id': user.employee_no if hasattr(user, 'employee_no') else 'anonymous',
            'session_id': data.get('session_id', f'session_{int(timezone.now().timestamp())}'),
            'timestamp': timezone.now().isoformat(),
            'status': 'active',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }
        
        return Response({
            'session': session_data,
            'message': 'Session established successfully',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        })
        
    except Exception as e:
        logger.error(f"Session proxy error: {str(e)}")
        return Response({
            'error': 'Session proxy failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
