"""
Chatbot Export Views
===================

CSV export endpoints for the chatbot system.
Provides secure data export functionality with permission filtering.
"""

from django.http import HttpResponse
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import csv
import logging

from ..config import ChatbotConfig
from .utils import get_request_user

logger = logging.getLogger(__name__)


def create_csv_response(filename: str, data: list, headers: list) -> HttpResponse:
    """Create a CSV response with proper headers"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    writer = csv.writer(response)
    writer.writerow(headers)
    
    for row in data:
        writer.writerow(row)
    
    return response


@api_view(['GET'])
@permission_classes([AllowAny])
def export_sales_csv(request):
    """Export sales data to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from sales.models import LeadFile
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Start with all lead files
        queryset = LeadFile.objects.select_related(
            'customer_id', 'marketer', 'project', 'plot'
        ).all()
        
        # Apply permission filters if not in bypass mode
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            queryset = PermissionFilterService.apply_leadfile_filters(queryset, filter_params)
        
        # Get query parameters for additional filtering
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer_no = request.GET.get('customer_no')
        
        if start_date:
            queryset = queryset.filter(booking_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(booking_date__lte=end_date)
        if customer_no:
            queryset = queryset.filter(customer_id__customer_no__icontains=customer_no)
        
        # Prepare data for CSV
        headers = [
            'Lead File No', 'Customer Name', 'Customer No', 'Plot No', 'Project',
            'Purchase Price', 'Selling Price', 'Total Paid', 'Balance', 'Purchase Type',
            'Booking Date', 'Completion Date', 'Status', 'Marketer', 'Marketer ID'
        ]
        
        data = []
        for lf in queryset[:ChatbotConfig.CSV_MAX_ROWS]:
            data.append([
                lf.lead_file_no or '',
                lf.customer_name or '',
                lf.customer_id.customer_no if lf.customer_id else '',
                lf.plot_no or '',
                lf.project.name if lf.project else '',
                str(lf.purchase_price or '0.00'),
                str(lf.selling_price or '0.00'),
                str(lf.total_paid or '0.00'),
                str(lf.balance_lcy or '0.00'),
                lf.purchase_type or '',
                lf.booking_date.strftime('%Y-%m-%d') if lf.booking_date else '',
                lf.completion_date.strftime('%Y-%m-%d') if lf.completion_date else '',
                'Active' if not lf.lead_file_status_dropped else 'Dropped',
                lf.marketer.fullnames if lf.marketer else '',
                lf.marketer_id or ''
            ])
        
        filename = f"sales_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Sales export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_mib_csv(request):
    """Export MIB (Money In Bank) data to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from sales.models import LeadFile
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # MIB = Lead files with installment purchase type
        queryset = LeadFile.objects.select_related(
            'customer_id', 'marketer', 'project'
        ).filter(purchase_type='Installment')
        
        # Apply permission filters if not in bypass mode
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            queryset = PermissionFilterService.apply_leadfile_filters(queryset, filter_params)
        
        # Get query parameters for additional filtering
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        if start_date:
            queryset = queryset.filter(booking_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(booking_date__lte=end_date)
        
        # Prepare data for CSV
        headers = [
            'Lead File No', 'Customer Name', 'Customer No', 'Plot No', 'Project',
            'Purchase Price', 'Installment Amount', 'Total Paid', 'Balance',
            'No of Installments', 'Booking Date', 'Marketer', 'Marketer ID'
        ]
        
        data = []
        for lf in queryset[:ChatbotConfig.CSV_MAX_ROWS]:
            data.append([
                lf.lead_file_no or '',
                lf.customer_name or '',
                lf.customer_id.customer_no if lf.customer_id else '',
                lf.plot_no or '',
                lf.project.name if lf.project else '',
                str(lf.purchase_price or '0.00'),
                str(lf.installment_amount or '0.00'),
                str(lf.total_paid or '0.00'),
                str(lf.balance_lcy or '0.00'),
                str(lf.no_of_installments or '0'),
                lf.booking_date.strftime('%Y-%m-%d') if lf.booking_date else '',
                lf.marketer.fullnames if lf.marketer else '',
                lf.marketer_id or ''
            ])
        
        filename = f"mib_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"MIB export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_installments_csv(request):
    """Export installment schedules to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from receipts.models import InstallmentSchedule
        from sales.models import LeadFile
        from ..services.permission_filters import extract_filter_params_from_request
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Start with all installment schedules
        queryset = InstallmentSchedule.objects.select_related().all()
        
        # Apply permission filtering through lead files
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            # Get lead file numbers that match permission filters
            leadfile_queryset = LeadFile.objects.all()
            from ..services.permission_filters import PermissionFilterService
            leadfile_queryset = PermissionFilterService.apply_leadfile_filters(leadfile_queryset, filter_params)
            allowed_leadfile_nos = list(leadfile_queryset.values_list('lead_file_no', flat=True))
            
            if allowed_leadfile_nos:
                queryset = queryset.filter(leadfile_no__in=allowed_leadfile_nos)
            else:
                # No lead files match permissions - return empty result
                queryset = queryset.none()
        
        # Get query parameters for additional filtering
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer_no = request.GET.get('customer_no')
        leadfile_no = request.GET.get('leadfile_no')
        
        if start_date:
            queryset = queryset.filter(due_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(due_date__lte=end_date)
        if customer_no:
            queryset = queryset.filter(member_no__icontains=customer_no)
        if leadfile_no:
            queryset = queryset.filter(leadfile_no__icontains=leadfile_no)
        
        # Prepare data for CSV
        headers = [
            'Member No', 'Lead File No', 'Installment No', 'Installment Amount',
            'Remaining Amount', 'Due Date', 'Paid', 'Plot No', 'Plot Name',
            'Amount Paid', 'Penalties Accrued'
        ]
        
        data = []
        for inst in queryset[:ChatbotConfig.CSV_MAX_ROWS]:
            data.append([
                inst.member_no or '',
                inst.leadfile_no or '',
                str(inst.installment_no or '0'),
                inst.installment_amount or '0.00',
                inst.remaining_Amount or '0.00',
                inst.due_date.strftime('%Y-%m-%d') if inst.due_date else '',
                inst.paid or 'No',
                inst.plot_No or '',
                inst.plot_Name or '',
                inst.amount_Paid or '0.00',
                str(inst.penaties_Accrued or '0')
            ])
        
        filename = f"installments_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Installments export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_customers_csv(request):
    """Export customers data to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from django.db import connections
        from ..services.permission_filters import extract_filter_params_from_request
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get filter parameters from query string
        customer_no = request.GET.get('customer_no')
        marketer_id = request.GET.get('marketer_id')
        office = request.GET.get('office')
        
        # Apply permission filters if not in bypass mode
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            if filter_params.get('restrict_to_marketer') and not marketer_id:
                marketer_id = filter_params.get('marketer_employee_no')
            if filter_params.get('restrict_to_office') and not office:
                office = filter_params.get('office')
        
        # Build SQL query with user permissions
        sql = """
        SELECT 
            c.customer_no,
            c.customer_name,
            c.phone,
            c.primary_email,
            c.alternative_phone,
            c.customer_type,
            c.national_id,
            c.kra_pin,
            u.fullnames as marketer_name,
            u.employee_no as marketer_employee_no,
            u.office as marketer_office,
            ls.name as lead_source
        FROM customers_customer c
        LEFT JOIN users_user u ON c.marketer_id = u.employee_no
        LEFT JOIN leads_leadsource ls ON c.lead_source_id = ls.leadsource_id
        WHERE 1=1
        """
        
        params = []
        
        # Apply filters
        if customer_no:
            sql += " AND c.customer_no ILIKE %s"
            params.append(f"%{customer_no}%")
        
        if marketer_id:
            sql += " AND u.employee_no = %s"
            params.append(marketer_id)
        elif user.is_marketer:
            # Marketers can only see their own customers
            sql += " AND u.employee_no = %s"
            params.append(user.employee_no)
        
        if office:
            sql += " AND u.office = %s"
            params.append(office)
        
        sql += " ORDER BY c.customer_name LIMIT 5000"  # Limit for performance
        
        # Execute query
        with connections['default'].cursor() as cursor:
            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()
        
        # Prepare CSV data
        headers = [
            'Customer No', 'Customer Name', 'Phone', 'Email', 'Alt Phone',
            'Customer Type', 'National ID', 'KRA PIN', 'Marketer', 'Marketer ID', 
            'Office', 'Lead Source'
        ]
        
        data = []
        for row in results:
            data.append([
                row[0] or '',  # customer_no
                row[1] or '',  # customer_name
                row[2] or '',  # phone
                row[3] or '',  # primary_email
                row[4] or '',  # alternative_phone
                row[5] or '',  # customer_type
                row[6] or '',  # national_id
                row[7] or '',  # kra_pin
                row[8] or '',  # marketer_name
                row[9] or '',  # marketer_employee_no
                row[10] or '',  # marketer_office
                row[11] or ''   # lead_source
            ])
        
        filename = f"customers_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Customers export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_site_visits_csv(request):
    """Export site visits (engagements with Visit type) to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from engagement.models import Engagement
        from ..services.permission_filters import extract_filter_params_from_request
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Site visits = Engagements with Visit type
        queryset = Engagement.objects.select_related(
            'customer', 'prospect', 'sale', 'created_by'
        ).filter(engagement_type='Visit')
        
        # Apply permission filtering through customers/prospects
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            if filter_params.get('restrict_to_marketer'):
                marketer_id = filter_params.get('marketer_employee_no')
                # Filter by marketer through customer or prospect
                from django.db.models import Q
                queryset = queryset.filter(
                    Q(customer__marketer_id=marketer_id) |
                    Q(prospect__marketer_id=marketer_id) |
                    Q(sale__marketer_id=marketer_id)
                )
        
        # Get query parameters for additional filtering
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        customer_no = request.GET.get('customer_no')
        
        if start_date:
            queryset = queryset.filter(created_at__gte=start_date)
        if end_date:
            queryset = queryset.filter(created_at__lte=end_date)
        if customer_no:
            queryset = queryset.filter(customer__customer_no__icontains=customer_no)
        
        # Prepare data for CSV
        headers = [
            'Engagement ID', 'Subject', 'Customer No', 'Customer Name', 'Prospect',
            'Visit Date', 'Description', 'Created By', 'Created At'
        ]
        
        data = []
        for eng in queryset[:ChatbotConfig.CSV_MAX_ROWS]:
            customer_no_val = eng.customer.customer_no if eng.customer else ''
            customer_name_val = eng.customer.customer_name if eng.customer else ''
            prospect_name_val = eng.prospect.name if eng.prospect else ''
            
            data.append([
                eng.engagement_id or '',
                eng.subject or '',
                customer_no_val,
                customer_name_val,
                prospect_name_val,
                eng.created_at.strftime('%Y-%m-%d %H:%M') if eng.created_at else '',
                eng.description[:200] if eng.description else '',  # Truncate long descriptions
                eng.created_by.fullnames if eng.created_by else '',
                eng.created_at.strftime('%Y-%m-%d %H:%M:%S') if eng.created_at else ''
            ])
        
        filename = f"site_visits_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Site visits export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_prospects_csv(request):
    """Export prospects data to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..config import ChatbotConfig
        from leads.models import Prospects
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Start with all prospects
        queryset = Prospects.objects.select_related('marketer').all()
        
        # Apply permission filters if not in bypass mode
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            queryset = PermissionFilterService.apply_prospect_filters(queryset, filter_params)
        
        # Prepare data for CSV
        headers = ['ID', 'Name', 'Phone', 'Email', 'Source', 'Status', 'Marketer']
        data = [
            [
                p.id,
                p.name,
                p.phone,
                p.email,
                p.lead_source.name if p.lead_source else 'N/A',
                p.status or 'New',
                p.marketer.fullnames if p.marketer else 'N/A'
            ]
            for p in queryset[:ChatbotConfig.CSV_MAX_ROWS]
        ]
        
        filename = f"prospects_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Prospects export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_customer_view_csv(request):
    """Export customer view data to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from django.db import connections
        from django.db.models import Sum, Count, Q
        from sales.models import LeadFile
        from ..services.permission_filters import extract_filter_params_from_request
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Build SQL query with customer data and aggregated sales info
        sql = """
        SELECT 
            c.customer_no,
            c.customer_name,
            c.phone,
            c.primary_email,
            c.alternative_phone,
            c.customer_type,
            u.fullnames as marketer_name,
            u.employee_no as marketer_employee_no,
            u.office as marketer_office,
            ls.name as lead_source,
            COALESCE(SUM(lf.purchase_price), 0) as total_value,
            COALESCE(SUM(lf.total_paid), 0) as total_paid,
            COALESCE(SUM(lf.balance_lcy), 0) as total_balance,
            COUNT(lf.lead_file_no) as total_sales,
            MAX(lf.booking_date) as last_sale_date
        FROM customers_customer c
        LEFT JOIN users_user u ON c.marketer_id = u.employee_no
        LEFT JOIN leads_leadsource ls ON c.lead_source_id = ls.leadsource_id
        LEFT JOIN sales_leadfile lf ON c.customer_no = lf.customer_id_id
        WHERE 1=1
        """
        
        params = []
        
        # Apply permission filters if not in bypass mode
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
                sql += " AND u.employee_no = %s"
                params.append(filter_params['marketer_employee_no'])
            if filter_params.get('restrict_to_office') and filter_params.get('office'):
                sql += " AND u.office = %s"
                params.append(filter_params['office'])
        
        # Get query parameters for additional filtering
        customer_no = request.GET.get('customer_no')
        customer_type = request.GET.get('customer_type')
        marketer_id = request.GET.get('marketer_id')
        office = request.GET.get('office')
        
        if customer_no:
            sql += " AND c.customer_no ILIKE %s"
            params.append(f"%{customer_no}%")
        if customer_type:
            sql += " AND c.customer_type = %s"
            params.append(customer_type)
        if marketer_id:
            sql += " AND u.employee_no = %s"
            params.append(marketer_id)
        if office:
            sql += " AND u.office = %s"
            params.append(office)
        
        sql += " GROUP BY c.customer_no, c.customer_name, c.phone, c.primary_email, c.alternative_phone, c.customer_type, u.fullnames, u.employee_no, u.office, ls.name"
        sql += " ORDER BY c.customer_name LIMIT %s"
        params.append(ChatbotConfig.CSV_MAX_ROWS)
        
        # Execute query
        with connections['default'].cursor() as cursor:
            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()
        
        # Prepare CSV data
        headers = [
            'Customer No', 'Customer Name', 'Phone', 'Email', 'Alt Phone',
            'Customer Type', 'Marketer', 'Marketer ID', 'Office', 'Lead Source',
            'Total Value', 'Total Paid', 'Total Balance', 'Total Sales', 'Last Sale Date'
        ]
        
        data = []
        for row in results:
            data.append([
                row[0] or '',  # customer_no
                row[1] or '',  # customer_name
                row[2] or '',  # phone
                row[3] or '',  # primary_email
                row[4] or '',  # alternative_phone
                row[5] or '',  # customer_type
                row[6] or '',  # marketer_name
                row[7] or '',  # marketer_employee_no
                row[8] or '',  # marketer_office
                row[9] or '',  # lead_source
                str(row[10] or '0.00'),  # total_value
                str(row[11] or '0.00'),  # total_paid
                str(row[12] or '0.00'),  # total_balance
                str(row[13] or '0'),  # total_sales
                row[14].strftime('%Y-%m-%d') if row[14] else ''  # last_sale_date
            ])
        
        filename = f"customer_view_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Customer view export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_bookings_csv(request):
    """Export plot bookings to CSV with permission filtering"""
    user = get_request_user(request)
    
    try:
        from inventory.models import PlotBooking
        from ..services.permission_filters import extract_filter_params_from_request
        from ..config import ChatbotConfig
        
        # Extract permission-based filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Start with all plot bookings
        queryset = PlotBooking.objects.select_related(
            'customer', 'lead', 'marketer'
        ).all()
        
        # Apply permission filtering through marketer
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS and filter_params:
            if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
                queryset = queryset.filter(marketer_id=filter_params['marketer_employee_no'])
            if filter_params.get('restrict_to_office') and filter_params.get('office'):
                queryset = queryset.filter(office=filter_params['office'])
        
        # Get query parameters for additional filtering
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        status_filter = request.GET.get('status')
        project_id = request.GET.get('project_id')
        
        if start_date:
            queryset = queryset.filter(creation_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(creation_date__lte=end_date)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        if project_id:
            # Filter by plots in the project (plots field contains plot IDs)
            queryset = queryset.filter(plots__icontains=project_id)
        
        # Prepare data for CSV
        headers = [
            'Booking ID', 'Booking Type', 'Plots', 'Customer No', 'Customer Name',
            'Lead Name', 'Marketer', 'Marketer ID', 'Amount', 'Expected Payment Date',
            'Deadline', 'Status', 'Office', 'Creation Date', 'Approval'
        ]
        
        data = []
        for booking in queryset[:ChatbotConfig.CSV_MAX_ROWS]:
            customer_no_val = booking.customer.customer_no if booking.customer else ''
            customer_name_val = booking.customer.customer_name if booking.customer else ''
            lead_name_val = booking.lead.name if booking.lead else ''
            
            data.append([
                booking.booking_id or '',
                booking.booking_type or '',
                booking.plots or '',
                customer_no_val,
                customer_name_val,
                lead_name_val,
                booking.marketer.fullnames if booking.marketer else '',
                booking.marketer_id or '',
                str(booking.amount or '0.00'),
                booking.expected_payment_date.strftime('%Y-%m-%d %H:%M') if booking.expected_payment_date else '',
                booking.deadline.strftime('%Y-%m-%d %H:%M') if booking.deadline else '',
                booking.status or '',
                booking.office or '',
                booking.creation_date.strftime('%Y-%m-%d %H:%M:%S') if booking.creation_date else '',
                'Yes' if booking.approval else 'No'
            ])
        
        filename = f"bookings_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Bookings export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
