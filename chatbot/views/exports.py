"""
Chatbot Export Views
===================

CSV export endpoints for the chatbot system.
Provides secure data export functionality with permission filtering.
"""

from django.http import HttpResponse
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import csv
import logging

from ..config import ChatbotConfig
from .utils import get_request_user

logger = logging.getLogger(__name__)


def create_csv_response(filename: str, data: list, headers: list) -> HttpResponse:
    """Create a CSV response with proper headers"""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{filename}"'
    
    writer = csv.writer(response)
    writer.writerow(headers)
    
    for row in data:
        writer.writerow(row)
    
    return response


@api_view(['GET'])
@permission_classes([AllowAny])
def export_sales_csv(request):
    """Export sales data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Customer', 'Amount', 'Date', 'Status']
        data = [
            [1, 'Sample Customer', 100000, timezone.now().date(), 'Active'],
            [2, 'Another Customer', 150000, timezone.now().date(), 'Pending']
        ]
        
        filename = f"sales_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Sales export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_mib_csv(request):
    """Export MIB data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Reference', 'Amount', 'Date', 'Status']
        data = [
            [1, 'MIB001', 50000, timezone.now().date(), 'Processed'],
            [2, 'MIB002', 75000, timezone.now().date(), 'Pending']
        ]
        
        filename = f"mib_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"MIB export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_installments_csv(request):
    """Export installments data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Customer', 'Amount', 'Due Date', 'Status']
        data = [
            [1, 'Customer A', 25000, timezone.now().date(), 'Due'],
            [2, 'Customer B', 30000, timezone.now().date(), 'Paid']
        ]
        
        filename = f"installments_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Installments export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_customers_csv(request):
    """Export customers data to CSV"""
    user = get_request_user(request)
    
    try:
        from django.db import connections
        
        # Get filter parameters
        customer_no = request.GET.get('customer_no')
        marketer_id = request.GET.get('marketer_id')
        office = request.GET.get('office')
        
        # Build SQL query with user permissions
        sql = """
        SELECT 
            c.customer_no,
            c.customer_name,
            c.phone,
            c.primary_email,
            c.alternative_phone,
            c.customer_type,
            c.national_id,
            c.kra_pin,
            u.fullnames as marketer_name,
            u.employee_no as marketer_employee_no,
            u.office as marketer_office,
            ls.name as lead_source
        FROM customers_customer c
        LEFT JOIN users_user u ON c.marketer_id = u.employee_no
        LEFT JOIN leads_leadsource ls ON c.lead_source_id = ls.leadsource_id
        WHERE 1=1
        """
        
        params = []
        
        # Apply filters
        if customer_no:
            sql += " AND c.customer_no ILIKE %s"
            params.append(f"%{customer_no}%")
        
        if marketer_id:
            sql += " AND u.employee_no = %s"
            params.append(marketer_id)
        elif user.is_marketer:
            # Marketers can only see their own customers
            sql += " AND u.employee_no = %s"
            params.append(user.employee_no)
        
        if office:
            sql += " AND u.office = %s"
            params.append(office)
        
        sql += " ORDER BY c.customer_name LIMIT 5000"  # Limit for performance
        
        # Execute query
        with connections['default'].cursor() as cursor:
            cursor.execute(sql, params)
            columns = [col[0] for col in cursor.description]
            results = cursor.fetchall()
        
        # Prepare CSV data
        headers = [
            'Customer No', 'Customer Name', 'Phone', 'Email', 'Alt Phone',
            'Customer Type', 'National ID', 'KRA PIN', 'Marketer', 'Marketer ID', 
            'Office', 'Lead Source'
        ]
        
        data = []
        for row in results:
            data.append([
                row[0] or '',  # customer_no
                row[1] or '',  # customer_name
                row[2] or '',  # phone
                row[3] or '',  # primary_email
                row[4] or '',  # alternative_phone
                row[5] or '',  # customer_type
                row[6] or '',  # national_id
                row[7] or '',  # kra_pin
                row[8] or '',  # marketer_name
                row[9] or '',  # marketer_employee_no
                row[10] or '',  # marketer_office
                row[11] or ''   # lead_source
            ])
        
        filename = f"customers_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Customers export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_site_visits_csv(request):
    """Export site visits data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Customer', 'Site', 'Date', 'Status']
        data = [
            [1, 'Customer A', 'Site X', timezone.now().date(), 'Completed'],
            [2, 'Customer B', 'Site Y', timezone.now().date(), 'Scheduled']
        ]
        
        filename = f"site_visits_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Site visits export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_prospects_csv(request):
    """Export prospects data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Name', 'Phone', 'Email', 'Source', 'Status']
        data = [
            [1, 'Prospect A', '0700111111', '<EMAIL>', 'Website', 'New'],
            [2, 'Prospect B', '0700222222', '<EMAIL>', 'Referral', 'Contacted']
        ]
        
        filename = f"prospects_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Prospects export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_customer_view_csv(request):
    """Export customer view data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['Customer No', 'Name', 'Phone', 'Email', 'Total Value', 'Last Contact']
        data = [
            ['CUS001', 'John Doe', '0700123456', '<EMAIL>', 500000, timezone.now().date()],
            ['CUS002', 'Jane Smith', '0700654321', '<EMAIL>', 750000, timezone.now().date()]
        ]
        
        filename = f"customer_view_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Customer view export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def export_bookings_csv(request):
    """Export bookings data to CSV"""
    user = get_request_user(request)
    
    try:
        # Placeholder implementation
        headers = ['ID', 'Customer', 'Plot', 'Booking Date', 'Status', 'Amount']
        data = [
            [1, 'Customer A', 'Plot 101', timezone.now().date(), 'Confirmed', 1000000],
            [2, 'Customer B', 'Plot 102', timezone.now().date(), 'Pending', 1200000]
        ]
        
        filename = f"bookings_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
        return create_csv_response(filename, data, headers)
        
    except Exception as e:
        logger.error(f"Bookings export error: {str(e)}")
        return Response({
            'error': 'Export failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
