"""
Create Prospect for Chatbot
============================

Simple direct database creation for prospects - no proxy, no authentication.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.utils import timezone

from leads.models import Prospects, LeadSource
from users.models import User
import re


def format_phone_number(phone):
    """Format phone number to E.164 format"""
    if not phone:
        return ''
    
    # Remove all non-digit characters
    phone = re.sub(r'\D', '', phone)
    
    # Handle Kenyan numbers
    if phone.startswith('254'):
        return f'+{phone}'
    elif phone.startswith('0'):
        return f'+254{phone[1:]}'
    elif phone.startswith('7') or phone.startswith('1'):
        return f'+254{phone}'
    else:
        return f'+{phone}'


@api_view(['POST'])
@permission_classes([AllowAny])
def create_prospect(request):
    """
    Create a new prospect directly in the database.
    
    Request Body:
        name (str, required): Prospect name
        phone (str, required): Phone number
        email (str, optional): Email address
        comment (str, optional): Notes/comments
        country (str, optional): Country, defaults to 'Kenya'
        city (str, optional): City/location
        lead_source (int, optional): Lead source ID
        marketer (str, optional): Marketer employee number
        lead_type (str, optional): Lead type, defaults to 'personal'
        status (str, optional): Status, defaults to 'Active'
        category (str, optional): Category, defaults to 'Warm'
        pipeline_level (str, optional): Pipeline level, defaults to 'Nurturing'
        
    Returns:
        Success message with created prospect details
    """
    try:
        # Get request data
        data = request.data
        
        # Validate required fields
        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        
        if not name:
            return Response({
                'success': False,
                'error': 'Name is required',
                'message': 'Please provide a prospect name'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not phone:
            return Response({
                'success': False,
                'error': 'Phone number is required',
                'message': 'Please provide a phone number'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Format phone number
        phone_formatted = format_phone_number(phone)
        
        # Check for duplicate phone number
        existing_prospect = Prospects.objects.filter(phone=phone_formatted).first()
        if existing_prospect:
            return Response({
                'success': False,
                'error': 'Phone number already exists',
                'message': f'A prospect with phone {phone_formatted} already exists',
                'existing_prospect': {
                    'id': existing_prospect.id,
                    'name': existing_prospect.name,
                    'phone': existing_prospect.phone,
                    'email': existing_prospect.email or ''
                }
            }, status=status.HTTP_409_CONFLICT)
        
        # Get lead source if provided
        lead_source = None
        lead_source_category_id = None
        lead_source_subcategory_id = None
        
        lead_source_id = data.get('lead_source')
        if lead_source_id:
            try:
                lead_source = LeadSource.objects.select_related(
                    'lead_source_subcategory__lead_source_category'
                ).get(leadsource_id=lead_source_id)
                
                # LeadSource -> lead_source_subcategory -> lead_source_category
                if lead_source.lead_source_subcategory:
                    lead_source_subcategory_id = lead_source.lead_source_subcategory.cat_lead_source_id
                    if lead_source.lead_source_subcategory.lead_source_category:
                        lead_source_category_id = lead_source.lead_source_subcategory.lead_source_category.category_id
            except LeadSource.DoesNotExist:
                pass
        
        # Get marketer if provided
        marketer = None
        marketer_employee_no = data.get('marketer', '').strip()
        if marketer_employee_no:
            try:
                marketer = User.objects.get(employee_no=marketer_employee_no, is_active=True)
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'Marketer with employee number {marketer_employee_no} not found',
                    'message': 'Invalid marketer employee number'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Prepare prospect data
        prospect_data = {
            'name': name,
            'phone': phone_formatted,
            'lead_source': lead_source,
            'lead_source_category_id': lead_source_category_id,
            'lead_source_subcategory_id': lead_source_subcategory_id,
            'email': data.get('email', ''),
            'comment': data.get('comment', ''),
            'country': data.get('country', 'Kenya'),
            'city': data.get('city', ''),
            'marketer': marketer,
            'lead_type': data.get('lead_type', 'personal'),
            'department': None,
            'status': data.get('status', 'Active'),
            'category': data.get('category', 'Warm'),
            'pipeline_level': data.get('pipeline_level', 'Nurturing'),
            'date': timezone.now()
        }
        
        # Create prospect
        prospect = Prospects.objects.create(**prospect_data)
        
        # Prepare response
        response_data = {
            'prospect_id': prospect.id,
            'name': prospect.name,
            'phone': prospect.phone,
            'email': prospect.email,
            'marketer': marketer.fullnames if marketer else 'Not assigned',
            'lead_source': lead_source.name if lead_source else 'Not specified',
            'comment': prospect.comment,
            'created_at': prospect.date.isoformat()
        }
        
        return Response({
            'success': True,
            'message': f'Prospect {name} created successfully!',
            'data': response_data
        }, status=status.HTTP_201_CREATED)
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to create prospect'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

