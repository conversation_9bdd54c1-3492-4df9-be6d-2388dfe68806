"""
Chatbot Utility Views
====================

Utility endpoints for health checks, testing, and user information.
These endpoints provide system status and basic functionality testing.
"""

from django.http import HttpResponse
from django.db import connections
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.core.cache import cache
from datetime import datetime

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from ..config import ChatbotConfig
from ..monitoring import ChatbotPerformanceMonitor
from ..n8n_integration import N8NSessionManager, N8NPerformanceTracker, N8NPermissionIntegrator

try:
    from ..services import ChatbotSearchService, ChatbotActionService, ChatbotPermissionService
    SERVICES_AVAILABLE = True
except ImportError as e:
    print(f"Services import error: {e}")
    SERVICES_AVAILABLE = False

User = get_user_model()


def get_test_user():
    """Get or create a test user for testing purposes"""
    try:
        # Try to get any active user for testing
        return User.objects.filter(is_active=True).first()
    except:
        # If no users exist, create a mock user object
        class MockUser:
            id = 1
            fullnames = "Test User"
            employee_no = "TEST001"
            email = "<EMAIL>"
            department = "IT"
            office = "HQ"
        return MockUser()


def get_request_user(request):
    """
    Get the user from the request, handling both authenticated and bypass modes.
    
    Returns:
    - Authenticated user for normal mode
    - Test user for testing mode 
    - Anonymous mock user for bypass mode
    """
    from ..config import ChatbotConfig
    
    # In bypass mode, allow anonymous access
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        if hasattr(request, 'user') and request.user.is_authenticated:
            return request.user
        else:
            # Return anonymous mock user for bypass mode
            class AnonymousUser:
                id = 'anonymous'
                fullnames = "Anonymous User (Bypass Mode)"
                employee_no = "BYPASS001"
                email = "<EMAIL>"
                department = None
                office = "Bypass"
                team = None
                group = None
                is_marketer = False
                is_authenticated = False
            
            return AnonymousUser()
    
    # Normal authenticated mode
    if hasattr(request, 'user') and request.user.is_authenticated:
        return request.user
    
    # Fallback to test user in testing mode
    if ChatbotConfig.TESTING_MODE:
        return get_test_user()
    
    # Should not reach here in normal production with proper authentication
    raise PermissionError("User not authenticated")


@api_view(['GET'])
@permission_classes([AllowAny])
def health_check(request):
    """Health check endpoint"""
    try:
        # Test database connection
        with connections['default'].cursor() as cursor:
            cursor.execute("SELECT 1")
        
        return Response({'status': 'OK', 'message': 'Health check passed'})
    except Exception as e:
        return Response({'status': 'ERROR', 'message': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def simple_test(request):
    """Simple test endpoint"""
    return Response({
        'status': 'success',
        'message': 'Chatbot API is working',
        'timestamp': timezone.now().isoformat(),
        'services_available': SERVICES_AVAILABLE,
        'testing_mode': ChatbotConfig.TESTING_MODE,
        'config': {
            'max_search_results': ChatbotConfig.MAX_SEARCH_RESULTS,
            'cache_timeout': ChatbotConfig.CACHE_TIMEOUT,
            'fuzzy_threshold': ChatbotConfig.FUZZY_SEARCH_THRESHOLD
        }
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def user_info(request):
    """
    Get basic user information for the chatbot personalization.
    In testing mode, returns a test user if not authenticated.
    """
    user = get_request_user(request)
    
    if user:
        # Check for 'fullnames' attribute for custom user models
        full_name = getattr(user, 'fullnames', 'there')
        
        # Fallback for standard Django user model
        if not full_name and hasattr(user, 'first_name') and hasattr(user, 'last_name'):
            full_name = f"{user.first_name} {user.last_name}".strip() or 'there'

        return Response({
            'full_name': full_name,
            'employee_no': getattr(user, 'employee_no', None),
            'office': getattr(user, 'office', None),
            'is_marketer': getattr(user, 'is_marketer', False),
            'testing_mode': ChatbotConfig.TESTING_MODE
        })
    
    return Response({'full_name': 'there'}, status=status.HTTP_404_NOT_FOUND)


# ==========================================================================
# PERFORMANCE MONITORING VIEWS
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def performance_summary(request):
    """Get performance summary for all operations"""
    try:
        hours_back = int(request.GET.get('hours', 1))
        summary = ChatbotPerformanceMonitor.get_performance_summary(hours_back)
        return Response(summary)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def performance_alerts(request):
    """Get performance alerts"""
    try:
        alerts = ChatbotPerformanceMonitor.check_performance_alerts()
        return Response({'alerts': alerts})
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def database_status(request):
    """Get database performance metrics"""
    try:
        db_metrics = ChatbotPerformanceMonitor.get_database_performance()
        return Response(db_metrics)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# ADMIN/DEBUG VIEWS
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def config_info(request):
    """Get chatbot configuration information"""
    return Response({
        'config': {
            'testing_mode': ChatbotConfig.TESTING_MODE,
            'max_search_results': ChatbotConfig.MAX_SEARCH_RESULTS,
            'cache_timeout': ChatbotConfig.CACHE_TIMEOUT,
            'fuzzy_threshold': ChatbotConfig.FUZZY_SEARCH_THRESHOLD,
            'read_only_mode': ChatbotConfig.READ_ONLY_MODE,
            'debug_mode': ChatbotConfig.DEBUG_MODE
        }
    })


@api_view(['GET'])
@permission_classes([AllowAny])
def cache_statistics(request):
    """Get cache performance statistics"""
    try:
        cache_metrics = ChatbotPerformanceMonitor.get_cache_performance()
        return Response(cache_metrics)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def clear_cache(request):
    """Clear chatbot cache"""
    try:
        # Clear all chatbot-related cache keys
        cache_keys = []
        
        # Get all cache keys with chatbot prefix
        try:
            # This is a simplified approach - in production you might want to be more specific
            cache.clear()
            cache_keys.append("All cache cleared")
        except Exception as e:
            return Response({'error': f'Failed to clear cache: {str(e)}'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        return Response({
            'message': 'Cache cleared successfully',
            'cleared_keys': cache_keys
        })
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def session_analytics(request):
    """
    Track session-level analytics without storing chat content.
    Useful for understanding user interaction patterns.
    """
    try:
        session_id = request.data.get('session_id')
        event_type = request.data.get('event_type')  # 'session_start', 'query_count', 'session_end'
        metadata = request.data.get('metadata', {})
        
        if not session_id or not event_type:
            return Response({
                'success': False,
                'error': {
                    'code': 'VALIDATION_ERROR',
                    'message': 'session_id and event_type are required'
                }
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Store session metrics in cache (not permanent storage)
        cache_key = f"session_analytics_{session_id}"
        session_data = cache.get(cache_key, {
            'session_id': session_id,
            'start_time': timezone.now().isoformat(),
            'query_count': 0,
            'events': []
        })
        
        # Update session data
        session_data['events'].append({
            'event_type': event_type,
            'timestamp': timezone.now().isoformat(),
            'metadata': metadata
        })
        
        if event_type == 'query':
            session_data['query_count'] += 1
        elif event_type == 'session_end':
            session_data['end_time'] = timezone.now().isoformat()
            session_data['duration_minutes'] = (
                timezone.now() - datetime.fromisoformat(session_data['start_time'].replace('Z', '+00:00'))
            ).total_seconds() / 60
        
        # Store for 1 hour only (no permanent storage)
        cache.set(cache_key, session_data, 3600)
        
        return Response({
            'success': True,
            'message': 'Session event tracked successfully',
            'data': {
                'session_id': session_id,
                'query_count': session_data['query_count'],
                'event_count': len(session_data['events'])
            }
        })
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def session_summary(request):
    """
    Get session summary without exposing chat content.
    Returns aggregated session metrics.
    """
    try:
        hours_back = int(request.GET.get('hours', 1))
        
        # Get session metrics from cache
        summary = {
            'period_hours': hours_back,
            'generated_at': timezone.now().isoformat(),
            'session_stats': {
                'total_sessions': 0,
                'avg_queries_per_session': 0,
                'avg_session_duration_minutes': 0,
                'most_common_events': []
            }
        }
        
        # This would aggregate session data from cache
        # without storing individual chat messages
        
        return Response(summary)
        
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# N8N PERMISSION INTEGRATION
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def user_capabilities(request):
    """Get user capabilities for N8N workflow routing"""
    try:
        user = get_request_user(request)
        capabilities = N8NPermissionIntegrator.get_user_capabilities(user)
        return Response(capabilities)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([AllowAny])
def validate_tool_access(request):
    """Validate if user can access a specific N8N tool"""
    try:
        user = get_request_user(request)
        tool_name = request.data.get('tool_name')
        
        if not tool_name:
            return Response({'error': 'tool_name is required'}, status=status.HTTP_400_BAD_REQUEST)
        
        access_result = N8NPermissionIntegrator.validate_tool_access(user, tool_name)
        return Response(access_result)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def workflow_suggestions(request):
    """Get workflow routing suggestions based on user permissions"""
    try:
        user = get_request_user(request)
        suggestions = N8NPermissionIntegrator.generate_workflow_suggestions(user)
        return Response(suggestions)
    except Exception as e:
        return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 