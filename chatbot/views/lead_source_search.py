"""
Lead Source Search for Chatbot
================================

Simple direct database query for lead sources - no proxy, no authentication.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q

from leads.models import LeadSource


@api_view(['GET'])
@permission_classes([AllowAny])
def search_lead_sources(request):
    """
    Search lead sources by name.
    
    Query Parameters:
        search (str): Search term for lead source name
        
    Returns:
        Paginated list of lead sources matching the search term
    """
    try:
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'count': 0,
                'next': None,
                'previous': None,
                'results': []
            })
        
        # Query lead sources directly from database
        lead_sources = LeadSource.objects.filter(
            Q(name__icontains=search_term)
        ).order_by('name')[:20]  # Limit to 20 results
        
        # Serialize results
        results = []
        for ls in lead_sources:
            # LeadSource -> lead_source_subcategory -> lead_source_category
            category_name = None
            subcategory_name = None
            
            if ls.lead_source_subcategory:
                subcategory_name = ls.lead_source_subcategory.name
                if ls.lead_source_subcategory.lead_source_category:
                    category_name = ls.lead_source_subcategory.lead_source_category.name
            
            results.append({
                'id': ls.leadsource_id,
                'name': ls.name,
                'category': category_name,
                'subcategory': subcategory_name
            })
        
        return Response({
            'count': len(results),
            'next': None,
            'previous': None,
            'results': results
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search lead sources'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

