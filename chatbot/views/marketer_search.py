"""
Marketer Search for Chatbot
============================

Simple direct database query for marketers - no proxy, no authentication.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from django.db.models import Q

from users.models import User


@api_view(['GET'])
@permission_classes([AllowAny])
def search_marketer(request):
    """
    Search marketers (users) by name or employee number.
    
    Query Parameters:
        search (str): Search term for name or employee number
        
    Returns:
        List of users matching the search term
    """
    try:
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'success': True,
                'count': 0,
                'results': []
            })
        
        # Query users directly from database
        users = User.objects.filter(
            Q(first_name__icontains=search_term) |
            Q(last_name__icontains=search_term) |
            Q(employee_no__icontains=search_term) |
            Q(fullnames__icontains=search_term)
        ).filter(is_active=True).select_related('department').order_by('first_name')[:20]
        
        # Serialize results
        results = []
        for user in users:
            results.append({
                'employee_no': user.employee_no,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'fullnames': user.fullnames,
                'department_name': user.department.dp_name if user.department else None
            })
        
        return Response({
            'success': True,
            'count': len(results),
            'results': results
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search marketers'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

