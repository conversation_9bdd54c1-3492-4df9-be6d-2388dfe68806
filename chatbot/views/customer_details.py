"""
Customer Details Views for Chatbot
==================================

This module provides optimized endpoints for complete customer information.
"""

from django.http import JsonResponse
from django.db.models import Sum, Count, Q
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import logging

from customers.models import Customer
from sales.models import LeadFile
from inventory.models import PlotBooking
from leads.models import Prospects, LeadSource
from users.models import User
from django.utils import timezone
import re

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_sales_data(request, customer_no):
    """
    Get comprehensive and efficiently aggregated sales data for a specific customer.
    """
    try:
        # Get customer basic info
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email'
        ).get(customer_no=customer_no)

        # Get sales data - using customer_id since it's the foreign key field
        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        # Aggregate sales data
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy'),
            total_penalties=Sum('penalties_accrued'),
            total_refunds=Sum('refunds')
        )

        # Get detailed sales records with correct field references
        sales_details = sales_query.select_related('project').values(
            'lead_file_no',
            'plot_no',  # Direct field in LeadFile
            'purchase_price',
            'total_paid',
            'balance_lcy',
            'lead_file_status_dropped',
            'project__name'  # Access project name through relationship (field is 'name')
        )[:20]  # Limit to 20 most recent

        return Response({
            'customer': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0),
                'total_penalties': float(sales_summary.get('total_penalties', 0) or 0),
                'total_refunds': float(sales_summary.get('total_refunds', 0) or 0)
            },
            'sales_details': list(sales_details)
        })

    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving sales data for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_title_status(request, customer_no):
    """
    Get title status information for a customer's plots.
    """
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name'
        ).get(customer_no=customer_no)

        # Get plots owned by this customer through sales
        plots_query = LeadFile.objects.filter(
            customer_id__customer_no=customer_no
        ).exclude(plot_no__isnull=True).exclude(plot_no='')

        # Get plot bookings for title status
        plot_numbers = [lf.plot_no for lf in plots_query]
        bookings = PlotBooking.objects.filter(plot_no__in=plot_numbers).values(
            'plot_no', 'status', 'booking_date'
        )

        # Create a mapping of plot_no to booking status
        booking_status_map = {b['plot_no']: b['status'] for b in bookings}

        plots_info = []
        for lf in plots_query:
            plots_info.append({
                'plot_no': lf.plot_no,
                'project': lf.project.project_name if lf.project else 'N/A',
                'title_status': booking_status_map.get(lf.plot_no, 'Not Booked'),
                'purchase_date': lf.date.isoformat() if lf.date else None
            })

        return Response({
            'customer': customer,
            'total_plots': len(plots_info),
            'plots': plots_info
        })

    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving title status for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_360_view(request, customer_no):
    """
    Get comprehensive 360-degree view of customer with all related data.
    """
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email',
            'alternative_phone', 'address', 'country_of_residence'
        ).get(customer_no=customer_no)

        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            active_sales=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy')
        )
        
        plots_owned = sales_query.exclude(plot_no__isnull=True).exclude(plot_no='').count()
        
        title_summary = {
            'plots_owned': plots_owned,
            'titles_processing': plots_owned,
            'titles_ready': 0
        }
        
        return Response({
            'success': True,
            'customer_info': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'active_sales': sales_summary.get('active_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0)
            },
            'title_summary': title_summary,
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/sales/",
                f"/api/chatbot/customer/{customer_no}/title-status/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving customer 360 view for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ==========================================================================
# N8N-COMPATIBLE ENDPOINTS (QUERY PARAMETERS)
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_sales_data_query(request):
    """
    N8N-compatible endpoint: Get customer sales data using query parameter.
    Usage: /api/chatbot/customer-sales-data/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Use the main function with the customer_no
    return customer_sales_data(request, customer_no)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_title_status_query(request):
    """
    N8N-compatible endpoint: Get customer title status using query parameter.
    Usage: /api/chatbot/customer-title-status/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Use the main function with the customer_no
    return customer_title_status(request, customer_no)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_360_view_query(request):
    """
    N8N-compatible endpoint: Get customer 360 view using query parameter.
    Usage: /api/chatbot/customer-360-view/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Check permission to access this customer
    from ..services.permission_filters import PermissionFilterService, extract_filter_params_from_request
    from ..config import ChatbotConfig
    from .utils import get_request_user
    
    if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        user = get_request_user(request)
        filter_params = extract_filter_params_from_request(request)
        
        can_access = PermissionFilterService.can_access_customer(user, customer_no, filter_params)
        if not can_access:
            return Response({
                'success': False,
                'error': 'You do not have permission to view this customer',
                'customer_no': customer_no
            }, status=403)
    
    # Implement the logic directly to avoid DRF request conflicts
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email',
            'alternative_phone', 'address', 'country_of_residence'
        ).get(customer_no=customer_no)

        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            active_sales=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy')
        )
        
        plots_owned = sales_query.exclude(plot_no__isnull=True).exclude(plot_no='').count()
        
        title_summary = {
            'plots_owned': plots_owned,
            'titles_processing': plots_owned,
            'titles_ready': 0
        }
        
        return Response({
            'success': True,
            'customer_info': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'active_sales': sales_summary.get('active_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0)
            },
            'title_summary': title_summary,
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/sales/",
                f"/api/chatbot/customer/{customer_no}/title-status/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving customer 360 view for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# PROSPECT CREATION (Consolidated from create_prospect.py)
# ==========================================================================

def format_phone_number(phone):
    """Format phone number to E.164 format"""
    if not phone:
        return ''
    
    # Remove all non-digit characters
    phone = re.sub(r'\D', '', phone)
    
    # Handle Kenyan numbers
    if phone.startswith('254'):
        return f'+{phone}'
    elif phone.startswith('0'):
        return f'+254{phone[1:]}'
    elif phone.startswith('7') or phone.startswith('1'):
        return f'+254{phone}'
    else:
        return f'+{phone}'


@api_view(['POST'])
@permission_classes([AllowAny])
def create_prospect(request):
    """
    Create a new prospect directly in the database.
    
    Request Body:
        name (str, required): Prospect name
        phone (str, required): Phone number
        email (str, optional): Email address
        comment (str, optional): Notes/comments
        country (str, optional): Country, defaults to 'Kenya'
        city (str, optional): City/location
        lead_source (int, optional): Lead source ID
        marketer (str, optional): Marketer employee number
        lead_type (str, optional): Lead type, defaults to 'personal'
        status (str, optional): Status, defaults to 'Active'
        category (str, optional): Category, defaults to 'Warm'
        pipeline_level (str, optional): Pipeline level, defaults to 'Nurturing'
        
    Returns:
        Success message with created prospect details
    """
    try:
        # Get request data
        data = request.data
        
        # Validate required fields
        name = data.get('name', '').strip()
        phone = data.get('phone', '').strip()
        
        if not name:
            return Response({
                'success': False,
                'error': 'Name is required',
                'message': 'Please provide a prospect name'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        if not phone:
            return Response({
                'success': False,
                'error': 'Phone number is required',
                'message': 'Please provide a phone number'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Format phone number
        phone_formatted = format_phone_number(phone)
        
        # Check for duplicate phone number
        existing_prospect = Prospects.objects.filter(phone=phone_formatted).first()
        if existing_prospect:
            return Response({
                'success': False,
                'error': 'Phone number already exists',
                'message': f'A prospect with phone {phone_formatted} already exists',
                'existing_prospect': {
                    'id': existing_prospect.id,
                    'name': existing_prospect.name,
                    'phone': existing_prospect.phone,
                    'email': existing_prospect.email or ''
                }
            }, status=status.HTTP_409_CONFLICT)
        
        # Get lead source if provided
        lead_source = None
        lead_source_category_id = None
        lead_source_subcategory_id = None
        
        lead_source_id = data.get('lead_source')
        if lead_source_id:
            try:
                lead_source = LeadSource.objects.select_related(
                    'lead_source_subcategory__lead_source_category'
                ).get(leadsource_id=lead_source_id)
                
                # LeadSource -> lead_source_subcategory -> lead_source_category
                if lead_source.lead_source_subcategory:
                    lead_source_subcategory_id = lead_source.lead_source_subcategory.cat_lead_source_id
                    if lead_source.lead_source_subcategory.lead_source_category:
                        lead_source_category_id = lead_source.lead_source_subcategory.lead_source_category.category_id
            except LeadSource.DoesNotExist:
                pass
        
        # Get marketer if provided
        marketer = None
        marketer_employee_no = data.get('marketer', '').strip()
        if marketer_employee_no:
            try:
                marketer = User.objects.get(employee_no=marketer_employee_no, is_active=True)
            except User.DoesNotExist:
                return Response({
                    'success': False,
                    'error': f'Marketer with employee number {marketer_employee_no} not found',
                    'message': 'Invalid marketer employee number'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        # Prepare prospect data
        prospect_data = {
            'name': name,
            'phone': phone_formatted,
            'lead_source': lead_source,
            'lead_source_category_id': lead_source_category_id,
            'lead_source_subcategory_id': lead_source_subcategory_id,
            'email': data.get('email', ''),
            'comment': data.get('comment', ''),
            'country': data.get('country', 'Kenya'),
            'city': data.get('city', ''),
            'marketer': marketer,
            'lead_type': data.get('lead_type', 'personal'),
            'department': None,
            'status': data.get('status', 'Active'),
            'category': data.get('category', 'Warm'),
            'pipeline_level': data.get('pipeline_level', 'Nurturing'),
            'date': timezone.now()
        }
        
        # Create prospect
        prospect = Prospects.objects.create(**prospect_data)
        
        # Prepare response
        response_data = {
            'prospect_id': prospect.id,
            'name': prospect.name,
            'phone': prospect.phone,
            'email': prospect.email,
            'marketer': marketer.fullnames if marketer else 'Not assigned',
            'lead_source': lead_source.name if lead_source else 'Not specified',
            'comment': prospect.comment,
            'created_at': prospect.date.isoformat()
        }
        
        return Response({
            'success': True,
            'message': f'Prospect {name} created successfully!',
            'data': response_data
        }, status=status.HTTP_201_CREATED)
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to create prospect'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
