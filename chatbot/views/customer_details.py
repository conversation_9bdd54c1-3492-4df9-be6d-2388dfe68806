"""
Customer Details Views for Chatbot
==================================

This module provides optimized endpoints for complete customer information.
"""

from django.http import JsonResponse
from django.db.models import Sum, Count, Q
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import logging

from customers.models import Customer
from sales.models import LeadFile
from inventory.models import PlotBooking

logger = logging.getLogger(__name__)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_sales_data(request, customer_no):
    """
    Get comprehensive and efficiently aggregated sales data for a specific customer.
    """
    try:
        # Get customer basic info
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email'
        ).get(customer_no=customer_no)

        # Get sales data - using customer_id since it's the foreign key field
        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        # Aggregate sales data
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy'),
            total_penalties=Sum('penalties_accrued'),
            total_refunds=Sum('refunds')
        )

        # Get detailed sales records with correct field references
        sales_details = sales_query.select_related('project').values(
            'lead_file_no',
            'plot_no',  # Direct field in LeadFile
            'project__name',  # Through ForeignKey to Project
            'purchase_price',
            'selling_price',
            'balance_lcy',
            'purchase_type',
            'total_paid',
            'completion_date',
            'booking_date',
            'lead_file_status_dropped'
        )

        response_data = {
            'customer': customer,
            'sales_summary': sales_summary,
            'sales_details': list(sales_details)
        }

        return Response({
            'success': True,
            'data': response_data
        })

    except Customer.DoesNotExist:
        return Response({
            'success': False,
            'error': f'Customer with number {customer_no} not found'
        }, status=404)
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e)
        }, status=500)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_title_status(request, customer_no):
    """
    Get optimized title status information for a specific customer.
    """
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email'
        ).get(customer_no=customer_no)

        # Get plot information from LeadFile (sales_leadfile) - the correct source for customer plots
        leadfiles = LeadFile.objects.filter(
            customer_id__customer_no=customer_no
        ).select_related('project').values(
            'lead_file_no',
            'plot_no',
            'project__name',
            'purchase_price',
            'total_paid',
            'balance_lcy',
            'completion_date',
            'lead_file_status_dropped',
            'purchase_type'
        ).order_by('-lead_file_no')

        title_data = [
            {
                'lead_file_no': lf['lead_file_no'],
                'plot_no': lf['plot_no'],
                'project': lf.get('project__name', 'N/A'),
                'purchase_price': lf.get('purchase_price', 0),
                'total_paid': lf.get('total_paid', 0),
                'balance': lf.get('balance_lcy', 0),
                'completion_date': lf.get('completion_date'),
                'status': 'Active' if not lf['lead_file_status_dropped'] else 'Dropped',
                'purchase_type': lf.get('purchase_type', 'N/A'),
            }
            for lf in leadfiles
        ]
        
        summary = {
            'total_plots': len(title_data),
            'active_sales': sum(1 for t in title_data if t['status'] == 'Active'),
            'dropped_sales': sum(1 for t in title_data if t['status'] == 'Dropped'),
            'total_purchase_value': sum(t['purchase_price'] for t in title_data),
            'total_paid': sum(t['total_paid'] for t in title_data),
            'total_balance': sum(t['balance'] for t in title_data)
        }

        return Response({
            'success': True,
            'customer': customer,
            'title_summary': summary,
            'title_data': title_data
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving title status for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_360_view(request, customer_no):
    """
    Get an optimized 360-degree view of a customer.
    """
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email',
            'alternative_phone', 'address', 'country_of_residence'
        ).get(customer_no=customer_no)

        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            active_sales=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy')
        )
        
        plots_owned = sales_query.exclude(plot_no__isnull=True).exclude(plot_no='').count()
        
        title_summary = {
            'plots_owned': plots_owned,
            'titles_processing': plots_owned,
            'titles_ready': 0
        }
        
        return Response({
            'success': True,
            'customer_info': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'active_sales': sales_summary.get('active_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0)
            },
            'title_summary': title_summary,
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/sales/",
                f"/api/chatbot/customer/{customer_no}/title-status/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving customer 360 view for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# ==========================================================================
# N8N-COMPATIBLE ENDPOINTS (QUERY PARAMETERS)
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_sales_data_query(request):
    """
    N8N-compatible endpoint: Get customer sales data using query parameter.
    Usage: /api/chatbot/customer-sales-data/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Implement the logic directly to avoid DRF request conflicts
    try:
        # Get customer basic info
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email'
        ).get(customer_no=customer_no)

        # Get sales data - using customer_id since it's the foreign key field
        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        # Aggregate sales data
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy'),
            total_penalties=Sum('penalties_accrued'),
            total_refunds=Sum('refunds')
        )

        # Get detailed sales records with correct field references
        sales_details = sales_query.select_related('project').values(
            'lead_file_no',
            'plot_no',  # Direct field in LeadFile
            'project__name',  # Related field from Project
            'project__initials',  # Related field from Project
            'purchase_price',
            'selling_price',
            'balance_lcy',
            'total_paid',
            'purchase_type',
            'completion_date',
            'lead_file_status_dropped'
        )[:10]  # Limit for performance

        return Response({
            'success': True,
            'customer_info': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0),
                'total_penalties': float(sales_summary.get('total_penalties', 0) or 0),
                'total_refunds': float(sales_summary.get('total_refunds', 0) or 0)
            },
            'sales_details': list(sales_details),
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/title-status/",
                f"/api/chatbot/customer/{customer_no}/360/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving sales data for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_title_status_query(request):
    """
    N8N-compatible endpoint: Get customer title status using query parameter.
    Usage: /api/chatbot/customer-title-status/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Implement the logic directly to avoid DRF request conflicts
    try:
        # Get customer basic info
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email'
        ).get(customer_no=customer_no)

        # Get lead files for title status
        lead_files = LeadFile.objects.filter(customer_id__customer_no=customer_no).values(
            'lead_file_no',
            'plot_no',
            'project__name',
            'title_status',
            'purchase_price',
            'balance_lcy',
            'lead_file_status_dropped'
        )

        # Calculate title summary
        total_plots = lead_files.count()
        active_plots = lead_files.filter(lead_file_status_dropped=False).count()

        return Response({
            'success': True,
            'customer_info': customer,
            'title_summary': {
                'total_plots': total_plots,
                'active_plots': active_plots,
                'titles_processing': total_plots,  # Simplified for now
                'titles_ready': 0  # Simplified for now
            },
            'title_details': list(lead_files),
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/sales/",
                f"/api/chatbot/customer/{customer_no}/360/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving title status for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def customer_360_view_query(request):
    """
    N8N-compatible endpoint: Get customer 360 view using query parameter.
    Usage: /api/chatbot/customer-360-view/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=400)
    
    # Implement the logic directly to avoid DRF request conflicts
    try:
        customer = Customer.objects.values(
            'customer_no', 'customer_name', 'phone', 'primary_email',
            'alternative_phone', 'address', 'country_of_residence'
        ).get(customer_no=customer_no)

        sales_query = LeadFile.objects.filter(customer_id__customer_no=customer_no)
        
        sales_summary = sales_query.aggregate(
            total_sales=Count('lead_file_no'),
            active_sales=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
            total_purchase_price=Sum('purchase_price'),
            total_paid=Sum('total_paid'),
            total_balance=Sum('balance_lcy')
        )
        
        plots_owned = sales_query.exclude(plot_no__isnull=True).exclude(plot_no='').count()
        
        title_summary = {
            'plots_owned': plots_owned,
            'titles_processing': plots_owned,
            'titles_ready': 0
        }
        
        return Response({
            'success': True,
            'customer_info': customer,
            'sales_summary': {
                'total_sales': sales_summary.get('total_sales', 0),
                'active_sales': sales_summary.get('active_sales', 0),
                'total_purchase_price': float(sales_summary.get('total_purchase_price', 0) or 0),
                'total_paid': float(sales_summary.get('total_paid', 0) or 0),
                'total_balance': float(sales_summary.get('total_balance', 0) or 0)
            },
            'title_summary': title_summary,
            'quick_actions': [
                f"/api/chatbot/customer/{customer_no}/sales/",
                f"/api/chatbot/customer/{customer_no}/title-status/",
                f"/api/chatbot/export/sales-csv/?customer_no={customer_no}"
            ]
        })
        
    except Customer.DoesNotExist:
        return Response({'success': False, 'message': f'Customer {customer_no} not found'}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.exception(f"Error retrieving customer 360 view for customer {customer_no}")
        return Response({'success': False, 'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR) 