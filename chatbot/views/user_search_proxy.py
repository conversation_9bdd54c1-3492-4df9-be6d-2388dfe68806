"""
User/Marketer Search Proxy with Domain Whitelisting
===================================================

Chatbot-specific proxy for searching users/marketers by name or employee number.
Only accepts requests from automations.optiven.co.ke (n8n server).
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from django.core.exceptions import PermissionDenied
from django.db.models import Q
from users.models import User


def is_request_from_n8n(request):
    """
    Check if request comes from the authorized n8n server.
    
    Checks:
    1. HTTP_HOST header
    2. HTTP_ORIGIN header
    3. HTTP_REFERER header
    4. X-Forwarded-Host header (for reverse proxy setups)
    """
    allowed_domains = [
        'automations.optiven.co.ke',
        'localhost',  # For testing
        '127.0.0.1',  # For testing
    ]
    
    # Check various headers that could indicate the source
    host = request.META.get('HTTP_HOST', '')
    origin = request.META.get('HTTP_ORIGIN', '')
    referer = request.META.get('HTTP_REFERER', '')
    forwarded_host = request.META.get('HTTP_X_FORWARDED_HOST', '')
    
    # Extract domain from URLs
    def extract_domain(url):
        if not url:
            return ''
        # Remove protocol
        url = url.replace('https://', '').replace('http://', '')
        # Remove path
        url = url.split('/')[0]
        # Remove port
        url = url.split(':')[0]
        return url
    
    # Extract domains from all headers
    host_domain = extract_domain(host)
    origin_domain = extract_domain(origin)
    referer_domain = extract_domain(referer)
    forwarded_domain = extract_domain(forwarded_host)
    
    # Check if any of the domains match
    for domain in allowed_domains:
        if domain in [host_domain, origin_domain, referer_domain, forwarded_domain]:
            return True
    
    return False


@api_view(['GET'])
@permission_classes([AllowAny])
def search_users_proxy(request):
    """
    Proxy endpoint for user/marketer search with domain whitelisting.
    
    Security: Only accepts requests from automations.optiven.co.ke (n8n server)
    
    Query Parameters:
        search (str): Search term for user name or employee number
        
    Returns:
        List of matching users with employee_no, names, and department
    """
    try:
        # Check if request is from authorized n8n server
        if not is_request_from_n8n(request):
            raise PermissionDenied(
                "Access denied. This endpoint is only accessible from authorized automation servers."
            )
        
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'count': 0,
                'results': [],
                'message': 'Please provide a search term (name or employee number)'
            })
        
        # Search users by name or employee number (case-insensitive)
        users = User.objects.filter(
            Q(first_name__icontains=search_term) |
            Q(last_name__icontains=search_term) |
            Q(employee_no__icontains=search_term) |
            Q(email__icontains=search_term)
        ).filter(
            is_active=True  # Only active users
        ).select_related('department').order_by('first_name', 'last_name')[:20]  # Limit to 20 results
        
        # Serialize results
        results = []
        for user in users:
            results.append({
                'employee_no': user.employee_no,
                'name': f"{user.first_name} {user.last_name}".strip(),
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
                'department': user.department.dp_name if user.department else None,
                'is_active': user.is_active
            })
        
        # Return results
        return Response({
            'count': len(results),
            'results': results,
            'search_term': search_term
        })
        
    except PermissionDenied as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Authentication failed'
        }, status=403)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search users'
        }, status=500)

