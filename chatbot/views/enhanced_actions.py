"""
Enhanced Action Endpoints with Multi-Entity Support
=================================================

Enhanced versions of the action endpoints that support customers, prospects, and leadfiles.
"""

from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import json
import logging

from ..config import ChatbotConfig
from .utils import get_request_user

logger = logging.getLogger(__name__)


# Dynamic permission class based on bypass mode
def get_permission_classes():
    """Get permission classes based on bypass mode"""
    if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        return [AllowAny]
    else:
        return [AllowAny]


@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_engagement_endpoint(request):
    """Direct endpoint for creating engagements (N8N integration) - Multi-entity support"""
    user = get_request_user(request)
    
    try:
        data = json.loads(request.body) if request.body else {}
        
        # Extract engagement data from request body (supports customers, prospects, leadfiles)
        engagement_data = {
            'customer_no': data.get('customer_no'),
            'prospect_id': data.get('prospect_id'),
            'lead_file_no': data.get('lead_file_no'),
            'client_type': data.get('client_type'),
            'engagement_type': data.get('engagement_type', 'Call'),
            'description': data.get('description', ''),
            'subject': data.get('subject', data.get('title', 'Chatbot Engagement'))
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_engagement(user, engagement_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create engagement endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create engagement failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_reminder_endpoint(request):
    """Direct endpoint for creating reminders (N8N integration) - Multi-entity support"""
    user = get_request_user(request)
    
    try:
        data = json.loads(request.body) if request.body else {}
        
        # Extract reminder data from request body (supports customers, prospects, leadfiles)
        reminder_data = {
            'customer_no': data.get('customer_no'),
            'prospect_id': data.get('prospect_id'),
            'lead_file_no': data.get('lead_file_no'),
            'client_type': data.get('client_type'),
            'title': data.get('title', 'Chatbot Reminder'),
            'description': data.get('description', ''),
            'reminder_notes': data.get('description', ''),
            'due_date': data.get('due_date'),
            'reminder_date': data.get('due_date'),
            'priority': data.get('priority', 'Normal'),
            'reminder_type': data.get('reminder_type', 'General')
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_reminder(user, reminder_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create reminder endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create reminder failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes(get_permission_classes())
def create_note_endpoint(request):
    """Direct endpoint for creating notes (N8N integration) - Multi-entity support"""
    user = get_request_user(request)
    
    try:
        data = json.loads(request.body) if request.body else {}
        
        # Extract note data from request body (supports customers, prospects, leadfiles)
        note_data = {
            'customer_no': data.get('customer_no'),
            'prospect_id': data.get('prospect_id'),
            'lead_file_no': data.get('lead_file_no'),
            'client_type': data.get('client_type'),
            'title': data.get('title', 'Chatbot Note'),
            'content': data.get('content', ''),
            'note_type': data.get('note_type', 'General')
        }
        
        from ..services.actions import ChatbotActionService
        action_service = ChatbotActionService()
        result = action_service.create_note(user, note_data)
        result['bypass_mode'] = ChatbotConfig.BYPASS_ALL_PERMISSIONS
        
        return Response(result)
        
    except Exception as e:
        logger.error(f"Create note endpoint error: {str(e)}")
        return Response({
            'success': False,
            'error': 'Create note failed',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
