"""
Optimized Chatbot Search Views
============================

High-performance search implementation that utilizes the centralized
ChatbotSearchService for all search operations.
"""

import logging
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from customers.models import Customer
from leads.models import Prospects
from ..services.search import ChatbotSearchService
from .utils import get_request_user

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@cache_page(60)
def search_unified(request):
    """
    Unified search endpoint that delegates to ChatbotSearchService.
    Supports both GET and POST to accept filter parameters from n8n.
    """
    try:
        query = request.GET.get('q', '').strip()
        search_type = request.GET.get('type', 'all').lower()
        limit = min(int(request.GET.get('limit', 10)), 50)
        user = get_request_user(request)

        if not query:
            return Response({
                'error': 'Search query is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Extract filter parameters from request
        from ..services.permission_filters import extract_filter_params_from_request
        filter_params = extract_filter_params_from_request(request)

        search_service = ChatbotSearchService()
        
        entity_types = []
        if search_type == 'all':
            entity_types = ['customers', 'prospects', 'leadfiles', 'users', 'plots', 'projects']
        elif search_type in ['customers', 'prospects', 'leadfiles', 'users', 'plots', 'projects']:
            entity_types = [search_type]

        results = search_service.search(
            query, 
            user=user, 
            entity_types=entity_types, 
            limit=limit,
            filter_params=filter_params
        )
        
        return Response(results)

    except Exception as e:
        logger.exception("An error occurred during unified search")
        return Response({
            'error': f'An unexpected error occurred: {str(e)}',
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_360_view(request, customer_no):
    """
    Get comprehensive customer view with all related data.
    Optimized with prefetch_related for related objects.
    """
    try:
        # Use prefetch_related to efficiently load related objects
        customer = Customer.objects.select_related('marketer', 'lead_source').prefetch_related(
            'lead_file_customer',  # Related leadfiles
            'lead_file_customer__marketer'  # Leadfile marketers
        ).get(customer_no=customer_no)
        
        # Get all leadfiles for this customer
        leadfiles = customer.lead_file_customer.all()
        
        # Get prospect if this customer was converted from one
        prospect = None
        if customer.customer_no:
            try:
                prospect = Prospects.objects.select_related('lead_source', 'marketer').get(
                    customer=customer.customer_no
                )
            except Prospects.DoesNotExist:
                pass
        
        response_data = {
            'customer': {
                'customer_no': customer.customer_no,
                'name': customer.customer_name,
                'phone': customer.phone,
                'email': customer.primary_email,
                'marketer_name': customer.marketer.fullnames if customer.marketer else 'N/A',
                'lead_source': customer.lead_source.name if customer.lead_source else 'N/A',
                'registration_date': customer.date_of_registration.isoformat() if customer.date_of_registration else None
            },
            'leadfiles': [
                {
                    'lead_file_no': lf.lead_file_no,
                    'status': 'Dropped' if lf.lead_file_status_dropped else 'Active',
                    'plot_number': lf.plot_no,
                    'purchase_price': float(lf.purchase_price),
                    'balance': float(lf.balance_lcy),
                    'total_paid': float(lf.total_paid),
                    'marketer_name': lf.marketer.fullnames if lf.marketer else 'N/A'
                }
                for lf in leadfiles
            ],
            'prospect_origin': {
                'id': prospect.id,
                'lead_source': prospect.lead_source.name,
                'marketer': prospect.marketer.fullnames if prospect.marketer else 'N/A',
                'date': prospect.date.isoformat()
            } if prospect else None,
            'summary': {
                'total_leadfiles': len(leadfiles),
                'active_leadfiles': len([lf for lf in leadfiles if not lf.lead_file_status_dropped]),
                'total_investment': sum(float(lf.purchase_price) for lf in leadfiles),
                'total_paid': sum(float(lf.total_paid) for lf in leadfiles),
                'total_balance': sum(float(lf.balance_lcy) for lf in leadfiles)
            }
        }
        
        return Response(response_data)
    
    except Customer.DoesNotExist:
        return Response({
            'error': f'Customer {customer_no} not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception:
        logger.exception(f"An error occurred in customer 360 view for customer: {customer_no}")
        return Response({
            'error': 'An unexpected error occurred while retrieving customer data.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def quick_search(request):
    """
    Ultra-fast search that delegates to the search service with a small limit.
    """
    query = request.GET.get('q', '').strip()
    user = get_request_user(request)
    if len(query) < 2:
        return Response({'results': []})

    search_service = ChatbotSearchService()
    results = search_service.search(query, user=user, entity_types=['customers', 'leadfiles'], limit=5)

    # Format for display
    display_results = []
    if 'customers' in results.get('results', {}):
        for customer in results['results']['customers']:
            display_results.append({
                'type': 'customer',
                'id': customer.get('customer_no'),
                'display': f"{customer.get('customer_name')} ({customer.get('customer_no')})"
            })

    if 'leadfiles' in results.get('results', {}):
        for leadfile in results['results']['leadfiles']:
            display_results.append({
                'type': 'leadfile',
                'id': leadfile.get('lead_file_no'),
                'display': f"{leadfile.get('customer_name')} - {leadfile.get('lead_file_no')}"
            })

    return Response({'results': display_results[:10]})

# Legacy support for old API endpoints
def simple_test(request):
    """Simple test endpoint for health checks"""
    return JsonResponse({
        'status': 'OK',
        'message': 'Chatbot search service is running',
        'version': '2.0'
    })

def health_check(request):
    """Health check endpoint"""
    return JsonResponse({
        'status': 'OK',
        'message': 'Health check passed'
    })

def user_info(request):
    """Get current user info for chatbot personalization"""
    if request.user.is_authenticated:
        return JsonResponse({
            'user_id': request.user.employee_no,
            'full_name': request.user.fullnames,
            'email': request.user.email,
            'department': request.user.department.dp_name if request.user.department else None,
            'team': request.user.team.team if request.user.team else None
        })
    return JsonResponse({
        'error': 'Not authenticated'
    }, status=401)

def debug_search(request):
    """Debug endpoint for testing search performance"""
    query = request.GET.get('q', '')
    search_type = request.GET.get('type', 'all')
    
    import time
    start_time = time.time()
    
    # Perform search
    if search_type == 'customers':
        results = search_customers_optimized(query, 10)
    elif search_type == 'prospects':
        results = search_prospects_optimized(query, 10)
    elif search_type == 'leadfiles':
        results = search_leadfiles_optimized(query, 10)
    else:
        results = []
    
    end_time = time.time()
    
    return JsonResponse({
        'query': query,
        'type': search_type,
        'results': results,
        'performance': {
            'execution_time_ms': round((end_time - start_time) * 1000, 2),
            'result_count': len(results)
        }
    })
