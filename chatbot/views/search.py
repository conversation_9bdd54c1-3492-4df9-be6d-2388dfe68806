"""
Optimized Chatbot Search Views
============================

High-performance search implementation that utilizes the centralized
ChatbotSearchService for all search operations.
"""

import logging
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from customers.models import Customer
from leads.models import Prospects
from users.models import User
from django.db.models import Q
from django.core.exceptions import PermissionDenied
from ..services.search import ChatbotSearchService
from .utils import get_request_user

logger = logging.getLogger(__name__)


@api_view(['GET', 'POST'])
@permission_classes([AllowAny])
@cache_page(60)
def search_unified(request):
    """
    Unified search endpoint that delegates to ChatbotSearchService.
    Supports both GET and POST to accept filter parameters from n8n.
    """
    try:
        query = request.GET.get('q', '').strip()
        search_type = request.GET.get('type', 'all').lower()
        limit = min(int(request.GET.get('limit', 10)), 50)
        user = get_request_user(request)

        if not query:
            return Response({
                'error': 'Search query is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Extract filter parameters from request
        from ..services.permission_filters import extract_filter_params_from_request
        filter_params = extract_filter_params_from_request(request)

        search_service = ChatbotSearchService()
        
        entity_types = []
        if search_type == 'all':
            entity_types = ['customers', 'prospects', 'leadfiles', 'users', 'plots', 'projects']
        elif search_type in ['customers', 'prospects', 'leadfiles', 'users', 'plots', 'projects']:
            entity_types = [search_type]

        results = search_service.search(
            query, 
            user=user, 
            entity_types=entity_types, 
            limit=limit,
            filter_params=filter_params
        )
        
        return Response(results)

    except Exception as e:
        logger.exception("An error occurred during unified search")
        return Response({
            'error': f'An unexpected error occurred: {str(e)}',
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_360_view(request, customer_no):
    """
    Get comprehensive customer view with all related data.
    Optimized with prefetch_related for related objects.
    """
    try:
        # Use prefetch_related to efficiently load related objects
        customer = Customer.objects.select_related('marketer', 'lead_source').prefetch_related(
            'lead_file_customer',  # Related leadfiles
            'lead_file_customer__marketer'  # Leadfile marketers
        ).get(customer_no=customer_no)
        
        # Get all leadfiles for this customer
        leadfiles = customer.lead_file_customer.all()
        
        # Get prospect if this customer was converted from one
        prospect = None
        if customer.customer_no:
            try:
                prospect = Prospects.objects.select_related('lead_source', 'marketer').get(
                    customer=customer.customer_no
                )
            except Prospects.DoesNotExist:
                pass
        
        response_data = {
            'customer': {
                'customer_no': customer.customer_no,
                'name': customer.customer_name,
                'phone': customer.phone,
                'email': customer.primary_email,
                'marketer_name': customer.marketer.fullnames if customer.marketer else 'N/A',
                'lead_source': customer.lead_source.name if customer.lead_source else 'N/A',
                'registration_date': customer.date_of_registration.isoformat() if customer.date_of_registration else None
            },
            'leadfiles': [
                {
                    'lead_file_no': lf.lead_file_no,
                    'status': 'Dropped' if lf.lead_file_status_dropped else 'Active',
                    'plot_number': lf.plot_no,
                    'purchase_price': float(lf.purchase_price),
                    'balance': float(lf.balance_lcy),
                    'total_paid': float(lf.total_paid),
                    'marketer_name': lf.marketer.fullnames if lf.marketer else 'N/A'
                }
                for lf in leadfiles
            ],
            'prospect_origin': {
                'id': prospect.id,
                'lead_source': prospect.lead_source.name,
                'marketer': prospect.marketer.fullnames if prospect.marketer else 'N/A',
                'date': prospect.date.isoformat()
            } if prospect else None,
            'summary': {
                'total_leadfiles': len(leadfiles),
                'active_leadfiles': len([lf for lf in leadfiles if not lf.lead_file_status_dropped]),
                'total_investment': sum(float(lf.purchase_price) for lf in leadfiles),
                'total_paid': sum(float(lf.total_paid) for lf in leadfiles),
                'total_balance': sum(float(lf.balance_lcy) for lf in leadfiles)
            }
        }
        
        return Response(response_data)
    
    except Customer.DoesNotExist:
        return Response({
            'error': f'Customer {customer_no} not found'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception:
        logger.exception(f"An error occurred in customer 360 view for customer: {customer_no}")
        return Response({
            'error': 'An unexpected error occurred while retrieving customer data.'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
def quick_search(request):
    """
    Ultra-fast search that delegates to the search service with a small limit.
    """
    query = request.GET.get('q', '').strip()
    user = get_request_user(request)
    if len(query) < 2:
        return Response({'results': []})

    search_service = ChatbotSearchService()
    results = search_service.search(query, user=user, entity_types=['customers', 'leadfiles'], limit=5)

    # Format for display
    display_results = []
    if 'customers' in results.get('results', {}):
        for customer in results['results']['customers']:
            display_results.append({
                'type': 'customer',
                'id': customer.get('customer_no'),
                'display': f"{customer.get('customer_name')} ({customer.get('customer_no')})"
            })

    if 'leadfiles' in results.get('results', {}):
        for leadfile in results['results']['leadfiles']:
            display_results.append({
                'type': 'leadfile',
                'id': leadfile.get('lead_file_no'),
                'display': f"{leadfile.get('customer_name')} - {leadfile.get('lead_file_no')}"
            })

    return Response({'results': display_results[:10]})

# ==========================================================================
# ADDITIONAL SEARCH ENDPOINTS (Consolidated from separate files)
# ==========================================================================

def debug_search(request):
    """Debug endpoint for testing search performance"""
    query = request.GET.get('q', '')
    search_type = request.GET.get('type', 'all')
    
    import time
    start_time = time.time()
    
    # Perform search
    if search_type == 'customers':
        results = search_customers_optimized(query, 10)
    elif search_type == 'prospects':
        results = search_prospects_optimized(query, 10)
    elif search_type == 'leadfiles':
        results = search_leadfiles_optimized(query, 10)
    else:
        results = []
    
    end_time = time.time()
    
    return JsonResponse({
        'query': query,
        'type': search_type,
        'results': results,
        'performance': {
            'execution_time_ms': round((end_time - start_time) * 1000, 2),
            'result_count': len(results)
        }
    })


# ==========================================================================
# MARKETER SEARCH (Consolidated from marketer_search.py)
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def search_marketer(request):
    """
    Search marketers (users) by name or employee number.
    
    Query Parameters:
        search (str): Search term for name or employee number
        
    Returns:
        List of users matching the search term
    """
    try:
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'success': True,
                'count': 0,
                'results': []
            })
        
        # Query users directly from database
        users = User.objects.filter(
            Q(first_name__icontains=search_term) |
            Q(last_name__icontains=search_term) |
            Q(employee_no__icontains=search_term) |
            Q(fullnames__icontains=search_term)
        ).filter(is_active=True).select_related('department').order_by('first_name')[:20]
        
        # Serialize results
        results = []
        for user in users:
            results.append({
                'employee_no': user.employee_no,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'fullnames': user.fullnames,
                'department_name': user.department.dp_name if user.department else None
            })
        
        return Response({
            'success': True,
            'count': len(results),
            'results': results
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search marketers'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# LEAD SOURCE SEARCH (Consolidated from lead_source_search.py)
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def search_lead_sources(request):
    """
    Search lead sources by name.
    
    Query Parameters:
        search (str): Search term for lead source name
        
    Returns:
        Paginated list of lead sources matching the search term
    """
    try:
        from leads.models import LeadSource
        
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'count': 0,
                'next': None,
                'previous': None,
                'results': []
            })
        
        # Query lead sources directly from database
        lead_sources = LeadSource.objects.filter(
            Q(name__icontains=search_term)
        ).order_by('name')[:20]  # Limit to 20 results
        
        # Serialize results
        results = []
        for ls in lead_sources:
            # LeadSource -> lead_source_subcategory -> lead_source_category
            category_name = None
            subcategory_name = None
            
            if ls.lead_source_subcategory:
                subcategory_name = ls.lead_source_subcategory.name
                if ls.lead_source_subcategory.lead_source_category:
                    category_name = ls.lead_source_subcategory.lead_source_category.name
            
            results.append({
                'id': ls.leadsource_id,
                'name': ls.name,
                'category': category_name,
                'subcategory': subcategory_name
            })
        
        return Response({
            'count': len(results),
            'next': None,
            'previous': None,
            'results': results
        })
    
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search lead sources'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# USER SEARCH PROXY (Consolidated from user_search_proxy.py)
# ==========================================================================

def is_request_from_n8n(request):
    """
    Check if request comes from the authorized n8n server.
    
    Checks:
    1. HTTP_HOST header
    2. HTTP_ORIGIN header
    3. HTTP_REFERER header
    4. X-Forwarded-Host header (for reverse proxy setups)
    """
    allowed_domains = [
        'automations.optiven.co.ke',
        'localhost',  # For testing
        '127.0.0.1',  # For testing
    ]
    
    # Check various headers that could indicate the source
    host = request.META.get('HTTP_HOST', '')
    origin = request.META.get('HTTP_ORIGIN', '')
    referer = request.META.get('HTTP_REFERER', '')
    forwarded_host = request.META.get('HTTP_X_FORWARDED_HOST', '')
    
    # Extract domain from URLs
    def extract_domain(url):
        if not url:
            return ''
        # Remove protocol
        url = url.replace('https://', '').replace('http://', '')
        # Remove path
        url = url.split('/')[0]
        # Remove port
        url = url.split(':')[0]
        return url
    
    # Extract domains from all headers
    host_domain = extract_domain(host)
    origin_domain = extract_domain(origin)
    referer_domain = extract_domain(referer)
    forwarded_domain = extract_domain(forwarded_host)
    
    # Check if any of the domains match
    for domain in allowed_domains:
        if domain in [host_domain, origin_domain, referer_domain, forwarded_domain]:
            return True
    
    return False


@api_view(['GET'])
@permission_classes([AllowAny])
def search_users_proxy(request):
    """
    Proxy endpoint for user/marketer search with domain whitelisting.
    
    Security: Only accepts requests from automations.optiven.co.ke (n8n server)
    
    Query Parameters:
        search (str): Search term for user name or employee number
        
    Returns:
        List of matching users with employee_no, names, and department
    """
    try:
        # Check if request is from authorized n8n server
        if not is_request_from_n8n(request):
            raise PermissionDenied(
                "Access denied. This endpoint is only accessible from authorized automation servers."
            )
        
        # Get search parameter
        search_term = request.query_params.get('search', '').strip()
        
        if not search_term:
            return Response({
                'count': 0,
                'results': [],
                'message': 'Please provide a search term (name or employee number)'
            })
        
        # Search users by name or employee number (case-insensitive)
        users = User.objects.filter(
            Q(first_name__icontains=search_term) |
            Q(last_name__icontains=search_term) |
            Q(employee_no__icontains=search_term) |
            Q(email__icontains=search_term)
        ).filter(
            is_active=True  # Only active users
        ).select_related('department').order_by('first_name', 'last_name')[:20]  # Limit to 20 results
        
        # Serialize results
        results = []
        for user in users:
            results.append({
                'employee_no': user.employee_no,
                'name': f"{user.first_name} {user.last_name}".strip(),
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
                'department': user.department.dp_name if user.department else None,
                'is_active': user.is_active
            })
        
        # Return results
        return Response({
            'count': len(results),
            'results': results,
            'search_term': search_term
        })
        
    except PermissionDenied as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Authentication failed'
        }, status=403)
        
    except Exception as e:
        return Response({
            'success': False,
            'error': str(e),
            'message': 'Failed to search users'
        }, status=500)
