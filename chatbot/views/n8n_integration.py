"""
N8N Integration Views (Seamless Access)
======================================

Streamlined endpoints for N8N workflow integration.
No permission barriers - authentication is sufficient for full access.
"""

from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth import get_user_model
from django.utils import timezone
import json
import logging
import re

logger = logging.getLogger(__name__)
User = get_user_model()

# Cache for compiled regex patterns (optimization)
_compiled_regex_cache = {}

def _get_compiled_regex(pattern):
    """Get cached compiled regex pattern"""
    if pattern not in _compiled_regex_cache:
        _compiled_regex_cache[pattern] = re.compile(pattern, re.IGNORECASE)
    return _compiled_regex_cache[pattern]

# All available tools for authenticated users
ALL_AVAILABLE_TOOLS = [
    'customer_search', 'prospect_search', 'lead_file_search',
    'customer_sales_data', 'customer_title_status', 'customer_360_view',
    'create_engagement', 'create_reminder', 'create_note',
    'export_sales_csv', 'export_customers_csv', 'export_prospects_csv', 'export_mib_csv',
    'generate_sales_report'
]

# Optimized pattern map for intent classification
OPTIMIZED_PATTERN_MAP = [
    ('greeting', r'^(hi|hello|hey|good\s*(morning|afternoon|evening)|greetings)$', 0.98, 'instant', "Hello! I'm Amani 😊 Ready to help you with customers, sales, or anything CRM-related!", []),
    ('customer_search', r'(find|search|look|show|get|who\s*is|customer|client|person)', 0.90, 'high', None, ['customer_search', 'prospect_search']),
    ('prospect_search', r'(prospect|potential\s*customer)', 0.92, 'high', None, ['prospect_search']),
    ('lead_file_search', r'(lead file|leadfile|\bLF\d+\b)', 0.93, 'high', None, ['lead_file_search']),
    ('customer_360', r'(complete|full|360|overview|summary|everything).*customer', 0.95, 'high', None, ['customer_360_view']),
    ('sales_data', r'(sales|purchase|payment|amount|balance|total|paid)', 0.88, 'high', None, ['customer_sales_data']),
    ('title_status', r'(title|status|plot|property|deed|ownership)', 0.87, 'high', None, ['customer_title_status']),
    ('quick_actions', r'(create|add|note|reminder|engagement)', 0.85, 'medium', None, ['create_engagement', 'create_reminder', 'create_note']),
    ('export_request', r'(export|download|csv|report)', 0.86, 'medium', None, ['export_sales_csv', 'export_customers_csv', 'export_prospects_csv', 'export_mib_csv']),
    ('help', r'(help|what\s*can|capabilities|features|assist)', 0.95, 'instant', "💡 **Quick Actions:**\\n🔍 \"Find John Smith\" - Search customers\\n📊 \"Sales for CUST001\" - Get sales data\\n📈 \"Customer overview XYZ\" - 360° view\\n📝 \"Add note for customer\" - Create note\\n📤 \"Export sales data\" - Download CSV\\n\\nAsk in plain language! 😊", []),
    ('thanks', r'(thank|thanks|appreciate|thx)', 0.98, 'instant', "You're welcome! 🙌 I'm here whenever you need help with customer data or CRM tasks.", []),
]


@csrf_exempt
@require_http_methods(["GET", "POST"])
def n8n_permission_check(request):
    """
    Seamless access endpoint for N8N workflows.
    Returns full access for all users - no permission barriers.
    """
    try:
        # Extract parameters (for logging purposes)
        if request.method == 'GET':
            session_id = request.GET.get('session_id', 'unknown')
            user_id = request.GET.get('user_id')
            employee_no = request.GET.get('employee_no')
        else:
            data = json.loads(request.body)
            session_id = data.get('session_id', 'unknown')
            user_id = data.get('user_id')
            employee_no = data.get('employee_no')
        
        # Find user (optional - for context)
        user = None
        if employee_no:
            try:
                user = User.objects.get(employee_no=employee_no, is_active=True)
            except User.DoesNotExist:
                pass
        elif user_id:
            try:
                user = User.objects.get(id=user_id, is_active=True)
            except User.DoesNotExist:
                pass
        
        # Always return full access
        response = {
            'success': True,
            'permission_level': 'full',
            'available_tools': ALL_AVAILABLE_TOOLS,
            'access_level': 'full',
            'bypass_mode': True,
            'user_context': {
                'id': user.id if user else 'anonymous',
                'employee_no': user.employee_no if user and hasattr(user, 'employee_no') else None,
                'full_name': f"{user.first_name} {user.last_name}".strip() or user.username if user else 'Anonymous',
                'authenticated': bool(user),
                'access_level': 'full'
            },
            'tool_categories': {
                'search': ['customer_search', 'prospect_search', 'lead_file_search'],
                'data': ['customer_sales_data', 'customer_title_status', 'customer_360_view'],  
                'actions': ['create_engagement', 'create_reminder', 'create_note'],
                'exports': ['export_sales_csv', 'export_customers_csv', 'export_prospects_csv', 'export_mib_csv']
            },
            'message': 'Full access granted - no permission barriers',
            'generated_at': timezone.now().isoformat(),
        }
        
        return JsonResponse(response)
        
    except Exception as e:
        logger.error(f"N8N access check error: {e}", exc_info=True)
        # Even on error, return full access
        return JsonResponse({
            'success': True,  # Keep success true to avoid N8N workflow errors
            'error': str(e),
            'permission_level': 'full',
            'available_tools': ALL_AVAILABLE_TOOLS,
            'access_level': 'full',
            'bypass_mode': True,
            'message': 'Error occurred but full access maintained'
        })


@csrf_exempt
@require_http_methods(["POST"])
def n8n_lightweight_intent(request):
    """
    Lightweight intent classification endpoint for N8N.
    Server-side processing with cached regex patterns for maximum speed.
    """
    try:
        data = json.loads(request.body)
        chat_input = data.get('chatInput', '')
        session_id = data.get('sessionId', 'default-session')
        history = data.get('history', [])
        user = data.get('user', {})
        
        user_input = chat_input.lower().strip()
        
        # Fast intent matching with early exit for high-confidence matches
        best_intent = 'conversational'
        best_confidence = 0.5
        priority = 'medium'
        cached_response = None
        suggested_tools = []
        requires_ai = True
        
        for intent, pattern_str, confidence, prio, cached_resp, tools in OPTIMIZED_PATTERN_MAP:
            regex = _get_compiled_regex(pattern_str)
            if regex.search(user_input):
                if confidence > best_confidence:
                    best_intent = intent
                    best_confidence = confidence
                    priority = prio
                    cached_response = cached_resp
                    suggested_tools = tools or []
                    requires_ai = priority != 'instant'
                    
                    # Early exit for instant responses
                    if confidence > 0.95 and priority == 'instant':
                        break
        
        # No permission filtering needed - all tools available
        allowed_suggested_tools = suggested_tools
        
        # Single-pass context extraction with early exit
        context_cno = None
        for i in range(len(history) - 1, -1, -1):
            message = history[i]
            if message.get('role') == 'assistant' and message.get('content'):
                match = re.search(r'\[CONTEXT_CNO:([^\]]+)\]', message['content'])
                if match:
                    context_cno = match[1]
                    break
        
        # Optimized entity extraction with pre-compiled regexes
        entity_regexes = {
            'customer_ids': r'\b(CUST\d+|CL\d+)\b',
            'lead_file_ids': r'\bLF\d+\b',
            'emails': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'phones': r'\b\d{10,}\b'
        }
        
        entities = {}
        for key, pattern in entity_regexes.items():
            regex = _get_compiled_regex(pattern)
            entities[key] = regex.findall(user_input)
        
        # Extract names efficiently
        stop_words = {'for', 'the', 'and', 'search', 'customer', 'client', 'find', 'show', 'get'}
        names = [name for name in re.findall(r'\b[a-z\'-]{3,}\b', user_input, re.IGNORECASE) if name.lower() not in stop_words]
        entities['names'] = names
        
        response_data = {
            'originalInput': chat_input,
            'processedInput': user_input,
            'intent': best_intent,
            'confidence': best_confidence,
            'priority': priority,
            'requiresAI': requires_ai,
            'entities': entities,
            'customer_no': context_cno,
            'accessLevel': 'full',
            'availableToolCount': len(ALL_AVAILABLE_TOOLS),
            'instantResponse': cached_response,
            'suggestedTools': allowed_suggested_tools,
            'cacheKey': f"intent_{best_intent}_{session_id}",
            'timestamp': timezone.now().isoformat(),
            'hasSearchableEntities': bool(entities.get('customer_ids') or entities.get('names') or context_cno),
            'optimizationLevel': 'server_side_seamless',
            'processingHint': 'sub-10ms' if priority == 'instant' else 'standard'
        }
        
        return JsonResponse(response_data)
        
    except json.JSONDecodeError:
        return JsonResponse({'error': 'Invalid JSON'}, status=400)
    except Exception as e:
        logger.error(f"Error in n8n_lightweight_intent: {e}", exc_info=True)
        return JsonResponse({
            'error': str(e),
            'intent': 'conversational',
            'confidence': 0.5,
            'requiresAI': True,
            'accessLevel': 'full',
            'suggestedTools': ['customer_search'],
            'fallback': True
        })