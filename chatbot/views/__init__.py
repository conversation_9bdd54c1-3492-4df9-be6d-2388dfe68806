# chatbot/views/__init__.py
from .search import (
    search_unified, debug_search, search_marketer, 
    search_lead_sources, search_users_proxy, is_request_from_n8n
)
from .inventory_search import (
    search_plots, search_projects, get_customer_plots, get_plot_details,
    get_customer_plots_query, get_plot_details_query
)
from .exports import (
    export_sales_csv, export_mib_csv, export_installments_csv,
    export_customers_csv, export_site_visits_csv, export_prospects_csv,
    export_customer_view_csv, export_bookings_csv
)
from .reports import (
    generate_sales_report, new_sales_report, mib_report,
    installments_due_today, overdue_collections_report, prospects_report
)
from .customer_details import (
    customer_sales_data, customer_title_status, customer_360_view,
    customer_sales_data_query, customer_title_status_query, customer_360_view_query,
    create_prospect, format_phone_number
)
from .utils import health_check, simple_test, user_info
from .actions import (
    chatbot_api_router, my_reminders, my_engagements, chatbot_session_proxy,
    create_engagement_endpoint, create_reminder_endpoint, create_note_endpoint
)
from .user_context import capture_user_context

__all__ = [
    # Search endpoints
    'search_unified', 'debug_search', 'search_marketer', 
    'search_lead_sources', 'search_users_proxy', 'is_request_from_n8n',
    
    # Inventory search endpoints
    'search_plots', 'search_projects', 'get_customer_plots', 'get_plot_details',
    'get_customer_plots_query', 'get_plot_details_query',
    
    # Export endpoints
    'export_sales_csv', 'export_mib_csv', 'export_installments_csv',
    'export_customers_csv', 'export_site_visits_csv', 'export_prospects_csv',
    'export_customer_view_csv', 'export_bookings_csv',
    
    # Report endpoints
    'generate_sales_report', 'new_sales_report', 'mib_report',
    'installments_due_today', 'overdue_collections_report', 'prospects_report',
    
    # Customer detail endpoints
    'customer_sales_data', 'customer_title_status', 'customer_360_view',
    'customer_sales_data_query', 'customer_title_status_query', 'customer_360_view_query',
    'create_prospect', 'format_phone_number',
    
    # Utility endpoints
    'health_check', 'simple_test', 'user_info',
    
    # Action endpoints
    'chatbot_api_router', 'my_reminders', 'my_engagements', 'chatbot_session_proxy',
    'create_engagement_endpoint', 'create_reminder_endpoint', 'create_note_endpoint',
    
    # User context endpoint
    'capture_user_context'
] 