"""
Chatbot Inventory Search Views
=============================

Search endpoints for inventory-related data including plots, projects,
and bookings integration with the chatbot system.
"""

import logging
from django.http import JsonResponse
from django.views.decorators.cache import cache_page
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from ..services.search import ChatbotSearchService
from .utils import get_request_user

logger = logging.getLogger(__name__)


@api_view(['GET'])
@permission_classes([AllowAny])
@cache_page(300)  # 5 minutes cache for inventory data
def search_plots(request):
    """
    Search for plots based on plot number, location, or project.
    """
    try:
        query = request.GET.get('q', '').strip()
        limit = min(int(request.GET.get('limit', 10)), 50)
        user = get_request_user(request)

        if not query:
            return Response({
                'error': 'Search query is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        search_service = ChatbotSearchService()
        results = search_service.search(query, user=user, entity_types=['plots'], limit=limit)
        
        return Response(results)

    except Exception as e:
        logger.exception("An error occurred during plot search")
        return Response({
            'error': f'Plot search failed: {str(e)}',
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
@cache_page(600)  # 10 minutes cache for project data
def search_projects(request):
    """
    Search for projects based on name, initials, or description.
    """
    try:
        query = request.GET.get('q', '').strip()
        limit = min(int(request.GET.get('limit', 10)), 50)
        user = get_request_user(request)

        if not query:
            return Response({
                'error': 'Search query is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        search_service = ChatbotSearchService()
        results = search_service.search(query, user=user, entity_types=['projects'], limit=limit)
        
        return Response(results)

    except Exception as e:
        logger.exception("An error occurred during project search")
        return Response({
            'error': f'Project search failed: {str(e)}',
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_plots(request, customer_no):
    """
    Get all plots purchased by a specific customer.
    """
    try:
        from sales.models import LeadFile
        from inventory.models import Plot
        
        # Get all lead files for the customer
        lead_files = LeadFile.objects.filter(
            customer_id__customer_no=customer_no,
            lead_file_status_dropped=False
        ).select_related('customer_id', 'project')
        
        if not lead_files.exists():
            return Response({
                'success': True,
                'message': f'No plots found for customer {customer_no}',
                'data': {
                    'customer_no': customer_no,
                    'plots': [],
                    'total_plots': 0,
                    'total_purchase_value': 0,
                    'total_paid': 0,
                    'total_balance': 0
                }
            })
        
        plots_data = []
        total_purchase_value = 0
        total_paid = 0
        total_balance = 0
        
        for lead_file in lead_files:
            # Get plot details
            try:
                plot = Plot.objects.get(plot_no=lead_file.plot_no)
                plot_data = {
                    'lead_file_no': lead_file.lead_file_no,
                    'plot_no': lead_file.plot_no,
                    'project_name': lead_file.project.name if lead_file.project else 'Unknown',
                    'project_initials': lead_file.project.initials if lead_file.project else '',
                    'plot_size': float(plot.plot_size),
                    'plot_type': plot.plot_type,
                    'plot_status': plot.plot_status,
                    'location': plot.location,
                    'cash_price': float(plot.cash_price),
                    'threshold_price': float(plot.threshold_price),
                    'purchase_price': float(lead_file.purchase_price or 0),
                    'selling_price': float(lead_file.selling_price or 0),
                    'total_paid': float(lead_file.total_paid or 0),
                    'balance': float(lead_file.balance_lcy_lcy or 0),
                    'lr_no': plot.lr_no,
                    'view': plot.view
                }
                plots_data.append(plot_data)
                
                # Add to totals
                total_purchase_value += float(lead_file.purchase_price or 0)
                total_paid += float(lead_file.total_paid or 0)
                total_balance += float(lead_file.balance_lcy or 0)
                
            except Plot.DoesNotExist:
                # Plot might not exist in inventory, but we have the lead file
                plot_data = {
                    'lead_file_no': lead_file.lead_file_no,
                    'plot_no': lead_file.plot_no,
                    'project_name': lead_file.project.name if lead_file.project else 'Unknown',
                    'project_initials': lead_file.project.initials if lead_file.project else '',
                    'purchase_price': float(lead_file.purchase_price or 0),
                    'selling_price': float(lead_file.selling_price or 0),
                    'total_paid': float(lead_file.total_paid or 0),
                    'balance': float(lead_file.balance_lcy or 0),
                    'note': 'Plot details not available in inventory'
                }
                plots_data.append(plot_data)
                
                # Add to totals
                total_purchase_value += float(lead_file.purchase_price or 0)
                total_paid += float(lead_file.total_paid or 0)
                total_balance += float(lead_file.balance_lcy or 0)
        
        return Response({
            'success': True,
            'data': {
                'customer_no': customer_no,
                'customer_name': lead_files.first().customer_id.customer_name if lead_files.first().customer_id else '',
                'plots': plots_data,
                'total_plots': len(plots_data),
                'total_purchase_value': total_purchase_value,
                'total_paid': total_paid,
                'total_balance': total_balance
            }
        })
        
    except Exception as e:
        logger.exception(f"Error retrieving plots for customer {customer_no}")
        return Response({
            'success': False,
            'error': f'Failed to retrieve customer plots: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_plot_details(request, plot_no):
    """
    Get detailed information about a specific plot.
    """
    try:
        from inventory.models import Plot
        from sales.models import LeadFile
        
        # Get plot details
        try:
            plot = Plot.objects.select_related('project').get(plot_no=plot_no)
        except Plot.DoesNotExist:
            return Response({
                'success': False,
                'error': f'Plot {plot_no} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get lead file if plot is sold
        lead_file = None
        customer_info = None
        if plot.plot_status == 'Sold':
            try:
                lead_file = LeadFile.objects.select_related('customer_id').get(
                    plot_no=plot_no,
                    lead_file_status_dropped=False
                )
                if lead_file.customer_id:
                    customer_info = {
                        'customer_no': lead_file.customer_id.customer_no,
                        'customer_name': lead_file.customer_id.customer_name,
                        'phone': lead_file.customer_id.phone,
                        'email': lead_file.customer_id.primary_email
                    }
            except LeadFile.DoesNotExist:
                pass
        
        plot_data = {
            'plot_no': plot.plot_no,
            'plot_id': plot.plotId,
            'plot_size': float(plot.plot_size),
            'plot_type': plot.plot_type,
            'plot_status': plot.plot_status,
            'location': plot.location,
            'cash_price': float(plot.cash_price),
            'threshold_price': float(plot.threshold_price),
            'lr_no': plot.lr_no,
            'view': plot.view,
            'project': {
                'project_id': plot.project.projectId,
                'name': plot.project.name,
                'initials': plot.project.initials,
                'tier': plot.project.tier,
                'priority': plot.project.priority,
                'bank': plot.project.bank,
                'account_no': plot.project.account_no
            },
            'customer': customer_info,
            'lead_file': {
                'lead_file_no': lead_file.lead_file_no,
                'purchase_price': float(lead_file.purchase_price or 0),
                'selling_price': float(lead_file.selling_price or 0),
                'total_paid': float(lead_file.total_paid or 0),
                'balance': float(lead_file.balance_lcy or 0)
            } if lead_file else None
        }
        
        return Response({
            'success': True,
            'data': plot_data
        })
        
    except Exception as e:
        logger.exception(f"Error retrieving details for plot {plot_no}")
        return Response({
            'success': False,
            'error': f'Failed to retrieve plot details: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ==========================================================================
# N8N-COMPATIBLE ENDPOINTS (QUERY PARAMETERS)
# ==========================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def get_customer_plots_query(request):
    """
    N8N-compatible endpoint: Get customer plots using query parameter.
    Usage: /api/chatbot/customer-plots/?customer_no=CL006869
    """
    customer_no = request.GET.get('customer_no')
    
    if not customer_no:
        return Response({
            'success': False,
            'error': 'customer_no parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Implement the logic directly to avoid request conflicts
    try:
        from sales.models import LeadFile
        from inventory.models import Plot
        
        # Get all lead files for the customer
        lead_files = LeadFile.objects.filter(
            customer_id__customer_no=customer_no,
            lead_file_status_dropped=False
        ).select_related('customer_id', 'project')
        
        if not lead_files.exists():
            return Response({
                'success': True,
                'message': f'No plots found for customer {customer_no}',
                'data': {
                    'customer_no': customer_no,
                    'plots': [],
                    'total_plots': 0,
                    'total_purchase_value': 0,
                    'total_paid': 0,
                    'total_balance': 0
                }
            })
        
        plots_data = []
        total_purchase_value = 0
        total_paid = 0
        total_balance = 0
        
        for lead_file in lead_files:
            # Get plot details
            try:
                plot = Plot.objects.get(plot_no=lead_file.plot_no)
                plot_data = {
                    'lead_file_no': lead_file.lead_file_no,
                    'plot_no': lead_file.plot_no,
                    'project_name': lead_file.project.name if lead_file.project else 'Unknown',
                    'project_initials': lead_file.project.initials if lead_file.project else '',
                    'plot_size': float(plot.plot_size),
                    'plot_type': plot.plot_type,
                    'plot_status': plot.plot_status,
                    'location': plot.location,
                    'cash_price': float(plot.cash_price),
                    'threshold_price': float(plot.threshold_price),
                    'purchase_price': float(lead_file.purchase_price or 0),
                    'selling_price': float(lead_file.selling_price or 0),
                    'total_paid': float(lead_file.total_paid or 0),
                    'balance': float(lead_file.balance_lcy or 0),
                    'lr_no': plot.lr_no,
                    'view': plot.view
                }
                plots_data.append(plot_data)
                
                # Add to totals
                total_purchase_value += float(lead_file.purchase_price or 0)
                total_paid += float(lead_file.total_paid or 0)
                total_balance += float(lead_file.balance_lcy or 0)
                
            except Plot.DoesNotExist:
                # Plot might not exist in inventory, but we have the lead file
                plot_data = {
                    'lead_file_no': lead_file.lead_file_no,
                    'plot_no': lead_file.plot_no,
                    'project_name': lead_file.project.name if lead_file.project else 'Unknown',
                    'project_initials': lead_file.project.initials if lead_file.project else '',
                    'purchase_price': float(lead_file.purchase_price or 0),
                    'selling_price': float(lead_file.selling_price or 0),
                    'total_paid': float(lead_file.total_paid or 0),
                    'balance': float(lead_file.balance_lcy or 0),
                    'note': 'Plot details not available in inventory'
                }
                plots_data.append(plot_data)
                
                # Add to totals
                total_purchase_value += float(lead_file.purchase_price or 0)
                total_paid += float(lead_file.total_paid or 0)
                total_balance += float(lead_file.balance_lcy or 0)
        
        return Response({
            'success': True,
            'data': {
                'customer_no': customer_no,
                'customer_name': lead_files.first().customer_id.customer_name if lead_files.first().customer_id else '',
                'plots': plots_data,
                'total_plots': len(plots_data),
                'total_purchase_value': total_purchase_value,
                'total_paid': total_paid,
                'total_balance': total_balance
            }
        })
        
    except Exception as e:
        logger.exception(f"Error retrieving plots for customer {customer_no}")
        return Response({
            'success': False,
            'error': f'Failed to retrieve customer plots: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def get_plot_details_query(request):
    """
    N8N-compatible endpoint: Get plot details using query parameter.
    Usage: /api/chatbot/plot-details/?plot_no=KC4
    """
    plot_no = request.GET.get('plot_no')
    
    if not plot_no:
        return Response({
            'success': False,
            'error': 'plot_no parameter is required'
        }, status=status.HTTP_400_BAD_REQUEST)
    
    # Implement the logic directly to avoid request conflicts
    try:
        from inventory.models import Plot
        from sales.models import LeadFile
        
        # Get plot details
        try:
            plot = Plot.objects.select_related('project').get(plot_no=plot_no)
        except Plot.DoesNotExist:
            return Response({
                'success': False,
                'error': f'Plot {plot_no} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Get lead file if plot is sold
        lead_file = None
        customer_info = None
        if plot.plot_status == 'Sold':
            try:
                lead_file = LeadFile.objects.select_related('customer_id').get(
                    plot_no=plot_no,
                    lead_file_status_dropped=False
                )
                if lead_file.customer_id:
                    customer_info = {
                        'customer_no': lead_file.customer_id.customer_no,
                        'customer_name': lead_file.customer_id.customer_name,
                        'phone': lead_file.customer_id.phone,
                        'email': lead_file.customer_id.primary_email
                    }
            except LeadFile.DoesNotExist:
                pass
        
        plot_data = {
            'plot_no': plot.plot_no,
            'plot_id': plot.plotId,
            'plot_size': float(plot.plot_size),
            'plot_type': plot.plot_type,
            'plot_status': plot.plot_status,
            'location': plot.location,
            'cash_price': float(plot.cash_price),
            'threshold_price': float(plot.threshold_price),
            'lr_no': plot.lr_no,
            'view': plot.view,
            'project': {
                'project_id': plot.project.projectId,
                'name': plot.project.name,
                'initials': plot.project.initials,
                'tier': plot.project.tier,
                'priority': plot.project.priority,
                'bank': plot.project.bank,
                'account_no': plot.project.account_no
            },
            'customer': customer_info,
            'lead_file': {
                'lead_file_no': lead_file.lead_file_no,
                'purchase_price': float(lead_file.purchase_price or 0),
                'selling_price': float(lead_file.selling_price or 0),
                'total_paid': float(lead_file.total_paid or 0),
                'balance': float(lead_file.balance_lcy or 0)
            } if lead_file else None
        }
        
        return Response({
            'success': True,
            'data': plot_data
        })
        
    except Exception as e:
        logger.exception(f"Error retrieving details for plot {plot_no}")
        return Response({
            'success': False,
            'error': f'Failed to retrieve plot details: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
