"""
Chatbot Report Endpoints
========================

Direct report endpoints that query the database directly.
These endpoints provide a chatbot-friendly interface with permission checking and filtering.
"""

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import logging
from functools import wraps
from datetime import date, datetime, timedelta
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db import connections

logger = logging.getLogger(__name__)


# ============================================================================
# HELPER FUNCTIONS - Reduce Code Duplication
# ============================================================================

def check_report_permission(user, report_name="report"):
    """
    Centralized permission check for report endpoints.
    
    Args:
        user: Django user object
        report_name: Name of the report for logging purposes
        
    Returns:
        Response object if permission denied, None if allowed
    """
    from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
    from ..config import ChatbotConfig
    
    if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
        session_perms = LightweightPermissionService.get_session_permissions(user)
        if 'can_view_reports' not in session_perms.available_tools and \
           not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
            logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access {report_name} without permission")
            return Response({
                'success': False,
                'error': 'Permission denied',
                'message': 'You do not have permission to view reports.'
            }, status=status.HTTP_403_FORBIDDEN)
    return None


def paginate_response(data, request, page_size=20):
    """
    Centralized pagination helper.
    
    Args:
        data: List of data to paginate
        request: Django request object
        page_size: Default page size
        
    Returns:
        Dictionary with paginated results
    """
    page = request.GET.get('page', 1)
    try:
        page = int(page)
    except ValueError:
        page = 1
    
    try:
        page_size = int(request.GET.get('page_size', page_size))
    except ValueError:
        page_size = page_size
    
    paginator = Paginator(data, page_size)
    try:
        paged_data = paginator.page(page)
    except PageNotAnInteger:
        paged_data = paginator.page(1)
    except EmptyPage:
        paged_data = paginator.page(paginator.num_pages)
    
    return {
        'count': paginator.count,
        'num_pages': paginator.num_pages,
        'current_page': paged_data.number,
        'results': paged_data.object_list
    }


def parse_date_range(request, default_days=30):
    """
    Parse start_date and end_date from request query parameters.
    
    Args:
        request: Django request object
        default_days: Default number of days to go back if start_date not provided
        
    Returns:
        Tuple of (start_date, end_date) as date objects
        
    Raises:
        ValueError: If date format is invalid or start_date > end_date
    """
    start_date_str = request.GET.get('start_date')
    end_date_str = request.GET.get('end_date')
    
    if not start_date_str:
        start_date = date.today() - timedelta(days=default_days)
    else:
        try:
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid start_date format. Use YYYY-MM-DD.")
    
    if not end_date_str:
        end_date = date.today()
    else:
        try:
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
        except ValueError:
            raise ValueError("Invalid end_date format. Use YYYY-MM-DD.")
    
    if start_date > end_date:
        raise ValueError("Start date must be before end date.")
    
    return start_date, end_date


def format_report_response(title, data, request, page_size=20):
    """
    Format standardized report response with pagination.
    
    Args:
        title: Report title
        data: List of data to include in response
        request: Django request object
        page_size: Default page size
        
    Returns:
        Response object with standardized format
    """
    if not data:
        return Response({
            'success': True,
            'data': {
                'Title': title,
                'Total Results': 0,
                'count': 0,
                'num_pages': 0,
                'current_page': 1,
                'results': []
            }
        })
    
    paginated = paginate_response(data, request, page_size)
    
    return Response({
        'success': True,
        'data': {
            'Title': title,
            'Total Results': paginated['count'],
            'count': paginated['count'],
            'num_pages': paginated['num_pages'],
            'current_page': paginated['current_page'],
            'results': paginated['results']
        }
    })


def serialize_dates(obj):
    """
    Convert date/datetime objects in a dictionary to ISO format strings.
    
    Args:
        obj: Dictionary that may contain date/datetime values
        
    Returns:
        Dictionary with dates serialized to strings
    """
    for k, v in obj.items():
        if isinstance(v, (date, datetime)):
            obj[k] = v.isoformat()
    return obj


def execute_reports_query(sql, params=None):
    """
    Execute a SQL query against the reports database connection.
    
    Args:
        sql: SQL query string
        params: Query parameters (optional)
        
    Returns:
        List of dictionaries representing query results
        
    Raises:
        Exception: If query execution fails
    """
    with connections["reports"].cursor() as cur:
        cur.execute(sql, params or [])
        cols = [c[0] for c in cur.description]
        return [dict(zip(cols, row)) for row in cur.fetchall()]


def report_error_handler(report_name):
    """
    Decorator for consistent error handling in report endpoints.
    
    Args:
        report_name: Name of the report for error messages
    """
    def decorator(func):
        @wraps(func)
        def wrapper(request, *args, **kwargs):
            try:
                return func(request, *args, **kwargs)
            except ValueError as e:
                logger.warning(f"Validation error in {report_name}: {str(e)}")
                return Response({
                    'success': False,
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                logger.error(f"Error in {report_name}: {str(e)}")
                import traceback
                logger.error(traceback.format_exc())
                return Response({
                    'success': False,
                    'error': f'Failed to generate {report_name}: {str(e)}'
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        return wrapper
    return decorator


@api_view(['GET'])
@permission_classes([AllowAny])
@report_error_handler("sales report")
def generate_sales_report(request):
    """
    Direct general sales report endpoint with permission checking and filtering.
    This is a generic sales report endpoint for the chatbot.
    Redirects to new_sales_report for consistency.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    # Check permissions
    permission_error = check_report_permission(user, "sales report")
    if permission_error:
        return permission_error
    
    # Redirect to new sales report (direct implementation)
    return new_sales_report(request)


@api_view(['GET'])
@permission_classes([AllowAny])
@report_error_handler("new sales report")
def new_sales_report(request):
    """
    Direct new sales report endpoint with permission checking and filtering.
    Queries database directly instead of proxying to reports app.
    """
    from .utils import get_request_user
    from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
    from sales.models import LeadFile
    user = get_request_user(request)
    
    # Check permissions
    permission_error = check_report_permission(user, "new sales report")
    if permission_error:
        return permission_error
    
    # Extract filter parameters
    filter_params = extract_filter_params_from_request(request)
    
    # Get query parameters
    office = request.GET.get('OFFICE', 'ALL')
    lead_source_id = request.GET.get('LEAD_SOURCE', 'ALL')
    diaspora_region = request.GET.get('DIASPORA_REGION', 'ALL')
    marketer_team = request.GET.get('MARKETER_TEAM', 'ALL')
    
    # Parse date range (default to 7 days)
    try:
        start_date, end_date = parse_date_range(request, default_days=7)
    except ValueError as e:
        return Response({"error": str(e)}, status=400)
    
    # Start with all lead files in date range
    queryset = LeadFile.objects.select_related('marketer', 'customer_id', 'project').filter(
        booking_date__gte=start_date,
        booking_date__lte=end_date
    )
    
    # Apply permission filters based on permission IDs
    # IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering
    # Bypass mode only bypasses permission checks, not data filtering
    if filter_params:
        queryset = PermissionFilterService.apply_leadfile_filters(queryset, filter_params)
    
    # Apply additional filters
    if office and office != 'ALL':
        queryset = queryset.filter(marketer__office=office)
    
    if lead_source_id and lead_source_id != 'ALL':
        queryset = queryset.filter(customer_id__lead_source_id=lead_source_id)
    
    if marketer_team and marketer_team != 'ALL':
        queryset = queryset.filter(marketer__team_id=marketer_team)
    
    # Paginate queryset using Django's paginator
    page = request.GET.get('page', 1)
    page_size = request.GET.get('page_size', 20)
    try:
        page = int(page)
    except ValueError:
        page = 1
    try:
        page_size = int(page_size)
    except ValueError:
        page_size = 20
    
    paginator = Paginator(queryset, page_size)
    try:
        paged_data = paginator.page(page)
    except PageNotAnInteger:
        paged_data = paginator.page(1)
    except EmptyPage:
        paged_data = paginator.page(paginator.num_pages)
    
    # Serialize results
    results = []
    for lf in paged_data.object_list:
        results.append({
            'lead_file_no': lf.lead_file_no or '',
            'customer_name': lf.customer_name or '',
            'plot_id': lf.plot_id if hasattr(lf, 'plot_id') else '',
            'selling_price': float(lf.selling_price) if lf.selling_price else 0.0,
            'total_paid': float(lf.total_paid) if hasattr(lf, 'total_paid') and lf.total_paid else 0.0,
            'balance_lcy': float(lf.balance_lcy) if lf.balance_lcy else 0.0,
            'marketer_id': lf.marketer_id or ''
        })
    
    # Build stat string
    stat = "for ALL"
    if filter_params:
        if filter_params.get('restrict_to_marketer'):
            stat = f"for Marketer {filter_params.get('marketer_employee_no')}"
        elif filter_params.get('restrict_to_office'):
            stat = f"for {filter_params.get('office')}"
        elif filter_params.get('restrict_to_team'):
            stat = f"for Team {filter_params.get('team_id')}"
    elif office and office != 'ALL':
        stat = f"for {office}"
    
    title = f"New Sales Reports from {start_date} to {end_date} {stat}"
    return format_report_response(title, results, request)


@api_view(['GET'])
@permission_classes([AllowAny])
def mib_report(request):
    """
    Direct MIB (Money In Bank) report endpoint with permission checking and filtering.
    Queries database directly instead of proxying to reports app.
    MIB = Lead files with installment purchase type.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from sales.models import LeadFile
        from datetime import date, timedelta, datetime
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has permission to view reports
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access MIB report without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports. Required: can_view_reports',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        office = request.GET.get('OFFICE', 'ALL')
        lead_source_id = request.GET.get('LEAD_SOURCE', 'ALL')
        diaspora_region = request.GET.get('DIASPORA_REGION', 'ALL')
        marketer_team = request.GET.get('MARKETER_TEAM', 'ALL')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=7)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # MIB = Lead files with installment purchase type
        queryset = LeadFile.objects.select_related('marketer', 'customer_id', 'project').filter(
            purchase_type='Installment',
            booking_date__gte=start_date,
            booking_date__lte=end_date,
            lead_file_status_dropped=False
        )
        
        # Apply permission filters based on permission IDs
        # IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering
        # Bypass mode only bypasses permission checks, not data filtering
        if filter_params:
            queryset = PermissionFilterService.apply_leadfile_filters(queryset, filter_params)
        
        # Apply additional filters
        if office and office != 'ALL':
            queryset = queryset.filter(marketer__office=office)
        
        if lead_source_id and lead_source_id != 'ALL':
            queryset = queryset.filter(customer_id__lead_source_id=lead_source_id)
        
        if marketer_team and marketer_team != 'ALL':
            queryset = queryset.filter(marketer__team_id=marketer_team)
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(queryset, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        # Serialize results
        results = []
        for lf in paged_data.object_list:
            results.append({
                'lead_file_no': lf.lead_file_no or '',
                'customer_name': lf.customer_name or '',
                'plot_id': lf.plot_id if hasattr(lf, 'plot_id') else '',
                'selling_price': float(lf.selling_price) if lf.selling_price else 0.0,
                'total_paid': float(lf.total_paid) if hasattr(lf, 'total_paid') and lf.total_paid else 0.0,
                'balance_lcy': float(lf.balance_lcy) if lf.balance_lcy else 0.0,
                'marketer_id': lf.marketer_id or ''
            })
        
        # Build stat string
        stat = "for ALL"
        if filter_params:
            if filter_params.get('restrict_to_marketer'):
                stat = f"for Marketer {filter_params.get('marketer_employee_no')}"
            elif filter_params.get('restrict_to_office'):
                stat = f"for {filter_params.get('office')}"
            elif filter_params.get('restrict_to_team'):
                stat = f"for Team {filter_params.get('team_id')}"
        elif office and office != 'ALL':
            stat = f"for {office}"
        
        title = f"MIB Reports from {start_date} to {end_date} {stat}"
        return format_report_response(title, results, request)
            
    except Exception as e:
        logger.error(f"Error in mib_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate MIB report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def installments_due_today(request):
    """
    Direct installments due today report endpoint with permission checking and filtering.
    Queries database directly instead of proxying to reports app.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from receipts.models import InstallmentSchedule
        from sales.models import LeadFile
        from django.utils import timezone
        from datetime import date
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has permission to view reports
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access installments due today report without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports. Required: can_view_reports',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        project_id = request.GET.get('project_id', 'ALL')
        marketer_no = request.GET.get('marketer_no', 'ALL')
        team = request.GET.get('team', 'ALL')
        credit_officer = request.GET.get('credit_officer', 'ALL')
        
        # Today's date
        today = date.today()
        
        # Start with installments due today
        # InstallmentSchedule uses leadfile_no as CharField, not ForeignKey
        queryset = InstallmentSchedule.objects.filter(due_date=today)
        
        # Apply permission filtering through lead files based on permission IDs
        # IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering
        if filter_params:
            # Get lead file numbers that match permission filters
            leadfile_queryset = LeadFile.objects.all()
            leadfile_queryset = PermissionFilterService.apply_leadfile_filters(leadfile_queryset, filter_params)
            allowed_leadfile_nos = list(leadfile_queryset.values_list('lead_file_no', flat=True))
            
            if allowed_leadfile_nos:
                queryset = queryset.filter(leadfile_no__in=allowed_leadfile_nos)
            else:
                # No lead files match permissions - return empty result
                queryset = queryset.none()
        
        # Apply additional filters through lead files
        if project_id and project_id != 'ALL':
            # Get lead files for this project
            project_leadfiles = LeadFile.objects.filter(project_id=project_id).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=project_leadfiles)
        
        if marketer_no and marketer_no != 'ALL':
            # Get lead files for this marketer
            marketer_leadfiles = LeadFile.objects.filter(marketer_id=marketer_no).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=marketer_leadfiles)
        
        if team and team != 'ALL':
            # Get lead files for this team
            team_leadfiles = LeadFile.objects.filter(marketer__team_id=team).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=team_leadfiles)
        
        if credit_officer and credit_officer != 'ALL':
            # Get lead files for this credit officer
            credit_officer_leadfiles = LeadFile.objects.filter(credit_officer_id=credit_officer).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=credit_officer_leadfiles)
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(queryset, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        # Get lead file data for serialization
        leadfile_nos = list(set([inst.leadfile_no for inst in paged_data.object_list if inst.leadfile_no]))
        leadfiles_dict = {}
        if leadfile_nos:
            leadfiles = LeadFile.objects.select_related('marketer', 'project').filter(lead_file_no__in=leadfile_nos)
            leadfiles_dict = {lf.lead_file_no: lf for lf in leadfiles}
        
        # Serialize results
        results = []
        for inst in paged_data.object_list:
            leadfile = leadfiles_dict.get(inst.leadfile_no) if inst.leadfile_no else None
            results.append({
                'no': inst.line_no if hasattr(inst, 'line_no') else '',
                'installment_no': inst.installment_no if hasattr(inst, 'installment_no') else '',
                'due_date': inst.due_date.strftime('%Y-%m-%d') if inst.due_date else '',
                'member_no': inst.member_no if hasattr(inst, 'member_no') else '',
                'installment_amount': float(inst.installment_amount) if hasattr(inst, 'installment_amount') and inst.installment_amount else 0.0,
                'remaining_Amount': float(inst.remaining_Amount) if hasattr(inst, 'remaining_Amount') and inst.remaining_Amount else 0.0,
                'amount_Paid': float(inst.amount_Paid) if hasattr(inst, 'amount_Paid') and inst.amount_Paid else 0.0,
                'penaties_Accrued': float(inst.penaties_Accrued) if hasattr(inst, 'penaties_Accrued') and inst.penaties_Accrued else 0.0,
                'plot_No': inst.plot_No if hasattr(inst, 'plot_No') else '',
                'plot_Name': inst.plot_Name if hasattr(inst, 'plot_Name') else '',
                'lead_file_no': inst.leadfile_no if hasattr(inst, 'leadfile_no') else '',
                'customer_name': leadfile.customer_name if leadfile else '',
                'project_id': leadfile.project_id if leadfile else '',
                'project_name': leadfile.project.name if leadfile and leadfile.project else '',
                'marketer_id': leadfile.marketer_id if leadfile else '',
                'marketer_name': leadfile.marketer.fullnames if leadfile and leadfile.marketer else ''
            })
        
        # Build stat string
        stat = "for Installments Due Today"
        if filter_params:
            if filter_params.get('restrict_to_marketer'):
                stat = f"for Marketer {filter_params.get('marketer_employee_no')}"
            elif filter_params.get('restrict_to_office'):
                stat = f"for {filter_params.get('office')}"
            elif filter_params.get('restrict_to_team'):
                stat = f"for Team {filter_params.get('team_id')}"
        
        title = f"Installments Due Today {stat}"
        return format_report_response(title, results, request)
            
    except Exception as e:
        logger.error(f"Error in installments_due_today: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate installments due today report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def overdue_collections_report(request):
    """
    Direct overdue collections report endpoint with permission checking and filtering.
    Queries database directly instead of proxying to reports app.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from receipts.models import InstallmentSchedule
        from sales.models import LeadFile
        from django.utils import timezone
        from datetime import date, timedelta, datetime
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has permission to view reports
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access overdue collections report without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports. Required: can_view_reports',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        project_id = request.GET.get('project_id', 'ALL')
        marketer_no = request.GET.get('marketer_no', 'ALL')
        team = request.GET.get('team', 'ALL')
        credit_officer = request.GET.get('credit_officer', 'ALL')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=30)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Overdue = installments with due_date < today and remaining amount > 0
        today = date.today()
        queryset = InstallmentSchedule.objects.filter(
            due_date__lt=today,
            due_date__gte=start_date,
            due_date__lte=end_date
        )
        
        # Filter for overdue (remaining amount > 0 or paid != 'Yes')
        from django.db.models import Q
        queryset = queryset.filter(
            Q(remaining_Amount__gt=0) | ~Q(paid='Yes')
        )
        
        # Apply permission filtering through lead files based on permission IDs
        # IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering
        if filter_params:
            # Get lead file numbers that match permission filters
            leadfile_queryset = LeadFile.objects.all()
            leadfile_queryset = PermissionFilterService.apply_leadfile_filters(leadfile_queryset, filter_params)
            allowed_leadfile_nos = list(leadfile_queryset.values_list('lead_file_no', flat=True))
            
            if allowed_leadfile_nos:
                queryset = queryset.filter(leadfile_no__in=allowed_leadfile_nos)
            else:
                # No lead files match permissions - return empty result
                queryset = queryset.none()
        
        # Apply additional filters through lead files
        if project_id and project_id != 'ALL':
            # Get lead files for this project
            project_leadfiles = LeadFile.objects.filter(project_id=project_id).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=project_leadfiles)
        
        if marketer_no and marketer_no != 'ALL':
            # Get lead files for this marketer
            marketer_leadfiles = LeadFile.objects.filter(marketer_id=marketer_no).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=marketer_leadfiles)
        
        if team and team != 'ALL':
            # Get lead files for this team
            team_leadfiles = LeadFile.objects.filter(marketer__team_id=team).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=team_leadfiles)
        
        if credit_officer and credit_officer != 'ALL':
            # Get lead files for this credit officer
            credit_officer_leadfiles = LeadFile.objects.filter(credit_officer_id=credit_officer).values_list('lead_file_no', flat=True)
            queryset = queryset.filter(leadfile_no__in=credit_officer_leadfiles)
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(queryset, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        # Get lead file data for serialization
        leadfile_nos = list(set([inst.leadfile_no for inst in paged_data.object_list if inst.leadfile_no]))
        leadfiles_dict = {}
        if leadfile_nos:
            leadfiles = LeadFile.objects.select_related('marketer', 'project').filter(lead_file_no__in=leadfile_nos)
            leadfiles_dict = {lf.lead_file_no: lf for lf in leadfiles}
        
        # Serialize results
        results = []
        for inst in paged_data.object_list:
            leadfile = leadfiles_dict.get(inst.leadfile_no) if inst.leadfile_no else None
            results.append({
                'no': inst.line_no if hasattr(inst, 'line_no') else '',
                'installment_no': inst.installment_no if hasattr(inst, 'installment_no') else '',
                'due_date': inst.due_date.strftime('%Y-%m-%d') if inst.due_date else '',
                'member_no': inst.member_no if hasattr(inst, 'member_no') else '',
                'installment_amount': float(inst.installment_amount) if hasattr(inst, 'installment_amount') and inst.installment_amount else 0.0,
                'remaining_Amount': float(inst.remaining_Amount) if hasattr(inst, 'remaining_Amount') and inst.remaining_Amount else 0.0,
                'amount_Paid': float(inst.amount_Paid) if hasattr(inst, 'amount_Paid') and inst.amount_Paid else 0.0,
                'penaties_Accrued': float(inst.penaties_Accrued) if hasattr(inst, 'penaties_Accrued') and inst.penaties_Accrued else 0.0,
                'plot_No': inst.plot_No if hasattr(inst, 'plot_No') else '',
                'plot_Name': inst.plot_Name if hasattr(inst, 'plot_Name') else '',
                'lead_file_no': inst.leadfile_no if hasattr(inst, 'leadfile_no') else '',
                'customer_name': leadfile.customer_name if leadfile else '',
                'project_id': leadfile.project_id if leadfile else '',
                'project_name': leadfile.project.name if leadfile and leadfile.project else '',
                'marketer_id': leadfile.marketer_id if leadfile else '',
                'marketer_name': leadfile.marketer.fullnames if leadfile and leadfile.marketer else ''
            })
        
        # Build stat string
        stat = "for ALL"
        if filter_params:
            if filter_params.get('restrict_to_marketer'):
                stat = f"for Marketer {filter_params.get('marketer_employee_no')}"
            elif filter_params.get('restrict_to_office'):
                stat = f"for {filter_params.get('office')}"
            elif filter_params.get('restrict_to_team'):
                stat = f"for Team {filter_params.get('team_id')}"
        
        title = f"Overdue Collections Reports from {start_date} to {end_date} {stat}"
        return format_report_response(title, results, request)
            
    except Exception as e:
        logger.error(f"Error in overdue_collections_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate overdue collections report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def prospects_report(request):
    """
    Direct prospects report endpoint with permission checking and filtering.
    Queries database directly instead of proxying to reports app.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request, PermissionFilterService
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from leads.models import Prospects
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            
            # Check if user has permission to view reports
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access prospects report without permission")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports. Required: can_view_reports',
                    'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        lead_type = request.GET.get('LEAD_TYPE', 'ALL')
        lead_status = request.GET.get('STATUS', 'ALL')
        conversion = request.GET.get('CONVERSION', 'ALL')
        lead_source_id = request.GET.get('LEAD_SOURCE', 'ALL')
        organization_team = request.GET.get('ORGANIZATION_TEAM', 'ALL')
        
        # Start with all prospects
        queryset = Prospects.objects.select_related('marketer', 'lead_source').all()
        
        # Apply permission filters based on permission IDs
        # IMPORTANT: Even in bypass mode, we respect permission IDs for data filtering
        # Bypass mode only bypasses permission checks, not data filtering
        # This ensures users only see what they have permissions for
        if filter_params:
            # Always apply marketer filter if restrict_to_marketer is True (even in bypass mode)
            if filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
                queryset = queryset.filter(marketer__employee_no=filter_params['marketer_employee_no'])
                logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']} (bypass mode: {ChatbotConfig.BYPASS_ALL_PERMISSIONS})")
            
            # Always apply team and office filters based on permission IDs (even in bypass mode)
            if filter_params.get('restrict_to_team') and filter_params.get('team_id'):
                queryset = queryset.filter(marketer__team_id=filter_params['team_id'])
                logger.debug(f"Applying team filter: {filter_params['team_id']} (bypass mode: {ChatbotConfig.BYPASS_ALL_PERMISSIONS})")
            
            if filter_params.get('restrict_to_office') and filter_params.get('office'):
                queryset = queryset.filter(marketer__office=filter_params['office'])
                logger.debug(f"Applying office filter: {filter_params['office']} (bypass mode: {ChatbotConfig.BYPASS_ALL_PERMISSIONS})")
        
        # Apply additional filters
        if lead_type and lead_type != 'ALL':
            queryset = queryset.filter(lead_type=lead_type)
        
        if lead_status and lead_status != 'ALL':
            queryset = queryset.filter(status=lead_status)
        
        if conversion and conversion != 'ALL':
            is_converted = (conversion == 'Converted')
            queryset = queryset.filter(is_converted=is_converted)
        
        if lead_source_id and lead_source_id != 'ALL':
            queryset = queryset.filter(lead_source_id=lead_source_id)
        
        if organization_team and organization_team != 'ALL':
            # Map organization team to department_id
            team_mapping = {
                'TELEMARKETING': 21,
                'DIGITAL': 10,
                'DIASPORA': None  # Handle separately if needed
            }
            if organization_team in team_mapping and team_mapping[organization_team]:
                queryset = queryset.filter(marketer__department_id=team_mapping[organization_team])
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(queryset, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        # Serialize results
        results = []
        for prospect in paged_data.object_list:
            results.append({
                'name': prospect.name or '',
                'phone': prospect.phone or '',
                'email': prospect.email or '',
                'marketer_id': prospect.marketer_id or '',
                'marketer_name': prospect.marketer.fullnames if prospect.marketer else '',
                'lead_source_id': prospect.lead_source_id or '',
                'lead_source_name': prospect.lead_source.name if prospect.lead_source else '',
                'project_id': prospect.project_id or '',
                'category': prospect.category or '',
                'lead_type': prospect.lead_type or '',
                'status': prospect.status or '',
                'is_converted': prospect.is_converted if hasattr(prospect, 'is_converted') else False
            })
        
        # Build stat string
        stat = "for ALL"
        if filter_params:
            if filter_params.get('restrict_to_marketer'):
                stat = f"for Marketer {filter_params.get('marketer_employee_no')}"
            elif filter_params.get('restrict_to_office'):
                stat = f"for {filter_params.get('office')}"
            elif filter_params.get('restrict_to_team'):
                stat = f"for Team {filter_params.get('team_id')}"
        
        p_stat = ""
        if lead_type != 'ALL':
            p_stat += f" {lead_type}"
        if lead_status != 'ALL':
            p_stat += f" {lead_status}"
        if conversion != 'ALL':
            p_stat += f" {conversion}"
        
        title = f"All{p_stat} prospects {stat}"
        return format_report_response(title, results, request)
            
    except Exception as e:
        logger.error(f"Error in prospects_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate prospects report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ============================================================================
# PERFORMANCE REPORTS
# ============================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
@report_error_handler("marketing periods report")
def marketing_periods_report(request):
    """
    Direct marketing periods report endpoint.
    Returns distinct marketing periods from marketer_targets table.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    # Check permissions
    permission_error = check_report_permission(user, "marketing periods report")
    if permission_error:
        return permission_error
    
    # Query marketing periods
    sql = """SELECT DISTINCT period_start_date, period_end_date 
             FROM marketer_targets 
             ORDER BY period_end_date DESC"""
    
    try:
        raw_data = execute_reports_query(sql)
        data = [serialize_dates(item.copy()) for item in raw_data]
    except Exception as e:
        logger.error(f"Database error in marketing_periods_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Database error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    return format_report_response('All Marketing Periods', data, request)


@api_view(['GET'])
@permission_classes([AllowAny])
@report_error_handler("marketers performance report")
def marketers_performance_report(request):
    """
    Direct marketers performance report endpoint.
    Returns individual marketer performance in marketing periods.
    """
    from .utils import get_request_user
    from ..services.permission_filters import extract_filter_params_from_request
    user = get_request_user(request)
    
    # Check permissions
    permission_error = check_report_permission(user, "marketers performance report")
    if permission_error:
        return permission_error
    
    # Extract filter parameters
    filter_params = extract_filter_params_from_request(request)
    
    # Get query parameters
    marketer_employee_no = request.GET.get('MARKETER_EMPLOYEE_NO', 'ALL')
    
    # Build WHERE clause
    where_clauses = "1=1"
    params = []
    stat = "for ALL"
    
    if marketer_employee_no and marketer_employee_no != "ALL":
        where_clauses = "m.marketer_no_id = %s"
        params = [marketer_employee_no]
        # Get marketer name
        try:
            marketer_data = execute_reports_query(
                "SELECT fullnames FROM users_user WHERE employee_no = %s",
                [marketer_employee_no]
            )
            marketer_fullname = marketer_data[0]['fullnames'] if marketer_data else marketer_employee_no
            stat = f"for marketer {marketer_fullname}"
        except Exception:
            stat = f"for marketer {marketer_employee_no}"
    
    # Apply permission filter if restrict_to_marketer
    if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
        if where_clauses == "1=1":
            where_clauses = "m.marketer_no_id = %s"
            params = [filter_params['marketer_employee_no']]
        else:
            where_clauses += " AND m.marketer_no_id = %s"
            params.append(filter_params['marketer_employee_no'])
        logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
    
    # Query database
    sql = f"""SELECT m.*, u.fullnames 
             FROM marketer_targets m 
             JOIN users_user u ON m.marketer_no_id = u.employee_no 
             WHERE {where_clauses} 
             ORDER BY m.period_end_date DESC"""
    
    try:
        data = execute_reports_query(sql, params)
    except Exception as e:
        logger.error(f"Database error in marketers_performance_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Database error: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    title = f'MIB performance {stat}'
    return format_report_response(title, data, request)


@api_view(['GET'])
@permission_classes([AllowAny])
def overall_marketer_performance_period_report(request):
    """
    Direct overall marketer performance per period report endpoint.
    Returns all marketer performance in a specific marketing period.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access overall marketer performance report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        marketer_employee_no = request.GET.get('MARKETER_EMPLOYEE_NO', 'ALL')
        
        # Build WHERE clause
        where_clauses = "1=1"
        params = []
        stat = "for ALL"
        
        if marketing_period and marketing_period != "ALL":
            where_clauses = "m.period_start_date = %s"
            params = [marketing_period]
            stat = f"for period started on {marketing_period}"
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses += " AND m.marketer_no_id = %s"
            params.append(marketer_employee_no)
            try:
                with connections["reports"].cursor() as cur:
                    cur.execute("SELECT fullnames FROM users_user WHERE employee_no = %s", [marketer_employee_no])
                    row = cur.fetchone()
                    marketer_fullname = row[0] if row else marketer_employee_no
                    stat += f" for marketer {marketer_fullname}"
            except Exception:
                stat += f" for marketer {marketer_employee_no}"
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            if where_clauses == "1=1":
                where_clauses = "m.marketer_no_id = %s"
                params = [filter_params['marketer_employee_no']]
            else:
                where_clauses += " AND m.marketer_no_id = %s"
                params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        # Query database
        sql = f"""SELECT m.*, u.fullnames 
                 FROM marketer_targets m 
                 JOIN users_user u ON m.marketer_no_id = u.employee_no 
                 WHERE {where_clauses} 
                 ORDER BY m.period_end_date DESC"""
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in overall_marketer_performance_period_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'MIB performance {stat}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'MIB performance {stat}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in overall_marketer_performance_period_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate overall marketer performance report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def overall_teams_performance_report(request):
    """
    Direct overall teams performance report endpoint.
    Returns team performance filtered by region and period.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access teams performance report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        region = request.GET.get('REGION', 'ALL')
        
        # Build WHERE clause
        where_clauses = "1=1"
        params = []
        stat = "for ALL"
        
        if marketing_period and marketing_period != "ALL":
            where_clauses = "period_start_date = %s"
            params = [marketing_period]
            stat = f"for period started on {marketing_period}"
        
        if region and region != "ALL":
            where_clauses += " AND team IN (SELECT team FROM users_teams WHERE office LIKE %s AND inactive=0)"
            params.append(f"%{region}%")
            stat += f" for {region}"
        
        # Query database
        sql = f"""SELECT * 
                 FROM teams_targets 
                 WHERE {where_clauses} 
                 ORDER BY period_start_date DESC"""
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in overall_teams_performance_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Team MIB performance {stat}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Team MIB performance {stat}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in overall_teams_performance_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate teams performance report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def overall_regions_performance_report(request):
    """
    Direct overall regions performance report endpoint.
    Returns HOS/GM performance filtered by region and period.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access regions performance report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        region = request.GET.get('REGION', 'ALL')
        
        # Build WHERE clause
        where_clauses = "1=1"
        params = []
        stat = "for ALL"
        
        if marketing_period and marketing_period != "ALL":
            where_clauses = "period_start_date = %s"
            params = [marketing_period]
            stat = f"for period started on {marketing_period}"
        
        if region and region != "ALL":
            if region == "ATLANTIC":
                where_clauses = "title LIKE 'Hos'"
            else:
                where_clauses = "title LIKE 'GM'"
            stat += f" for {region}"
        
        # Query database
        sql = f"""SELECT * 
                 FROM hos_gm_targets 
                 WHERE {where_clauses} 
                 ORDER BY period_start_date DESC"""
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in overall_regions_performance_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Team MIB performance {stat}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Team MIB performance {stat}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in overall_regions_performance_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate regions performance report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def commission_headers_report(request):
    """
    Direct commission headers report endpoint.
    Returns commission headers filtered by role, period, and marketer.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access commission headers report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        role = request.GET.get('ROLE', 'ALL')
        marketer_employee_no = request.GET.get('MARKETER_EMPLOYEE_NO', 'ALL')
        
        # Build WHERE clause
        where_clauses = "1=1"
        params = []
        stat = "for ALL"
        
        if marketing_period and marketing_period != "ALL":
            where_clauses = "period_start_date = %s"
            params = [marketing_period]
            stat = f"for period started on {marketing_period}"
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses += " AND emp_no_id = %s"
            params.append(marketer_employee_no)
            try:
                with connections["reports"].cursor() as cur:
                    cur.execute("SELECT fullnames FROM users_user WHERE employee_no = %s", [marketer_employee_no])
                    row = cur.fetchone()
                    marketer_fullname = row[0] if row else marketer_employee_no
                    stat += f" for marketer {marketer_fullname}"
            except Exception:
                stat += f" for marketer {marketer_employee_no}"
        
        if role and role != "ALL":
            if role == "ATLANTIC":
                ck = 'HoS'
            elif role == "PACIFIC":
                ck = 'HoS'
            elif role == "TEAM-LEADERS":
                ck = 'Leader'
            else:
                ck = 'Member'
            where_clauses += " AND role LIKE %s"
            params.append(f"%{ck}%")
            stat += f" for {role}"
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            if where_clauses == "1=1":
                where_clauses = "emp_no_id = %s"
                params = [filter_params['marketer_employee_no']]
            else:
                where_clauses += " AND emp_no_id = %s"
                params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        # Query database
        sql = f"""SELECT * 
                 FROM commission_headers 
                 WHERE {where_clauses} 
                 ORDER BY period_start_date DESC"""
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in commission_headers_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Commission Headers Report {stat}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Commission Headers Report {stat}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in commission_headers_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate commission headers report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def commission_lines_report(request):
    """
    Direct commission lines report endpoint.
    Returns commission lines filtered by period and marketer.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access commission lines report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        marketer_employee_no = request.GET.get('MARKETER_EMPLOYEE_NO', 'ALL')
        
        # Build WHERE clause
        where_clauses = "1=1"
        params = []
        stat = "for ALL"
        
        if marketing_period and marketing_period != "ALL":
            where_clauses = "period_start_date = %s"
            params = [marketing_period]
            stat = f"for period started on {marketing_period}"
        
        if marketer_employee_no and marketer_employee_no != "ALL":
            where_clauses += " AND marketer_no_id = %s"
            params.append(marketer_employee_no)
            try:
                with connections["reports"].cursor() as cur:
                    cur.execute("SELECT fullnames FROM users_user WHERE employee_no = %s", [marketer_employee_no])
                    row = cur.fetchone()
                    marketer_fullname = row[0] if row else marketer_employee_no
                    stat += f" for marketer {marketer_fullname}"
            except Exception:
                stat += f" for marketer {marketer_employee_no}"
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            if where_clauses == "1=1":
                where_clauses = "marketer_no_id = %s"
                params = [filter_params['marketer_employee_no']]
            else:
                where_clauses += " AND marketer_no_id = %s"
                params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        # Query database
        sql = f"""SELECT * 
                 FROM commission_lines 
                 WHERE {where_clauses} 
                 ORDER BY period_start_date DESC"""
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in commission_lines_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Commission Lines Report {stat}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Commission Lines Report {stat}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in commission_lines_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate commission lines report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def team_marketer_targets_report(request):
    """
    Direct team marketer targets report endpoint.
    Returns team targets with team leader and members + their targets in the current marketing period.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access team marketer targets report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        team_leader_employee_no = request.GET.get('TEAM_LEADER_EMPLOYEE_NO', 'ALL')
        marketing_period = request.GET.get('MARKETING_PERIOD', 'ALL')
        params = []
        
        # Determine period filter
        where_period = ""
        if marketing_period and marketing_period != "ALL":
            where_period = "tt.period_start_date = %s"
            params.append(marketing_period)
        elif marketing_period == "ALL":
            where_period = "1=1"
        else:
            # Default to latest
            try:
                with connections["reports"].cursor() as cur:
                    cur.execute("SELECT MAX(period_start_date) FROM teams_targets")
                    latest_period = cur.fetchone()[0]
                if not latest_period:
                    return Response({
                        'success': True,
                        'data': []
                    })
                where_period = "tt.period_start_date = %s"
                params.append(latest_period)
            except Exception as e:
                logger.error(f"Error getting latest period: {str(e)}")
                where_period = "1=1"
        
        # Team leader filter
        where_clauses = "1=1"
        if team_leader_employee_no and team_leader_employee_no != "ALL":
            where_clauses = "t.tl_code = %s"
            params.append(team_leader_employee_no)
        
        # Query teams
        sql = f"""
            SELECT 
                tt.line_no,
                tt.team,
                tt.period_start_date,
                tt.period_end_date,
                tt.monthly_target,
                tt.daily_target,
                tt.MIB_achieved,
                tt.MIB_Perfomance,
                u.employee_no AS leader_employee_no,
                u.fullnames AS leader_fullnames
            FROM teams_targets tt
            JOIN users_teams t ON t.team = tt.team
            LEFT JOIN users_user u ON u.employee_no = t.tl_code
            WHERE {where_period}
            AND {where_clauses}
            ORDER BY tt.period_start_date DESC, tt.team ASC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                team_data = [dict(zip(cols, row)) for row in cur.fetchall()]
            
            # Fetch members for each team/period
            for team in team_data:
                team_code = team["team"]
                period_start_date = team["period_start_date"]
                
                try:
                    with connections["reports"].cursor() as cur:
                        cur.execute(
                            """
                            SELECT uu.employee_no, uu.fullnames,
                                mt.period_start_date, mt.period_end_date,
                                mt.monthly_target, mt.daily_target,
                                mt.MIB_achieved, mt.MIB_Perfomance
                            FROM users_user uu
                            LEFT JOIN marketer_targets mt 
                                ON uu.employee_no = mt.marketer_no_id
                            AND mt.period_start_date = %s
                            WHERE uu.team_id = %s AND uu.is_marketer = 1
                            ORDER BY uu.fullnames ASC
                            """,
                            [period_start_date, team_code],
                        )
                        member_cols = [c[0] for c in cur.description]
                        members = [dict(zip(member_cols, row)) for row in cur.fetchall()]
                    team["members"] = members
                except Exception as e:
                    logger.error(f"Error fetching members for team {team_code}: {str(e)}")
                    team["members"] = []
            
            return Response({
                'success': True,
                'data': team_data
            })
            
        except Exception as e:
            logger.error(f"Database error in team_marketer_targets_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    except Exception as e:
        logger.error(f"Error in team_marketer_targets_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate team marketer targets report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# ============================================================================
# LOGISTICS REPORTS
# ============================================================================

@api_view(['GET'])
@permission_classes([AllowAny])
def site_visits_dashboard_report(request):
    """
    Direct site visits dashboard report endpoint.
    Provides summary statistics with breakdown by status.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from datetime import date, timedelta, datetime
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access site visits dashboard")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get date range
        date_range = request.GET.get('date_range', 'this_week')
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        
        today = date.today()
        if date_range == 'today':
            start_date = today
            end_date = today
        elif date_range == 'this_week':
            start_date = today - timedelta(days=today.weekday())
            end_date = start_date + timedelta(days=6)
        elif date_range == 'this_month':
            start_date = today.replace(day=1)
            if start_date.month == 12:
                end_date = start_date.replace(year=start_date.year + 1, month=1) - timedelta(days=1)
            else:
                end_date = start_date.replace(month=start_date.month + 1) - timedelta(days=1)
        elif date_range == 'this_quarter':
            quarter_start_month = ((today.month - 1) // 3) * 3 + 1
            start_date = today.replace(month=quarter_start_month, day=1)
            end_date = start_date.replace(month=start_date.month + 2)
            end_date = end_date.replace(day=1) + timedelta(days=32)
            end_date = end_date.replace(day=1) - timedelta(days=1)
        elif date_range == 'this_year':
            start_date = today.replace(month=1, day=1)
            end_date = today.replace(month=12, day=31)
        else:
            # Use custom dates
            if start_date_str:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            else:
                start_date = today - timedelta(days=30)
            if end_date_str:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            else:
                end_date = today
        
        # Get current period statistics
        current_stats_sql = """
            SELECT 
                COUNT(*) as all_site_visits,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as pending_site_visits,
                COUNT(CASE WHEN status = 'Approved' THEN 1 END) as approved_site_visits,
                COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as enroute_site_visits,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as completed_site_visits,
                COUNT(CASE WHEN status = 'Rejected' THEN 1 END) as rejected_site_visits,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as cancelled_site_visits
            FROM logistics_sitevisit 
            WHERE pickup_date BETWEEN %s AND %s
        """
        
        # Get previous period for comparison
        period_days = (end_date - start_date).days + 1
        prev_start_date = start_date - timedelta(days=period_days)
        prev_end_date = start_date - timedelta(days=1)
        
        prev_stats_sql = """
            SELECT 
                COUNT(*) as prev_all_site_visits,
                COUNT(CASE WHEN status = 'Pending' THEN 1 END) as prev_pending_site_visits,
                COUNT(CASE WHEN status = 'Approved' THEN 1 END) as prev_approved_site_visits,
                COUNT(CASE WHEN status = 'In Progress' THEN 1 END) as prev_enroute_site_visits,
                COUNT(CASE WHEN status = 'Completed' THEN 1 END) as prev_completed_site_visits,
                COUNT(CASE WHEN status = 'Rejected' THEN 1 END) as prev_rejected_site_visits,
                COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) as prev_cancelled_site_visits
            FROM logistics_sitevisit 
            WHERE pickup_date BETWEEN %s AND %s
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(current_stats_sql, [start_date, end_date])
                cols = [c[0] for c in cur.description]
                current_stats = dict(zip(cols, cur.fetchone()))
                
                cur.execute(prev_stats_sql, [prev_start_date, prev_end_date])
                cols = [c[0] for c in cur.description]
                prev_stats = dict(zip(cols, cur.fetchone()))
        except Exception as e:
            logger.error(f"Database error in site_visits_dashboard_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        # Calculate percentage changes
        def calculate_percentage_change(current, previous):
            if previous == 0:
                return 100 if current > 0 else 0
            return round(((current - previous) / previous) * 100, 1)
        
        status_breakdown = {
            'all_site_visits': {
                'count': current_stats['all_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['all_site_visits'], 
                    prev_stats['prev_all_site_visits']
                ),
                'period': date_range
            },
            'pending_site_visits': {
                'count': current_stats['pending_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['pending_site_visits'], 
                    prev_stats['prev_pending_site_visits']
                ),
                'period': date_range
            },
            'approved_site_visits': {
                'count': current_stats['approved_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['approved_site_visits'], 
                    prev_stats['prev_approved_site_visits']
                ),
                'period': date_range
            },
            'enroute_site_visits': {
                'count': current_stats['enroute_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['enroute_site_visits'], 
                    prev_stats['prev_enroute_site_visits']
                ),
                'period': date_range
            },
            'completed_site_visits': {
                'count': current_stats['completed_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['completed_site_visits'], 
                    prev_stats['prev_completed_site_visits']
                ),
                'period': date_range
            },
            'rejected_site_visits': {
                'count': current_stats['rejected_site_visits'],
                'change_percentage': calculate_percentage_change(
                    current_stats['rejected_site_visits'], 
                    prev_stats['prev_rejected_site_visits']
                ),
                'period': date_range
            }
        }
        
        completion_rate = round(
            (current_stats['completed_site_visits'] * 100.0 / 
             max(current_stats['all_site_visits'], 1)), 2
        )
        approval_rate = round(
            (current_stats['approved_site_visits'] * 100.0 / 
             max(current_stats['all_site_visits'], 1)), 2
        )
        
        return Response({
            'success': True,
            'data': {
                'title': f'Site Visits Dashboard - {date_range.replace("_", " ").title()}',
                'period': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'range': date_range,
                    'days': period_days
                },
                'summary': {
                    'total_site_visits': current_stats['all_site_visits'],
                    'completion_rate': completion_rate,
                    'approval_rate': approval_rate
                },
                'status_breakdown': status_breakdown
            }
        })
        
    except Exception as e:
        logger.error(f"Error in site_visits_dashboard_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate site visits dashboard: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def approved_site_visits_report(request):
    """
    Direct approved site visits report endpoint.
    Returns all approved site visits with marketer and project details.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from datetime import date, datetime, timedelta
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access approved site visits report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        marketer_employee_no = request.GET.get('marketer_employee_no')
        project_id = request.GET.get('project_id')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=30)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build WHERE clauses
        where_clauses = ["sv.status = 'Approved'"]
        params = [start_date, end_date]
        
        if marketer_employee_no:
            where_clauses.append("sv.marketer_id = %s")
            params.append(marketer_employee_no)
        
        if project_id:
            where_clauses.append("sv.project_id = %s")
            params.append(project_id)
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            where_clauses.append("sv.marketer_id = %s")
            params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        sql = f"""
            SELECT 
                sv.id as site_visit_id,
                sv.pickup_date,
                sv.pickup_time,
                sv.pickup_location,
                sv.status,
                sv.remarks,
                sv.created_at,
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                p.projectId as project_id,
                v.vehicle_registration,
                v.make as vehicle_make,
                v.model as vehicle_model,
                ud.fullnames as driver_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                COUNT(svc.id) as client_count
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_vehicle v ON sv.vehicle_id = v.id
            LEFT JOIN users_user ud ON sv.driver_id = ud.employee_no
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            WHERE {' AND '.join(where_clauses)} 
                AND sv.pickup_date BETWEEN %s AND %s
            GROUP BY sv.id, sv.pickup_date, sv.pickup_time, sv.pickup_location, sv.status, 
                     sv.remarks, sv.created_at, u.fullnames, sv.marketer_id, p.name, p.projectId,
                     v.vehicle_registration, v.make, v.model, ud.fullnames
            ORDER BY sv.pickup_date DESC, sv.pickup_time DESC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in approved_site_visits_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Approved Site Visits Report from {start_date} to {end_date}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Approved Site Visits Report from {start_date} to {end_date}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in approved_site_visits_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate approved site visits report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def site_visits_summary_report(request):
    """
    Direct site visits summary report endpoint.
    Provides aggregated statistics by marketer and project.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from datetime import date, datetime, timedelta
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access site visits summary report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=30)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build WHERE clauses
        where_clauses = []
        params = [start_date, end_date]
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            where_clauses.append("sv.marketer_id = %s")
            params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        where_clause = " AND " + " AND ".join(where_clauses) if where_clauses else ""
        
        sql = f"""
            SELECT 
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                COUNT(sv.id) as total_site_visits,
                COUNT(CASE WHEN sv.status = 'Pending' THEN 1 END) as pending_visits,
                COUNT(CASE WHEN sv.status = 'Approved' THEN 1 END) as approved_visits,
                COUNT(CASE WHEN sv.status = 'In Progress' THEN 1 END) as enroute_visits,
                COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) as completed_visits,
                COUNT(CASE WHEN sv.status = 'Rejected' THEN 1 END) as rejected_visits,
                COUNT(CASE WHEN sv.status = 'Cancelled' THEN 1 END) as cancelled_visits,
                ROUND(
                    (COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(sv.id), 0)), 2
                ) as completion_rate,
                ROUND(
                    (COUNT(CASE WHEN sv.status = 'Approved' THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(sv.id), 0)), 2
                ) as approval_rate
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            WHERE sv.pickup_date BETWEEN %s AND %s{where_clause}
            GROUP BY sv.marketer_id, u.fullnames, p.name
            ORDER BY total_site_visits DESC, completion_rate DESC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in site_visits_summary_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Site Visits Summary Report from {start_date} to {end_date}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Site Visits Summary Report from {start_date} to {end_date}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in site_visits_summary_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate site visits summary report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def most_booked_sites_report(request):
    """
    Direct most booked sites report endpoint.
    Shows sites with highest booking success rates from completed site visits.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from datetime import date, datetime, timedelta
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access most booked sites report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=30)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        sql = """
            SELECT 
                COALESCE(p.name, 'Unknown Project') as project_name,
                COALESCE(p.projectId, 'N/A') as project_id,
                COUNT(sv.id) as total_site_visits,
                COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END) as completed_visits,
                COUNT(CASE WHEN svs.booked = 1 THEN 1 END) as successful_bookings,
                COUNT(CASE WHEN svs.visited = 1 THEN 1 END) as actual_visits,
                ROUND(
                    (COUNT(CASE WHEN svs.booked = 1 THEN 1 END) * 100.0 / 
                     NULLIF(COUNT(CASE WHEN sv.status = 'Completed' THEN 1 END), 0)), 2
                ) as booking_conversion_rate,
                COALESCE(SUM(CASE WHEN svs.amount_reserved IS NOT NULL THEN svs.amount_reserved ELSE 0 END), 0) as total_reserved_amount,
                COALESCE(AVG(CASE WHEN svs.amount_reserved IS NOT NULL THEN svs.amount_reserved ELSE 0 END), 0) as avg_reserved_amount
            FROM logistics_sitevisit sv
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_sitevisitsurvey svs ON sv.id = svs.site_visit_id
            WHERE sv.pickup_date BETWEEN %s AND %s
                AND sv.status = 'Completed'
            GROUP BY p.projectId, p.name
            HAVING successful_bookings > 0
            ORDER BY booking_conversion_rate DESC, successful_bookings DESC, total_reserved_amount DESC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, [start_date, end_date])
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in most_booked_sites_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Most Booked Sites Report from {start_date} to {end_date}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Most Booked Sites Report from {start_date} to {end_date}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in most_booked_sites_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate most booked sites report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def chauffeur_itinerary_report(request):
    """
    Direct chauffeur itinerary report endpoint.
    Shows driver schedules and assignments.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from datetime import date, datetime, timedelta
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access chauffeur itinerary report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        driver_employee_no = request.GET.get('driver_employee_no')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today()
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today() + timedelta(days=7)
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build WHERE clauses
        where_clauses = []
        params = [start_date, end_date]
        
        if driver_employee_no:
            where_clauses.append("sv.driver_id = %s")
            params.append(driver_employee_no)
        
        where_clause = " AND " + " AND ".join(where_clauses) if where_clauses else ""
        
        sql = f"""
            SELECT 
                ud.fullnames as driver_name,
                sv.driver_id,
                sv.pickup_date,
                sv.pickup_time,
                sv.pickup_location,
                sv.status,
                v.vehicle_registration,
                v.make as vehicle_make,
                v.model as vehicle_model,
                u.fullnames as marketer_name,
                p.name as project_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                COUNT(svc.id) as client_count,
                sv.remarks,
                CASE 
                    WHEN sv.pickup_time < '12:00:00' THEN 'Morning'
                    WHEN sv.pickup_time < '18:00:00' THEN 'Afternoon'
                    ELSE 'Evening'
                END as time_slot
            FROM logistics_sitevisit sv
            LEFT JOIN users_user ud ON sv.driver_id = ud.employee_no
            LEFT JOIN logistics_vehicle v ON sv.vehicle_id = v.id
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            WHERE sv.pickup_date BETWEEN %s AND %s{where_clause}
            GROUP BY sv.id, ud.fullnames, sv.driver_id, sv.pickup_date, sv.pickup_time, 
                     sv.pickup_location, sv.status, v.vehicle_registration, v.make, v.model,
                     u.fullnames, p.name, sv.remarks
            ORDER BY sv.pickup_date ASC, sv.pickup_time ASC, ud.fullnames ASC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in chauffeur_itinerary_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f'Chauffeur Itinerary Report from {start_date} to {end_date}',
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f'Chauffeur Itinerary Report from {start_date} to {end_date}',
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in chauffeur_itinerary_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate chauffeur itinerary report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def marketers_feedback_report(request):
    """
    Direct marketers feedback report endpoint.
    Shows site visit outcomes and feedback from completed site visits.
    """
    from .utils import get_request_user
    user = get_request_user(request)
    
    try:
        from ..services.permission_filters import extract_filter_params_from_request
        from ..services.lightweight_permissions import LightweightPermissionService, UnifiedChatbotPermissionSystem
        from ..config import ChatbotConfig
        from django.db import connections
        from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
        from datetime import date, datetime, timedelta
        
        # Check permissions unless bypass mode is enabled
        if not ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            session_perms = LightweightPermissionService.get_session_permissions(user)
            if 'can_view_reports' not in session_perms.available_tools and \
               not UnifiedChatbotPermissionSystem.check_permission(user, 'can_view_reports'):
                logger.warning(f"Permission denied: User {getattr(user, 'employee_no', 'unknown')} attempted to access marketers feedback report")
                return Response({
                    'success': False,
                    'error': 'Permission denied',
                    'message': 'You do not have permission to view reports.'
                }, status=status.HTTP_403_FORBIDDEN)
        
        # Extract filter parameters
        filter_params = extract_filter_params_from_request(request)
        
        # Get query parameters
        start_date_str = request.GET.get('start_date')
        end_date_str = request.GET.get('end_date')
        marketer_employee_no = request.GET.get('marketer_employee_no')
        feedback_status = request.GET.get('feedback_status', 'all')
        
        # Set default dates if not provided
        if not start_date_str:
            start_date = date.today() - timedelta(days=30)
        else:
            try:
                start_date = datetime.strptime(start_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid start_date format. Use YYYY-MM-DD."}, status=400)
        
        if not end_date_str:
            end_date = date.today()
        else:
            try:
                end_date = datetime.strptime(end_date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response({"error": "Invalid end_date format. Use YYYY-MM-DD."}, status=400)
        
        if start_date > end_date:
            return Response({"error": "Start date must be before end date."}, status=400)
        
        # Build WHERE clauses
        where_clauses = ["sv.status = 'Completed'"]
        params = [start_date, end_date]
        
        if marketer_employee_no:
            where_clauses.append("sv.marketer_id = %s")
            params.append(marketer_employee_no)
        
        if feedback_status != "all":
            if feedback_status == "booked":
                where_clauses.append("svs.booked = 1")
            elif feedback_status == "not_booked":
                where_clauses.append("svs.booked = 0")
            elif feedback_status == "visited":
                where_clauses.append("svs.visited = 1")
            elif feedback_status == "not_visited":
                where_clauses.append("svs.visited = 0")
        
        # Apply permission filter if restrict_to_marketer
        if filter_params and filter_params.get('restrict_to_marketer') and filter_params.get('marketer_employee_no'):
            where_clauses.append("sv.marketer_id = %s")
            params.append(filter_params['marketer_employee_no'])
            logger.debug(f"Applying marketer filter: {filter_params['marketer_employee_no']}")
        
        sql = f"""
            SELECT 
                sv.id as site_visit_id,
                sv.pickup_date,
                u.fullnames as marketer_name,
                sv.marketer_id,
                p.name as project_name,
                GROUP_CONCAT(CONCAT(svc.name, ' (', svc.phone_number, ')') SEPARATOR '; ') as clients,
                svs.booked,
                svs.visited,
                svs.amount_reserved,
                svs.plot_details,
                svs.reason_not_visited,
                svs.reason_not_booked,
                sv.remarks as visit_remarks,
                CASE 
                    WHEN svs.booked = 1 THEN 'Successfully Booked'
                    WHEN svs.visited = 1 AND svs.booked = 0 THEN 'Visited but Not Booked'
                    WHEN svs.visited = 0 THEN 'Did Not Visit'
                    ELSE 'No Feedback'
                END as outcome_summary
            FROM logistics_sitevisit sv
            LEFT JOIN users_user u ON sv.marketer_id = u.employee_no
            LEFT JOIN inventory_project p ON sv.project_id = p.projectId
            LEFT JOIN logistics_sitevisitclient svc ON sv.id = svc.site_visit_id
            LEFT JOIN logistics_sitevisitsurvey svs ON sv.id = svs.site_visit_id
            WHERE {' AND '.join(where_clauses)} 
                AND sv.pickup_date BETWEEN %s AND %s
            GROUP BY sv.id, sv.pickup_date, u.fullnames, sv.marketer_id, p.name,
                     svs.booked, svs.visited, svs.amount_reserved, svs.plot_details,
                     svs.reason_not_visited, svs.reason_not_booked, sv.remarks
            ORDER BY sv.pickup_date DESC
        """
        
        try:
            with connections["reports"].cursor() as cur:
                cur.execute(sql, params)
                cols = [c[0] for c in cur.description]
                data = [dict(zip(cols, row)) for row in cur.fetchall()]
        except Exception as e:
            logger.error(f"Database error in marketers_feedback_report: {str(e)}")
            return Response({
                'success': False,
                'error': f'Database error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
        if not data:
            return Response({
                'success': True,
                'data': {
                    'Title': f"Marketers' Feedback Report from {start_date} to {end_date}",
                    'Total Results': 0,
                    'count': 0,
                    'num_pages': 0,
                    'current_page': 1,
                    'results': []
                }
            })
        
        # Pagination
        page = request.GET.get('page', 1)
        page_size = request.GET.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20
        
        paginator = Paginator(data, page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)
        
        return Response({
            'success': True,
            'data': {
                'Title': f"Marketers' Feedback Report from {start_date} to {end_date}",
                'Total Results': paginator.count,
                'count': paginator.count,
                'num_pages': paginator.num_pages,
                'current_page': paged_data.number,
                'results': paged_data.object_list
            }
        })
        
    except Exception as e:
        logger.error(f"Error in marketers_feedback_report: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return Response({
            'success': False,
            'error': f'Failed to generate marketers feedback report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
