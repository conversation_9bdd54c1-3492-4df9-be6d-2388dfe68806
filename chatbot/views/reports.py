"""
Chatbot Report Endpoints
========================

Proxy endpoints to connect chatbot with the reports app.
These endpoints provide a chatbot-friendly interface to existing reports.
"""

from django.http import JsonResponse
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
import requests
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


def get_reports_base_url():
    """Get the base URL for reports API"""
    return f"{settings.BASE_URL}/api/reports/" if hasattr(settings, 'BASE_URL') else "http://localhost:8000/api/reports/"


@api_view(['GET'])
@permission_classes([AllowAny])
def generate_sales_report(request):
    """
    Proxy to general sales report endpoint.
    This is a generic sales report endpoint for the chatbot.
    """
    try:
        # For now, redirect to new sales report
        return new_sales_report(request)
    except Exception as e:
        logger.error(f"Error in generate_sales_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate sales report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def new_sales_report(request):
    """
    Proxy to reports app New-Sales endpoint.
    """
    try:
        reports_url = f"{get_reports_base_url()}New-Sales/"
        
        # Forward query parameters
        params = dict(request.GET)
        
        # Set default parameters if not provided
        if 'start_date' not in params:
            from datetime import date, timedelta
            params['start_date'] = (date.today() - timedelta(days=7)).isoformat()
        if 'end_date' not in params:
            from datetime import date
            params['end_date'] = date.today().isoformat()
        
        # Make request to reports app
        response = requests.get(reports_url, params=params, timeout=30)
        
        if response.status_code == 200:
            return Response({
                'success': True,
                'data': response.json(),
                'message': 'New sales report generated successfully'
            })
        else:
            return Response({
                'success': False,
                'error': f'Reports API returned status {response.status_code}',
                'details': response.text[:500]
            }, status=response.status_code)
            
    except requests.RequestException as e:
        logger.error(f"Error connecting to reports API: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to connect to reports service: {str(e)}'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error in new_sales_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate new sales report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def mib_report(request):
    """
    Proxy to reports app MIB-report endpoint.
    """
    try:
        reports_url = f"{get_reports_base_url()}MIB-report/"
        
        # Forward query parameters
        params = dict(request.GET)
        
        # Set default parameters if not provided
        if 'start_date' not in params:
            from datetime import date, timedelta
            params['start_date'] = (date.today() - timedelta(days=7)).isoformat()
        if 'end_date' not in params:
            from datetime import date
            params['end_date'] = date.today().isoformat()
        
        # Make request to reports app
        response = requests.get(reports_url, params=params, timeout=30)
        
        if response.status_code == 200:
            return Response({
                'success': True,
                'data': response.json(),
                'message': 'MIB report generated successfully'
            })
        else:
            return Response({
                'success': False,
                'error': f'Reports API returned status {response.status_code}',
                'details': response.text[:500]
            }, status=response.status_code)
            
    except requests.RequestException as e:
        logger.error(f"Error connecting to reports API: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to connect to reports service: {str(e)}'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error in mib_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate MIB report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def installments_due_today(request):
    """
    Proxy to reports app Installmets-due-today endpoint.
    """
    try:
        reports_url = f"{get_reports_base_url()}Installmets-due-today/"
        
        # Forward query parameters
        params = dict(request.GET)
        
        # Make request to reports app
        response = requests.get(reports_url, params=params, timeout=30)
        
        if response.status_code == 200:
            return Response({
                'success': True,
                'data': response.json(),
                'message': 'Installments due today report generated successfully'
            })
        else:
            return Response({
                'success': False,
                'error': f'Reports API returned status {response.status_code}',
                'details': response.text[:500]
            }, status=response.status_code)
            
    except requests.RequestException as e:
        logger.error(f"Error connecting to reports API: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to connect to reports service: {str(e)}'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error in installments_due_today: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate installments due today report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def overdue_collections_report(request):
    """
    Proxy to reports app overdue-collections endpoint.
    """
    try:
        reports_url = f"{get_reports_base_url()}overdue-collections/"
        
        # Forward query parameters
        params = dict(request.GET)
        
        # Set default parameters if not provided
        if 'start_date' not in params:
            from datetime import date, timedelta
            params['start_date'] = (date.today() - timedelta(days=30)).isoformat()
        if 'end_date' not in params:
            from datetime import date
            params['end_date'] = date.today().isoformat()
        
        # Make request to reports app
        response = requests.get(reports_url, params=params, timeout=30)
        
        if response.status_code == 200:
            return Response({
                'success': True,
                'data': response.json(),
                'message': 'Overdue collections report generated successfully'
            })
        else:
            return Response({
                'success': False,
                'error': f'Reports API returned status {response.status_code}',
                'details': response.text[:500]
            }, status=response.status_code)
            
    except requests.RequestException as e:
        logger.error(f"Error connecting to reports API: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to connect to reports service: {str(e)}'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error in overdue_collections_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate overdue collections report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([AllowAny])
def prospects_report(request):
    """
    Proxy to reports app all-prospects-view endpoint.
    """
    try:
        reports_url = f"{get_reports_base_url()}all-prospects-view/"
        
        # Forward query parameters
        params = dict(request.GET)
        
        # Make request to reports app
        response = requests.get(reports_url, params=params, timeout=30)
        
        if response.status_code == 200:
            return Response({
                'success': True,
                'data': response.json(),
                'message': 'Prospects report generated successfully'
            })
        else:
            return Response({
                'success': False,
                'error': f'Reports API returned status {response.status_code}',
                'details': response.text[:500]
            }, status=response.status_code)
            
    except requests.RequestException as e:
        logger.error(f"Error connecting to reports API: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to connect to reports service: {str(e)}'
        }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
    except Exception as e:
        logger.error(f"Error in prospects_report: {str(e)}")
        return Response({
            'success': False,
            'error': f'Failed to generate prospects report: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
