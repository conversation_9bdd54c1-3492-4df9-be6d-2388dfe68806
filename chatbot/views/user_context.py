"""
User Context Capture Endpoint
==============================

Endpoint for capturing user details and permissions from frontend.
Generates and caches filter parameters based on user permissions.
"""

import logging
import json
from typing import Dict, Any
from django.utils import timezone
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from users.models import User
from ..config import ChatbotConfig
from ..services.permission_filters import PermissionFilterService

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([AllowAny])
def capture_user_context(request):
    """
    Capture user context from frontend and generate filter parameters.
    
    Expected request body:
    {
        "userContext": {
            "employee_no": "EMP001",
            "email": "<EMAIL>",
            "department": "Sales",
            "office": "Nairobi",
            "team": "Team A",
            "user_group": "Marketers",
            "user_permissions": [
                {"permission_name": "can_view_own_customers"},
                {"permission_name": "can_view_own_prospects"}
            ]
        },
        "session_id": "unique-session-id",
        "chatInput": "search for customer"
    }
    
    Returns:
    {
        "success": true,
        "session_id": "unique-session-id",
        "user_id": "EMP001",
        "filter_params": {...},
        "bypass_mode": true/false,
        "cached": true,
        "timestamp": "2024-..."
    }
    """
    try:
        # Extract data from request
        data = request.data if hasattr(request, 'data') else json.loads(request.body)
        user_context = data.get('userContext', {})
        session_id = data.get('session_id') or data.get('sessionId') or f"session_{int(timezone.now().timestamp())}"
        
        # Validate required fields
        employee_no = user_context.get('employee_no')
        if not employee_no:
            return Response({
                'success': False,
                'error': 'employee_no is required',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Check if bypass mode is enabled
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.info(f"BYPASS MODE: User context captured for {employee_no} but filters will not be applied")
            
            # Still cache for tracking purposes
            context_data = {
                'user_context': user_context,
                'filter_params': None,
                'session_id': session_id,
                'bypass_mode': True,
                'timestamp': timezone.now().isoformat()
            }
            
            PermissionFilterService.cache_user_context(session_id, context_data)
            
            return Response({
                'success': True,
                'session_id': session_id,
                'user_id': employee_no,
                'filter_params': None,
                'bypass_mode': True,
                'message': 'User context captured (bypass mode active - no filtering)',
                'cached': True,
                'timestamp': timezone.now().isoformat()
            })
        
        # Validate user exists in database
        try:
            user = User.objects.get(employee_no=employee_no)
        except User.DoesNotExist:
            logger.warning(f"User {employee_no} not found in database")
            return Response({
                'success': False,
                'error': f'User {employee_no} not found',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_404_NOT_FOUND)
        
        # Extract permissions from user_context
        user_permissions = user_context.get('user_permissions', [])
        permission_names = []
        
        # Handle different permission formats
        if isinstance(user_permissions, list):
            for perm in user_permissions:
                if isinstance(perm, dict):
                    perm_name = perm.get('permission_name')
                    if perm_name:
                        permission_names.append(perm_name)
                elif isinstance(perm, str):
                    permission_names.append(perm)
        
        # Generate filter parameters
        filter_params = PermissionFilterService.generate_filters_from_permissions(
            user, permission_names
        )
        
        # Enhance filter_params with additional user context
        filter_params.update({
            'email': user_context.get('email'),
            'department': user_context.get('department'),
            'user_group': user_context.get('user_group')
        })
        
        # Cache user context with filter parameters
        context_data = {
            'user_context': user_context,
            'filter_params': filter_params,
            'session_id': session_id,
            'bypass_mode': False,
            'timestamp': timezone.now().isoformat()
        }
        
        PermissionFilterService.cache_user_context(session_id, context_data)
        
        logger.info(
            f"User context captured for {employee_no} - "
            f"Session: {session_id}, "
            f"Permissions: {len(permission_names)}, "
            f"Filters: marketer={filter_params.get('restrict_to_marketer')}, "
            f"team={filter_params.get('restrict_to_team')}, "
            f"office={filter_params.get('restrict_to_office')}"
        )
        
        return Response({
            'success': True,
            'session_id': session_id,
            'user_id': employee_no,
            'filter_params': filter_params,
            'bypass_mode': False,
            'message': 'User context captured and filters generated',
            'cached': True,
            'cache_ttl': PermissionFilterService.USER_CONTEXT_CACHE_TTL,
            'timestamp': timezone.now().isoformat(),
            'restrictions': {
                'marketer': filter_params.get('restrict_to_marketer', False),
                'team': filter_params.get('restrict_to_team', False),
                'office': filter_params.get('restrict_to_office', False)
            }
        })
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in request body: {str(e)}")
        return Response({
            'success': False,
            'error': 'Invalid JSON format',
            'message': str(e),
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.exception(f"Error capturing user context: {str(e)}")
        return Response({
            'success': False,
            'error': 'Failed to capture user context',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

