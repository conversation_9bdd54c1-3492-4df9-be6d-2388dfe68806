"""
Permission Hints API Endpoint
============================

Ultra-fast permission hints API for N8N chatbot workflow integration.
Provides cached permission data with <50ms response time.

Endpoints:
- GET /api/chatbot/permissions/hints/ - Get user permission hints
- POST /api/chatbot/permissions/clear-cache/ - Clear user permission cache
- GET /api/chatbot/permissions/analytics/ - Get permission analytics

Author: CRM Optimization Team
Version: 1.0
"""

from django.http import JsonResponse
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.views.decorators.cache import cache_control

from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from rest_framework.throttling import UserRateThrottle, AnonRateThrottle

import logging
import json
from typing import Dict, Optional

from ..config import ChatbotConfig
from ..services.lightweight_permissions import LightweightPermissionService, PermissionLevel
from .utils import get_request_user, get_permission_classes

logger = logging.getLogger(__name__)


class ChatbotPermissionThrottle(UserRateThrottle):
    """Custom throttling for permission endpoints"""
    scope = 'chatbot_permissions'
    rate = '100/hour'  # Allow 100 permission requests per hour per user


class ChatbotPermissionAnonThrottle(AnonRateThrottle):
    """Anonymous user throttling for permission endpoints"""
    scope = 'chatbot_permissions_anon'
    rate = '50/hour'  # Lower rate for anonymous users


@api_view(['GET'])
@permission_classes(get_permission_classes())
@cache_control(max_age=60)  # Cache in browser for 1 minute
def permission_hints(request):
    """
    Get comprehensive permission hints for N8N chatbot workflow.
    
    Query Parameters:
    - session_id: Optional session identifier for caching
    - force_refresh: Force cache refresh (admin only)
    - include_analytics: Include performance analytics
    
    Response:
    {
        "user_id": "EMP001",
        "session_id": "session_123",
        "permission_level": "full",
        "available_tools": ["customer_search", "create_engagement", ...],
        "tool_categories": {
            "search": ["customer_search", "prospect_search"],
            "action": ["create_engagement", "create_note"],
            ...
        },
        "restrictions": {"customers": "own_only"},
        "performance": {
            "response_time_ms": 15.2,
            "cache_hit": true,
            "cached_at": "2024-01-15T10:30:00Z"
        },
        "expires_at": "2024-01-15T11:00:00Z",
        "bypass_mode": false
    }
    """
    try:
        # Get request parameters
        session_id = request.GET.get('session_id')
        employee_no = request.GET.get('employee_no')  # Support employee_no for n8n calls
        force_refresh = request.GET.get('force_refresh', '').lower() == 'true'
        include_analytics = request.GET.get('include_analytics', '').lower() == 'true'
        
        # Get user (handles both authenticated and bypass modes)
        # If employee_no is provided (from n8n), try to get user by employee_no first
        if employee_no:
            try:
                from users.models import User
                user = User.objects.get(employee_no=employee_no, is_active=True)
                logger.debug(f"Permission API: Found user by employee_no: {employee_no}")
            except User.DoesNotExist:
                logger.warning(f"Permission API: User not found for employee_no: {employee_no}, falling back to request user")
                user = get_request_user(request)
            except Exception as e:
                logger.error(f"Permission API: Error getting user by employee_no {employee_no}: {e}, falling back to request user")
                user = get_request_user(request)
        else:
            user = get_request_user(request)
        
        # Validate session_id format if provided
        if session_id and not _is_valid_session_id(session_id):
            return Response({
                'error': 'Invalid session_id format',
                'message': 'Session ID must be alphanumeric and up to 50 characters',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Get session permissions (cached)
        session_perms = LightweightPermissionService.get_session_permissions(
            user=user,
            session_id=session_id,
            force_refresh=force_refresh
        )
        
        # Build response
        response_data = _build_permission_response(session_perms, include_analytics)
        
        # Add request metadata
        response_data['request_info'] = {
            'timestamp': timezone.now().isoformat(),
            'session_id': session_id or 'default',
            'force_refresh': force_refresh,
            'user_agent': request.META.get('HTTP_USER_AGENT', '')[:100]  # Truncate for security
        }
        
        # Log permission request for monitoring
        _log_permission_request(user, session_perms, request)
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except ValidationError as ve:
        logger.warning(f"Permission validation error: {ve}")
        return Response({
            'error': 'Validation error',
            'message': str(ve),
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_400_BAD_REQUEST)
        
    except Exception as e:
        logger.error(f"Permission hints error: {e}", exc_info=True)
        
        # Return emergency fallback permissions
        return Response({
            'error': 'Permission service temporarily unavailable',
            'message': 'Using emergency fallback permissions',
            'available_tools': ['customer_search'],  # Minimal safe tools
            'permission_level': 'read_only',
            'tool_categories': {'search': ['customer_search']},
            'restrictions': {'mode': 'emergency'},
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS,
            'fallback': True
        }, status=status.HTTP_206_PARTIAL_CONTENT)


@api_view(['POST'])
@permission_classes(get_permission_classes())
@csrf_exempt
def clear_permission_cache(request):
    """
    Clear permission cache for current user session.
    
    Request Body:
    {
        "session_id": "optional_session_id",
        "clear_all_sessions": false
    }
    
    Response:
    {
        "success": true,
        "message": "Permission cache cleared",
        "sessions_cleared": 1
    }
    """
    try:
        user = get_request_user(request)
        
        # Parse request data
        try:
            data = json.loads(request.body) if request.body else {}
        except json.JSONDecodeError:
            return Response({
                'error': 'Invalid JSON in request body',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=status.HTTP_400_BAD_REQUEST)
        
        session_id = data.get('session_id')
        clear_all_sessions = data.get('clear_all_sessions', False)
        
        # Clear cache
        if clear_all_sessions:
            # This would require additional implementation to track user sessions
            sessions_cleared = 1  # Placeholder
            LightweightPermissionService.clear_user_session_cache(user, None)
        else:
            sessions_cleared = 1
            LightweightPermissionService.clear_user_session_cache(user, session_id)
        
        logger.info(f"Permission cache cleared for user {getattr(user, 'employee_no', 'unknown')}")
        
        return Response({
            'success': True,
            'message': 'Permission cache cleared successfully',
            'sessions_cleared': sessions_cleared,
            'timestamp': timezone.now().isoformat(),
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        })
        
    except Exception as e:
        logger.error(f"Cache clear error: {e}")
        return Response({
            'error': 'Failed to clear permission cache',
            'message': str(e) if ChatbotConfig.TESTING_MODE else 'Internal server error',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# COMMENTED OUT - Analytics endpoint needs implementation fix (non-critical)
# @api_view(['GET'])
# @permission_classes(get_permission_classes())
# def permission_analytics(request):
#     """
#     Get permission system analytics and health metrics.
#     
#     Response:
#     {
#         "system_health": "healthy",
#         "cache_statistics": {...},
#         "permission_distribution": {...},
#         "performance_metrics": {...}
#     }
#     """
#     try:
#         # Check if user has admin permissions for detailed analytics
#         user = get_request_user(request)
#         
#         # Get basic analytics (safe for all users)
#         analytics = LightweightPermissionService.get_permission_analytics()
#         
#         # Add system health information
#         response_data = {
#             'system_health': 'healthy',
#             'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS,
#             'cache_statistics': {
#                 'active_sessions': analytics.get('cache_keys_active', 0),
#                 'cache_ttl_minutes': analytics.get('cache_ttl_normal', 1800) // 60
#             },
#             'tool_statistics': {
#                 'total_tools_available': analytics.get('total_tools_defined', 0),
#                 'permission_levels': analytics.get('permission_levels', [])
#             },
#             'timestamp': timezone.now().isoformat()
#         }
#         
#         # Add detailed metrics if in testing mode or bypass mode
#         if ChatbotConfig.TESTING_MODE or ChatbotConfig.BYPASS_ALL_PERMISSIONS:
#             response_data['detailed_metrics'] = analytics
#         
#         return Response(response_data)
#         
#     except Exception as e:
#         logger.error(f"Analytics error: {e}")
#         return Response({
#             'error': 'Failed to retrieve analytics',
#             'message': 'Analytics service temporarily unavailable',
#             'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
#         }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes(get_permission_classes())
def permission_analytics(request):
    """
    TEMPORARY STUB: Analytics endpoint (under development)
    """
    return Response({
        'status': 'under_development',
        'message': 'Analytics endpoint is being developed',
        'basic_metrics': {
            'system_health': 'healthy',
            'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS,
            'timestamp': timezone.now().isoformat()
        }
    })


@api_view(['GET'])
@permission_classes(get_permission_classes())
def health_check(request):
    """
    Simple health check for permission service.
    
    Response:
    {
        "status": "healthy",
        "timestamp": "2024-01-15T10:30:00Z",
        "version": "1.0"
    }
    """
    return Response({
        'status': 'healthy',
        'service': 'chatbot_permissions',
        'version': '1.0',
        'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS,
        'timestamp': timezone.now().isoformat()
    })


# Helper functions

def _is_valid_session_id(session_id: str) -> bool:
    """Validate session ID format"""
    if not session_id or len(session_id) > 50:
        return False
    
    # Allow alphanumeric, hyphens, underscores
    return all(c.isalnum() or c in '-_' for c in session_id)


def _build_permission_response(session_perms, include_analytics: bool = False) -> Dict:
    """Build standardized permission response"""
    response = {
        'user_id': session_perms.user_id,
        'session_id': session_perms.session_id,
        'permission_level': session_perms.permission_level.value if hasattr(session_perms.permission_level, 'value') else str(session_perms.permission_level),
        'available_tools': session_perms.available_tools,
        'tool_categories': session_perms.tool_categories,
        'restrictions': session_perms.restrictions,
        'expires_at': session_perms.expires_at,
        'bypass_mode': session_perms.bypass_mode
    }
    
    # Add performance information
    response['performance'] = {
        'response_time_ms': session_perms.cache_info['response_time_ms'],
        'cache_hit': session_perms.cache_info['cache_hit'],
        'cached_at': session_perms.cache_info['cached_at']
    }
    
    # Add error information if present
    if session_perms.error:
        response['error'] = session_perms.error
        response['fallback_mode'] = True
    
    # Add detailed analytics if requested
    if include_analytics:
        response['analytics'] = {
            'total_tools_available': len(session_perms.available_tools),
            'tools_by_category': {
                category: len(tools) 
                for category, tools in session_perms.tool_categories.items()
            },
            'cache_mode': session_perms.cache_info.get('mode', 'normal')
        }
    
    return response


def _log_permission_request(user, session_perms, request):
    """Log permission request for monitoring and security"""
    user_id = getattr(user, 'employee_no', 'unknown')
    client_ip = _get_client_ip(request)
    
    logger.info(
        f"Permission request - User: {user_id}, "
        f"IP: {client_ip}, "
        f"Level: {session_perms.permission_level.value if hasattr(session_perms.permission_level, 'value') else str(session_perms.permission_level)}, "
        f"Tools: {len(session_perms.available_tools)}, "
        f"Cache: {'HIT' if session_perms.cache_info['cache_hit'] else 'MISS'}, "
        f"Time: {session_perms.cache_info['response_time_ms']}ms"
    )
    
    # Log security events
    if session_perms.bypass_mode:
        logger.warning(f"BYPASS MODE: Permission request from {user_id} at {client_ip}")
    
    if session_perms.error:
        logger.error(f"Permission error for {user_id}: {session_perms.error}")


def _get_client_ip(request) -> str:
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        return x_forwarded_for.split(',')[0].strip()
    return request.META.get('REMOTE_ADDR', 'unknown')


# Error handling decorators

def handle_permission_errors(func):
    """Decorator for consistent permission error handling"""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except ValidationError as ve:
            logger.warning(f"Permission validation error in {func.__name__}: {ve}")
            return JsonResponse({
                'error': 'Validation error',
                'message': str(ve),
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=400)
        except Exception as e:
            logger.error(f"Permission error in {func.__name__}: {e}", exc_info=True)
            return JsonResponse({
                'error': 'Permission service error',
                'message': 'Service temporarily unavailable',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=500)
    
    return wrapper
