"""
Chatbot Configuration
====================

Centralized configuration for chatbot functionality.
All settings should be environment-variable configurable.
"""

from django.conf import settings
import os


class ChatbotConfig:
    """Central configuration for chatbot functionality"""
    
    # Core settings
    TESTING_MODE = getattr(settings, 'CHATBOT_TESTING_MODE', settings.DEBUG)
    
    # SEAMLESS AUTHENTICATED ACCESS - Authentication is sufficient for full access
    BYPASS_ALL_PERMISSIONS = True
    FORCE_UNRESTRICTED_ACCESS = True
    SEAMLESS_AUTHENTICATED_ACCESS = True  # No permission barriers for logged-in users
    
    # Optimized Cache settings
    CACHE_TIMEOUT = int(os.environ.get('CHATBOT_CACHE_TIMEOUT', 900))  # 15 minutes - optimized
    CACHE_KEY_PREFIX = os.environ.get('CHATBOT_CACHE_PREFIX', 'chatbot_search_')
    PERMISSION_CACHE_TIMEOUT = int(os.environ.get('CHATBOT_PERMISSION_CACHE_TIMEOUT', 1800))  # 30 minutes
    
    # Optimized Search settings  
    MAX_SEARCH_RESULTS = int(os.environ.get('CHATBOT_MAX_RESULTS', 100))  # Increased for better UX
    DEFAULT_SEARCH_LIMIT = int(os.environ.get('CHATBOT_DEFAULT_LIMIT', 20))  # Increased default
    FUZZY_SEARCH_THRESHOLD = float(os.environ.get('CHATBOT_FUZZY_THRESHOLD', 0.6))
    MIN_QUERY_LENGTH = int(os.environ.get('CHATBOT_MIN_QUERY_LENGTH', 2))
    MAX_QUERY_LENGTH = int(os.environ.get('CHATBOT_MAX_QUERY_LENGTH', 100))
    
    # AI/N8N settings
    N8N_WEBHOOK_URL = os.environ.get('N8N_WEBHOOK_URL', 'https://127.0.0.1:8000/api/chatbot/')
    AI_MODEL_NAME = os.environ.get('CHATBOT_AI_MODEL', 'fast-deepseek-model')
    AI_MAX_TOKENS = int(os.environ.get('CHATBOT_AI_MAX_TOKENS', 2000))
    AI_TEMPERATURE = float(os.environ.get('CHATBOT_AI_TEMPERATURE', 0.3))
    
    # Security settings  
    RATE_LIMIT_REQUESTS = int(os.environ.get('CHATBOT_RATE_LIMIT', 100))  # per hour
    RATE_LIMIT_WINDOW = int(os.environ.get('CHATBOT_RATE_WINDOW', 3600))  # 1 hour
    
    # CSV Export settings
    CSV_MAX_ROWS = int(os.environ.get('CHATBOT_CSV_MAX_ROWS', 10000))
    CSV_TIMEOUT = int(os.environ.get('CHATBOT_CSV_TIMEOUT', 300))  # 5 minutes
    
    # Additional settings
    READ_ONLY_MODE = os.environ.get('CHATBOT_READ_ONLY_MODE', 'false').lower() == 'true'
    DEBUG_MODE = os.environ.get('CHATBOT_DEBUG_MODE', settings.DEBUG)
    
    # Permission filtering (infrastructure ready, activated when bypass disabled)
    PERMISSION_FILTERING_ENABLED = not BYPASS_ALL_PERMISSIONS
    STRICT_TEAM_FILTERING = os.environ.get('CHATBOT_STRICT_TEAM_FILTERING', 'false').lower() == 'true'
    STRICT_OFFICE_FILTERING = os.environ.get('CHATBOT_STRICT_OFFICE_FILTERING', 'false').lower() == 'true'
    LOG_PERMISSION_DENIALS = os.environ.get('CHATBOT_LOG_DENIALS', 'true').lower() == 'true'
    
    # Optimized Logging settings (reduced for performance)
    LOG_SEARCH_QUERIES = os.environ.get('CHATBOT_LOG_QUERIES', 'false').lower() == 'true'  # Disabled by default
    LOG_LEVEL = os.environ.get('CHATBOT_LOG_LEVEL', 'WARNING')  # Reduced logging
    ENABLE_PERFORMANCE_MONITORING = os.environ.get('CHATBOT_ENABLE_PERF_MONITORING', 'false').lower() == 'true'
    
    @classmethod
    def validate_config(cls):
        """Validate configuration settings"""
        errors = []
        
        if cls.MAX_SEARCH_RESULTS > 1000:
            errors.append("MAX_SEARCH_RESULTS should not exceed 1000")
            
        if cls.FUZZY_SEARCH_THRESHOLD < 0.1 or cls.FUZZY_SEARCH_THRESHOLD > 1.0:
            errors.append("FUZZY_SEARCH_THRESHOLD must be between 0.1 and 1.0")
            
        if cls.MIN_QUERY_LENGTH < 1:
            errors.append("MIN_QUERY_LENGTH must be at least 1")
            
        if cls.CSV_MAX_ROWS > 100000:
            errors.append("CSV_MAX_ROWS should not exceed 100,000 for performance")
            
        return errors


# Validate configuration on import
_config_errors = ChatbotConfig.validate_config()
if _config_errors:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"Chatbot configuration issues: {', '.join(_config_errors)}") 