"""
Chatbot Middleware
=================

Middleware for rate limiting and security enhancements, with support for both
sync and async requests.
"""

from django.http import JsonResponse
from django.core.cache import cache
from django.utils import timezone
from typing import Dict, Any
import logging
import hashlib
import asyncio

from .config import ChatbotConfig

logger = logging.getLogger(__name__)


class ChatbotRateLimitMiddleware:
    """
    Rate limiting middleware for chatbot endpoints.
    Prevents API abuse and improves security.
    """
    def __init__(self, get_response):
        self.get_response = get_response
        self.is_async = asyncio.iscoroutinefunction(self.get_response)

    async def __call__(self, request):
        # Only apply rate limiting to chatbot endpoints
        if not request.path.startswith('/api/chatbot/'):
            if self.is_async:
                return await self.get_response(request)
            return self.get_response(request)
        
        # Skip rate limiting in testing mode OR bypass mode
        if ChatbotConfig.TESTING_MODE or ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
                client_id = self._get_client_identifier(request)
                logger.info(f"BYPASS MODE: Rate limiting skipped for {client_id}")
            if self.is_async:
                return await self.get_response(request)
            return self.get_response(request)
        
        # Check rate limit
        if not self._check_rate_limit(request):
            return JsonResponse({
                'error': 'Rate limit exceeded',
                'message': f'Maximum {ChatbotConfig.RATE_LIMIT_REQUESTS} requests per hour allowed',
                'retry_after': self._get_retry_after(request)
            }, status=429)
        
        if self.is_async:
            response = await self.get_response(request)
        else:
            response = self.get_response(request)

        # Add rate limit headers
        self._add_rate_limit_headers(request, response)
        
        return response
    
    def _get_client_identifier(self, request) -> str:
        """Get unique identifier for the client"""
        if hasattr(request, 'user') and request.user.is_authenticated:
            if hasattr(request.user, 'employee_no'):
                return f"user_{request.user.employee_no}"
            return f"user_{request.user.id}"
        
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        
        return f"ip_{ip}"
    
    def _get_cache_key(self, client_id: str) -> str:
        """Generate cache key for rate limiting"""
        current_hour = timezone.now().strftime('%Y%m%d_%H')
        key_data = f"rate_limit_{client_id}_{current_hour}"
        return hashlib.md5(key_data.encode()).hexdigest()[:16]
    
    def _check_rate_limit(self, request) -> bool:
        """Check if request is within rate limit"""
        client_id = self._get_client_identifier(request)
        cache_key = self._get_cache_key(client_id)
        
        current_count = cache.get(cache_key, 0)
        
        if current_count >= ChatbotConfig.RATE_LIMIT_REQUESTS:
            logger.warning(f"Rate limit exceeded for {client_id}: {current_count} requests")
            return False
        
        cache.set(cache_key, current_count + 1, ChatbotConfig.RATE_LIMIT_WINDOW)
        
        return True
    
    def _get_retry_after(self, request) -> int:
        """Get retry-after time in seconds"""
        now = timezone.now()
        next_hour = now.replace(minute=0, second=0, microsecond=0) + timezone.timedelta(hours=1)
        return int((next_hour - now).total_seconds())
    
    def _add_rate_limit_headers(self, request, response):
        """Add rate limit headers to response"""
        client_id = self._get_client_identifier(request)
        cache_key = self._get_cache_key(client_id)
        current_count = cache.get(cache_key, 0)
        
        response['X-RateLimit-Limit'] = str(ChatbotConfig.RATE_LIMIT_REQUESTS)
        response['X-RateLimit-Remaining'] = str(max(0, ChatbotConfig.RATE_LIMIT_REQUESTS - current_count))
        response['X-RateLimit-Reset'] = str(self._get_retry_after(request))


class ChatbotSecurityMiddleware:
    """
    Security middleware for chatbot endpoints.
    Adds security headers and input validation.
    """
    def __init__(self, get_response):
        self.get_response = get_response
        self.is_async = asyncio.iscoroutinefunction(self.get_response)

    async def __call__(self, request):
        if not request.path.startswith('/api/chatbot/'):
            if self.is_async:
                return await self.get_response(request)
            return self.get_response(request)
        
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            logger.info(f"BYPASS MODE: Security middleware applied with relaxed validation")
        
        max_size = 51200 if ChatbotConfig.BYPASS_ALL_PERMISSIONS else 10240
        if hasattr(request, 'body') and len(request.body) > max_size:
            size_limit = "50KB" if ChatbotConfig.BYPASS_ALL_PERMISSIONS else "10KB"
            return JsonResponse({
                'error': 'Request too large',
                'message': f'Request body must be less than {size_limit}',
                'bypass_mode': ChatbotConfig.BYPASS_ALL_PERMISSIONS
            }, status=413)

        if self.is_async:
            response = await self.get_response(request)
        else:
            response = self.get_response(request)
        
        response['X-Content-Type-Options'] = 'nosniff'
        response['X-Frame-Options'] = 'DENY'
        response['X-XSS-Protection'] = '1; mode=block'
        
        if ChatbotConfig.BYPASS_ALL_PERMISSIONS:
            response['X-Chatbot-Bypass-Mode'] = 'true'
        
        return response