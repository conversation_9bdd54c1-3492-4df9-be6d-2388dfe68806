"""
Chatbot Models Module
====================

This module contains Django models for the chatbot application.
Currently, the chatbot uses models from other apps (users, services)
and doesn't define its own models.

All chatbot data is handled through:
- User authentication (users app)
- Engagements, Reminders, Notes (services app)
- Customer, Prospect, LeadFile data (respective apps)
"""

from django.db import models

# No models defined - chatbot uses existing models from other apps
