"""
Chatbot Admin Configuration
===========================

This module contains Django admin configuration for the chatbot application.
Since the chatbot doesn't define its own models, no admin configuration is needed.

All related models are managed through their respective apps:
- Users (users app)
- Engagements, Reminders, Notes (services app)
"""

from django.contrib import admin

# No admin registration needed - chatbot uses existing models from other apps
