"""
Django Management Command for Chatbot Permissions
================================================

Easy-to-use management command for enabling, disabling, and monitoring
the chatbot permission system.

Usage:
    python manage.py chatbot_permissions status
    python manage.py chatbot_permissions enable
    python manage.py chatbot_permissions disable
    python manage.py chatbot_permissions test [user_id]

Author: CRM Optimization Team
Version: 1.0
"""

from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.contrib.auth import get_user_model
from typing import Optional
import sys
import json

from ...permission_config import PermissionConfigCommand, PermissionConfigManager
from ...services.lightweight_permissions import LightweightPermissionService

User = get_user_model()


class Command(BaseCommand):
    """Django management command for chatbot permission management"""
    
    help = 'Manage chatbot permission system configuration and testing'
    
    def add_arguments(self, parser):
        """Add command line arguments"""
        parser.add_argument(
            'action',
            choices=['status', 'enable', 'disable', 'test', 'analytics', 'clear-cache'],
            help='Action to perform'
        )
        
        parser.add_argument(
            '--user-id',
            type=str,
            help='User ID for testing permissions (employee_no)'
        )
        
        parser.add_argument(
            '--session-id',
            type=str,
            default='management-command',
            help='Session ID for permission testing'
        )
        
        parser.add_argument(
            '--json',
            action='store_true',
            help='Output results in JSON format'
        )
        
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force action without confirmation prompts'
        )
    
    def handle(self, *args, **options):
        """Handle the management command"""
        try:
            action = options['action']
            
            if action == 'status':
                self.handle_status(options)
            elif action == 'enable':
                self.handle_enable(options)
            elif action == 'disable':
                self.handle_disable(options)
            elif action == 'test':
                self.handle_test(options)
            elif action == 'analytics':
                self.handle_analytics(options)
            elif action == 'clear-cache':
                self.handle_clear_cache(options)
            else:
                raise CommandError(f"Unknown action: {action}")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Command failed: {e}")
            )
            sys.exit(1)
    
    def handle_status(self, options):
        """Handle status command"""
        if options['json']:
            status = PermissionConfigManager.get_configuration_status()
            self.stdout.write(json.dumps(status, indent=2, default=str))
        else:
            PermissionConfigCommand.status_command()
    
    def handle_enable(self, options):
        """Handle enable command"""
        if not options['force']:
            confirm = input("Enable chatbot permission system? This will require proper user authentication. (y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                self.stdout.write("Operation cancelled.")
                return
        
        if options['json']:
            instructions = PermissionConfigManager.enable_permissions()
            self.stdout.write(json.dumps(instructions, indent=2))
        else:
            PermissionConfigCommand.enable_permissions_command()
            
        self.stdout.write(
            self.style.SUCCESS("Permission system enablement instructions provided.")
        )
        self.stdout.write(
            self.style.WARNING("Remember to restart your Django application after making configuration changes.")
        )
    
    def handle_disable(self, options):
        """Handle disable command"""
        if not options['force']:
            confirm = input("Disable chatbot permission system? This removes all security checks. (y/N): ")
            if confirm.lower() not in ['y', 'yes']:
                self.stdout.write("Operation cancelled.")
                return
        
        if options['json']:
            instructions = PermissionConfigManager.disable_permissions()
            self.stdout.write(json.dumps(instructions, indent=2))
        else:
            PermissionConfigCommand.disable_permissions_command()
            
        self.stdout.write(
            self.style.WARNING("Permission system disabled. All users now have unrestricted access.")
        )
    
    def handle_test(self, options):
        """Handle test command"""
        user_id = options.get('user_id')
        session_id = options.get('session_id', 'management-test')
        
        if not user_id:
            self.stdout.write(
                self.style.ERROR("User ID is required for testing. Use --user-id=EMP001")
            )
            return
        
        try:
            # Find user by employee_no
            user = self._find_user_by_employee_no(user_id)
            
            if not user:
                self.stdout.write(
                    self.style.ERROR(f"User not found: {user_id}")
                )
                return
            
            # Test permission system
            self.stdout.write(f"Testing permissions for user: {user_id}")
            self.stdout.write("=" * 50)
            
            # Get session permissions
            start_time = timezone.now()
            session_perms = LightweightPermissionService.get_session_permissions(
                user=user,
                session_id=session_id
            )
            end_time = timezone.now()
            
            if options['json']:
                result = {
                    'user_id': user_id,
                    'test_timestamp': start_time.isoformat(),
                    'response_time_ms': (end_time - start_time).total_seconds() * 1000,
                    'session_permissions': session_perms.to_dict()
                }
                self.stdout.write(json.dumps(result, indent=2, default=str))
            else:
                self._display_test_results(session_perms, start_time, end_time)
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Permission test failed: {e}")
            )
    
    def handle_analytics(self, options):
        """Handle analytics command"""
        try:
            analytics = LightweightPermissionService.get_permission_analytics()
            
            if options['json']:
                self.stdout.write(json.dumps(analytics, indent=2))
            else:
                self.stdout.write("Permission System Analytics")
                self.stdout.write("=" * 50)
                self.stdout.write(f"Active Cache Keys: {analytics.get('cache_keys_active', 0)}")
                self.stdout.write(f"Total Tools Defined: {analytics.get('total_tools_defined', 0)}")
                self.stdout.write(f"Permission Levels: {', '.join(analytics.get('permission_levels', []))}")
                self.stdout.write(f"Normal Cache TTL: {analytics.get('cache_ttl_normal', 0)} seconds")
                self.stdout.write(f"Fallback Cache TTL: {analytics.get('cache_ttl_fallback', 0)} seconds")
                self.stdout.write(f"Cache Backend: {analytics.get('cache_backend', 'Unknown')}")
                self.stdout.write(f"Analytics Timestamp: {analytics.get('analytics_timestamp', 'Unknown')}")
                
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f"Analytics retrieval failed: {e}")
            )
    
    def handle_clear_cache(self, options):
        """Handle clear cache command"""
        user_id = options.get('user_id')
        session_id = options.get('session_id')
        
        if user_id:
            try:
                user = self._find_user_by_employee_no(user_id)
                if user:
                    LightweightPermissionService.clear_user_session_cache(user, session_id)
                    self.stdout.write(
                        self.style.SUCCESS(f"Cache cleared for user: {user_id}")
                    )
                else:
                    self.stdout.write(
                        self.style.ERROR(f"User not found: {user_id}")
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Cache clear failed: {e}")
                )
        else:
            # Clear all chatbot permission cache
            from django.core.cache import cache
            pattern = LightweightPermissionService.CACHE_KEY_PREFIX + "*"
            
            try:
                # Try to clear specific cache keys if supported
                if hasattr(cache, 'keys'):
                    cache_keys = cache.keys(pattern)
                    if cache_keys:
                        cache.delete_many(cache_keys)
                        self.stdout.write(
                            self.style.SUCCESS(f"Cleared {len(cache_keys)} permission cache entries")
                        )
                    else:
                        self.stdout.write("No permission cache entries found")
                else:
                    # For LocMemCache and similar backends, clear all cache
                    cache.clear()
                    self.stdout.write(
                        self.style.WARNING("Cache backend doesn't support selective clearing. Cleared entire cache.")
                    )
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Cache clear failed: {e}")
                )
    
    def _find_user_by_employee_no(self, employee_no: str):
        """Find user by employee number"""
        try:
            # Try to find by employee_no field
            if hasattr(User, 'employee_no'):
                return User.objects.get(employee_no=employee_no)
            else:
                # Fallback: try username or email
                return User.objects.get(username=employee_no)
        except User.DoesNotExist:
            return None
        except User.MultipleObjectsReturned:
            # Return the first match
            if hasattr(User, 'employee_no'):
                return User.objects.filter(employee_no=employee_no).first()
            else:
                return User.objects.filter(username=employee_no).first()
    
    def _display_test_results(self, session_perms, start_time, end_time):
        """Display human-readable test results"""
        response_time = (end_time - start_time).total_seconds() * 1000
        
        # Basic information
        self.stdout.write(f"User ID: {session_perms.user_id}")
        self.stdout.write(f"Session ID: {session_perms.session_id}")
        self.stdout.write(f"Permission Level: {session_perms.permission_level.value.upper()}")
        self.stdout.write(f"Response Time: {response_time:.2f}ms")
        self.stdout.write(f"Cache Hit: {'Yes' if session_perms.cache_info.get('cache_hit') else 'No'}")
        
        if session_perms.bypass_mode:
            self.stdout.write(self.style.WARNING("BYPASS MODE: All permissions granted"))
        
        if session_perms.error:
            self.stdout.write(self.style.ERROR(f"Error: {session_perms.error}"))
        
        # Available tools
        self.stdout.write("\nAvailable Tools:")
        if session_perms.available_tools:
            for category, tools in session_perms.tool_categories.items():
                if tools:
                    self.stdout.write(f"  {category.title()}: {', '.join(tools)}")
        else:
            self.stdout.write("  No tools available")
        
        # Restrictions
        if session_perms.restrictions:
            self.stdout.write("\nRestrictions:")
            for resource, restriction in session_perms.restrictions.items():
                self.stdout.write(f"  {resource}: {restriction}")
        
        # Performance information
        self.stdout.write(f"\nPerformance:")
        self.stdout.write(f"  Total Tools Available: {len(session_perms.available_tools)}")
        self.stdout.write(f"  Cache Mode: {session_perms.cache_info.get('mode', 'normal')}")
        self.stdout.write(f"  Expires At: {session_perms.expires_at}")
        
        # Security status
        security_status = "SECURE" if not session_perms.bypass_mode else "BYPASS"
        self.stdout.write(f"\nSecurity Status: {security_status}")
        
        self.stdout.write("\nPermission test completed successfully")
