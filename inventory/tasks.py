from celery import shared_task
from django.utils import timezone
from django.conf import settings
import os
from inventory.models import Plot,Project
from utils.email import send_email
from services.models import CustomSystemLog
from users.models import User
from copy import deepcopy
from django.db import transaction
from django.db.models import Count
from threading import Thread
from urllib.parse import quote
from requests_ntlm import HttpNtlmAuth
import requests
import os
import json

def normalize_emails(field):
        if not field:
            return []
        if isinstance(field, str):
            # This handles comma-separated strings
            return [email.strip() for email in field.split(",") if email.strip()]
        if isinstance(field, list):
            return [email.strip() for email in field if isinstance(email, str)]
        raise ValueError(f"Invalid email field: {field}")


# ---------------------------------------
#   CELERY TASKS 
# ---------------------------------------


@shared_task
def send_email_task(subject, recipients, template_name=None, context=None, attachments=None, cc=None, bcc=None, from_name=None, from_email=None):
    send_email(
        subject=subject,
        recipients=recipients,
        template_name=template_name,
        context=context,
        attachments=attachments,
        cc=cc,
        bcc=bcc,
        from_name=from_name or "Optiven Engage360 CRM",
        from_email=from_email or "<EMAIL>"
    )

# ---------------------------------------
#   CELERY BEATS TASKS
# ---------------------------------------
def update_erp_plot_status(plot_no, status):
    """
    Update the ERP portal with the plot status asynchronously.
    Logs the result to CustomSystemLog.
    """

    def _send_request(plot_no, status):
        url2= f'https://crm.optiven.co.ke/API/sync_from_crm.php'
        try: 
            plot = Plot.objects.get(plot_no=plot_no)
            pk=plot.plotId
            company_name = quote("Optiven R.E")
            erp_username = os.getenv('ERP_USERNAME')
            erp_password = os.getenv('ERP_PASSWORD')
            auth = HttpNtlmAuth(erp_username, erp_password)
            headers = {
                'User-Agent': 'Mozilla/5.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            }

            url = f"http://192.168.0.5:8048/optiven/ODataV4/Company('{company_name}')/Property_Card('{pk}')"
            # Get ETag
            response_data = requests.get(url, auth=auth, headers=headers)
            response_data.raise_for_status()
            response_data = response_data.json()
            etag = response_data.get('@odata.etag')
            if etag:
                headers['If-Match'] = etag
            # Prepare data
            data = {
                # 'Status': status,
                'Portal_Status': status,
            }
            # Patch request
            response = requests.patch(url, auth=auth, headers=headers, json=data)
            print(f"Status: {response.status_code}")
            print(json.dumps(response.json(), indent=4))

        except Exception as e:
            print (f"Error updating ERP for plot {plot_no}: {e}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action='Status Update',
                module='Inventory',
                message=str(e)
            )
        try:
            # Send a request to the second URL
            payload = {"plot_no": plot_no, "status": status}
            response2 = requests.post(url2, json=payload, headers={"Content-Type": "application/json"}, timeout=10)
            print(f"Response for portal: {response2.status_code} - {response2.text}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action=f"{plot_no} Status Update to portal",
                message=f"Status: {status}, HTTP {response2.status_code}, Response: {response2.text}"
            )
        except Exception as e:
            print(f"Error updating to portal for plot {plot_no}: {e}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action='Status Update to portal',
                module='Inventory',
                message=str(e)
            )

    Thread(target=_send_request, args=(plot_no, status)).start()


def reserve_plots(plotsList,status):
    for plot_id in plotsList:
        try:
            plot = Plot.objects.get(plot_no=plot_id)  # or use .get(id=...) if it's a PK
            plot.plot_status = status
            plot.save(update_fields=["plot_status"])

            # Update ERP portal status
            update_erp_plot_status(plot_id, status)            
            
        except Plot.DoesNotExist:
            print(f"Plot {plot_id} not found")
            return {"status": False, "message": f"Plot number: {plot_id} could not be {status}"}
    return {"status": True, "message": f"All plots {status}"}



@shared_task
def expire_plot_booking():
    """
    Task to expire plot bookings that have not been confirmed after deadline.
    """
    from inventory.models import PlotBooking
    now = timezone.now()

    # Get all unconfirmed plot bookings
    unconfirmed_bookings = PlotBooking.objects.filter(status='WAITING', deadline__lt=now, booking_type__in=['SPECIAL'])[:1]
    if not unconfirmed_bookings.exists():
        print("No unconfirmed bookings to expire.")
        return

    # set  booking to timed out and update plot status to Openexit
    for booking in unconfirmed_bookings:
        booked_plots = booking.plots.split(',')
        plots = Plot.objects.filter(plot_no__in=booked_plots)
        plotsList = [plot for plot in plots]
        
        results=reserve_plots(plotsList,'Open')
        if results['status'] == False:
            CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message="Error changing plot status to Open via ERP ROUTE")

            for plot in plots:
                plot.plot_status = 'Open'
                plot.save()
                # Update ERP portal status
                update_erp_plot_status(plot.plot_no, 'Open')



                m=f"Changed plot {plot.plot_no} - {plot.plotId} to  : {plot.plot_status} manually, please check ERP ROUTE"
                print (m)
                #log 
                CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message=m)

        booking.status = 'TIMED'
        booking.save()
        m=f"Timed out booking {booking.booking_id} by {booking.marketer.fullnames} for plot(s) {booking.plots}"
        print (m)
        #log
        plot_booking_template = 'inventory/timed_out_booking.html'
        plot_booking_context = {
                                    'employee': f'{booking.marketer.fullnames}',
                                    'booking_type': str(booking.booking_type),
                                    'plots': str(booking.plots),
                                    'head': "Booking Timed Out",
                                    'creation_date': str(booking.creation_date),
                                    'year': str(timezone.now().year),
                                }
        accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
        # 
        send_email_task.delay(subject=f" Booking Timed Out",recipients=normalize_emails([booking.marketer.email]),
            template_name=plot_booking_template,context=deepcopy(plot_booking_context),cc= normalize_emails(accounts_cc.split(",") if accounts_cc else []),
                            from_name='Plot Booking System' )
        CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message=m)
        # cc=normalize_emails(["<EMAIL>"])

@shared_task
def drop_diaspora_reservation():
    """
    Task to expire plot bookings that have not been confirmed after deadline.
    """
    from inventory.models import DiasporaReservation
    now = timezone.now()

    # Get all unconfirmed plot bookings
    unconfirmed_bookings = DiasporaReservation.objects.filter(deadline__lt=now)
    if not unconfirmed_bookings.exists():
        print("No unconfirmed bookings to expire.")
        return

    # set  booking to timed out and update plot status to Openexit
    for booking in unconfirmed_bookings:
        booked_plots = booking.plots.split(',')
        plots = Plot.objects.filter(plot_no__in=booked_plots)
        plotsList = [plot.plot_no for plot in plots]
        results=reserve_plots(plotsList,'Open')
        if results['status'] == False:
            CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message="Error changing plot status to Open via ERP ROUTE")

            for plot in plots:
                plot.plot_status = 'Open'
                plot.save()
                # Update ERP portal status
                update_erp_plot_status(plot.plot_no, 'Open')
                
                m=f"Changed plot {plot.plot_no} - {plot.plotId} to  : {plot.plot_status} manually, please check ERP ROUTE"
                print (m)
                #log 
                CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message=m)

        
        user= User.objects.get(employee_no=booking.marketer_id)
        m=f"Timed out Diaspora reservations {booking.id} by {user.fullnames} for plot(s) {booking.plots}"
        
        print (m)
        #log
        plot_booking_template = 'inventory/timed_out_booking.html'
        plot_booking_context = {
                                    'employee': f'{user.fullnames}',
                                    'booking_type': "Diaspora Reservation",
                                    'plots': str(booking.plots),
                                    'head': "Booking Timed Out",
                                    'creation_date': str(booking.created_at),
                                    'year': str(timezone.now().year),
                                }
        # 
        booking.delete()
        accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
        send_email_task.delay(subject=f" Booking Timed Out",recipients=([user.email]),
            template_name=plot_booking_template,context=deepcopy(plot_booking_context),cc= normalize_emails(accounts_cc.split(",") if accounts_cc else []),
                            from_name='Plot Booking System' )
        CustomSystemLog.objects.create(system_level='Booking Cron',action='Edit',module='Inventory',message=m)
        # cc=normalize_emails(["<EMAIL>"])
        
        
@shared_task
def legal_team_leadfile_offerletter_checker():
    """
    Task to check for leadfile offer letters that are not yet confirmed.
    """
    from offerletters.models import OfferLetterMain
    now = timezone.now()

    # Get all leadfile offer letters that are not confirmed
    with transaction.atomic():
        try:
            offerletters_withno_leadfiles = OfferLetterMain.objects.filter(is_completed=True, lead_file__isnull=True)

            if not offerletters_withno_leadfiles.exists():
                print(" No offerleter with no leadfile found.")
                return

            for offer_letter in offerletters_withno_leadfiles:
                # Check if the plot number is set
                if not offer_letter.plot_number:
                    print(f"Offer letter {offer_letter.id} does not have a plot number set.")
                    continue
                else:
                    plot_numbers = [p.strip() for p in offer_letter.plot_number.split(",") if p.strip()]
                    leadfiles = []
                    customers= []
                    from sales.models import LeadFile
                    for plot_no in plot_numbers:
                        leadfile = LeadFile.objects.filter(plot_no=plot_no, lead_file_status_dropped=False).first()
                        if leadfile:
                            leadfiles.append(str(leadfile.lead_file_no))
                            customers.append(leadfile.customer_id.customer_no)
                        else:
                            print(f"No leadfile found for plot number {plot_no}.")
                            continue
                    if not leadfiles:
                        print(f"No leadfiles found for any plot numbers in offer letter {offer_letter.id}.")
                        continue
                    else:
                        offer_letter.lead_file = ",".join(leadfiles)
                        offer_letter.customers = ",".join(customers)
                        offer_letter.save()
                        m = f"Updated Offer Letter {offer_letter.id} with Leadfiles {offer_letter.lead_file} for plots {offer_letter.plot_number}"
                        print(m)

                    booking = offer_letter.booking_id
                    if booking:
                        booking.created_leadfiles = offer_letter.lead_file
                        booking.created_customers = offer_letter.customers
                        booking.save()


                        if booking.lead not in [None, '']:
                            from leads.models import Prospects
                            lead= Prospects.objects.filter(id=booking.lead.id).first()
                            if lead:
                                customers_comma = ', '.join([str(customer) for customer in customers if customer])
                                leadfiles_comma = ', '.join(leadfiles)
                                lead.is_converted= True
                                lead.no_of_sales= len(leadfiles)
                                lead.customer = customers_comma
                                lead.leadfiles = leadfiles_comma
                                lead.save()
                                m = f"Updated Lead {lead.id} with Leadfile {offer_letter.lead_file}"
                                print(m)
        except Exception as e:
            print(f"An error occurred while processing leadfile offer letters: {e}")
            CustomSystemLog.objects.create(system_level='OL-Cron', action='CHK-LF-OF', module='Offer Letters', message=str(e))
                   
                    
@shared_task
def update_project_priority():
    # Get projects with open plots, order by open plot count descending
    open_plot_counts = (
        Plot.objects.filter(plot_status='Open')
        .values('project_id')
        .annotate(open_count=Count('plot_no'))
        .order_by('-open_count')
    )

    updated_ids = []
    priority = 1
    for item in open_plot_counts:
        project_id = item['project_id']
        Project.objects.filter(projectId=project_id).update(priority=priority)
        updated_ids.append(project_id)
        priority += 1

    # Set priority = 0 for projects with no open plots
    Project.objects.exclude(projectId__in=updated_ids).update(priority=999)

    return f"Updated priorities for {len(updated_ids)} projects"          
            
        