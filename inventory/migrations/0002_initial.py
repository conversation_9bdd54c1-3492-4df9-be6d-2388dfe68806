# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0002_initial'),
        ('inventory', '0001_initial'),
        ('leads', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='diasporareservation',
            name='trip',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diaspora_reservation_trip', to='leads.diasporatrips'),
        ),
        migrations.AddField(
            model_name='plotbooking',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='plot_bookings_customer', to='customers.customer'),
        ),
        migrations.AddField(
            model_name='plotbooking',
            name='lead',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='plot_bookings_lead', to='leads.prospects'),
        ),
        migrations.AddField(
            model_name='plotbooking',
            name='marketer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='plot_booking_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no'),
        ),
        migrations.AddField(
            model_name='plot',
            name='project',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='plots', to='inventory.project'),
        ),
    ]
