# Generated by Django 5.1.7 on 2025-07-18 12:41

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0004_alter_mpesatransaction_options_alter_plot_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiasporaReceipts',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('client_id_pass', models.Char<PERSON>ield(max_length=100)),
                ('client_phone', models.CharField(max_length=20)),
                ('client_email', models.EmailField(max_length=254)),
                ('client_name', models.Char<PERSON>ield(max_length=255)),
                ('plot_no', models.CharField(max_length=100)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                ('payment_mode', models.Char<PERSON>ield(max_length=100)),
                ('acc_no', models.Char<PERSON>ield(max_length=100)),
                ('payments_of', models.Char<PERSON>ield(max_length=100)),
                ('marketer', models.CharField(max_length=255)),
                ('state', models.CharField(max_length=100)),
                ('country', models.CharField(max_length=100)),
                ('receipt', models.FileField(upload_to='diaspora/receipts/')),
                ('cash_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('purchase_price', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('total_paid', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('balance_lcy', models.DecimalField(blank=True, decimal_places=2, max_digits=12, null=True)),
                ('agreement_signed', models.BooleanField(blank=True, null=True)),
                ('region', models.CharField(blank=True, max_length=100, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('booking_id', models.ForeignKey(max_length=100, on_delete=django.db.models.deletion.CASCADE, to='inventory.plotbooking')),
            ],
            options={
                'verbose_name': 'Diaspora Receipt',
                'verbose_name_plural': 'Diaspora Receipts',
                'ordering': ['-created_at'],
            },
        ),
    ]
