# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Forex',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('USD', models.FloatField()),
                ('GBP', models.FloatField()),
                ('EURO', models.FloatField()),
            ],
        ),
        migrations.CreateModel(
            name='MpesaTransaction',
            fields=[
                ('trans_id', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=32)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('account_ref', models.CharField(max_length=50)),
                ('name1', models.CharField(max_length=50)),
                ('name2', models.CharField(max_length=50)),
                ('name3', models.CharField(max_length=50)),
                ('number', models.CharField(max_length=50)),
                ('status', models.CharField(choices=[('CLOSED', 'CLOSED'), ('OPEN', 'OPEN'), ('PENDING', 'PENDING')], default='OPEN', max_length=10)),
                ('confirmation', models.BooleanField(default=False)),
                ('utilityBalance', models.DecimalField(decimal_places=2, max_digits=32)),
                ('notif', models.CharField(blank=True, max_length=20, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Plot',
            fields=[
                ('plotId', models.CharField(max_length=50, primary_key=True, serialize=False)),
                ('plot_no', models.CharField(max_length=50, unique=True)),
                ('plot_size', models.DecimalField(decimal_places=5, max_digits=32)),
                ('plot_type', models.CharField(choices=[('Residential', 'Residential'), ('Commercial', 'Commercial')], max_length=50)),
                ('plot_status', models.CharField(choices=[('Open', 'Open'), ('Reserved', 'Reserved'), ('Sold', 'Sold')], default='Open', max_length=50)),
                ('erp_status', models.CharField(choices=[('Open', 'Open'), ('Reserved', 'Reserved'), ('Sold', 'Sold')], default='Open', max_length=50)),
                ('location', models.CharField(max_length=50)),
                ('cash_price', models.DecimalField(decimal_places=2, max_digits=32)),
                ('threshold_price', models.DecimalField(decimal_places=2, max_digits=32)),
                ('lr_no', models.CharField(max_length=255, null=True)),
                ('view', models.CharField(max_length=30, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='PlotBooking',
            fields=[
                ('booking_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('booking_type', models.CharField(choices=[('MPESA', 'MPESA'), ('SPECIAL', 'SPECIAL'), ('OTHER', 'OTHER'), ('DIASPORA', 'DIASPORA')], default='MPESA', max_length=255)),
                ('plots', models.CharField(max_length=255)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=32)),
                ('expected_payment_date', models.DateTimeField()),
                ('deadline', models.DateTimeField(blank=True, null=True)),
                ('transaction_id', models.CharField(blank=True, max_length=50, null=True)),
                ('transportation_transaction_id', models.CharField(blank=True, max_length=50, null=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('office', models.CharField(blank=True, max_length=50, null=True)),
                ('proof_of_payment', models.FileField(blank=True, max_length=255, null=True, upload_to='plots/bookings')),
                ('upload_time', models.DateTimeField(blank=True, null=True)),
                ('status', models.CharField(choices=[('OPEN', 'OPEN'), ('OLD', 'OLD'), ('TIMED', 'TIMED'), ('DONE', 'DONE'), ('WAITING', 'WAITING'), ('SUSPENDED', 'SUSPENDED'), ('REJECTED', 'REJECTED'), ('REVERTED', 'REVERTED')], default='OPEN', max_length=50)),
                ('creation_date', models.DateTimeField(auto_now_add=True)),
                ('approval', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='PlotSizeCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(max_length=5, unique=True)),
                ('start_size', models.DecimalField(decimal_places=5, max_digits=32)),
                ('end_size', models.DecimalField(decimal_places=5, max_digits=32)),
                ('definition', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Project',
            fields=[
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('initials', models.CharField(blank=True, max_length=10, null=True)),
                ('link', models.CharField(blank=True, max_length=255, null=True)),
                ('priority', models.IntegerField(blank=True, null=True)),
                ('tier', models.CharField(blank=True, choices=[('1', '1'), ('2', '2'), ('3', '3'), ('4', '4'), ('5', '5')], max_length=50, null=True)),
                ('visibiliy', models.CharField(blank=True, choices=[('SHOW', 'SHOW'), ('HIDE', 'HIDE')], default='SHOW', max_length=50, null=True)),
                ('bank', models.CharField(blank=True, max_length=50, null=True)),
                ('account_no', models.CharField(blank=True, max_length=50, null=True)),
                ('projectId', models.CharField(max_length=200, primary_key=True, serialize=False)),
                ('website_link', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='DiasporaReservation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plots', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('deadline', models.DateTimeField(blank=True, null=True)),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='diaspora_reservation_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
    ]
