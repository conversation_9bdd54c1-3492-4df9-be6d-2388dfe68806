from leads.serializers import DiasporaTripsSerializer
from rest_framework import serializers
from users.serializers import UserSerializer
from .models import DiasporaReceipts, DiasporaReservation, Forex, MpesaTransaction, Plot, PlotBooking, PlotSizeCategory, Project



class PlotSizeCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = PlotSizeCategory
        fields = "__all__"  # Include all fields from the model

class PlotSerializer(serializers.ModelSerializer):
    class Meta:
        model = Plot
        fields = "__all__"  # Include all fields from the model

class ProjectsSyncSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = "__all__"  # Include all fields from the model


class ProjectSerializer(serializers.ModelSerializer):
    class Meta:
        model = Project
        fields = "__all__"  # Include all fields from the model

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        plots = Plot.objects.filter(project=instance)
        plots_count = plots.count()
        representation['open_plots'] = plots.filter(plot_status='Open').count()
        representation['sold_plots'] = plots.filter(plot_status='Sold').count()
        representation['reserved_plots'] = plots.filter(plot_status='Reserved').count()
        representation['percentage_sold'] = (representation['sold_plots'] / plots_count) * 100 if plots_count > 0 else 0
        return representation


class ProjectDetailsSerializer(serializers.ModelSerializer):
    plots = PlotSerializer(many=True, read_only=True)

    class Meta:
        model = Project
        fields = "__all__"  # Include all fields from the model
        depth = 1  # Include related plots in the serialized output

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        plots = Plot.objects.filter(project=instance)
        plots_count = plots.count()
        representation['open_plots'] = plots.filter(plot_status='Open').count()
        representation['sold_plots'] = plots.filter(plot_status='Sold').count()
        representation['reserved_plots'] = plots.filter(plot_status='Reserved').count()
        representation['percentage_sold'] = (representation['sold_plots'] / plots_count) * 100 if plots_count > 0 else 0
        return representation

class ForexSerializer(serializers.ModelSerializer):
    class Meta:
        model = Forex
        fields = "__all__"  # Include all fields from the model


class PlotBookingSerializer(serializers.ModelSerializer):
    proof_of_payment = serializers.FileField(required=False)  
    class Meta:
        model = PlotBooking
        fields = "__all__"  # Include all fields from the model

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['lead_name'] = instance.lead.name if instance.lead else ""
        representation['customer_name'] = instance.customer.customer_name if instance.customer else ""
        representation['marketer_name'] = instance.marketer.fullnames if instance.marketer else ""
        return representation

class MpesaTransactionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MpesaTransaction
        fields = "__all__"  # Include all fields from the model


class DiasporaReservationSerializer(serializers.ModelSerializer):
    class Meta:
        model = DiasporaReservation
        fields = "__all__"  # Include all fields from the model

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['marketer_name'] = instance.marketer.fullnames if instance.marketer else ""
        representation['trip'] = DiasporaTripsSerializer(instance.trip).data
        return representation
    

class DiasporaReceiptsSerializer(serializers.ModelSerializer):
    class Meta:
        model = DiasporaReceipts
        fields = "__all__"  
        
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['marketer'] = instance.booking_id.marketer.fullnames if instance.booking_id and instance.booking_id.marketer else None
        representation['proof_of_payment'] = (
            instance.booking_id.proof_of_payment.url 
            if instance.booking_id and instance.booking_id.proof_of_payment 
            else None
        )

        return representation
    
    