from decimal import Decimal
import os
from inventory.models import Plot, PlotSizeCategory
from rest_framework.response import Response
from rest_framework import status
from inventory.tasks import send_email_task
from users.models import User
from .models import DiasporaReservation, Forex, MpesaTransaction, Plot, PlotBooking, PlotSizeCategory, Project
from copy import deepcopy
import base64
from django.utils import timezone
import requests
from services.models import CustomSystemLog
from asgiref.sync import sync_to_async
from threading import Thread
from urllib.parse import quote
from requests_ntlm import HttpNtlmAuth
import json

def update_erp_plot_status(plot_no, status):
    """
    Update the ERP portal with the plot status asynchronously.
    Logs the result to CustomSystemLog.
    """

    def _send_request(plot_no, status):
        url2= f'https://crm.optiven.co.ke/API/sync_from_crm.php'
        try: 
            plot = Plot.objects.get(plot_no=plot_no)
            pk=plot.plotId
            company_name = quote("Optiven R.E")
            erp_username = os.getenv('ERP_USERNAME')
            erp_password = os.getenv('ERP_PASSWORD')
            auth = HttpNtlmAuth(erp_username, erp_password)
            headers = {
                'User-Agent': 'Mozilla/5.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json',
            }

            url = f"http://192.168.0.5:8048/optiven/ODataV4/Company('{company_name}')/Property_Card('{pk}')"
            # Get ETag
            response_data = requests.get(url, auth=auth, headers=headers)
            response_data.raise_for_status()
            response_data = response_data.json()
            etag = response_data.get('@odata.etag')
            if etag:
                headers['If-Match'] = etag
            # Prepare data
            data = {
                # 'Status': status,
                'Portal_Status': status,
            }
            # Patch request
            response = requests.patch(url, auth=auth, headers=headers, json=data)
            print(f"Status: {response.status_code}")
            print(json.dumps(response.json(), indent=4))

        except Exception as e:
            print (f"Error updating ERP for plot {plot_no}: {e}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action='Status Update',
                module='Inventory',
                message=str(e)
            )
        try:
            # Send a request to the second URL
            payload = {"plot_no": plot_no, "status": status}
            response2 = requests.post(url2, json=payload, headers={"Content-Type": "application/json"}, timeout=10)
            print(f"Response for portal: {response2.status_code} - {response2.text}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action=f"{plot_no} Status Update to portal",
                message=f"Status: {status}, HTTP {response2.status_code}, Response: {response2.text}"
            )
        except Exception as e:
            print(f"Error updating to portal for plot {plot_no}: {e}")
            CustomSystemLog.objects.create(
                system_level='CRM-ERP',
                action='Status Update to portal',
                module='Inventory',
                message=str(e)
            )

    Thread(target=_send_request, args=(plot_no, status)).start()

def plots_availability_checker(plots_numbers):
    """
    Validate the availability of plots.
    """
    if not plots_numbers:
        return {"message": "No plots provided", 'success': False}

    plots = Plot.objects.filter(plot_no__in=plots_numbers)
    found_plot_nos = set(plot.plot_no for plot in plots)

    for plot_number in plots_numbers:
        if plot_number not in found_plot_nos:
            return {"message": f"Plot Number - {plot_number} not found", 'success': False}

    for plot in plots:
        if plot.plot_status != 'Open':
            return {"message": f"Plot Number - {plot.plot_no} is not open", 'success': False}

    return {"message": "Plots available for reservation", 'success': True}

def transaction_validator(transaction):

    # Check if the transaction ID is valid
    
    if not transaction:
        data = {'error': "Invalid transaction ID"}
        json_string = json.dumps(data)
        return  json_string
    if transaction.status == 'CLOSED':
        data = {'error': "Transaction ID already used in booking"}
        json_string = json.dumps(data)
        return  json_string 
    data = {
        'amount': float(transaction.amount),
        'id': transaction.trans_id,
        'status':transaction.status
        }

    json_string = json.dumps(data)
    
    return  json_string 

def plot_booking_amount_validator(paid_amount, selected_plots):
    """
    Validate the booking amount against the plots.
    Returns a dictionary with `status`, `message`, and `final_amount`.
    """
    if not paid_amount or not selected_plots:
        return {"status": False, "message": "Paid amount and plots are required"}

    total_amount = 0
    for plot in selected_plots:
        if not plot:
            return {"status": False, "message": f"Plot ID - {plot.plot_no} not found"}
        
        # if plot.plot_status != 'Open':
        #     return {"status": False, "message": f"Plot {plot.plot_no} is not available"}
        
        if plot.project.projectId in ['PRJ00026', 'PRJ00068']:
            amount = 200000
        elif plot.project.projectId == 'PRJ00062':
            amount = 69500
        else:
            amount = 100000
        total_amount=total_amount+amount
        

    if paid_amount < Decimal(total_amount):
        return {"status": False, "message": f"Paid amount is less than the required booking fee, Total required is ksh {total_amount}"}

    return {
        "status": True,  # True = no error
        "final_amount": Decimal(total_amount)
    }
              
def reserve_plots(plotsList,status):
    for plot_id in plotsList:
        try:
            plot = Plot.objects.get(plot_no=plot_id)  # or use .get(id=...) if it's a PK
            plot.plot_status = status
            plot.save(update_fields=["plot_status"])

            # Update ERP portal status
            update_erp_plot_status(plot_id, status)
            
            
        except Plot.DoesNotExist:
            print(f"Plot {plot_id} not found")
            return {"status": False, "message": f"Plot number: {plot_id} could not be {status}"}
    return {"status": True, "message": f"All plots {status}"}

def send_offerletter(booking_id):
    try:
        booking= PlotBooking.objects.get(booking_id=booking_id)
    except PlotBooking.DoesNotExist:
        return {"status": False, "message": f"Plot booking not found."}
    def encode_booking_id(booking_id):
        booking_id_bytes = str(booking_id).encode('utf-8')
        base64_bytes = base64.urlsafe_b64encode(booking_id_bytes)
        base64_booking_id = base64_bytes.decode('utf-8')
        return base64_booking_id
    encoded_id = encode_booking_id(booking.booking_id)
    offerLetter_link=os.getenv('OFFERLETTER_LINK', 'https://offerletter.optiven.co.ke?id=')
    context = {
                'employee': f'{booking.marketer.fullnames}',
                'plots': booking.plots,
                'bookingid': str(booking.booking_id),
                'year': str(timezone.now().year),
                'link': str(f"{offerLetter_link}{encoded_id}"),
            }
    
    
    email = booking.marketer.email
    offerletter_template = 'inventory/offerletter.html'
    send_email_task.delay(
         subject=f"{booking.plots} offer letter",
         recipients=[email],
         template_name=offerletter_template,
         context=deepcopy(context),
         )
    return {"status": True, "message": f"All plots {status}"}

def parse_plots(plots_json):
    if isinstance(plots_json, list):
        plots_json = ','.join(str(p) for p in plots_json)
    plots_json = plots_json.strip().replace(' ', '').upper()
    if not plots_json:
        return []
    try:
        parsed = json.loads(plots_json)
        if isinstance(parsed, list):
            return parsed
        else:
            return [parsed]  
    except json.JSONDecodeError:
        pass
    
    if ',' in plots_json:
        return [item.strip().upper() for item in plots_json.split(',') if item.strip()]
    return [plots_json.strip().upper()]