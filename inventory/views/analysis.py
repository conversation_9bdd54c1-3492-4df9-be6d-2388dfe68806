from drf_yasg.utils import swagger_auto_schema
from inventory.models import Plot, PlotBooking, Project
from rest_framework import views, viewsets, filters
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from services.models import CustomSystemLog
from services.serializers import CustomSystemLogSerializer
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.conf import settings

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator



class InventoryStatisticsView(views.APIView):
    
    """
    View to retrieve inventory statistics.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Inventory'],
        operation_summary="Get Inventory Statistics",
        operation_description="Retrieve statistics about the inventory, such as total items, available items, and reserved items.",
        responses={200: 'Inventory statistics retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        projects = Project.objects.all()
        projects_count = projects.count()

        plots = Plot.objects.all()
        plots_count = plots.count()

        open_plots_count = plots.filter(plot_status='Open').count()
        sold_plots_count = plots.filter(plot_status='Sold').count()

        plot_bookings = PlotBooking.objects.all()
        special_bookings_count = plot_bookings.filter(booking_type='SPECIAL').count()
        mpesa_bookings_count = plot_bookings.filter(booking_type='MPESA').count()
        other_bookings_count = plot_bookings.filter(booking_type='OTHER').count()

        statistics = {
            "projects_count": projects_count,
            "plots_count": plots_count,
            "open_plots_count": open_plots_count,
            "sold_plots_count": sold_plots_count,
            "special_bookings_count": special_bookings_count,
            "mpesa_bookings_count": mpesa_bookings_count,
            "other_bookings_count": other_bookings_count,
        }
        
        return Response(statistics, status=200)
    

class InventoryLogsViewset(viewsets.ModelViewSet):
    serializer_class = CustomSystemLogSerializer
    queryset = CustomSystemLog.objects.filter(module='Inventory')
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['system_level','user','action', 'module','message','timestamp']
    search_fields = ['system_level','user__fullnames','action', 'module','message','timestamp']
    ordering_fields = ['system_level','user','action', 'module','message','timestamp']
    http_method_names=['get']
    @swagger_auto_schema(tags=['Inventory'],operation_summary="Get Inventory Logs",responses={200: 'Inventory logs retrieved successfully.'})
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Inventory'],operation_summary="Get Inventory Logs",responses={200: 'Inventory logs retrieved successfully.'})
    def retrieve(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    


class ProjectPlotsReportView(views.APIView):
    
    """
    View to retrieve plots report for a specific project.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Inventory'],
        operation_summary="Get plots report for a specific project and statistics",
        responses={200: 'Project plots report retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        params = request.query_params
        project_id = params.get('project_id')

        try:

            project = Project.objects.filter(projectId=project_id).first()
            if not project:
                return Response(dict(success=False, message='No such project found'), status=400)
            
            plots = Plot.objects.filter(project=project)
            

            title = f'{project.name} Plots Report'

            thead = [
                "plot_no",
                "plot_size",
                "plot_type",
                "plot_status",
                "location",
                "cash_price",
                "threshold_price",
                "lr_no",
            ]

            tbody = []
            for plot in plots:                
                tbody.append({
                "plot_no": plot.plot_no,
                "plot_size": plot.plot_size,
                "plot_type": plot.plot_type,
                "plot_status": plot.plot_status,
                "location": plot.location,
                "cash_price": "{:,.2f}".format(plot.cash_price) or 0,
                "threshold_price": "{:,.2f}".format(plot.threshold_price) or 0,
                "lr_no": plot.lr_no if plot.lr_no else '--',
                })

            # Import pandas and BytesIO for Excel file creation
            import pandas as pd
            from io import BytesIO
            from django.http import HttpResponse

            # create dataframe and write excel file 
            df = pd.DataFrame(tbody)
            excel_file = BytesIO()
            writer = pd.ExcelWriter(excel_file, engine='xlsxwriter')
            df.to_excel(writer, sheet_name='Sheet1', index=False, startrow=2)

            workbook = writer.book
            worksheet = writer.sheets['Sheet1']

            # Title
            title_format = workbook.add_format({'bold': True, 'align': 'center', 'font_size': 14})
            worksheet.merge_range(0, 0, 0, len(thead)-1, title, title_format)


            # Column width
            for i, col in enumerate(df.columns):
                max_len = max(df[col].astype(str).apply(len).max(), len(thead[i]))
                worksheet.set_column(i, i, max_len + 2)

            writer.close()
            excel_file.seek(0)

            response = HttpResponse(
                excel_file.read(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{project.name}_plots_report.xlsx"'
            return response


        except Exception as e:
            return Response(dict(success=False, message=str(e)), status=500)
    
