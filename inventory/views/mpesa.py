from django_filters.rest_framework import DjangoFilterBackend
from inventory.models import MpesaTransaction
from inventory.serializers import MpesaTransactionSerializer
from rest_framework import viewsets, filters
from drf_yasg.utils import swagger_auto_schema
from rest_framework.viewsets import ReadOnlyModelViewSet
from rest_framework.permissions import IsAuthenticated, AllowAny
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve','partial_update']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

class MpesaTransactionViewSets(viewsets.ModelViewSet):
    """
    API endpoint that allows Mpesa transactions to be viewed or edited.
    """
    queryset = MpesaTransaction.objects.all()
    serializer_class = MpesaTransactionSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ('trans_id', 'status', 'amount',)
    search_fields = ('trans_id', 'status', 'amount')
    ordering_fields = ('trans_id', 'status', 'amount')
    http_method_names=('get','partial_update',)

    @swagger_auto_schema( tags=['Inventory'],operation_description="List, filter,search,order transactions")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a transactions by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Update a transactions ")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)