from decimal import Decimal
import json
from django.contrib.auth import authenticate
from customers.models import Customer
from inventory.helpers import plot_booking_amount_validator, plots_availability_checker, transaction_validator,reserve_plots,send_offerletter,parse_plots
from inventory.tasks import send_email_task
from leads.models import DiasporaTrips, Prospects,LeadSource
from rest_framework import status, viewsets, filters
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from django.db import transaction
from django.db.models import Q
from django.conf import settings
from rest_framework.viewsets import ViewSet
import uuid
from copy import deepcopy
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.parsers import MultiPartParser, FormParser
from services.models import CustomSystemLog
from ..serializers import DiasporaReceiptsSerializer, DiasporaReservationSerializer, ForexSerializer, PlotBookingSerializer, PlotSerializer, PlotSizeCategorySerializer, ProjectDetailsSerializer, ProjectSerializer
from ..models import DiasporaReservation, Forex, MpesaTransaction, Plot, PlotBooking, PlotSizeCategory, Project,DiasporaReceipts
from users.models import User
from utils.pdf import generate_pdf
from datetime import datetime
import os


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

# Normalize email fields
def normalize_emails(field):
        if not field:
            return []
        if isinstance(field, str):
            # This handles comma-separated strings
            return [email.strip() for email in field.split(",") if email.strip()]
        if isinstance(field, list):
            return [email.strip() for email in field if isinstance(email, str)]
        raise ValueError(f"Invalid email field: {field}")

# Create your views here.
class projects_view(viewsets.ModelViewSet):
    """
    Viewset for viewing projects
    """
    queryset = Project.objects.all().order_by('priority')
    serializer_class = ProjectSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'priority', 'visibiliy', 'projectId', 'initials', 'bank']
    search_fields = ['name', 'priority', 'visibiliy', 'projectId', 'initials', 'bank']
    # ordering_fields = ['-priority', 'name']
    # ordering = ['-priority', 'name']
    def get_serializer_class(self):
        if self.action == 'retrieve':
            return ProjectDetailsSerializer  
        return super().get_serializer_class()
    def paginate_queryset(self, queryset):
        return None

    def get_paginated_response(self, data):
        return Response(data)

    @swagger_auto_schema( tags=['Inventory'],operation_description="List, filter,search,order Projects")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a Project by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
class plots_view(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for viewing plots
    """
    queryset = Plot.objects.all()
    serializer_class = PlotSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['plot_no', 'plot_type', 'plot_status', 'lr_no', 'plotId', 'project', 'cash_price']
    search_fields = ['plot_no', 'plot_type', 'plot_status', 'lr_no', 'plotId', 'project', 'cash_price']
    ordering_fields = ['plotId','plot_number']
    @swagger_auto_schema( tags=['Inventory'],operation_description="List, filter,search,order Plots")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a Plot by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
class PlotSizeCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for viewing plots
    """
    queryset = PlotSizeCategory.objects.all()
    serializer_class = PlotSizeCategorySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    @swagger_auto_schema( tags=['Inventory'],operation_description="List plot size categories")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a Plot Size Category by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
class PlotBookingViewSet(viewsets.ModelViewSet):
    """
    Viewset for Plot Booking
    """
    queryset = PlotBooking.objects.all()
    serializer_class = PlotBookingSerializer
    parser_classes = [MultiPartParser, FormParser]
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['booking_type','office', 'status', 'deadline', 'marketer']
    search_fields = ['plots', 'status', 'transaction_id','transportation_transaction_id', 'booking_type','creation_date', 'deadline']
    ordering_fields = ['plots', 'office']
    http_method_names = ['get', 'post', 'patch', 'delete']

    # Add required fields for documentation or validation
    required_fields = ['plots', 'booking_type', 'amount', 'customer']
    ordering = ['-creation_date']    

    def get_queryset(self):
        queryset = PlotBooking.objects.all()
        _type = self.request.query_params.get('type', "")

        if _type == "all":
            queryset = queryset.filter(status__in=['DONE', 'SUSPENDED']).order_by('-creation_date')
        return queryset

    @swagger_auto_schema(tags=['Inventory'],operation_description="List, filter, search, order plot bookings")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a plot booking by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Create a plot booking")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user
        if user == None:
            return Response({"error": " User id required", 'data': f'you have to be logged in to proceed'}, status=status.HTTP_400_BAD_REQUEST)
        try:
            with transaction.atomic():
                booking_type = data.get('booking_type')
                reserved_plot = None  # used on diaspora booking since diapora allows only one plot booking at a time
                plots_json = data.get('plots')
                plotsList=parse_plots(plots_json)
                if not plotsList:
                    return Response({"error": "No plots provided", 'data': f'{data}', 'plotsList': f'{plotsList}'}, status=status.HTTP_400_BAD_REQUEST)
                
                # trim the plots in plots list and set to uppercase
                plotsList = [plot.strip().upper() for plot in plotsList ]
                paid_amount = ''
                if data.get('amount'):
                    paid_amount = Decimal(data.get('amount'))
                else:
                    paid_amount =0
                
                if booking_type == 'DIASPORA':
                    try:
                        reserved_plot = Plot.objects.get(plot_no=plotsList[0], plot_status='Reserved')
                    except Plot.DoesNotExist:
                        return Response({"error": f"Plot Number - {plotsList[0]} not found or not available"}, status=status.HTTP_400_BAD_REQUEST)
                else:
                    # check if plots are available
                    availability = plots_availability_checker(plotsList)
                    if availability.get('success') == False:
                        return Response({"error": availability['message'], 'success': availability['success']}, status=status.HTTP_400_BAD_REQUEST)
                
                selected_plots = Plot.objects.filter(plot_no__in=plotsList)
                plots = ','.join(plotsList)  # plot numbers
                data['plots'] = plots
                data['marketer'] = user.employee_no
                timeout_period = 0  # in hours (applicable for diaspora and special bookings)
                customer = None
                
                 # General data validation and saving                 
                if booking_type != 'MPESA' and booking_type != 'DIASPORA':
                    validated_total_amount = plot_booking_amount_validator(paid_amount, selected_plots)
                    if validated_total_amount['status'] == False:
                        return Response({"error": validated_total_amount["message"]}, status=status.HTTP_400_BAD_REQUEST)
                    data['amount'] = validated_total_amount['final_amount']
                
                if user.region == 'ATLANTIC TEAM':
                    data['office'] = 'HQ'
                else:
                    data['office'] = 'Global'
                
                
                if booking_type != 'DIASPORA':
                    if booking_type == 'SPECIAL':
                        # chack if marketer has more than 3 open bookings
                        bookings = PlotBooking.objects.filter(marketer=user, status__in=['WAITING', 'OPEN']).count()
                        if bookings >= 3:
                            return Response({"error": "You have reached the maximum number of bookings"}, status=status.HTTP_400_BAD_REQUEST)

                    # check if customer or lead is provided 
                    customer = data.get('customer')
                    if customer == None :
                        return Response({"error": "Customer or Lead must be provided"}, status=status.HTTP_400_BAD_REQUEST)
                    if customer.startswith('CL'):
                        customer = Customer.objects.filter(customer_no=customer).first()
                        if not customer:
                            return Response({"error": "Customer not found"}, status=status.HTTP_400_BAD_REQUEST)
                        data['customer'] = customer.customer_no
                        customer_name= customer.customer_name
                        data['customer_name']=customer_name
                        data['lead']= None
                    elif customer=='000':
                        return Response({"error": "Customer not found"}, status=status.HTTP_400_BAD_REQUEST)
                    else:
                        customer = Prospects.objects.filter(id=customer).first()
                        if not customer:
                            return Response({"error": "Leads not found"}, status=status.HTTP_400_BAD_REQUEST)
                        data['lead'] = customer.id
                        customer_name= customer.name
                        data['customer_name']= customer_name
                        data['customer'] = None

                    # handle special booking type
                    if booking_type == 'SPECIAL':
                        data['status'] = 'OPEN'
                        data['approval'] = True
                        timeout_period = 48
                        data['transaction_id'] = None
                        data['transportation_transaction_id'] = None

                    # handle mpesa booking type
                    if booking_type == 'MPESA':
                        data['status'] = 'DONE'
                        data['approval'] = False

                        transaction_id_value = data.get('transaction_id')
                        if not transaction_id_value:
                            return Response({"error": "Transaction ID is required"}, status=status.HTTP_400_BAD_REQUEST)

                        transaction_obj = MpesaTransaction.objects.filter(trans_id=transaction_id_value).first()
                        if not transaction_obj:
                            return Response({"error": "M-Pesa transaction not found"}, status=status.HTTP_400_BAD_REQUEST)
                        transaction_data = json.loads(transaction_validator(transaction_obj))
                        if transaction_data.get('error'):
                            return  Response({"error": transaction_data['error']}, status=400)
                        data['transaction_id'] = transaction_data['id']
                        mpesa_paid_amount = transaction_data['amount']
                        if data.get('transportation_transaction_id'):
                            trans_trans_id_value = data.get('transportation_transaction_id')
                            transport_transaction = MpesaTransaction.objects.filter(trans_id=trans_trans_id_value).first()
                            if not transport_transaction:
                                return Response({"error": "Transportation M-Pesa transaction not found"}, status=status.HTTP_400_BAD_REQUEST)
                            
                            transport_data = json.loads(transaction_validator(transport_transaction))
                            if transport_data.get('error'):
                                return  Response({"error": transport_data['error']}, status=400)
                            
                            data['transportation_transaction_id'] = transport_data['id']
                            tt_amount = transport_data['amount']
                            mpesa_paid_amount += tt_amount

                        validated_total_amount=plot_booking_amount_validator(mpesa_paid_amount, selected_plots)
                        if validated_total_amount['status'] == False:
                            return Response({"error": validated_total_amount["message"]}, status=status.HTTP_400_BAD_REQUEST)
                        data['amount']=mpesa_paid_amount
                        transaction_obj.status = 'CLOSED'
                        transaction_obj.save(update_fields=["status"])
                        if transport_transaction:
                            transport_transaction.status = 'CLOSED'
                            transport_transaction.save(update_fields=["status"])
                        
                        
                    # handle other booking type
                    if booking_type == 'OTHER':
                        data['approval'] = False
                        data['status'] = 'DONE'
                        files = request.FILES.get('proof_of_payment')
                        if not files:
                            return Response({"error": "Proof of payment is required"}, status=status.HTTP_400_BAD_REQUEST)
                        
                        data['proof_of_payment'] = files
                        upload_time = timezone.now()
                        data['upload_time'] = upload_time
                        
                
                # handle diaspora booking type / diaspora receipt
                if booking_type == 'DIASPORA':
                    data['status'] = 'SUSPENDED'
                    data['approval'] = False
                    data['plots'] = reserved_plot.plot_no
                    files = request.FILES.get('proof_of_payment')
                    if not files:
                        return Response({"error": "Proof of payment is required"}, status=status.HTTP_400_BAD_REQUEST)
                    data['proof_of_payment'] = files
                    upload_time = timezone.now()
                    data['upload_time'] = upload_time
                    
                    
                    required_fields = ['first_name','last_name','client_id_pass', 'phone', 'email','plots', 'amount','payment_mode', 'payments_of','marketer', 'state', 'country']
                    missing_fields = [field for field in required_fields if not data.get(field)]
                    if missing_fields:
                        return Response({"error": f"Missing required fields: {', '.join(missing_fields)}"},status=status.HTTP_400_BAD_REQUEST)
                    
                    
                    # remove the plot from DiasporaReservation
                    diaspora_rev_id = data.get('diaspora_reservation')
                    diaspora_rev =  DiasporaReservation.objects.filter(id=diaspora_rev_id).first()
                    if not diaspora_rev:
                        diaspora_rev = DiasporaReservation.objects.filter(plots__icontains=reserved_plot.plot_no).first()
                    if not diaspora_rev:
                        return Response({"error": "Diaspora Reservation not found"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    rev_plots = diaspora_rev.plots.split(',')
                    
                    normalized_rev_plots = [p.strip().upper() for p in rev_plots]
                    plot_no = reserved_plot.plot_no.strip().upper()
                    if plot_no not in normalized_rev_plots:
                        return Response({"error": f"Plot {reserved_plot.plot_no} is not found in your reservations"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    # Remove the plot (original casing match)
                    for p in rev_plots:
                        if p.strip().upper() == plot_no:
                            rev_plots.remove(p)
                            break
                        
                    # After removal, check if any plots are left
                    remaining_plots = [p.strip() for p in rev_plots if p.strip()]
                    
                    if not remaining_plots:
                        diaspora_rev.delete()
                    else:
                        diaspora_rev.plots = ','.join(remaining_plots)
                        diaspora_rev.save(update_fields=["plots"])
                                        
                    # if reserved_plot.plot_no.strip().upper() not in [p.strip().upper() for p in rev_plots]:
                    #     return Response({"error": f" plot {reserved_plot.plot_no} is not found in your reservations " }, status=status.HTTP_400_BAD_REQUEST)
                    # rev_plots.remove(reserved_plot.plot_no)
                    # diaspora_rev.plots = ','.join(rev_plots)
                    # diaspora_rev.save(update_fields=["plots"]) 
                    # if 
                    
                    trip_id= diaspora_rev.trip_id 
                    trip= DiasporaTrips.objects.filter(id=trip_id).first()
                    if not trip:
                        return Response({"error": "Diaspora Trip not found"}, status=status.HTTP_400_BAD_REQUEST)
                    leadsource_id= trip.lead_source_id
                    leadsource= LeadSource.objects.filter(leadsource_id=leadsource_id).first()
                    if not leadsource:
                        return Response({"error": "lead source not found"}, status=status.HTTP_400_BAD_REQUEST) 
                    leadsource_sub_category =leadsource.lead_source_subcategory           

                    # create customer
                    plot_instance = Plot.objects.get(plot_no=plot_no)
                    project_name=plot_instance.project.name
                    client_name = data.get('first_name') + ' ' + data.get('last_name')
                    client_phone = data.get('phone')
                    client_email = data.get('email')
                    payment_mode= data.get('payment_mode')
                    client_id_pass = data.get('client_id_pass')
                    amount = paid_amount
                    payments_of = data.get('payments_of')
                    marketer = data.get('marketer')
                    state = data.get('state')
                    country = data.get('country')
                    
                    #get account number
                    if payment_mode == 'M-PESA - 921225':
                        acc_no = '921225'
                    elif payment_mode == 'JP MORGAN CHASE - *********':
                        acc_no = '*********'
                    elif payment_mode == 'CASH - N/A':
                        acc_no = 'N/A'
                    elif payment_mode == 'LOCAL BANK':
                        payment_mode = plot_instance.project.bank
                        acc_no = plot_instance.project.account_no
                    else:
                        acc_no = 'N/A'
                    
                    
                    
                    
                    customer = Customer.objects.filter(Q(phone=client_phone) | Q(alternative_phone=client_phone) | Q(primary_email=client_email) | Q(alternative_email=client_email)).first()
                    if not customer:
                        # search from prospects
                        lead =  Prospects.objects.filter(Q(phone=client_phone) | Q(alternate_phone=client_phone) | Q(email=client_email) ).first()
                        if not lead:
                            new_lead = Prospects.objects.create(
                                 lead_type= "personal",
                                 name=client_name,
                                 phone =client_phone,
                                 email = client_email,
                                 pipeline_level	="Booking",
                                 marketer_id = user.employee_no,
                                 lead_source_id = leadsource.leadsource_id,
                                 lead_source_subcategory_id = leadsource_sub_category.id,
                                 )
                            data['lead_id'] = new_lead.id     
                        else:
                            data['lead_id'] = lead.id
                        
                    else:
                        

                        # TODO  : add an engagement
                        data['customer'] = customer.customer_no
                    customer_name=client_name

                # reserve selected plots if booking_type is not special
                if not booking_type == 'SPECIAL':
                    
                    results=reserve_plots(plotsList,'Reserved')
                    if results['status'] == False:
                        return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    # Add Log
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'Plot(s) {plots} changed to Reserved by {user.fullnames} for {customer_name}'
                    )

                data['booking_id'] = f"PLB-{uuid.uuid4().hex[:10].upper()}"
                
                serializer = self.serializer_class(data=data)
                if serializer.is_valid():
                    booking_instance = serializer.save()  
                    
                    if booking_type == 'SPECIAL':
                        booking_instance.deadline = booking_instance.creation_date + timezone.timedelta(hours=timeout_period)
                        booking_instance.save(update_fields=["deadline"])
                        
                    # TODO: ADD ENGAGEMENT
                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Create',
                        module='Inventory',
                        message=f'{booking_type} Plot Booking Created by {user.fullnames} for {customer_name} for plots {plots}'
                    )


                    # send email marketer and accounts department
                    if booking_instance.marketer.region in ['ATLANTIC TEAM','ATLANTIC']:
                        manager_email = os.environ.get('SYS_HQ_GM_EMAIL', '<EMAIL>')
                        manager = os.environ.get('SYS_HQ_GM_NAME', 'Joseph')
                    else:
                        manager_email = os.environ.get('SYS_KAREN_GM_EMAIL', '<EMAIL>')
                        manager = os.environ.get('SYS_KAREN_GM_NAME', 'Dennis')
                    customer = customer_name
                    plot_booking_context = {
                                                'employee': f'{booking_instance.marketer.fullnames}',
                                                'booking_type': booking_type,
                                                'manager_email': manager_email,
                                                'manager': str(manager),
                                                'plots': str(booking_instance.plots),
                                                'customer': str(customer),
                                                'creation_date': str(booking_instance.creation_date),
                                                'year': str(timezone.now().year),
                                            }
                    
                    if booking_type == 'OTHER' or booking_type == 'MPESA':                            
                        
                        # to marketer   
                        plot_booking_template = 'inventory/marketer_plot_booking.html'
                        send_email_task.delay(
                            subject=f"{booking_type} Booking Success",
                            recipients=normalize_emails([booking_instance.marketer.email]),
                            template_name=plot_booking_template,
                            context=deepcopy(plot_booking_context)
                            )
                        
                        # to accounts   
                        accounts_email = os.environ.get('SYS_ACCOUNTS_HEAD', '<EMAIL>')
                        accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                        accounts_plot_booking_template = 'inventory/accounts_plot_booking.html'
                        send_email_task.delay(
                            subject=f"{booking_type} Booking Success",
                            recipients=[accounts_email],
                            template_name=accounts_plot_booking_template,
                            context=deepcopy(plot_booking_context),
                            cc= normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                            )
                        results=send_offerletter(booking_instance.booking_id) 
                        if results['status'] == False:
                            return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    if booking_type == 'SPECIAL':
                        
                        # to marketer   
                        plot_booking_template = 'inventory/marketer_plot_booking.html'
                        accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                        send_email_task.delay(
                            subject="Plot Special Booking",
                            recipients=[booking_instance.marketer.email],
                            template_name=plot_booking_template,
                            context=deepcopy(plot_booking_context),
                            from_name='Plot Booking System',
                            cc= normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                            )
                        
                        # to Head of branch   
                        head_plot_booking_template = 'inventory/head_plot_booking.html'
                        send_email_task.delay(
                            subject="Plot Booking Approval",
                            recipients=[manager_email],
                            # cc=[booking_instance.marketer.email] if booking_instance.marketer else [],
                            template_name=head_plot_booking_template,
                            context=deepcopy(plot_booking_context),
                            )
                        
                    if booking_type == 'DIASPORA':
                        
                        messages = {
                                        'Deposit': 'Thank you for joining Optiven family,',
                                        'Additional Deposit': 'Thank you for joining Optiven family,',
                                        'Installment': 'Thank you for being part of Optiven family,',
                                        'Final Payment': 'Congratulations on completion of your payments towards your investment,'
                                    }

                        foot_note = messages.get(payments_of, '')  # default to '' if no match

                        
                        # Create reciept attach and send
                        receipt_template = 'inventory/diapora_receipt.html'
                        receipt_context =  {
                                                'date': datetime.now().strftime("%d %B %Y"),
                                                'receipt_no': str(booking_instance.booking_id),
                                                'client_name': str(customer_name), 
                                                'payment_mode': str(payment_mode),
                                                'account_no': str(acc_no),
                                                'plot_no': str(plot_no),
                                                'payments_of': str(payments_of),
                                                'amount': str(amount),
                                                'plot_number': str(plot_no),
                                                'project': str(project_name),
                                                'total_amount': str(amount),
                                                'foot_note': str(foot_note),
                                                'reciepted_by': f'{booking_instance.marketer.fullnames}',
                                            }
                        document_name=f"OPTIVEN-{booking_instance.booking_id}.pdf"
                    
                        receipt=generate_pdf(receipt_template, receipt_context, output_filename=document_name)
                        
                       
                        # to client   
                        plot_booking_template = 'inventory/diaspora_booking.html'
                        allcc = ''
                        if booking_instance.marketer and booking_instance.marketer.email:
                            allcc = booking_instance.marketer.email
                        accounts_cc = allcc + ',' + os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                        diaspora_bcc= os.environ.get('SYS_ACCOUNTS_BOOKINGS_BCC', '<EMAIL>')
                        send_email_task.delay(
                            subject=f"Official Receipt",
                            recipients=normalize_emails([client_email]),
                            template_name=plot_booking_template,
                            context=deepcopy(receipt_context),
                            attachments=[receipt],
                            cc= normalize_emails(accounts_cc.split(",") if accounts_cc else []),
                            bcc=normalize_emails(diaspora_bcc.split(",") if diaspora_bcc else [])
                            )
                        
                        #  create diaspora reciepts records
                        
                        DiasporaReceipts.objects.create(
                            
                            booking_id=booking_instance,
                            client_id_pass= client_id_pass,
                            client_phone=client_phone,
                            client_email=client_email,
                            client_name= customer_name,
                            plot_no=plot_no,
                            amount =amount,
                            payment_mode =payment_mode,
                            acc_no =acc_no,
                            payments_of =payments_of,
                            marketer=marketer,
                            state =state,
                            country =country,
                            receipt =receipt,
                            trip = trip,
                            cash_price= None,
                            purchase_price= None,
                            total_paid= None,
                            balance_lcy= None,
                            agreement_signed= False,
                            region= None
                            )
                        
                        results=send_offerletter(booking_instance.booking_id) 
                        if results['status'] == False:
                            return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    return Response(dict(success= True, message=f'{booking_type} Booking Was successful', data=serializer.data), status=status.HTTP_201_CREATED)
                else:
                    return Response(dict(success=False, error=serializer.errors), status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Partial Update a Plot Booking by id"
                         )
    def partial_update(self, request, *args, **kwargs):
        '''
        Actions: special_booking_approval, special_file_upload, diaspora_receipt
        '''
        data = request.data.copy()
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user
        action = data.get('action')

        try:
            with transaction.atomic():
                booking_id = data.get('id')
                if not booking_id:
                    return Response({"error": "Booking id required"}, status=status.HTTP_400_BAD_REQUEST)

                booking_instance = PlotBooking.objects.filter(booking_id=data.get('id')).first()
                if not booking_instance:
                    return Response({"error": "Booking not found"}, status=status.HTTP_400_BAD_REQUEST)
                
                if action == 'activate':
                    activation_status=data.get('status')
                    booking_instance.status = activation_status
                    booking_instance.save(update_fields=["status"])
                    return Response({"success": True, "message": "Booking status updated successfully"}, status=status.HTTP_200_OK)
                
                    
               
                if action == 'swap':
                    swap_to_plot_numbers = data.get('swap_to_plot_number')
                    if not swap_to_plot_numbers:
                        return Response({"error": "swap_to_plot_numbers is required"}, status=status.HTTP_400_BAD_REQUEST)
                
                    plotsList=parse_plots(swap_to_plot_numbers)
                    
                    if not plotsList:
                        return Response({"error": "No plots provided", 'data': f'{data}', 'plotsList': f'{plotsList}'}, status=status.HTTP_400_BAD_REQUEST)

                    # trim the plots in plots list and set to uppercase
                    plotsList = [plot.strip().upper() for plot in plotsList ]
                    swap_to_plot_numbers=plotsList
                    booking = booking_instance
                    inital_plots = booking.plots

                    
                    plots_availability = plots_availability_checker(swap_to_plot_numbers)
                    if plots_availability['success'] == False:
                        return Response({"error": plots_availability['message'], 'success': plots_availability['success']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    
                    # update the swap to plot status to reserved
                    booked_plots = Plot.objects.filter(plot_no__in=swap_to_plot_numbers)
                
                    results=reserve_plots(swap_to_plot_numbers,'Reserved')
                    if results['status'] == False:
                        return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    # log changes
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'{user.fullnames} swaped plot(s) {inital_plots} to {swap_to_plot_numbers} '
                    )
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'plot(s) {swap_to_plot_numbers} status changed to Reserved - action by {user.fullnames} ON SWAP '
                    )
                    
                    
                    # update the old plot status to open
                    old_plot_numbers = parse_plots(inital_plots)
                    results=reserve_plots(old_plot_numbers,'Open')
                    if results['status'] == False:
                        return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)    
                    # log changes
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f' plot(s) {old_plot_numbers} changed status to Open - action by {user.fullnames} ON SWAP '
                    )
                    # update the booking instance with the new plot number
                    booking.plots = ','.join(swap_to_plot_numbers)
                    booking.save(update_fields=["plots"])

                    context={   'employee': f'{booking_instance.marketer.fullnames}',
                                'Action': str("Swap"),
                                'inital_plots': str(inital_plots),
                                'marketer': str(booking_instance.marketer.fullnames),
                                'new_plots': str(booking_instance.plots),
                                'year': str(timezone.now().year),}  
                    booking_template = 'inventory/booking_swap_revert.html'
                    send_email_task.delay(
                        subject=f"Plot Swap Completed",
                        recipients=normalize_emails([booking_instance.marketer.email]),
                        template_name=booking_template,
                        context=deepcopy(context)
                        )
                    #accounts
                    accounts_email = os.environ.get('SYS_ACCOUNTS_HEAD', '<EMAIL>')
                    accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                        
                    context={   'employee': f'Accounts team',
                                'Action': str("Swap"),
                                'inital_plots': str(inital_plots),
                                'marketer': str(booking_instance.marketer.fullnames),
                                'new_plots': str(booking_instance.plots),
                                'year': str(timezone.now().year),}  
                    booking_template = 'inventory/booking_swap_revert.html'
                    send_email_task.delay(
                        subject=f"Plot Swap Completed",
                        recipients=normalize_emails([booking_instance.marketer.email]),
                        template_name=booking_template,
                        context=deepcopy(context),
                        cc= normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                        )
                    

                    return Response({"success": True, "message": "Booking Swap was successful"}, status=status.HTTP_200_OK)         


                if action == 'revert':   
                    booking_id = data.get('id')
                    reverting_reason = data.get('reason')
                    if not booking_id and reverting_reason:
                        return Response({"error": "Booking id and reasons required"}, status=status.HTTP_400_BAD_REQUEST)     

                    booking = PlotBooking.objects.filter(booking_id=booking_id).first()
                    if not booking:
                        return Response({"error": "Booking not found"}, status=status.HTTP_400_BAD_REQUEST)

                    
                    # set plots satus to Open
                    initial_plots = booking.plots
                    booked_plots = parse_plots(initial_plots) 
                    results=reserve_plots(booked_plots,'Open')
                    if results['status'] == False:
                            return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    #log changes
                    # CustomSystemLog.objects.create(
                    #     system_level='User',
                    #     user=user,
                    #     action='Edit',
                    #     module='Inventory',
                    #     message=f"{user.fullnames} REVERTED plot(s) {initial_plots} booking(s) and status changed to Open with because of the following reason - '{reverting_reason}' "
                    # )
                    
                    # set bookng status as reverted
                    booking.status = 'REVERTED'
                    booking.save(update_fields=["status"])

                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'Plot Booking of plots {booking_instance.plots} booked by {booking_instance.marketer.fullnames} (employee no :{booking_instance.marketer.employee_no}) was REVERTED by {user.fullnames} because of {reverting_reason} and booking status changed to REVERTED and plots {booking_instance.plots} status changed to Open'
                    )
                    accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                    context={   'employee': f'{booking_instance.marketer.fullnames}',
                                'Action': str("Revert"),
                                'initial_plots': str(initial_plots),
                                'new_plots': str(booked_plots),
                                'reason': str(reverting_reason),
                                'marketer': str(booking_instance.marketer.fullnames),
                                'year': str(timezone.now().year),}  
                    booking_template = 'inventory/booking_swap_revert.html'
                    send_email_task.delay(
                        subject=f"Booking on plot {initial_plots} Reverted",
                        recipients=normalize_emails([booking_instance.marketer.email]),
                        template_name=booking_template,
                        context=deepcopy(context),
                        cc=normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                        )
                    



                    return Response({"success": True, "message": "Booking approved"}, status=status.HTTP_200_OK)
                

                if action == 'special_booking_approval':
                    # approve special booking
                    if booking_instance.status != 'OPEN' or booking_instance.booking_type != 'SPECIAL' or booking_instance.approval != True:
                        return Response({"error": "Booking not open or its not a special booking"}, status=status.HTTP_400_BAD_REQUEST)
                
                    if not data.get('approval_action'):
                        return Response({"error": "Approval action is required"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    if data.get('approval_action') not in ['approve', 'reject']:
                        return Response({"error": "Invalid approval action"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    if data.get('approval_action') == 'approve':
                        booking_instance.status = 'WAITING'
                        booking_instance.deadline = timezone.datetime.now() + timezone.timedelta(hours=48)
                        reason = ''
                        booked_plots_list = booking_instance.plots.split(',')
                        plots_availability = plots_availability_checker(booked_plots_list)
                        if plots_availability['success'] == False:
                            return Response({"error": plots_availability['message'], 'success': plots_availability['success']}, status=status.HTTP_400_BAD_REQUEST)
                        
                        results=reserve_plots(booked_plots_list,'Reserved')
                        if results['status'] == False:
                            return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                        context={   'employee': f'{booking_instance.marketer.fullnames}',
                                'booking_type': str("SPECIAL"),
                                'booking_status': str(booking_instance.status),
                                'deadline': str(booking_instance.deadline),
                                'reason': str(reason),
                                'plots': str(booking_instance.plots),
                                'creation_date': str(booking_instance.creation_date),
                                'year': str(timezone.now().year),}  
                        booking_template = 'inventory/booking_approval.html'
                        send_email_task.delay(
                            subject=f"Special Booking Approved",
                            recipients=normalize_emails([booking_instance.marketer.email]),
                            template_name=booking_template,
                            context=deepcopy(context)
                            )
                        
                    elif data.get('approval_action') == 'reject':
                        if not data.get('reason'):
                            return Response({"error": "Reason for rejection is required"}, status=status.HTTP_400_BAD_REQUEST)
                        booking_instance.status = 'REJECTED'
                        booking_instance.deadline = timezone.datetime.now()
                        reason = data.get('reason')
                        context={   'employee': f'{booking_instance.marketer.fullnames}',
                                'booking_type': str("SPECIAL"),
                                'booking_status': str(booking_instance.status),
                                'deadline': str(booking_instance.deadline),
                                'reason': str(reason),
                                'plots': str(booking_instance.plots),
                                'creation_date': str(booking_instance.creation_date),
                                'year': str(timezone.now().year),}  
                        booking_template = 'inventory/booking_approval.html'
                        send_email_task.delay(
                            subject=f"Special Booking Rejected",
                            recipients=normalize_emails([booking_instance.marketer.email]),
                            template_name=booking_template,
                            context=deepcopy(context)
                            )
                    booking_instance.save(update_fields=["status"])

                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'Special Plot Booking Approved by {user.fullnames} (employee no {user.employee_no}) on plots {booking_instance.plots}'
                    )

                    return Response({"success": True, "message": "Booking approved"}, status=status.HTTP_200_OK)
                
                
                if action == 'special_file_upload':
                    if booking_instance.status != 'WAITING' or booking_instance.booking_type != 'SPECIAL' or booking_instance.approval != True:
                        return Response({"error": "Booking not open or its not a special booking"}, status=status.HTTP_400_BAD_REQUEST)
                
                    file = request.FILES.get('proof_of_payment')
                    if not file:
                        return Response({"error": "Proof of payment is required"}, status=status.HTTP_400_BAD_REQUEST)
                    
                    booking_instance.proof_of_payment = file
                    booking_instance.upload_time = timezone.now()
                    booking_instance.status = 'DONE'
                    reason = ''
                    booking_instance.save(update_fields=["proof_of_payment", "upload_time","status"])

                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'Plot Booking Proof of Payment Updated by {user.fullnames} for for plots {booking_instance.plots}'
                    )
                    
                    # to accounts 
                    accounts_email = os.environ.get('SYS_ACCOUNTS_HEAD', '<EMAIL>')
                    accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                     
                    booking_template = 'inventory/booking_approval.html'
                    context={   'employee': "Accounts Team",
                                'booking_type': str("SPECIAL"),
                                'booking_status': str(booking_instance.status),
                                'deadline': str(booking_instance.upload_time),
                                'marketer': str(booking_instance.marketer.fullnames),
                                'reason': str(reason),
                                'plots': str(booking_instance.plots),
                                'creation_date': str(booking_instance.creation_date),
                                'year': str(timezone.now().year),} 
                    send_email_task.delay(
                        subject=f"Special booking Completed",
                        recipients= normalize_emails(accounts_email.split(",") if accounts_email else []),
                        template_name=booking_template,
                        context=deepcopy(context),
                        cc= normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                        )
                    
                    results=send_offerletter(booking_instance.booking_id) 
                    if results['status'] == False:
                        return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                         
                    return Response({"success": True, "message": "Booking updated"}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

       
    @swagger_auto_schema(tags=['Inventory'],operation_description="Delete a Plot Booking by id")
    def destroy(self, request, *args, **kwargs):
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user
        booking = PlotBooking.objects.filter(booking_id=request.data.get('booking_id')).first()
          # create logs
        CustomSystemLog.objects.create(
            system_level='User',
            user=user,
            action='Delete',
            module='Inventory',
            message=f'Plot Booking {booking.booking_id} Deleted by {user.fullnames} for {booking.customer.customer_name} for plots {booking.plots}'
        )

        return super().destroy(request, *args, **kwargs)
    
class DiasporaReservationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for Diaspora Reservation
    """
    queryset = DiasporaReservation.objects.all().order_by("-id")
    serializer_class = DiasporaReservationSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['plots', 'marketer', 'trip']
    search_fields = ['plots',  'marketer', 'trip']
    ordering_fields = ['plots', 'marketer', 'trip']

    @swagger_auto_schema(tags=['Inventory'],operation_description="List, filter,search,order Diaspora Reservations")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a Diaspora Reservation by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Create a Diaspora Reservation")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user

        try:
            with transaction.atomic():
                data['marketer'] = user.employee_no

                # check if trip is provided
                trip_id = data.get('trip')
                if not trip_id:
                    return Response({"error": "Trip is required"}, status=status.HTTP_400_BAD_REQUEST)
                
                trip = DiasporaTrips.objects.filter(id=trip_id).first()
                if not trip:
                    return Response({"error": "Trip not found"}, status=status.HTTP_400_BAD_REQUEST)            

                # check if plots are available
                plots_json = data.get('plots')
                


                plotsList=parse_plots(plots_json)
                if not plotsList:
                    return Response({"error": "No plots provided", 'data': f'{data}', 'plotsList': f'{plotsList}'}, status=status.HTTP_400_BAD_REQUEST)
                
                # trim the plots in plots list and set to uppercase
                plotsList = [plot.strip().upper() for plot in plotsList]
                
                availability = plots_availability_checker(plotsList)
                if availability['success'] == False:
                        return Response({"error": availability['message'], 'success': availability['success']}, status=status.HTTP_400_BAD_REQUEST)

            

                plots = ','.join(plotsList)
                data['plots'] = plots
                # create diaspora reservation
                serializer = self.serializer_class(data=data)
                if serializer.is_valid():
                    rsvd = serializer.save()
                    
                    rsvd.deadline = rsvd.created_at + timezone.timedelta(hours=72)
                    rsvd.save(update_fields=["deadline"])
                    
                    
                    results=reserve_plots(plotsList,'Reserved')
                    if results['status'] == False:
                        return Response({"error": results['message']}, status=status.HTTP_400_BAD_REQUEST)
                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Create',
                        module='Inventory',
                        message=f'Diaspora Reservation Created by {user.fullnames} for {trip.trip_name} for plots {plots}'
                    )

                    #accounts
                    accounts_email = os.environ.get('SYS_ACCOUNTS_HEAD', '<EMAIL>')
                    accounts_cc = os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '<EMAIL>,<EMAIL>')
                        
                    context={   'employee': f'Accounts team',
                                'trip': str(trip.trip_name),
                                'plots': str(plots),
                                'marketer': str(request.user.fullnames),
                                'date': rsvd.created_at,
                                'year': str(timezone.now().year),}  
                    booking_template = 'inventory/plot_reservation_accounts.html'

                    # send email to accounts team
                    send_email_task.delay(
                        subject=f"Plot Reservation",
                        recipients=normalize_emails([accounts_email]),
                        template_name=booking_template,
                        context=deepcopy(context),
                        cc= normalize_emails(accounts_cc.split(",") if accounts_cc else [])
                        )
                    
                    return Response(dict(success=True, message='Diaspora Reservation Created', data=serializer.data), status=status.HTTP_201_CREATED)
                    
                else:
                    return Response(dict(success=False, error=serializer.errors), status=status.HTTP_400_BAD_REQUEST)
                
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


    @swagger_auto_schema(tags=['Inventory'],operation_description="Partial Update a Diaspora Reservation by id")
    def partial_update(self, request, *args, **kwargs):

        data = request.data.copy()
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user

        try:
            with transaction.atomic():
                reservation = DiasporaReservation.objects.filter(id=data.get('id')).first()
                if not reservation:
                    return Response({"error": "Diaspora Reservation not found"}, status=status.HTTP_400_BAD_REQUEST)
                
                data['marketer'] = reservation.marketer.employee_no

                # check if trip is provided
                trip_id = data.get('trip')
                if trip_id:                    
                    trip = DiasporaTrips.objects.filter(id=trip_id).first()
                    if not trip:
                        return Response({"error": "Trip not found"}, status=status.HTTP_400_BAD_REQUEST)            

                # check if plots are available
                plotsList = data.get('plots')
                if plotsList:
                    plotsList = [plot.strip().upper() for plot in plotsList]
                    reserved_plots = reservation.plots.split(',')

                    plots_not_existing = [new_plot for new_plot in plotsList if new_plot not in reserved_plots]

                    if len(plots_not_existing) > 0:
                        availability = plots_availability_checker(plots_not_existing)
                        if availability['success'] == False:
                            return Response({"error": availability['message'], 'success': availability['success']}, status=status.HTTP_400_BAD_REQUEST)
                    
                    # update the new plots to Reserved and log
                    new_plots = Plot.objects.filter(plot_no__in=plots_not_existing)       
                    if new_plots is not None:             
                        for np in new_plots:
                            np.plot_status = 'Reserved'
                            np.save(update_fields=["plot_status"])
                        
                        CustomSystemLog.objects.create(
                            system_level='User',
                            user=user,
                            action='Edit',
                            module='Inventory',
                            message=f'Diaspora Reservation Plots {",".join(plots_not_existing)} Updated to Reserved by {user.fullnames} for {trip.trip_name}'
                        )
                    
                    # change removed plots status to open and log
                    existing_plots_removed =  [ex_plot for ex_plot in reserved_plots if ex_plot not in plotsList]
                    removed_plots = Plot.objects.filter(plot_no__in=existing_plots_removed)    
                    if removed_plots is not None:                
                        for rp in removed_plots:
                            rp.plot_status = 'Open'
                            rp.save(update_fields=["plot_status"])
                        
                        CustomSystemLog.objects.create(
                            system_level='User',
                            user=user,
                            action='Edit',
                            module='Inventory',
                            message=f'Diaspora Reservation Plots {",".join(existing_plots_removed)} Updated to Open by {user.fullnames} for {trip.trip_name}'
                        )
                    
                    plots = ','.join(plotsList)
                    data['plots'] = plots
                
                # update diaspora reservation
                serializer = self.serializer_class(reservation, data=data, many=False)
                if serializer.is_valid():
                    serializer.save()

                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'Diaspora Reservation Updated by {user.fullnames} for {trip.trip_name} for plots {plots}'
                    )

                    return Response(dict(success=True, message='Diaspora Reservation Updated', data=serializer.data), status=status.HTTP_200_OK)
                
                else:
                    return Response(dict(success=False, error=serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Delete a Diaspora Reservation by id")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
class ForexViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for Forex Rates
    """
    queryset = Forex.objects.all().order_by("-id")
    serializer_class = ForexSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['USD', 'GBP', 'EURO']
    search_fields = ['USD', 'GBP', 'EURO']
    ordering_fields = ['USD', 'GBP', 'EURO']

    @swagger_auto_schema(tags=['Inventory'],operation_description="List, filter,search,order Forex Rates")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Retrieve a Forex Rate by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Inventory'],operation_description="Partial Update a Forex Rate by id")
    def partial_update(self, request, *args, **kwargs):
        if settings.DEBUG:
            user= User.objects.get(employee_no='OL/HR/506')
        else:
            user = request.user
        try:
            with transaction.atomic():
                forex_instance = Forex.objects.first()
                if not forex_instance:
                    return Response({"error": "Forex Rates not found"}, status=status.HTTP_400_BAD_REQUEST)
                
                serializer = self.serializer_class(forex_instance, data=request.data, partial=True)
                if serializer.is_valid():
                    serializer.save()

                    # create logs
                    CustomSystemLog.objects.create(
                        system_level='User',
                        user=user,
                        action='Edit',
                        module='Inventory',
                        message=f'{user.fullnames} updated Forex Rates: USD - {serializer.validated_data.get("USD", forex_instance.USD)}, GBP - {serializer.validated_data.get("GBP", forex_instance.GBP)}, EURO - {serializer.validated_data.get("EURO", forex_instance.EURO)}'
                    )
                    return Response(dict(success=True, message='Forex Rates Updated', data=serializer.data), status=status.HTTP_200_OK)
                else:
                    return Response(dict(success=False, error=serializer.errors), status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class DiasporaReceiptViewSet(viewsets.ModelViewSet):
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    queryset = DiasporaReceipts.objects.all().order_by("-id")
    serializer_class = DiasporaReceiptsSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['plot_no', 'is_cancelled', 'client_phone','client_name', 'client_email', 'payment_mode', 'country', 'trip']
    search_fields = ['plot_no', 'is_cancelled', 'client_phone','client_name', 'client_email', 'payment_mode', 'country', 'trip__id', 'trip__trip_name']
    ordering_fields = ['plot_no', 'is_cancelled', 'client_phone','client_name', 'client_email', 'payment_mode', 'country', 'trip']
    http_method_names = ['get', 'patch']

    @swagger_auto_schema(tags=['Inventory'],operation_description="List, filter,search,order Diaspora Reservations")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Inventory'],
        operation_description="Used to patch a diaspora receipt",
    )
    def partial_update(self, request,  *args, **kwargs):
        data = request.data.copy()
        id = data.get('id')
        action = data.get('action', '')
        try:
            with transaction.atomic():
                
                if action == 'cancel':
                    if data.get('reason') is None:
                        return Response({"error": "Reason for cancellation is required"}, status=400)
                    reason= data.get('reason')
                    receipt = DiasporaReceipts.objects.filter(id=id, is_cancelled=False).first()
                    if not receipt:
                        return Response({"error": "Receipt not found"}, status=404)
                    
                    # plot = Plot.objects.get(plot_no=receipt.plot_no)
                    # plot.plot_status = 'Open'
                    # plot.save(update_fields=["plot_status"])

                    plot_no=[receipt.plot_no]
                    reserve_plots(plot_no, status='Open')

                    receipt.is_cancelled = True
                    receipt.save(update_fields=["is_cancelled"])

                     
                    to=receipt.client_email
                    cancelled_receipt_template = 'inventory/cancelled_receipt.html'
                    context = { 
                        'Client': f'{receipt.client_name}',
                        'plot_no': f'{receipt.plot_no}',
                        'amount': f'{receipt.amount}',
                        'payment_mode': f'{receipt.payment_mode}',
                        'Reason': f'{reason}',
                        'year': str(timezone.now().year),

                        }
                    send_email_task.delay(
                            subject="Official Receipt Cancellation",
                            recipients=[to],
                            template_name=cancelled_receipt_template,
                            context=deepcopy(context),
                            cc=normalize_emails(os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', '')),
                            bcc=normalize_emails(os.environ.get('SYS_ACCOUNTS_BOOKINGS_CC', ''))
                        )




                    return Response({"success": True, "message": "Receipt cancelled successfully"}, status=200)
                
                else: 
                    return super().partial_update(request, *args, **kwargs)
                
        except Exception as e:
            return Response({"error": f"{e}", 'message':'Something went wrong'}, status=500)


class ResendOfferLetterViewSet(ViewSet):
    queryset = None 
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=['Inventory'],
        operation_description="Used to resend an offer letter for a plot booking",
        manual_parameters=[
           openapi.Parameter(name='BOOKING_ID', in_=openapi.IN_QUERY, description='Filter by booking ID.',
                type=openapi.TYPE_STRING, required=True, default='ALL'),
        ]
    )
    def create(self, request):
        """
        List all plot payment options.
        """
        data= request.data.copy()
        booking_id = data.get('BOOKING_ID', 'ALL')
        if booking_id != 'ALL':
            booking = PlotBooking.objects.filter(booking_id=booking_id).first()
            if not booking:
                return Response({"error": "Booking not found"}, status=404)
            if booking.status in ['WAITING', 'SUSPENDED', 'REVERTED','TIMED']:
                return Response({"error": "Booking is not completed,timed out or reverted"}, status=400)
            results = send_offerletter(booking.booking_id)
            if results['status'] == False:
                return Response({"error": results['message']}, status=400)
            
            return Response({"success": True, "message": "Offer letter resent successfully"}, status=200)
        else:
            return Response({"error": "Booking ID is required"}, status=400)