
from django.db import models

from customers.models import Customer
from leads.models import DiasporaTrips, Prospects
from users.models import User

PROJECT_TIER_CHOICES = [
    ('1', '1'),
    ('2', '2'),
    ('3', '3'),
    ('4', '4'),
    ('5', '5'),
]

PLOT_STATUS_CHOICES = [
    ('Open', 'Open'),
    ('Reserved', 'Reserved'),
    ('Sold', 'Sold'),
]

PLOT_TYPE_CHOICES = [
    ('Residential', 'Residential'),
    ('Commercial', 'Commercial')
]   

PLOT_BOOKING_STATUS_CHOICES = [
    ('OPEN', 'OPEN'),
    ('OLD', 'OLD'),
    ('TIMED', 'TIMED'),
    ('DONE', 'DONE'),
    ('WAITING', 'WAITING'),
    ('SUSPENDED', 'SUSPENDED'),
    ('REJECTED', 'REJECTED'),
    ('REVERTED', 'REVERTED'),
]   

PLOT_BOOKING_TYPES_CHOICES = [
    ('MPESA', 'MPESA'),
    ('SPECIAL', 'SPECIAL'),
    ('OTHER', 'OTHER'),
    ('DIASPORA', 'DIASPORA'),
]   

class Project(models.Model):
    projectId: str = models.CharField(max_length=200, primary_key=True,blank=False, null=False)    
    name: str = models.CharField(max_length=255, null=False, unique=True)
    description: str = models.TextField(null=True, blank=True)
    initials: str = models.CharField(max_length=10, null=True, blank=True)
    link: str = models.CharField(max_length=255, null=True, blank=True)
    priority: int = models.IntegerField(null=True, blank=True)  
    tier: str = models.CharField(max_length=50, null=True, blank=True, choices=PROJECT_TIER_CHOICES)
    visibiliy: str = models.CharField(max_length=50, choices=[("SHOW", "SHOW"), ("HIDE", "HIDE")], default="SHOW", null=True, blank=True)
    bank: str = models.CharField(max_length=50, null=True, blank=True)  
    account_no: str = models.CharField(max_length=50, null=True, blank=True) 
    website_link: str = models.CharField(max_length=255, null=True, blank=True) 
    
    class Meta:
        ordering = ['priority']

    def __str__(self):
        return f'{self.initials} - {self.name}'


class Plot(models.Model):
    plotId: str = models.CharField(max_length=50, null=False, primary_key=True) 
    project = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='plots')
    plot_no: str = models.CharField(max_length=50, null=False,unique=True)  
    plot_size: str = models.DecimalField(max_digits=32, decimal_places=5, null=False)
    plot_type: str = models.CharField(max_length=50, null=False, choices=PLOT_TYPE_CHOICES)  
    plot_status: str = models.CharField(max_length=50, null=False, choices=PLOT_STATUS_CHOICES, default='Open') 
    erp_status: str = models.CharField(max_length=50, choices=PLOT_STATUS_CHOICES, default='Open')
    location: str = models.CharField(max_length=50, null=False)
    cash_price: str = models.DecimalField(max_digits=32, decimal_places=2, null=False)
    threshold_price: str = models.DecimalField(max_digits=32, decimal_places=2, null=False)
    lr_no: str = models.CharField(max_length=255, null=True)
    view: str = models.CharField(max_length=30, null=True)
    
    class Meta:
        ordering = ['project__priority', 'plot_no']

    def __str__(self):
        return f'{self.plot_no} - {self.plot_type} - {self.project.name}'
    

class PlotSizeCategory(models.Model):
    category = models.CharField(max_length=5, null=False, unique=True)   
    start_size = models.DecimalField(max_digits=32, decimal_places=5, null=False)
    end_size = models.DecimalField(max_digits=32, decimal_places=5, null=False)
    definition = models.CharField(max_length=100, null=False)

    def __str__(self):
        return f'{self.category} - {self.start_size} - {self.end_size} - {self.definition}'
    

class Forex(models.Model):
    USD = models.FloatField(null=False)
    GBP = models.FloatField(null=False)
    EURO = models.FloatField(null=False)

    def __str__(self):
        return f'Forex Rates: USD - {self.USD}, GBP - {self.GBP}, EURO - {self.EURO}'
    
class DiasporaReservation(models.Model):
    plots = models.CharField(max_length=255, null=False)
    marketer = models.ForeignKey(User, on_delete=models.CASCADE,to_field='employee_no', related_name='diaspora_reservation_marketer')
    trip = models.ForeignKey(DiasporaTrips, on_delete=models.CASCADE, related_name='diaspora_reservation_trip')
    created_at = models.DateTimeField(auto_now_add=True)
    deadline = models.DateTimeField(blank=True, null=True)

class PlotBooking(models.Model):
    booking_id = models.CharField(max_length=50, null=False, primary_key=True,  unique=True) 
    booking_type = models.CharField(max_length=255, choices=PLOT_BOOKING_TYPES_CHOICES, default='MPESA')
    plots = models.CharField(max_length=255, null=False)
    marketer = models.ForeignKey(User, on_delete=models.CASCADE,to_field='employee_no', related_name='plot_booking_marketer')
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='plot_bookings_customer', null=True, blank=True)
    lead = models.ForeignKey(Prospects, on_delete=models.CASCADE, related_name='plot_bookings_lead', null=True, blank=True)
    amount = models.DecimalField(max_digits=32, decimal_places=2, null=False)
    expected_payment_date = models.DateTimeField()
    deadline = models.DateTimeField(blank=True, null=True)
    transaction_id = models.CharField(max_length=50, null=True, blank=True)
    transportation_transaction_id = models.CharField(max_length=50, null=True, blank=True)
    description = models.TextField( null=True, blank=True)
    office = models.CharField(max_length=50,  null=True, blank=True)
    proof_of_payment = models.FileField(upload_to='plots/bookings', max_length=255, blank=True, null=True)
    upload_time = models.DateTimeField( null=True, blank=True)
    status = models.CharField(max_length=50, choices=PLOT_BOOKING_STATUS_CHOICES, default='OPEN')
    creation_date = models.DateTimeField(auto_now_add=True)
    approval = models.BooleanField(default=False)  
    created_leadfiles= models.TextField( null=True, blank=True,default=None)
    created_customers= models.TextField( null=True, blank=True,default=None)
    
    
    class Meta:
        ordering = ['creation_date']

    def __str__(self):
        return f'{self.customer}'
    
class MpesaTransaction(models.Model):
    trans_id = models.CharField(max_length=50, null=False, primary_key=True)
    amount = models.DecimalField(max_digits=32, decimal_places=2, null=False)
    date = models.DateTimeField(auto_now_add=True)
    account_ref = models.CharField(max_length=50, null=False)
    name1 = models.CharField(max_length=50, null=False)
    name2 = models.CharField(max_length=50, null=False)
    name3 = models.CharField(max_length=50, null=False)
    number = models.CharField(max_length=50, null=False)
    status = models.CharField(max_length=10, choices=[('CLOSED', 'CLOSED'), ('OPEN', 'OPEN'), ('PENDING','PENDING')], default='OPEN')
    confirmation = models.BooleanField(default=False)
    utilityBalance = models.DecimalField(max_digits=32, decimal_places=2, null=False)
    notif = models.CharField(max_length=20, blank=True, null=True)
    
    class Meta:
        ordering = ['-date']

class DiasporaReceipts(models.Model):
    booking_id = models.ForeignKey(PlotBooking, on_delete=models.CASCADE,to_field='booking_id',max_length=100)  # Can be a FK if related to a Booking model
    client_id_pass = models.CharField(max_length=100)
    client_phone = models.CharField(max_length=20)
    client_email = models.EmailField()
    client_name = models.CharField(max_length=255)
    plot_no = models.CharField(max_length=100)
    amount = models.DecimalField(max_digits=12, decimal_places=2)
    payment_mode = models.CharField(max_length=100)
    acc_no = models.CharField(max_length=100)
    payments_of = models.CharField(max_length=100)
    marketer = models.CharField(max_length=255)
    state = models.CharField(max_length=100)
    country = models.CharField(max_length=100)
    receipt = models.FileField(upload_to='Diaspora_receipts_pdfs/',max_length=600)
    is_cancelled = models.BooleanField(default=False)
    trip = models.ForeignKey(DiasporaTrips, on_delete=models.CASCADE, related_name='diaspora_receipts_trip', null=True, blank=True,default=None)  # Optional field for trip association
    # Optional fields
    
    cash_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    total_paid = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    balance_lcy = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True)
    agreement_signed = models.BooleanField(null=True, blank=True)
    region = models.CharField(max_length=100, null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Diaspora Receipt'
        verbose_name_plural = 'Diaspora Receipts'
        ordering = ['-created_at']  
        

    def __str__(self):
        return f"Receipt - {self.client_name} ({self.plot_no})"
