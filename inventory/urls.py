from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from inventory.views import mpesa, projects, analysis
inventory_router = DefaultRouter(trailing_slash=False)



# inventory_router.register('dashboard-stats', analysis.InventoryStatisticsView, basename='dashboard-stats')
inventory_router.register('projects', projects.projects_view)
inventory_router.register('plots', projects.plots_view)
inventory_router.register('plot-size-categories', projects.PlotSizeCategoryViewSet)
inventory_router.register('plot-booking', projects.PlotBookingViewSet)
inventory_router.register('diaspora-reservation', projects.DiasporaReservationViewSet)
inventory_router.register('forex', projects.ForexViewSet)
inventory_router.register('mpesa-transactions', mpesa.MpesaTransactionViewSets)
inventory_router.register('logs', analysis.InventoryLogsViewset)
inventory_router.register(r'resend-offerletter', projects.ResendOfferLetterViewSet, basename='resend-offerletter')
inventory_router.register('diaspora-receipts', projects.DiasporaReceiptViewSet)



urlpatterns = [
     path('inventory/', include(inventory_router.urls)),
     path('inventory/dashboard-stats', analysis.InventoryStatisticsView.as_view(), name='dashboard-stats'),
     path('inventory/project-plots-report', analysis.ProjectPlotsReportView.as_view(), name='project-plots-report'),
]
