from celery import shared_task
from django.core.mail import send_mail
from django.conf import settings
from datetime import date, datetime, timedelta
from django.utils import timezone
from .models import Reminder

@shared_task
def send_reminder_emails():
    """Send reminder emails to users 30 minutes before reminder_time for active reminders"""
    now = timezone.now()
    # Calculate the time window: 30 minutes from now
    notification_time = now + timedelta(minutes=30)
    
    # Get active reminders that should be notified (30 min before reminder_time)
    # and haven't been sent yet
    due_reminders = Reminder.objects.filter(
        status='Active',
        reminder_sent=False,
        reminder_time__isnull=False,
        reminder_time__lte=notification_time,
        reminder_time__gte=now
    ).select_related('created_by', 'customer', 'prospect', 'sale')
    
    sent_count = 0
    
    for reminder in due_reminders:
        if reminder.created_by and reminder.created_by.email:
            try:
                subject = f'Reminder: {reminder.title} - Due in 30 minutes'
                
                # Get client info based on client_type
                client_info = ""
                if reminder.client_type == 'Customer' and reminder.customer:
                    client_info = f"Customer: {reminder.customer}"
                elif reminder.client_type == 'Prospect' and reminder.prospect:
                    client_info = f"Prospect: {reminder.prospect}"
                elif reminder.client_type == 'Sale' and reminder.sale:
                    client_info = f"Sale: {reminder.sale}"
                
                # Calculate time until reminder
                time_until = reminder.reminder_time - now
                minutes_until = int(time_until.total_seconds() / 60)
                
                message = f"""
Hello {reminder.created_by.first_name or reminder.created_by.username},

You have a reminder coming up in approximately {minutes_until} minutes:

Title: {reminder.title}
Type: {reminder.reminder_type}
Priority: {reminder.priority}
Scheduled Date: {reminder.reminder_date}
Scheduled Time: {reminder.reminder_time.strftime('%H:%M') if reminder.reminder_time else 'Not set'}
{client_info}
Notes: {reminder.reminder_notes or 'No additional notes'}

Reminder ID: {reminder.reminder_id}

Please be prepared for your scheduled {reminder.reminder_type.lower()}.

Best regards,
CRM System
                """
                
                send_mail(
                    subject=subject,
                    message=message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[reminder.created_by.email],
                    fail_silently=False,
                )
                
                # Mark as sent
                reminder.reminder_sent = True
                reminder.save(update_fields=['reminder_sent'])
                sent_count += 1
                
                print(f"Reminder email sent to {reminder.created_by.email} for reminder {reminder.reminder_id}")
                
            except Exception as e:
                print(f"Failed to send email for reminder {reminder.reminder_id}: {str(e)}")
    
    return f"Processed {due_reminders.count()} reminders, sent {sent_count} emails"
