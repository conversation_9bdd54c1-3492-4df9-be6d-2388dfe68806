# Generated by Django 5.1.7 on 2025-07-25 17:02

import django.db.models.deletion
import reminders.utils
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Reminder',
            fields=[
                ('reminder_id', models.CharField(default=reminders.utils.generate_reminder_id, max_length=50, primary_key=True, serialize=False, unique=True)),
                ('reminder_type', models.CharField(choices=[('Follow-up Call', 'Follow-up Call'), ('Payment Reminder', 'Payment Reminder'), ('Document Collection', 'Document Collection'), ('Site Visit', 'Site Visit'), ('Meeting', 'Meeting'), ('Email', 'Email'), ('SMS', 'SMS'), ('General', 'General')], max_length=50)),
                ('reminder_date', models.DateField(blank=True, help_text='Date for the reminder', null=True)),
                ('reminder_time', models.TimeField(blank=True, help_text='Time for the reminder', null=True)),
                ('priority', models.CharField(choices=[('Low', 'Low'), ('Normal', 'Normal'), ('High', 'High'), ('Urgent', 'Urgent')], default='Normal', max_length=20)),
                ('reminder_notes', models.TextField(blank=True, help_text='Details about the reminder', null=True)),
                ('action_url', models.URLField(blank=True, help_text='URL for action related to this reminder', null=True)),
                ('title', models.CharField(default='', help_text='Title of the reminder', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Detailed description', null=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Snoozed', 'Snoozed'), ('Completed', 'Completed'), ('Cancelled', 'Cancelled')], default='Active', max_length=20)),
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reminders_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reminders', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_reminders', to='leads.prospects')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_reminders', to='sales.leadfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
