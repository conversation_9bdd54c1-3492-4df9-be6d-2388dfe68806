from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer
from leads.models import Prospects
from sales.models import LeadFile
from reminders.utils import generate_reminder_id
from ticketing.models import Ticket



class Reminder(models.Model):
    REMINDER_TYPE_CHOICES = [
        ('Follow-up Call', 'Follow-up Call'),
        ('Payment Reminder', 'Payment Reminder'),
        ('Document Collection', 'Document Collection'),
        ('Site Visit', 'Site Visit'),
        ('Meeting', 'Meeting'),
        ('Email', 'Email'),
        ('SMS', 'SMS'),
        ('General', 'General'),
    ]
    PRIORITY_CHOICES = [
        ('Low', 'Low'),
        ('Normal', 'Normal'),
        ('High', 'High'),
        ('Urgent', 'Urgent'),
    ]
    STATUS_CHOICES = [
        ('Active', 'Active'),
        ('Snoozed', 'Snoozed'),
        ('Completed', 'Completed'),
        ('Cancelled', 'Cancelled'),
    ]
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]
    
    
    reminder_id = models.CharField(max_length=50, unique=True, primary_key=True, default=generate_reminder_id)
    reminder_type = models.CharField(max_length=50, choices=REMINDER_TYPE_CHOICES)
    reminder_date = models.DateField(null=True, blank=True, help_text="Date for the reminder")
    reminder_time = models.DateTimeField(null=True, blank=True, help_text="Time for the reminder")
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='Normal')
    reminder_notes = models.TextField(null=True, blank=True, help_text="Details about the reminder")
    action_url = models.URLField(blank=True, null=True, help_text="URL for action related to this reminder")
    
    # Additional fields for proper functionality
    title = models.CharField(max_length=255, help_text="Title of the reminder", default="")
    description = models.TextField(blank=True, null=True, help_text="Detailed description")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='Active')
    reminder_sent = models.BooleanField(default=False, help_text="Track if reminder email has been sent")
    
    
    
    
    
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect', null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='reminders_created', to_field='employee_no')
    customer = models.ForeignKey( Customer, on_delete=models.CASCADE, related_name='reminders', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_reminders',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_reminders', to_field='lead_file_no', null=True, blank=True)
    ticket= models.ForeignKey( Ticket, on_delete=models.CASCADE, related_name='ticket_reminders',  null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure reminders_id is unique"""

        if not self.reminder_id:
            self.reminder_id = generate_reminder_id()
            while Reminder.objects.filter(reminder_id=self.reminder_id).exists():
                self.reminder_id = generate_reminder_id()
        print(f"Saving reminders:with ID: {self.reminder_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.reminder_id} - {self.title}"

    class Meta:
        ordering = ['-created_at']

