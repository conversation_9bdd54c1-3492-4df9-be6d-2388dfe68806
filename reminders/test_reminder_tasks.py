import os
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.contrib.auth import get_user_model
from datetime import date
from reminders.models import <PERSON>minder
from reminders.tasks import send_reminder_emails
from customers.models import Customer
from leads.models import Prospects

User = get_user_model()

def test_reminder_email_task():
    """Simple test for reminder email task"""
    
    try:
        # Create test user
        user = User.objects.create_user(
            username='testuser123',
            email='<EMAIL>',
            first_name='Test',
            employee_no='EMP001'
        )
        
        # Create test reminder
        reminder = Reminder.objects.create(
            title='Test Reminder',
            reminder_type='Follow-up Call',
            reminder_date=date.today(),
            priority='Normal',
            reminder_notes='Test notes for reminder',
            created_by=user,
            client_type='Prospect'
        )
        
        print(f"Created reminder: {reminder.reminder_id}")
        print(f"Reminder date: {reminder.reminder_date}")
        print(f"Created by: {reminder.created_by.username}")
        
        # Run the task
        result = send_reminder_emails()
        print(f"Task result: {result}")
        
        # Cleanup
        reminder.delete()
        user.delete()
        print("Test completed and cleaned up")
        
    except Exception as e:
        print(f"Test failed: {str(e)}")

if __name__ == "__main__":
    test_reminder_email_task()
