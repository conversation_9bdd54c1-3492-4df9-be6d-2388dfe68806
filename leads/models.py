from django.db import models
from users.models import Departments, User


PIPELINE_LEVEL_CHOICE = [
        ('New', 'New'), 
        ('Qualified Leads', 'Qualified Leads'), 
        ('Nurturing', 'Nurturing'), 
        ('Site Visits', 'Site Visits'), 
        ('Booking', 'Booking'), 
        ('Offer Letter', 'Offer Letter'), 
        ('Sale Agreement', 'Sale Agreement'),
        ('Converted', 'Converted'),
        ('Lost', 'Lost'),
        ('Completed', 'Completed')
    ]
MANAGING_TEAM_CHOICES = [('Digital', 'Digital'), 
                         ('Telemarketing', 'Telemarketing'),
                           ('Diaspora', 'Diaspora'),
                           ('Legal', 'Legal'),
                           ('Marketing', 'Marketing'),
                           ('Other', 'Other')]

class LeadSourceCategory(models.Model):
    # CRUD = 'list','retrieve'
    category_id = models.IntegerField(unique=True, null=False, blank=False)  # Making it nullable initially
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.name

class LeadSourceSubCategory(models.Model):
    # CRUD = 'list','retrieve'
    cat_lead_source_id = models.IntegerField(unique=True, null=False, blank=False)  # ID from ERP system
    lead_source_category = models.ForeignKey(LeadSourceCategory, to_field='category_id', on_delete=models.CASCADE, related_name='subcategories')
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    manager = models.ForeignKey(User, to_field='employee_no', on_delete=models.SET_NULL, related_name='lead_source_subcategories', blank=True, null=True)
    manager_name = models.CharField(max_length=255, blank=True, null=True)

class LeadSource(models.Model):
    # CRUD = 'list','retrieve'
    leadsource_id = models.IntegerField(unique=True, null=False, blank=False)  # ID from ERP system
    lead_source_subcategory = models.ForeignKey(LeadSourceSubCategory, to_field='cat_lead_source_id', on_delete=models.CASCADE, related_name='lead_sources_sub_category')
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    qr_code = models.ImageField(upload_to='uploads/lead_source_qr_codes/', blank=True, null=True)
    ref_code = models.CharField(max_length=50, unique=True)

    
    link = models.URLField(blank=True, null=True)
    sales = models.IntegerField(default=0,null=True)
    ongoing_sales = models.IntegerField(default=0,null=True)
    dropped_sales = models.IntegerField(default=0,null=True)
    completed_sales = models.IntegerField(default=0,null=True)
    active_leads = models.IntegerField(default=0,null=True)
    dormant_leads = models.IntegerField(default=0,null=True)
    last_updated = models.DateTimeField(auto_now=True,null=True)
    created_at = models.DateTimeField(auto_now_add=True,null=True)
    manager = models.ForeignKey(User, to_field='employee_no', on_delete=models.SET_NULL, related_name='manager_lead_sources', blank=True, null=True)
    manager_name = models.CharField(max_length=255, blank=True, null=True)
    managing_team = models.CharField(max_length=255, choices=MANAGING_TEAM_CHOICES, blank=True, null=True, default=None)
    
    def __str__(self):
        return self.name
    
class Prospects(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    lead_source_category = models.ForeignKey(LeadSourceCategory, on_delete=models.CASCADE, related_name='lead_sources_category_p',null=True, default=None)
    lead_source_subcategory = models.ForeignKey(LeadSourceSubCategory, on_delete=models.CASCADE, related_name='lead_sources_sub_category_p',null=True, default=None)
    lead_source = models.ForeignKey(LeadSource, to_field='leadsource_id', on_delete=models.CASCADE, related_name='lead_sources',null=True, default=None)
    project = models.ForeignKey('inventory.Project', on_delete=models.CASCADE, related_name='prospect_project', blank=True, null=True, default=None)
    lead_type = models.CharField(max_length=30, choices=[('personal', 'personal'), ('allocated', 'allocated')])
    department = models.ForeignKey(Departments, on_delete=models.CASCADE, related_name='prospect_department', blank=True, null=True, default=None) 
    department_member = models.ForeignKey(User, to_field='employee_no', on_delete=models.CASCADE, related_name='prospect_department_member', blank=True, null=True, default=None) # the allocator
    marketer = models.ForeignKey(User,  to_field='employee_no', on_delete=models.CASCADE, related_name='prospect_marketer',null=True, default=None) # allacated marketer if lead type is allocated
    name = models.CharField(max_length=255)
    phone = models.CharField(max_length=20, blank=True,unique=True ,null=True,)
    alternate_phone = models.CharField(max_length=20, blank=True, null=True, default=None)
    email = models.EmailField(blank=True, null=True , default=None)
    city = models.CharField(max_length=255, blank=True, null=True , default=None)
    country = models.CharField(max_length=255, blank=True, null=True , default=None)
    comment = models.TextField(blank=True, null=True, default=None)
    date = models.DateTimeField(auto_now_add=True)
    status = models.CharField(max_length=50, choices=[('Active', 'Active'), ('Dormant', 'Dormant')], default='Active')
    category = models.CharField(max_length=255, choices=[('Hot', 'Hot'), ('Warm', 'Warm'), ('Cold', 'Cold')], default='Warm') 
    pipeline_level = models.CharField(max_length=50, choices=PIPELINE_LEVEL_CHOICE, default='New')
    is_verified = models.BooleanField(blank=True, null=True,default=False)
    is_converted = models.BooleanField(blank=True, null=True,default=False)
    customer = models.CharField(max_length=255, blank=True, null=True, default=None)
    no_of_sales = models.IntegerField(blank=True, null=True, default=None)
    leadfiles=models.CharField(max_length=255, blank=True, null=True, default=None)
    class Meta:
        ordering = ['-date']
         

    def __str__(self):
        return self.name
    
class Engagements(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    lead_source = models.ForeignKey(LeadSource, to_field='leadsource_id', on_delete=models.CASCADE, related_name='engagements_lead_source')
    prospect = models.ForeignKey(Prospects, on_delete=models.CASCADE, related_name='engagements_prospects')
    engagement_date = models.DateTimeField(auto_now_add=True)
    engagement_type = models.CharField(max_length=50, choices=[('Call', 'Call'), ('Email', 'Email'), ('Meeting', 'Meeting')])
    notes = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=50, choices=[('Active', 'Active'), ('Closed', 'Closed')], default='Active')
    follow_up_date = models.DateTimeField(blank=True, null=True)
    follow_up_notes = models.TextField(blank=True, null=True)
    follow_up_status = models.CharField(max_length=50, choices=[('Pending', 'Pending'), ('Completed', 'Completed')], default='Pending')
    follow_up_by = models.ForeignKey(User,to_field='employee_no', on_delete=models.SET_NULL, related_name='follow_up_engagements', blank=True, null=True)

    def __str__(self):
        return self.prospect.name + " - " + self.engagement_type + " on " + str(self.engagement_date)
    
class prospectFlags(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    prospect = models.ForeignKey(Prospects, on_delete=models.CASCADE, related_name='flags_prospects')
    flag_date = models.DateTimeField(auto_now_add=True)
    flag_notes = models.TextField(blank=True, null=True)
    flag_type = models.CharField(max_length=50, choices=[('Warning', 'Warning'), ('Danger', 'Danger'), ('Info', 'Info')], default='Info')
    flag_status = models.CharField(max_length=50, choices=[('Active', 'Active'), ('Resolved', 'Resolved')], default='Active')
    flag_by = models.ForeignKey(User,to_field='employee_no', on_delete=models.SET_NULL, null=True, related_name='flags_flagger',)

    def __str__(self):
        return self.prospect.name + " - Flag on " + str(self.flag_date)
         
class DiasporaRegions(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    manager = models.ForeignKey(User,to_field='employee_no', on_delete=models.SET_NULL, null=True, related_name='diaspora_regions',)
    manager_name = models.CharField(max_length=255, blank=True, null=True)

    def __str__(self):
        return self.name

class DiasporaTrips(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    lead_source = models.ForeignKey(LeadSource, to_field='leadsource_id', on_delete=models.CASCADE, related_name='ls_diaspora_trips')
    trip_name = models.CharField(max_length=255)
    trip_date = models.DateTimeField(auto_now_add=True)
    trip_notes = models.TextField(blank=True, null=True)
    target_MIB = models.IntegerField( blank=True, null=True)
    visiting_country = models.CharField(max_length=255, blank=True, null=True)
    manager = models.ForeignKey(User,to_field='employee_no', on_delete=models.SET_NULL, null=True, related_name='man_diaspora_trips',)
    diaspora_region = models.ForeignKey(DiasporaRegions,to_field='name',on_delete=models.SET_NULL, blank=True, null=True)
    is_active = models.BooleanField(blank=False, null=False,default=True)

    def __str__(self):
        return self.trip_name + " - Trip on " + str(self.trip_date)
    
class DiasporaTripsMarketers(models.Model):
    # CRUD = 'list','retrieve','create','partial_update','destroy'
    DiasporaTrips = models.ForeignKey(DiasporaTrips, on_delete=models.CASCADE, related_name='trip_diaspora_trips_marketers')
    marketer = models.ForeignKey(User,to_field='employee_no', on_delete=models.CASCADE, related_name='marketer_diaspora_trips_marketers')
    
    def __str__(self):
        return f"{self.marketer.fullnames} {self.DiasporaTrips.trip_name} on  {str(self.DiasporaTrips.trip_date)}"