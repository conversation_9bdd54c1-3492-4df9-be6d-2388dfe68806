from celery import shared_task
from django.db.models import Count, Q
from django.utils import timezone
import logging

from .models import LeadSource, Prospects
from sales.models import LeadFile

logger = logging.getLogger(__name__)


@shared_task(bind=True, max_retries=3, default_retry_delay=60)
def update_lead_source_counts(self):
    """
    Update lead source counts every 5 minutes:
    - active_leads: Count of prospects with status='Active'
    - dormant_leads: Count of prospects with status='Dormant' 
    - ongoing_sales: Count of lead files with lead_file_status_dropped=False
    - dropped_sales: Count of lead files with lead_file_status_dropped=True
    - completed_sales: Count of lead files with balance_lcy < 10
    """
    try:
        logger.info("Starting lead source counts update task")
        
        # Get all lead sources
        lead_sources = LeadSource.objects.all()
        
        updated_count = 0
        
        for lead_source in lead_sources:
            try:
                # Count active and dormant leads from Prospects
                prospects_counts = Prospects.objects.filter(
                    lead_source=lead_source
                ).aggregate(
                    active_count=Count('id', filter=Q(status='Active')),
                    dormant_count=Count('id', filter=Q(status='Dormant'))
                )
                
                # Count sales from LeadFile using both customer_lead_source and engagement fields
                sales_counts = LeadFile.objects.filter(
                    Q(customer_lead_source=lead_source) | Q(engagement=lead_source)
                ).aggregate(
                    ongoing_count=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
                    dropped_count=Count('lead_file_no', filter=Q(lead_file_status_dropped=True)),
                    completed_count=Count('lead_file_no', filter=Q(balance_lcy__lt=10))
                )
                
                # Update the lead source with new counts
                lead_source.active_leads = prospects_counts['active_count'] or 0
                lead_source.dormant_leads = prospects_counts['dormant_count'] or 0
                lead_source.ongoing_sales = sales_counts['ongoing_count'] or 0
                lead_source.dropped_sales = sales_counts['dropped_count'] or 0
                lead_source.completed_sales = sales_counts['completed_count'] or 0
                lead_source.last_updated = timezone.now()
                
                lead_source.save()
                updated_count += 1
                
                logger.debug(f"Updated counts for LeadSource {lead_source.name}: "
                           f"Active: {lead_source.active_leads}, "
                           f"Dormant: {lead_source.dormant_leads}, "
                           f"Ongoing: {lead_source.ongoing_sales}, "
                           f"Dropped: {lead_source.dropped_sales}, "
                           f"Completed: {lead_source.completed_sales}")
                
            except Exception as e:
                logger.error(f"Error updating counts for LeadSource {lead_source.name}: {str(e)}")
                continue
        
        logger.info(f"Lead source counts update task completed. Updated {updated_count} lead sources.")
        
        return {
            'status': 'success',
            'updated_count': updated_count,
            'total_lead_sources': lead_sources.count(),
            'timestamp': timezone.now().isoformat()
        }
        
    except Exception as exc:
        logger.error(f"Lead source counts update task failed: {str(exc)}")
        # Retry the task with exponential backoff
        raise self.retry(exc=exc, countdown=60 * (2 ** self.request.retries))