from datetime import datetime
import secrets
import string
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from django.db import connection
from users.models import User


class Command(BaseCommand): 
    help = 'Refresh user codes'

    def add_arguments(self, parser):
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of users to process in each batch'
        )

    def handle(self, *args, **options):
        # First, check the actual database schema
        self.check_mkcode_field()
        
        batch_size = options['batch_size']
        total_users = User.objects.count()
        processed = 0
        failed = 0

        self.stdout.write(f'Processing {total_users} users in batches of {batch_size}...')

        # Process users in batches
        for offset in range(0, total_users, batch_size):
            try:
                user_ids = list(User.objects.values_list('id', flat=True)[offset:offset + batch_size])
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'Error fetching user batch: {str(e)}'))
                continue
            
            # Process each user individually
            for user_id in user_ids:
                try:
                    with transaction.atomic():
                        user = User.objects.get(id=user_id)
                        
                        # Handle created_date if it's a string
                        if isinstance(user.created_date, str):
                            try:
                                parsed_date = datetime.fromisoformat(user.created_date.replace('Z', '+00:00'))
                                if timezone.is_naive(parsed_date):
                                    parsed_date = timezone.make_aware(parsed_date)
                                user.created_date = parsed_date
                            except (ValueError, AttributeError):
                                user.created_date = timezone.now()

                        # Generate unique code
                        code = generate_marketer_code(user)
                        if code:
                            user.mkcode = str(code)  # Ensure it's a string
                            user.save(update_fields=['mkcode', 'created_date'])
                            self.stdout.write(f'User {user.fullnames} code refreshed to {code}')
                            processed += 1
                        else:
                            self.stdout.write(self.style.ERROR(
                                f'Could not generate unique code for {user.fullnames}'
                            ))
                            failed += 1
                            
                except Exception as e:
                    self.stdout.write(self.style.ERROR(
                        f'Error processing user ID {user_id}: {str(e)}'
                    ))
                    failed += 1

        self.stdout.write(self.style.SUCCESS(
            f'User codes refreshed successfully. Processed: {processed}, Failed: {failed}'
        ))

    def check_mkcode_field(self):
        """Check the actual database schema for mkcode field"""
        with connection.cursor() as cursor:
            cursor.execute("DESCRIBE users_user")
            columns = cursor.fetchall()
            for column in columns:
                if column[0] == 'mkcode':
                    self.stdout.write(f'mkcode field type: {column[1]}')
                    break



def generate_marketer_code(user):
    """Generate cryptographically secure random string"""
    employee_no = user.employee_no 
    last_digits = employee_no.split('/')[-1] 
    return f'SM-{last_digits}'
    # return 'SM-' + generate_code
