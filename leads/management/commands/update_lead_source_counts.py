from django.core.management.base import BaseCommand
from django.db.models import Count, Q
from django.utils import timezone

from leads.models import LeadSource, Prospects
from sales.models import LeadFile


class Command(BaseCommand):
    help = 'Update lead source counts for active/dormant leads and ongoing/dropped/completed sales'

    def add_arguments(self, parser):
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed output for each lead source update',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting lead source counts update...'))
        
        # Get all lead sources
        lead_sources = LeadSource.objects.all()
        
        updated_count = 0
        total_count = lead_sources.count()
        
        for lead_source in lead_sources:
            try:
                # Count active and dormant leads from Prospects
                prospects_counts = Prospects.objects.filter(
                    lead_source=lead_source
                ).aggregate(
                    active_count=Count('id', filter=Q(status='Active')),
                    dormant_count=Count('id', filter=Q(status='Dormant'))
                )
                
                # Count sales from LeadFile using both customer_lead_source and engagement fields
                sales_counts = LeadFile.objects.filter(
                    Q(customer_lead_source=lead_source) | Q(engagement=lead_source)
                ).aggregate(
                    ongoing_count=Count('lead_file_no', filter=Q(lead_file_status_dropped=False)),
                    dropped_count=Count('lead_file_no', filter=Q(lead_file_status_dropped=True)),
                    completed_count=Count('lead_file_no', filter=Q(balance_lcy__lt=10))
                )
                
                # Store old values for comparison
                old_values = {
                    'active_leads': lead_source.active_leads,
                    'dormant_leads': lead_source.dormant_leads,
                    'ongoing_sales': lead_source.ongoing_sales,
                    'dropped_sales': lead_source.dropped_sales,
                    'completed_sales': lead_source.completed_sales,
                }
                
                # Update the lead source with new counts
                lead_source.active_leads = prospects_counts['active_count'] or 0
                lead_source.dormant_leads = prospects_counts['dormant_count'] or 0
                lead_source.ongoing_sales = sales_counts['ongoing_count'] or 0
                lead_source.dropped_sales = sales_counts['dropped_count'] or 0
                lead_source.completed_sales = sales_counts['completed_count'] or 0
                lead_source.last_updated = timezone.now()
                
                lead_source.save()
                updated_count += 1
                
                if options['verbose']:
                    self.stdout.write(
                        f"Updated {lead_source.name}:\n"
                        f"  Active Leads: {old_values['active_leads']} → {lead_source.active_leads}\n"
                        f"  Dormant Leads: {old_values['dormant_leads']} → {lead_source.dormant_leads}\n"
                        f"  Ongoing Sales: {old_values['ongoing_sales']} → {lead_source.ongoing_sales}\n"
                        f"  Dropped Sales: {old_values['dropped_sales']} → {lead_source.dropped_sales}\n"
                        f"  Completed Sales: {old_values['completed_sales']} → {lead_source.completed_sales}"
                    )
                
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'Error updating {lead_source.name}: {str(e)}')
                )
                continue
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Lead source counts update completed. Updated {updated_count}/{total_count} lead sources.'
            )
        )