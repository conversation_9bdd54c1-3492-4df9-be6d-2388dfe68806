import random
import secrets
import string
from django.core.management.base import BaseCommand
from leads.models import LeadSource


class Command(BaseCommand): 
    help = 'Refresh lead_source codes'

    def handle(self, *args, **options):
        generate_lead_source_ref_code()

        self.stdout.write(self.style.SUCCESS('lead_source codes refreshed successfully'))


def generate_lead_source_ref_code(): 
    lead_sources = LeadSource.objects.all()
    for lead_source in lead_sources:
        code = generate_random_string()
        lead_source.ref_code = code 

        import base64
        encoded_string = base64.b64encode(code.encode('utf-8')).decode('utf-8')

        link = f'https://engage360.optiven.co.ke/lead-form/?ls={encoded_string}'
        lead_source.link = link
        lead_source.save()
        print(f'lead_source {lead_source.name} code refreshed to {code} - encoded  {encoded_string}')


def generate_random_string(size=7):
    characters = string.ascii_letters + string.digits  # Uppercase, lowercase, and digits
    generate_code = ''.join(secrets.choice(characters) for _ in range(size))
    return 'LS-' + generate_code