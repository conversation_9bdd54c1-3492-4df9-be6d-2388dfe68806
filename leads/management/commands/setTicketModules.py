from django.core.management.base import BaseCommand
from django.core.exceptions import ObjectDoesNotExist
from ticketing.models import Ticket


class Command(BaseCommand): 
    help = 'Refresh ticket modules'

    def handle(self, *args, **options):
        set_ticket_module()

        self.stdout.write(self.style.SUCCESS('ticket modules refreshed successfully'))


def set_ticket_module(): 
    tickets = Ticket.objects.all()
    for ticket in tickets:
        if ticket.module is None: 
            module = 'Customers'
            try:
                if ticket.customer_id and ticket.customer:
                    module = 'Customers'
                elif ticket.sales_id and ticket.sales:
                    module = 'Sales'
                elif ticket.prospect_id and ticket.prospect:
                    module = 'Prospects'
            except ObjectDoesNotExist:
                # Related object was deleted; skip this ticket
                print(f"⚠️ Skipping ticket {ticket.ticket_id}: related object missing")
                continue
            
            ticket.module = module 
            ticket.save(update_fields=['module'])

        print(f'lead source {ticket.ticket_id} module set to {ticket.module}')
