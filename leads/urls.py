from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from leads import views

lead_router = DefaultRouter(trailing_slash=False)

lead_router.register('lead-source-category', views.LeadSourceCategoryView)
lead_router.register('lead-source-sub-category', views.LeadSourceSubCategoryView)
lead_router.register('lead-source', views.LeadSourceView)
lead_router.register('prospects', views.ProspectsView)
lead_router.register('filter', views.ProspectsFilterView, basename='prospects-filter')
lead_router.register('diaspora-regions', views.DiasporaRegionsView)
lead_router.register('diaspora-trips', views.DiasporaTripsView)
lead_router.register('diaspora-marketer-trips', views.DiasporaTripsMarketersView)
lead_router.register('prospect-view', views.ProspectsView, basename='prospect-view')

urlpatterns = [
    path('leads/', include(lead_router.urls)),
    path('leads/diapora-trip-receipts-report', views.DiasporaTripsReceiptsStaticsView.as_view(), name='diapora-trip-receipts-report'),
    path('leads/generate-lead-form-link', views.GenerateLeadFromLinkView.as_view(), name='generate-lead-form-link'),
    path('leads/add-lead-form-link', views.AddLeadFromLinkView.as_view(), name='add-lead-form-link'),
]

