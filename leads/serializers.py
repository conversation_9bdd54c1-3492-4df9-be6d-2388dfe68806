from rest_framework import serializers
from leads.models import DiasporaTrips, DiasporaTripsMarketers, Engagements, LeadSource, LeadSourceCategory, LeadSourceSubCategory, Prospects, DiasporaRegions
from users.serializers import UserSerializer
from users.models import User




class LeadSourceSubCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for LeadSourceSubCategory
    """
    class Meta:
        model = LeadSourceSubCategory
        fields = '__all__'


class LeadSourceCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for LeadSourceCategory
    """

    class Meta:
        model = LeadSourceCategory
        fields = '__all__'



class LeadSourceCategoryDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer for LeadSourceCategory with subcategories
    """

    class Meta:
        model = LeadSourceCategory
        fields = '__all__'

    def to_representation(self, instance):
        """
        Custom representation to include subcategories
        """
        representation = super().to_representation(instance)
        representation['subcategories'] = LeadSourceSubCategorySerializer(instance.subcategories.all(), many=True).data
        return representation



class LeadSourceSerializer(serializers.ModelSerializer):
    """
    Serializer for LeadSource
    """
    lead_source_category = LeadSourceCategorySerializer(read_only=True)
    lead_source_subcategory = LeadSourceSubCategorySerializer(read_only=True)

    class Meta:
        model = LeadSource
        fields = '__all__'


class ProspectsSerializer(serializers.ModelSerializer):
    """
    Serializer for Prospects
    """

    class Meta:
        model = Prospects
        fields = '__all__'
    
    
    def validate_phone(self, value):
        """
        Validate that phone number is unique (excluding self on update)
        """
        if value:
            qs = Prospects.objects.filter(phone=value)
            if self.instance:
                qs = qs.exclude(pk=self.instance.pk)
            if qs.exists():
                raise serializers.ValidationError("Phone number already exists in the system")
        return value

    
    def to_representation(self, instance):
        """
        Custom representation to include lead source category and subcategory
        """
        representation = super().to_representation(instance)
    
        representation['lead_source'] = instance.lead_source.id if instance.lead_source else None
        lead_subcategory = instance.lead_source_subcategory if instance.lead_source_subcategory else None
        lead_category = lead_subcategory.lead_source_category if lead_subcategory else None
    
        representation['lead_source_category_name'] = lead_category.name if lead_category else ""
        representation['lead_source_subcategory_name'] = lead_subcategory.name if lead_subcategory else ""
        representation['lead_source_name'] = instance.lead_source.name if instance.lead_source else ""
        representation['project_name'] = instance.project.name if instance.project else None
        #representation['marketer'] = instance.marketer.fullnames if instance.marketer else ""
        try:
            representation['marketer'] = instance.marketer.fullnames
        except (AttributeError, User.DoesNotExist):
            representation['marketer'] = ""
        
        try:
            representation['marketer_department'] = instance.marketer.department.dp_name if instance.marketer else ""
        except (AttributeError, User.DoesNotExist):
            representation['marketer_department'] = ""
        
        representation['department_member_name'] = instance.department_member.fullnames if instance.department_member else ""
        representation['department_name'] = instance.department.dp_name if instance.department else None
        # representation['department_member_name'] = instance.department_member.fullnames if instance.department_member else None
    
        return representation
    

class EngagementsSerializer(serializers.ModelSerializer):
    """
    Serializer for Engagements
    """

    class Meta:
        model = Engagements
        fields = '__all__'
    
    def to_representation(self, instance):
        """
        Custom representation to include
        """
        representation = super().to_representation(instance)
        representation['lead_source'] = LeadSourceSerializer(instance.lead_source).data
        representation['prospects'] = ProspectsSerializer(instance.prospects).data
        representation['follow_up_by'] = UserSerializer(instance.follow_up_by).data
        return representation
    

class DiasporaRegionsSerializer(serializers.ModelSerializer):
    """
    Serializer for DiasporaRegions
    """

    class Meta:
        model = DiasporaRegions
        fields = '__all__'


class DiasporaTripsMarketersSerializer(serializers.ModelSerializer):
    """
    Serializer for DiasporaTripsMarketers
    """

    class Meta:
        model = DiasporaTripsMarketers
        fields = '__all__'
    
    def to_representation(self, instance):
        """
        Custom representation to include lead source category and subcategory
        """
        representation = super().to_representation(instance)
        representation['marketer_name'] = instance.marketer.fullnames if instance.marketer is not None else ""
        return representation
        

class DiasporaTripsSerializer(serializers.ModelSerializer):
    """
    Serializer for DiasporaTrips
    """

    class Meta:
        model = DiasporaTrips
        fields = '__all__'
    
    def to_representation(self, instance):
        """
        Custom representation to include lead source category and subcategory
        """
        representation = super().to_representation(instance)
        representation['lead_source_name'] = instance.lead_source.name if instance.lead_source is not None else ""
        representation['manager_name'] = instance.manager.fullnames if instance.manager is not None else ""
        
        marketers =  DiasporaTripsMarketers.objects.filter(DiasporaTrips=instance)
        mList = []
        for mk in marketers:
            mDict = {
                'id': mk.id,
                'employee_no': mk.marketer.employee_no,
                'name': mk.marketer.fullnames
            }
            mList.append(mDict)
        
        representation['marketers'] = mList

        
        return representation
    
