import json
from customers.models import Customer
from engagement.models import Engagement
from inventory.models import DiasporaReceipts
from leads.filtersets import ProspectsFilter
from leads.models import DiasporaRegions, DiasporaTrips, DiasporaTripsMarketers, Engagements, LeadSource, LeadSourceCategory, LeadSourceSubCategory, Prospects
from leads.serializers import DiasporaRegionsSerializer, DiasporaTripsMarketersSerializer, DiasporaTripsSerializer, EngagementsSerializer, LeadSourceCategoryDetailsSerializer, LeadSourceCategorySerializer, LeadSourceSerializer, LeadSourceSubCategorySerializer, ProspectsSerializer
from services.models import CustomSystemLog
from users.models import Departments, User
from django.conf import settings
from rest_framework import viewsets, filters, views
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated,AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.viewsets import ReadOnlyModelViewSet
from drf_yasg.utils import swagger_auto_schema
from django.db import transaction
from config.perm_filters import leads_permission_filters
from rest_framework.exceptions import PermissionDenied
from copy import deepcopy
from inventory.tasks import send_email_task,normalize_emails
from datetime import datetime
from offerletters.models import OfferLetterMain
from sales.models import LeadFile

from django.db.models import Q, Sum
from django.utils import timezone
import os
import pandas as pd
from io import BytesIO
from django.http import HttpResponse
from users.models import User


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


# Create your views here.
class LeadSourceCategoryView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSourceCategory
    """
    queryset = LeadSourceCategory.objects.all()
    serializer_class = LeadSourceCategorySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'description']


    def get_serializer_class(self):
        if self.action == 'retrieve':
            return LeadSourceCategoryDetailsSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSourceCategory")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve LeadSourceCategory")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class LeadSourceSubCategoryView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSourceSubCategory
    """
    queryset = LeadSourceSubCategory.objects.all()
    serializer_class = LeadSourceSubCategorySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'description']


    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSourceSubCategory")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="retrieve LeadSourceSubCategory")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class LeadSourceView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSource
    """
    queryset = LeadSource.objects.all()
    serializer_class = LeadSourceSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description', 'ref_code', 'link', 'sales', 'ongoing_sales', 'dropped_sales', 'completed_sales', 'active_leads', 'dormant_leads']
    search_fields = ['name', 'description', 'ref_code', 'link', 'sales', 'ongoing_sales', 'dropped_sales', 'completed_sales', 'active_leads', 'dormant_leads']
    ordering_fields = ['name', 'description']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSource")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve LeadSource")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class ProspectsView(viewsets.ModelViewSet):
    """
    Viewset for Prospects
    """
    queryset = Prospects.objects.none() 
    serializer_class = ProspectsSerializer
    filterset_class = ProspectsFilter

    # Custom permission: Check for 'X-API-KEY' in headers for POST requests
    def get_permissions(self):
        if self.request.method == 'POST' and not self.request.user.is_authenticated:
            api_key = self.request.headers.get('X-API-KEY')
            expected_key = os.environ.get('PROSPECTS_API_KEY', None)
            if not api_key or api_key != expected_key:
                raise PermissionDenied("Invalid or missing API key.")
            service_user = User.objects.filter(employee_no="OL/HR/365").first()
            if service_user:
                self.request.user = service_user
            return [AllowAny()]
        if settings.DEBUG:
            return [AllowAny()]
        return [IsAuthenticated()]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'phone', 'alternate_phone', 'city', 'country', 'comment', 'date', 'status', 'category', 'no_of_sales', 'marketer__fullnames','department__dp_name']
    ordering_fields = ['name', 'description']
    
    
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = Prospects.objects.all().order_by('-date')
        queryset = leads_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these prospects records or you have more than 1 right .")
        
        # route_name = self.request.resolver_match.url_name if self.request.resolver_match else None
        # if route_name == 'ongoing-sales-list':
        #    queryset = queryset.filter(lead_file_status_dropped=False)
        # if route_name == 'completed-sales-list':
        #     queryset = queryset.filter(balance_lcy__lte=0, lead_file_status_dropped=False)
        
        return queryset

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order Prospects")
    def list(self, request, *args, **kwargs):
        try:
            return super().list(request, *args, **kwargs)
        except Exception as e:
            import logging
            import traceback
            logger = logging.getLogger(__name__)
            
            # Get detailed error context
            error_context = {
                'user': request.user.fullnames if hasattr(request.user, 'fullnames') else str(request.user),
                'query_params': dict(request.query_params),
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc()
            }
            
            # Try to identify problematic records
            try:
                queryset = self.get_queryset()
                total_count = queryset.count()
                error_context['total_prospects'] = total_count
                
                # Check for prospects with missing or invalid lead sources
                prospects_with_issues = []
                for prospect in queryset:  # Check first 10 to avoid timeout
                    issues = []
                    
                    if not prospect.lead_source:
                        issues.append("Missing lead_source")
                    elif not hasattr(prospect.lead_source, 'name'):
                        issues.append("Invalid lead_source object")
                    
                    if not prospect.lead_source_category:
                        issues.append("Missing lead_source_category")
                    
                    if not prospect.lead_source_subcategory:
                        issues.append("Missing lead_source_subcategory")
                    
                    if not prospect.marketer:
                        issues.append("Missing marketer")
                    elif not hasattr(prospect.marketer, 'fullnames'):
                        issues.append("Invalid marketer object")
                    
                    if issues:
                        prospects_with_issues.append({
                            'prospect_id': prospect.id,
                            'prospect_name': prospect.name,
                            'issues': issues
                        })
                
                if prospects_with_issues:
                    error_context['problematic_prospects'] = prospects_with_issues
                
            except Exception as inner_e:
                error_context['queryset_error'] = str(inner_e)
            
            logger.error(f"ProspectsView list error: {error_context}", exc_info=True)
            
            return Response({
                'success': False,
                'error': str(e),
                'message': 'Failed to retrieve prospects',
                'error_type': type(e).__name__,
                'debug_info': error_context if settings.DEBUG else None
            }, status=500)
        
    @swagger_auto_schema(tags=['Leads'], operation_description="retrieve Prospects")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Leads'], operation_description="Create Prospects")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        user = request.user
        department = data.get('department')
        lead_source_id = data.get('lead_source')

        try:
            with transaction.atomic():
                # Check if phone number already exists
                if data.get('phone'):
                    existingPropspect = Prospects.objects.filter(phone=data['phone']).first()
                    if existingPropspect is not None:
                        #create an engagement for the existing prospect
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f" Follow-up on existing prospect",
                            description=f"{user.fullnames} was Following up on this prospect",
                            created_by=user,
                            prospect=existingPropspect
                        )
                        mk='Unknown'
                        if existingPropspect.marketer:
                            mk=f' Marketer {existingPropspect.marketer.fullnames}'
                        if existingPropspect.department:
                            mk = f' Department {existingPropspect.department.dp_name}'

                        return Response(
                            dict(success=False, message=f'Phone number already exists as {existingPropspect.lead_type} lead  ({existingPropspect.name}) under {mk}'), 
                            status=400
                        )
                    existingCustomer = Customer.objects.filter(phone=data['phone']).first()
                    if existingCustomer is not None:
                        #create an engagement for the existing customer
                        Engagement.objects.create(
                            client_type='Customer',
                            engagement_type='Contact-add',
                            subject=f"Follow-up on existing customer",
                            description=f"{user.fullnames} was Following up on this customer",
                            created_by=user,
                            customer=existingCustomer
                        )
                        return Response(
                            dict(success=False, message=f'Phone number already exists as customer ({existingCustomer.customer_name}) under marketer {existingCustomer.marketer.fullnames}'), 
                            status=400
                        )
                

                if department == 'CONVERSION' or department == 'Marketing':
                    data['department'] = None
                    data['department_member'] = None
                    data['lead_type'] = 'personal'
                    data['marketer'] = user.employee_no
                else:
                    department_instance = Departments.objects.filter(dp_name = data.get('department')).first()
                    if not department_instance:
                        return Response(dict(success=False, message='No department found'), status=400)                    

                    data['department'] = department_instance.dp_id
                    data['department_member'] = user.employee_no
                    data['lead_type'] = 'allocated'
                    data['marketer'] = data.get('marketer')
                
                print (f"Lead source id: {lead_source_id}")
                lead_source = LeadSource.objects.filter(id=lead_source_id).first()
                if not lead_source:
                    return Response(dict(success=False, message='No such Lead source found'), status=400)  
                
                phone= data.get('phone')
                if phone:
                    # Remove spaces, dashes, and leading plus
                    phone = phone.replace(" ", "").replace("-", "")
                    # Ensure phone starts with '+'
                    if not phone.startswith("+"):
                        # If phone starts with '0', replace with country code (assume Kenya +254)
                        if phone.startswith("0"):
                            phone = "+254" + phone[1:]
                        # If phone starts with country code without '+', add '+'
                        elif phone[:3] == "254":
                            phone = "+" + phone
                        else:
                            # Default: add '+' at the start
                            phone = "+" + phone
                    # Validate length (international numbers are usually 10-15 digits after '+')
                    digits_only = phone[1:]
                    if not digits_only.isdigit():
                        return Response(dict(success=False, message='Phone number must contain only digits after +'), status=400)
                    if len(digits_only) < 10:
                        return Response(dict(success=False, message='Phone number is too short'), status=400)
                    if len(digits_only) > 15:
                        return Response(dict(success=False, message='Phone number is too long'), status=400)
                    data['phone'] = phone

                data['lead_source'] = lead_source.leadsource_id
                data['lead_source_category'] = lead_source.lead_source_subcategory.lead_source_category.id   
                data['lead_source_subcategory'] = lead_source.lead_source_subcategory.id    

                serializer = self.serializer_class(data=data)
                serializer.is_valid(raise_exception=True)    
                lead = serializer.save()


                # send email to marketer
                if lead.marketer:
                    marketing_allocation_context = {
                        'marketer': lead.marketer.fullnames,
                        'marketer_department': lead.marketer.department.dp_name,
                        'prospect_name': lead.name,
                        'prospect_phone': lead.phone,
                        'prospect_alt_number': lead.alternate_phone if lead.alternate_phone else 'N/A',
                        'prospect_email': lead.email if lead.email else 'N/A',
                        'prospect_category': lead.category if lead.category else 'N/A',
                        'prospect_city': lead.city if lead.city else 'N/A',
                        'prospect_country': lead.country if lead.country else 'N/A',
                        'prospect_comment': lead.comment if lead.comment else 'N/A',
                        'department': lead.department.dp_name if lead.department else 'CONVERSION',
                        'allocator': lead.department_member.fullnames if lead.department_member else request.user.fullnames,
                        'year': str(timezone.now().year)
                    }
                    marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                    recipients_list = [lead.marketer.email] 
                    send_email_task.delay(
                        subject="Prospect Allocation",
                        recipients=normalize_emails([email for email in recipients_list if email]),
                        template_name=marketing_allocation_template,
                        context=deepcopy(marketing_allocation_context),
                        )
                    

                return Response(dict(success=True, message='Lead saved successfully', data=serializer.data), status=201)  
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)

    @swagger_auto_schema(tags=['Leads'], operation_description="partial_update Prospects")
    def partial_update(self, request, *args, **kwargs):

       
        try:
            with transaction.atomic(): 

                #check old marketer
                data = request.data.copy()
                marketer= data.get('marketer')
                # Get the current instance before saving
                instance = self.get_object()

                if marketer:
                    # old_marketer = instance.marketer
                    # if old_marketer != marketer:
                    #     #notify old marketer
                    #     Engagement.objects.create(
                    #         client_type='Prospect',
                    #         engagement_type='Contact-add',
                    #         subject=f"Prospect {instance.name} has been reassigned",
                    #         description=f"{request.user.fullnames} has reassigned this prospect to you",
                    #         created_by=request.user,
                    #         prospect=instance
                    #     )
                        #notify new marketer

                    new_marketer = User.objects.filter(employee_no=marketer).first()
                    if new_marketer:
                        
                        # assign allocator on leads 
                        if instance.lead_source.name == 'AMANI CHATBOT':
                            data['department_member'] = request.user.employee_no
                            data['department'] = request.user.department.dp_id
                            data['lead_type'] = 'allocated'

                        # add engagement 
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f"Prospect {instance.name} has been assigned to you",
                            description=f"{request.user.fullnames} has assigned this prospect to you",
                            created_by=request.user,
                            prospect=instance
                        )


                        # Add Log
                        CustomSystemLog.objects.create(
                            system_level='User',
                            user=request.user,
                            action='Edit',
                            module='Leads',
                            message=f'Lead/Prospect {instance.name} ({instance.phone}) from lead source {instance.lead_source.name} was assigned to {new_marketer.fullnames} by {request.user.fullnames}'
                        )

                        # send email to new marketer

                        marketing_allocation_context = {
                            'marketer': new_marketer.fullnames,
                            'marketer_department': new_marketer.department.dp_name,
                            'prospect_name': instance.name,
                            'prospect_phone': instance.phone,
                            'prospect_alt_number': instance.alternate_phone if instance.alternate_phone else 'N/A',
                            'prospect_email': instance.email if instance.email else 'N/A',
                            'prospect_category': instance.category if instance.category else 'N/A',
                            'prospect_city': instance.city if instance.city else 'N/A',
                            'prospect_country': instance.country if instance.country else 'N/A',
                            'prospect_comment': instance.comment if instance.comment else 'N/A',
                            'department': instance.department.dp_name if instance.department else 'CONVERSION',
                            'allocator': instance.department_member.fullnames if instance.department_member else request.user.fullnames,
                            'year': str(timezone.now().year)
                        }
                        marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                        recipients_list = [new_marketer.email] 
                        send_email_task.delay(
                            subject="Prospect Allocation",
                            recipients=normalize_emails([email for email in recipients_list if email]),
                            template_name=marketing_allocation_template,
                            context=deepcopy(marketing_allocation_context),
                            )
                        
                serializer = ProspectsSerializer(instance, data=data, partial=True)
                serializer.is_valid(raise_exception=True)    
                serializer.save()                
                
                return Response(dict(message=f'Updated successfully', data=serializer.data, success=True), 500)
            
        except Exception as e:
            return Response(dict(message=f'Error: {e}', error=f'{e}', success=False), 500)
        

    @swagger_auto_schema(tags=['Leads'], operation_description="partial_update Prospects")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

# class EngagementsView(viewsets.ModelViewSet):
#     """
#     Viewset for Engagements
#     """
#     queryset = Engagements.objects.all()
#     serializer_class = EngagementsSerializer
#     if settings.DEBUG:
#         permission_classes = [AllowAny]
#     else:
#         permission_classes = [IsAuthenticated]
#     filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
#     filterset_fields = ['engagement_type', 'engagement_date', 'engagement_time', 'engagement_duration', 'engagement_notes', 'follow_up_date', 'follow_up_time', 'follow_up_notes']
#     search_fields = ['engagement_type', 'engagement_date', 'engagement_time', 'engagement_duration', 'engagement_notes', 'follow_up_date', 'follow_up_time', 'follow_up_notes']
#     ordering_fields = ['engagement_date', 'engagement_type']

#     @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order Engagements")
#     def list(self, request, *args, **kwargs):
#         return super().list(request, *args, **kwargs)
    
#     @swagger_auto_schema(tags=['Leads'], operation_description="Create Engagements")
#     def create(self, request, *args, **kwargs):
#         return super().create(request, *args, **kwargs)
    
#     @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve Engagements")
#     def retrieve(self, request, *args, **kwargs):
#         return super().retrieve(request, *args, **kwargs)
    
    
class DiasporaRegionsView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaRegions
    """
    queryset = DiasporaRegions.objects.all()
    serializer_class = DiasporaRegionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description', 'manager_name']
    search_fields = ['name', 'description', 'manager_name']
    ordering_fields = ['name', 'description', 'manager_name']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaRegions")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaRegions")
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaRegions")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaRegions")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaRegions")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

class DiasporaTripsView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaTrips
    """
    queryset = DiasporaTrips.objects.all()
    serializer_class = DiasporaTripsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']
    search_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']
    ordering_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaTrips")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaTrips")
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaTrips")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaTrips")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaTrips")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

class DiasporaTripsMarketersView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaTripsMarketers
    """
    queryset = DiasporaTripsMarketers.objects.all()
    serializer_class = DiasporaTripsMarketersSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer','DiasporaTrips']
    search_fields = ['marketer','DiasporaTrips', 'marketer__employee_no', 'DiasporaTrips__trip_name', 'marketer__fullnames']
    ordering_fields = ['marketer','DiasporaTrips']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaTripsMarketers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaTripsMarketers")
    def create(self, request, *args, **kwargs):

        data = request.data.copy()
        diaspora_trip_id = data.get('trip')
        diaspora_trip = DiasporaTrips.objects.filter(id=diaspora_trip_id).first()

        if not diaspora_trip:
            return Response(dict(success=False, message='No such Diaspora trip found'), status=400)
        
        marketers = data.get('members')
        if marketers:
            if isinstance(marketers, list):
                for marketer in marketers:
                    userMarketer = User.objects.filter(employee_no=marketer).first()
                    if not userMarketer:
                        return Response(
                            dict(success=False, message=f'Marketer with employee_no {marketer} does not exist'), 
                            status=400
                        )
                    diaspora_trip_marketer, create = DiasporaTripsMarketers.objects.get_or_create(DiasporaTrips=diaspora_trip, marketer=userMarketer)
                    

            else:
                userMarketer = User.objects.filter(employee_no=marketer).first()
                if not userMarketer:
                    return Response(
                        dict(success=False, message=f'Marketer with employee_no {marketer} does not exist'), 
                        status=400
                    )
                diaspora_trip_marketer, create = DiasporaTripsMarketers.objects.get_or_create(DiasporaTrips=diaspora_trip, marketer=userMarketer)
                return Response(
                    dict(success=True, message=f'Added successfully', data=self.serializer_class(diaspora_trip_marketer).data), 
                    status=201
                )
        return Response(
            dict(success=True, message=f'Added successfully', data=self.serializer_class(diaspora_trip_marketer).data), 
            status=201
        )

    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaTripsMarketers")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaTripsMarketers")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaTripsMarketers")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class DiasporaTripsReceiptsStaticsView(views.APIView):
    
    """
    View to retrieve reciepts for a specific trip.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Diaspora Trips Reports'],
        operation_summary="Get receipts for a specific trip and statistics",
        responses={200: 'Receipts retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        params = request.query_params
        trip_id = params.get('trip_id')
        export_type = params.get('export', 'json')  # default to JSON

        try:

            try:
                trip_id = int(params.get('trip_id', 0))
            except (ValueError, TypeError):
                return Response(dict(success=False, message='Invalid trip_id'), status=400)

            trip = DiasporaTrips.objects.filter(id=trip_id).first()
            if not trip:
                return Response(dict(success=False, message='No such trip found'), status=400)
            
            receipts = DiasporaReceipts.objects.filter(trip_id=trip_id)
            
            
            title = f'{trip.trip_name} '

            thead = [
                "Date",
                "Client's Name",
                "Marketer",
                "Plot Number",
                "Amount Receipted",
                "Cash Price",
                "Purchase price",
                "Total Paid",
                "BALANCE LCY",
                "Agreement Sent",
                "Agreement Signed",
                "Account",
                "Region"
            ]

            tbody = []
            for receipt in receipts:
                cashprice = 0
                purchaseprice = 0
                totalpaid = 0
                balance_lcy = 0
                agreement_sent = "NO"
                agreement_signed = "NO"
                marketer= User.objects.filter(employee_no=receipt.marketer).first()
                offerletter = OfferLetterMain.objects.filter(booking_id=receipt.booking_id).first()
                if offerletter:
                    if offerletter.lead_file is not None:
                        leadfile = LeadFile.objects.filter(lead_file_no=offerletter.lead_file).first()
                        if leadfile:
                            cashprice = leadfile.selling_price
                            purchaseprice = leadfile.purchase_price
                            totalpaid = leadfile.total_paid
                            balance_lcy = leadfile.balance_lcy
                            agreement_sent = leadfile.sale_agreement_sent
                            agreement_signed = leadfile.sale_agreement_signed
                            if leadfile.sale_agreement_sent == True:
                                agreement_sent = "YES"
                            if leadfile.sale_agreement_signed == True:
                                agreement_signed = "YES"
                        

                
                
                tbody.append({
                    "date": receipt.created_at.strftime('%Y-%m-%d %H:%M:%S') if receipt.created_at else None,
                    "client_name": receipt.client_name,
                    "marketer": marketer.fullnames if marketer else "Unknown",
                    "plot_number": receipt.plot_no,
                    "amount_receipted": "{:,.2f}".format(receipt.amount) if receipt.amount else 0,
                    "cash_price": "{:,.2f}".format(cashprice) if cashprice else 0,
                    "purchase_price": "{:,.2f}".format(purchaseprice) if purchaseprice else 0,
                    "total_paid": "{:,.2f}".format(totalpaid) if totalpaid else 0,
                    "balance_lcy": "{:,.2f}".format(balance_lcy) if balance_lcy else 0,
                    "agreement_sent": "YES" if agreement_sent == "YES" else "NO",
                    "agreement_signed": "YES" if agreement_signed == "YES" else "NO",
                    "account": receipt.acc_no,
                    "region": receipt.region
                })
                
            tfooter = {
                "date": "",
                "total_clients": receipts.count(),
                "total_marketers": User.objects.filter(employee_no__in=receipts.values_list('marketer', flat=True)).count(),
                "plot_number": "",
                "total_amount_receipted": "{:,.2f}".format(receipts.aggregate(Sum('amount'))['amount__sum'] or 0),
                "total_cash_price": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('selling_price'))['selling_price__sum'] or 0),
                "total_purchase_price": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('purchase_price'))['purchase_price__sum'] or 0),
                "total_paid": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('total_paid'))['total_paid__sum'] or 0),
                "total_balance_lcy": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('balance_lcy'))['balance_lcy__sum'] or 0),
                "agreement_sent": "",
                "agreement_signed": "",
                "account": "",
                "region": "",
}

            

            if export_type == 'excel':
                df = pd.DataFrame(tbody)
                excel_file = BytesIO()
                writer = pd.ExcelWriter(excel_file, engine='xlsxwriter')
                df.to_excel(writer, sheet_name='Sheet1', index=False, startrow=2)

                workbook = writer.book
                worksheet = writer.sheets['Sheet1']

                # Title
                title_format = workbook.add_format({'bold': True, 'align': 'center', 'font_size': 14})
                worksheet.merge_range(0, 0, 0, len(thead)-1, title, title_format)

                # Header
                # header_format = workbook.add_format({'bold': True, 'align': 'center', 'border': 1})
                # for col_num, header in enumerate(thead):
                #     worksheet.write(1, col_num, header, header_format)

                # Footer
                footer_format = workbook.add_format({'bold': True, 'border': 1})
                footer_row = len(tbody) + 2
                for col_num, (key, value) in enumerate(tfooter.items()):
                    worksheet.write(footer_row, col_num, value, footer_format)

                # Column width
                for i, col in enumerate(df.columns):
                    max_len = max(df[col].astype(str).apply(len).max(), len(thead[i]))
                    worksheet.set_column(i, i, max_len + 2)

                writer.close()
                excel_file.seek(0)

                response = HttpResponse(
                    excel_file.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="{trip.trip_name}_receipts.xlsx"'
                return response

            # Else return JSON
            return Response({
                'success': True,
                'message': 'Receipts retrieved successfully',
                'data': {
                    'title': title,
                    'thead': thead,
                    'tbody': tbody,
                    'tfooter': tfooter,
                }
            }, status=200)

        except Exception as e:
            return Response(dict(success=False, message=str(e)), status=500)
    

class GenerateLeadFromLinkView(views.APIView):
    permission_classes = [AllowAny]
    http_method_names = ['post']
    
    @swagger_auto_schema(
        tags=['Leads'],
        operation_summary="Generate link for diaspora marketer",
        responses={200: 'Lead lead Link generated successfully.'}
    )
    def post(self, request): 
        data = request.data.copy()
        marketer_no = data.get('marketer')
        lead_source_ref = data.get('ref_code')

        try:
            with transaction.atomic():
                
                lead_source = LeadSource.objects.filter(ref_code=lead_source_ref).first()
                if not lead_source: 
                    return Response("No such leadsource was found!!")
                
                marketer = User.objects.get(employee_no=marketer_no, status='Active')
                if not marketer: 
                    return Response("No such sales manager is found!!")
                
                from urllib.parse import urlparse, parse_qs

                lead_source_link = lead_source.link
                query = urlparse(lead_source_link).query
                params = parse_qs(query) 
                encoded_code = params.get("ls", [None])[0]  # get first "ls" value

                if not encoded_code:
                    return Response(dict(message="Invalid lead source link!", success=False), status=400)

                import base64        
                ref_code = base64.b64decode(encoded_code).decode("utf-8")

                __params = {
                    "leadSourceName":lead_source.name,
                    "marketerCode": marketer.mkcode,
                    "refCode": ref_code,
                    "marketerName":marketer.fullnames
                }

                # Convert dict to JSON string 
                json_string = json.dumps(__params)

                encoded_string = base64.b64encode(json_string.encode('utf-8')).decode('utf-8')

                new_link = lead_source_link.split('=')[0] + '=' + encoded_string

                email_context = {
                    'lead_source_name':lead_source.name,
                    'lead_link':new_link,
                    'mkcode':marketer.mkcode,
                    'marketer': marketer.fullnames,
                    'year': str(timezone.now().year),
                }

                email_template = 'leads/lead_form_link.html'
                send_email_task.delay(
                    subject=f"Diaspora Lead Form Link",
                    recipients=normalize_emails([marketer.email]),
                    template_name=email_template,
                    context=deepcopy(email_context),
                    )
                
                return Response(dict(message='Link generated and sent to marketer successfully', success=True), status=200)                
                 
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
                
  

class AddLeadFromLinkView(views.APIView):
    permission_classes = [AllowAny]
    http_method_names = ['post']
    
    @swagger_auto_schema(
        tags=['Leads'],
        operation_summary="Post Lead from Link",
        responses={200: 'Lead added successfully.'}
    )
    def post(self, request): 
        data = request.data.copy()
        marketer_code = data.get('marketer_code')
        lead_source_ref_code = data.get('lead_source_ref_code')


        try:
            with transaction.atomic():

                user = User.objects.get(mkcode=marketer_code, status='Active')
                if not user: 
                    return Response("No such sales manager is found!!")
                
                # Check if phone number already exists
                if data.get('phone'):
                    existingPropspect = Prospects.objects.filter(phone=data['phone']).first()
                    if existingPropspect is not None:
                        #create an engagement for the existing prospect
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f" Follow-up on existing prospect",
                            description=f"{user.fullnames} was Following up on this prospect",
                            created_by=user,
                            prospect=existingPropspect
                        )
                        mk='Unknown'
                        if existingPropspect.marketer:
                            mk=f' Marketer {existingPropspect.marketer.fullnames}'
                        if existingPropspect.department:
                            mk = f' Department {existingPropspect.department.dp_name}'

                        return Response(
                            dict(success=False, message=f'You already exists as lead  ({existingPropspect.name}) under {mk}'), 
                            status=400
                        )
                    existingCustomer = Customer.objects.filter(phone=data['phone']).first()
                    if existingCustomer is not None:
                        #create an engagement for the existing customer
                        Engagement.objects.create(
                            client_type='Customer',
                            engagement_type='Contact-add',
                            subject=f"Follow-up on existing customer",
                            description=f"{user.fullnames} was Following up on this customer",
                            created_by=user,
                            customer=existingCustomer
                        )
                        return Response(
                            dict(success=False, message=f'You already exists as customer ({existingCustomer.customer_name}) under sales manager {existingCustomer.marketer.fullnames}'), 
                            status=400
                        )
                

                data['department'] = None
                data['department_member'] = None
                data['lead_type'] = 'personal'
                data['marketer'] = user.employee_no
                
                lead_source = LeadSource.objects.filter(ref_code=lead_source_ref_code).first()
                if not lead_source:
                    return Response(dict(success=False, message='No such Lead source found'), status=400)  
                
                phone= data.get('phone')
                if phone:
                    # Remove spaces, dashes, and leading plus
                    phone = phone.replace(" ", "").replace("-", "")
                    # Ensure phone starts with '+'
                    if not phone.startswith("+"):
                        # If phone starts with '0', replace with country code (assume Kenya +254)
                        if phone.startswith("0"):
                            phone = "+254" + phone[1:]
                        # If phone starts with country code without '+', add '+'
                        elif phone[:3] == "254":
                            phone = "+" + phone
                        else:
                            # Default: add '+' at the start
                            phone = "+" + phone
                    # Validate length (international numbers are usually 10-15 digits after '+')
                    digits_only = phone[1:]
                    if not digits_only.isdigit():
                        return Response(dict(success=False, message='Phone number must contain only digits after +'), status=400)
                    if len(digits_only) < 10:
                        return Response(dict(success=False, message='Phone number is too short'), status=400)
                    if len(digits_only) > 15:
                        return Response(dict(success=False, message='Phone number is too long'), status=400)
                    data['phone'] = phone

                data['lead_source'] = lead_source.leadsource_id
                data['lead_source_category'] = lead_source.lead_source_subcategory.lead_source_category.id   
                data['lead_source_subcategory'] = lead_source.lead_source_subcategory.id    

                serializer = ProspectsSerializer(data=data)
                serializer.is_valid(raise_exception=True)    
                lead = serializer.save()


                # send email to marketer

                marketing_allocation_context = {
                    'marketer': lead.marketer.fullnames,
                    'marketer_department': lead.marketer.department.dp_name,
                    'prospect_name': lead.name,
                    'prospect_phone': lead.phone,
                    'prospect_alt_number': lead.alternate_phone if lead.alternate_phone else 'N/A',
                    'prospect_email': lead.email if lead.email else 'N/A',
                    'prospect_category': lead.category if lead.category else 'N/A',
                    'prospect_city': lead.city if lead.city else 'N/A',
                    'prospect_country': lead.country if lead.country else 'N/A',
                    'prospect_comment': lead.comment if lead.comment else 'N/A',
                    'department': lead.department.dp_name if lead.department else 'CONVERSION',
                    'allocator': lead.department_member.fullnames if lead.department_member else request.user.fullnames,
                    'year': str(timezone.now().year)
                }
                marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                recipients_list = [lead.marketer.email] 
                send_email_task.delay(
                    subject="Prospect Allocation",
                    recipients=normalize_emails([email for email in recipients_list if email]),
                    template_name=marketing_allocation_template,
                    context=deepcopy(marketing_allocation_context),
                    )
                

                return Response(dict(success=True, message='Lead saved successfully', data=serializer.data), status=201)  
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
                
