import json
from customers.models import Customer
from engagement.models import Engagement
from inventory.models import DiasporaReceipts
from leads.filtersets import ProspectsFilter
from leads.models import DiasporaRegions, DiasporaTrips, DiasporaTripsMarketers, Engagements, LeadSource, LeadSourceCategory, LeadSourceSubCategory, Prospects
from leads.serializers import DiasporaRegionsSerializer, DiasporaTripsMarketersSerializer, DiasporaTripsSerializer, EngagementsSerializer, LeadSourceCategoryDetailsSerializer, LeadSourceCategorySerializer, LeadSourceSerializer, LeadSourceSubCategorySerializer, ProspectsSerializer
from services.models import CustomSystemLog
from users.models import Departments, User
from django.conf import settings
from rest_framework import viewsets, filters, views
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated,AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.viewsets import ReadOnlyModelViewSet
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.db import transaction
from config.perm_filters import leads_permission_filters
from rest_framework.exceptions import PermissionDenied
from copy import deepcopy
from inventory.tasks import send_email_task,normalize_emails
from datetime import datetime
from offerletters.models import OfferLetterMain
from sales.models import LeadFile
from utils.export_mixins import ReportExportMixin, AdvancedReportExportMixin

from django.db.models import Q, Sum
from django.utils import timezone
import os
import pandas as pd
from io import BytesIO
from django.http import HttpResponse
from users.models import User


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


# Create your views here.
class LeadSourceCategoryView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSourceCategory
    """
    queryset = LeadSourceCategory.objects.all()
    serializer_class = LeadSourceCategorySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'description']


    def get_serializer_class(self):
        if self.action == 'retrieve':
            return LeadSourceCategoryDetailsSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSourceCategory")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve LeadSourceCategory")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class LeadSourceSubCategoryView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSourceSubCategory
    """
    queryset = LeadSourceSubCategory.objects.all()
    serializer_class = LeadSourceSubCategorySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description', 'lead_source_category']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'description']


    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSourceSubCategory")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaRegions")
    def create(self, request, *args, **kwargs):
        try: 
            with transaction.atomic():
                data = request.data.copy() 
                manager = data.get('manager') 
                if not manager: 
                    return Response(dict(success=False, message='Manager is required'), status=400)
                
                user = User.objects.filter(employee_no=manager, status='Active').first()
                if not user: 
                    return Response(dict(success=False, message='User not found'), status=400)

                data['manager_name'] = user.fullnames if user.fullnames else f'{user.first_name} {user.last_name}'

                last_record = self.queryset.last()
                data['cat_lead_source_id'] = last_record.cat_lead_source_id + 1 if last_record else 1

                serializer = self.serializer_class(data=data)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(dict(success=True, message='Created successfully', data=serializer.data), status=201)
                
                
        except Exception as e: 
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="retrieve LeadSourceSubCategory")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="update LeadSourceSubCategory")
    def partial_update(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                instance = self.get_object()
                data = request.data.copy()
                
                # Only validate and update manager if it's provided in the request
                if 'manager' in data:
                    manager = data.get('manager')
                    if not manager:
                        return Response(dict(success=False, message='Manager cannot be empty'), status=400)
                    
                    user = User.objects.filter(employee_no=manager, status='Active').first()
                    if not user:
                        return Response(dict(success=False, message='User not found'), status=400)
                    
                    data['manager_name'] = user.fullnames if user.fullnames else f'{user.first_name} {user.last_name}'
                
                serializer = self.serializer_class(instance, data=data, partial=True)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(dict(success=True, message='Updated successfully', data=serializer.data), status=200)
                
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
        

class LeadSourceView(ReadOnlyModelViewSet):
    """
    Viewset for LeadSource
    """
    queryset = LeadSource.objects.all()
    serializer_class = LeadSourceSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description', 'ref_code', 'link', 'sales', 'ongoing_sales', 'dropped_sales', 'completed_sales', 'active_leads', 'dormant_leads', 'lead_source_subcategory__id', 'lead_source_subcategory__cat_lead_source_id', 'lead_source_subcategory__lead_source_category__id']
    search_fields = ['name', 'description', 'ref_code', 'link', 'sales', 'ongoing_sales', 'dropped_sales', 'completed_sales', 'active_leads', 'dormant_leads']
    ordering_fields = ['name', 'description']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order LeadSource")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve LeadSource")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class ProspectsView(viewsets.ModelViewSet):
    """
    Viewset for Prospects
    """
    queryset = Prospects.objects.none() 
    serializer_class = ProspectsSerializer
    filterset_class = ProspectsFilter

    # Custom permission: Check for 'X-API-KEY' in headers for POST requests
    def get_permissions(self):
        if self.request.method == 'POST' and not self.request.user.is_authenticated:
            api_key = self.request.headers.get('X-API-KEY')
            expected_key = os.environ.get('PROSPECTS_API_KEY', None)
            if not api_key or api_key != expected_key:
                raise PermissionDenied("Invalid or missing API key.")
            service_user = User.objects.filter(employee_no="OL/HR/365").first()
            if service_user:
                self.request.user = service_user
            return [AllowAny()]
        if settings.DEBUG:
            return [AllowAny()]
        return [IsAuthenticated()]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'phone', 'alternate_phone', 'city', 'country', 'comment', 'date', 'status', 'category', 'no_of_sales', 'marketer__fullnames','department__dp_name']
    ordering_fields = ['name', 'description']
    
    
    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = Prospects.objects.all().order_by('-date')
        queryset = leads_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have permission to view these prospects records or you have more than 1 right .")
        
        # route_name = self.request.resolver_match.url_name if self.request.resolver_match else None
        # if route_name == 'ongoing-sales-list':
        #    queryset = queryset.filter(lead_file_status_dropped=False)
        # if route_name == 'completed-sales-list':
        #     queryset = queryset.filter(balance_lcy__lte=0, lead_file_status_dropped=False)
        
        return queryset

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order Prospects")
    def list(self, request, *args, **kwargs):
        try:
            return super().list(request, *args, **kwargs)
        except Exception as e:
            import logging
            import traceback
            logger = logging.getLogger(__name__)
            
            # Get detailed error context
            error_context = {
                'user': request.user.fullnames if hasattr(request.user, 'fullnames') else str(request.user),
                'query_params': dict(request.query_params),
                'error_type': type(e).__name__,
                'error_message': str(e),
                'traceback': traceback.format_exc()
            }
            
            # Try to identify problematic records
            try:
                queryset = self.get_queryset()
                total_count = queryset.count()
                error_context['total_prospects'] = total_count
                
                # Check for prospects with missing or invalid lead sources
                prospects_with_issues = []
                for prospect in queryset:  # Check first 10 to avoid timeout
                    issues = []
                    
                    if not prospect.lead_source:
                        issues.append("Missing lead_source")
                    elif not hasattr(prospect.lead_source, 'name'):
                        issues.append("Invalid lead_source object")
                    
                    if not prospect.lead_source_category:
                        issues.append("Missing lead_source_category")
                    
                    if not prospect.lead_source_subcategory:
                        issues.append("Missing lead_source_subcategory")
                    
                    if not prospect.marketer:
                        issues.append("Missing marketer")
                    elif not hasattr(prospect.marketer, 'fullnames'):
                        issues.append("Invalid marketer object")
                    
                    if issues:
                        prospects_with_issues.append({
                            'prospect_id': prospect.id,
                            'prospect_name': prospect.name,
                            'issues': issues
                        })
                
                if prospects_with_issues:
                    error_context['problematic_prospects'] = prospects_with_issues
                
            except Exception as inner_e:
                error_context['queryset_error'] = str(inner_e)
            
            logger.error(f"ProspectsView list error: {error_context}", exc_info=True)
            
            return Response({
                'success': False,
                'error': str(e),
                'message': 'Failed to retrieve prospects',
                'error_type': type(e).__name__,
                'debug_info': error_context if settings.DEBUG else None
            }, status=500)
        
    @swagger_auto_schema(tags=['Leads'], operation_description="retrieve Prospects")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Leads'], operation_description="Create Prospects")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        user = request.user
        department = data.get('department')
        lead_source_id = data.get('lead_source')

        try:
            with transaction.atomic():
                # Check if phone number already exists
                if data.get('phone'):
                    existingPropspect = Prospects.objects.filter(phone=data['phone']).first()
                    if existingPropspect is not None:
                        #create an engagement for the existing prospect
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f" Follow-up on existing prospect",
                            description=f"{user.fullnames} was Following up on this prospect",
                            created_by=user,
                            prospect=existingPropspect
                        )
                        mk='Unknown'
                        if existingPropspect.marketer:
                            mk=f' Marketer {existingPropspect.marketer.fullnames}'
                        if existingPropspect.department:
                            mk = f' Department {existingPropspect.department.dp_name}'

                        return Response(
                            dict(success=False, message=f'Phone number already exists as {existingPropspect.lead_type} lead  ({existingPropspect.name}) under {mk}'), 
                            status=400
                        )
                    existingCustomer = Customer.objects.filter(phone=data['phone']).first()
                    if existingCustomer is not None:
                        #create an engagement for the existing customer
                        Engagement.objects.create(
                            client_type='Customer',
                            engagement_type='Contact-add',
                            subject=f"Follow-up on existing customer",
                            description=f"{user.fullnames} was Following up on this customer",
                            created_by=user,
                            customer=existingCustomer
                        )
                        return Response(
                            dict(success=False, message=f'Phone number already exists as customer ({existingCustomer.customer_name}) under marketer {existingCustomer.marketer.fullnames}'), 
                            status=400
                        )
                

                if department == 'CONVERSION' or department == 'Marketing':
                    data['department'] = None
                    data['department_member'] = None
                    data['lead_type'] = 'personal'
                    data['marketer'] = user.employee_no
                else:
                    department_instance = Departments.objects.filter(dp_name = data.get('department')).first()
                    if not department_instance:
                        return Response(dict(success=False, message='No department found'), status=400)                    

                    data['department'] = department_instance.dp_id
                    data['department_member'] = user.employee_no
                    data['lead_type'] = 'allocated'
                    data['marketer'] = data.get('marketer')
                
                print (f"Lead source id: {lead_source_id}")
                lead_source = LeadSource.objects.filter(id=lead_source_id).first()
                if not lead_source:
                    return Response(dict(success=False, message='No such Lead source found'), status=400)  
                
                phone= data.get('phone')
                if phone:
                    # Remove spaces, dashes, and leading plus
                    phone = phone.replace(" ", "").replace("-", "")
                    # Ensure phone starts with '+'
                    if not phone.startswith("+"):
                        # If phone starts with '0', replace with country code (assume Kenya +254)
                        if phone.startswith("0"):
                            phone = "+254" + phone[1:]
                        # If phone starts with country code without '+', add '+'
                        elif phone[:3] == "254":
                            phone = "+" + phone
                        else:
                            # Default: add '+' at the start
                            phone = "+" + phone
                    # Validate length (international numbers are usually 10-15 digits after '+')
                    digits_only = phone[1:]
                    if not digits_only.isdigit():
                        return Response(dict(success=False, message='Phone number must contain only digits after +'), status=400)
                    if len(digits_only) < 10:
                        return Response(dict(success=False, message='Phone number is too short'), status=400)
                    if len(digits_only) > 15:
                        return Response(dict(success=False, message='Phone number is too long'), status=400)
                    data['phone'] = phone

                data['lead_source'] = lead_source.leadsource_id
                data['lead_source_category'] = lead_source.lead_source_subcategory.lead_source_category.id   
                data['lead_source_subcategory'] = lead_source.lead_source_subcategory.id    

                serializer = self.serializer_class(data=data)
                serializer.is_valid(raise_exception=True)    
                lead = serializer.save()


                # send email to marketer
                if lead.marketer:
                    marketing_allocation_context = {
                        'marketer': lead.marketer.fullnames,
                        'marketer_department': lead.marketer.department.dp_name,
                        'prospect_name': lead.name,
                        'prospect_phone': lead.phone,
                        'prospect_alt_number': lead.alternate_phone if lead.alternate_phone else 'N/A',
                        'prospect_email': lead.email if lead.email else 'N/A',
                        'prospect_category': lead.category if lead.category else 'N/A',
                        'prospect_city': lead.city if lead.city else 'N/A',
                        'prospect_country': lead.country if lead.country else 'N/A',
                        'prospect_comment': lead.comment if lead.comment else 'N/A',
                        'department': lead.department.dp_name if lead.department else 'CONVERSION',
                        'allocator': lead.department_member.fullnames if lead.department_member else request.user.fullnames,
                        'year': str(timezone.now().year)
                    }
                    marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                    recipients_list = [lead.marketer.email] 
                    send_email_task.delay(
                        subject="Prospect Allocation",
                        recipients=normalize_emails([email for email in recipients_list if email]),
                        template_name=marketing_allocation_template,
                        context=deepcopy(marketing_allocation_context),
                        )
                    

                return Response(dict(success=True, message='Lead saved successfully', data=serializer.data), status=201)  
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)

    @swagger_auto_schema(tags=['Leads'], operation_description="partial_update Prospects")
    def partial_update(self, request, *args, **kwargs):

       
        try:
            with transaction.atomic(): 

                #check old marketer
                data = request.data.copy()
                marketer= data.get('marketer')
                # Get the current instance before saving
                instance = self.get_object()

                if marketer:
                    # old_marketer = instance.marketer
                    # if old_marketer != marketer:
                    #     #notify old marketer
                    #     Engagement.objects.create(
                    #         client_type='Prospect',
                    #         engagement_type='Contact-add',
                    #         subject=f"Prospect {instance.name} has been reassigned",
                    #         description=f"{request.user.fullnames} has reassigned this prospect to you",
                    #         created_by=request.user,
                    #         prospect=instance
                    #     )
                        #notify new marketer

                    new_marketer = User.objects.filter(employee_no=marketer).first()
                    if new_marketer:
                        
                        # assign allocator on leads 
                        if instance.lead_source.name == 'AMANI CHATBOT':
                            data['department_member'] = request.user.employee_no
                            data['department'] = request.user.department.dp_id
                            data['lead_type'] = 'allocated'

                        # add engagement 
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f"Prospect {instance.name} has been assigned to you",
                            description=f"{request.user.fullnames} has assigned this prospect to you",
                            created_by=request.user,
                            prospect=instance
                        )


                        # Add Log
                        CustomSystemLog.objects.create(
                            system_level='User',
                            user=request.user,
                            action='Edit',
                            module='Leads',
                            message=f'Lead/Prospect {instance.name} ({instance.phone}) from lead source {instance.lead_source.name} was assigned to {new_marketer.fullnames} by {request.user.fullnames}'
                        )

                        # send email to new marketer

                        marketing_allocation_context = {
                            'marketer': new_marketer.fullnames,
                            'marketer_department': new_marketer.department.dp_name,
                            'prospect_name': instance.name,
                            'prospect_phone': instance.phone,
                            'prospect_alt_number': instance.alternate_phone if instance.alternate_phone else 'N/A',
                            'prospect_email': instance.email if instance.email else 'N/A',
                            'prospect_category': instance.category if instance.category else 'N/A',
                            'prospect_city': instance.city if instance.city else 'N/A',
                            'prospect_country': instance.country if instance.country else 'N/A',
                            'prospect_comment': instance.comment if instance.comment else 'N/A',
                            'department': instance.department.dp_name if instance.department else 'CONVERSION',
                            'allocator': instance.department_member.fullnames if instance.department_member else request.user.fullnames,
                            'year': str(timezone.now().year)
                        }
                        marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                        recipients_list = [new_marketer.email] 
                        send_email_task.delay(
                            subject="Prospect Allocation",
                            recipients=normalize_emails([email for email in recipients_list if email]),
                            template_name=marketing_allocation_template,
                            context=deepcopy(marketing_allocation_context),
                            )
                        
                serializer = ProspectsSerializer(instance, data=data, partial=True)
                serializer.is_valid(raise_exception=True)    
                serializer.save()                
                
                return Response(dict(message=f'Updated successfully', data=serializer.data, success=True), 200)
            
        except Exception as e:
            return Response(dict(message=f'Error: {e}', error=f'{e}', success=False), 500)
        

    @swagger_auto_schema(tags=['Leads'], operation_description="partial_update Prospects")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

# class EngagementsView(viewsets.ModelViewSet):
#     """
#     Viewset for Engagements
#     """
#     queryset = Engagements.objects.all()
#     serializer_class = EngagementsSerializer
#     if settings.DEBUG:
#         permission_classes = [AllowAny]
#     else:
#         permission_classes = [IsAuthenticated]
#     filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
#     filterset_fields = ['engagement_type', 'engagement_date', 'engagement_time', 'engagement_duration', 'engagement_notes', 'follow_up_date', 'follow_up_time', 'follow_up_notes']
#     search_fields = ['engagement_type', 'engagement_date', 'engagement_time', 'engagement_duration', 'engagement_notes', 'follow_up_date', 'follow_up_time', 'follow_up_notes']
#     ordering_fields = ['engagement_date', 'engagement_type']

#     @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order Engagements")
#     def list(self, request, *args, **kwargs):
#         return super().list(request, *args, **kwargs)
    
#     @swagger_auto_schema(tags=['Leads'], operation_description="Create Engagements")
#     def create(self, request, *args, **kwargs):
#         return super().create(request, *args, **kwargs)
    
#     @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve Engagements")
#     def retrieve(self, request, *args, **kwargs):
#         return super().retrieve(request, *args, **kwargs)
    
    
class DiasporaRegionsView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaRegions
    """
    queryset = DiasporaRegions.objects.all()
    serializer_class = DiasporaRegionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['name', 'description', 'manager_name']
    search_fields = ['name', 'description', 'manager_name']
    ordering_fields = ['name', 'description', 'manager_name']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaRegions")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaRegions")
    def create(self, request, *args, **kwargs):
        try: 
            with transaction.atomic():
                data = request.data.copy() 
                manager = data.get('manager') 
                if not manager: 
                    return Response(dict(success=False, message='Manager is required'), status=400)
                
                user = User.objects.filter(employee_no=manager, status='Active').first()
                if not user: 
                    return Response(dict(success=False, message='User not found'), status=400)

                data['manager_name'] = user.fullnames if user.fullnames else f'{user.first_name} {user.last_name}'

                serializer = self.serializer_class(data=data)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(dict(success=True, message='Created successfully', data=serializer.data), status=201)
                
                
        except Exception as e: 
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaRegions")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaRegions")
    def partial_update(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                instance = self.get_object()
                data = request.data.copy()
                
                # Only validate and update manager if it's provided in the request
                if 'manager' in data:
                    manager = data.get('manager')
                    if not manager:
                        return Response(dict(success=False, message='Manager cannot be empty'), status=400)
                    
                    user = User.objects.filter(employee_no=manager, status='Active').first()
                    if not user:
                        return Response(dict(success=False, message='User not found'), status=400)
                    
                    data['manager_name'] = user.fullnames if user.fullnames else f'{user.first_name} {user.last_name}'
                
                serializer = self.serializer_class(instance, data=data, partial=True)
                serializer.is_valid(raise_exception=True)
                serializer.save()
                return Response(dict(success=True, message='Updated successfully', data=serializer.data), status=200)
                
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaRegions")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

class DiasporaTripsView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaTrips
    """
    queryset = DiasporaTrips.objects.all()
    serializer_class = DiasporaTripsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']
    search_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']
    ordering_fields = ['lead_source', 'trip_name', 'trip_date', 'trip_notes', 'target_MIB', 'visiting_country', 'manager', 'diaspora_region']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaTrips")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaTrips")
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaTrips")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaTrips")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaTrips")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    

class DiasporaTripsMarketersView(viewsets.ModelViewSet):
    """
    Viewset for DiasporaTripsMarketers
    """
    queryset = DiasporaTripsMarketers.objects.all()
    serializer_class = DiasporaTripsMarketersSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['marketer','DiasporaTrips']
    search_fields = ['marketer','DiasporaTrips', 'marketer__employee_no', 'DiasporaTrips__trip_name', 'marketer__fullnames']
    ordering_fields = ['marketer','DiasporaTrips']

    @swagger_auto_schema(tags=['Leads'], operation_description="List, filter, search, order DiasporaTripsMarketers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Create DiasporaTripsMarketers")
    def create(self, request, *args, **kwargs):

        data = request.data.copy()
        diaspora_trip_id = data.get('trip')
        diaspora_trip = DiasporaTrips.objects.filter(id=diaspora_trip_id).first()

        if not diaspora_trip:
            return Response(dict(success=False, message='No such Diaspora trip found'), status=400)
        
        marketers = data.get('members')
        if marketers:
            if isinstance(marketers, list):
                for marketer in marketers:
                    userMarketer = User.objects.filter(employee_no=marketer).first()
                    if not userMarketer:
                        return Response(
                            dict(success=False, message=f'Marketer with employee_no {marketer} does not exist'), 
                            status=400
                        )
                    diaspora_trip_marketer, create = DiasporaTripsMarketers.objects.get_or_create(DiasporaTrips=diaspora_trip, marketer=userMarketer)
                    

            else:
                userMarketer = User.objects.filter(employee_no=marketer).first()
                if not userMarketer:
                    return Response(
                        dict(success=False, message=f'Marketer with employee_no {marketer} does not exist'), 
                        status=400
                    )
                diaspora_trip_marketer, create = DiasporaTripsMarketers.objects.get_or_create(DiasporaTrips=diaspora_trip, marketer=userMarketer)
                return Response(
                    dict(success=True, message=f'Added successfully', data=self.serializer_class(diaspora_trip_marketer).data), 
                    status=201
                )
        return Response(
            dict(success=True, message=f'Added successfully', data=self.serializer_class(diaspora_trip_marketer).data), 
            status=201
        )

    
    @swagger_auto_schema(tags=['Leads'], operation_description="Retrieve DiasporaTripsMarketers")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Partial Update DiasporaTripsMarketers")
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Leads'], operation_description="Destroy DiasporaTripsMarketers")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


class DiasporaTripsReceiptsStaticsView(views.APIView):
    
    """
    View to retrieve reciepts for a specific trip.
    """
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get']
    
    @swagger_auto_schema(
        tags=['Diaspora Trips Reports'],
        operation_summary="Get receipts for a specific trip and statistics",
        responses={200: 'Receipts retrieved successfully.'}
    )
    
    def get(self, request, *args, **kwargs):
        params = request.query_params
        trip_id = params.get('trip_id')
        export_type = params.get('export', 'json')  # default to JSON

        try:

            try:
                trip_id = int(params.get('trip_id', 0))
            except (ValueError, TypeError):
                return Response(dict(success=False, message='Invalid trip_id'), status=400)

            trip = DiasporaTrips.objects.filter(id=trip_id).first()
            if not trip:
                return Response(dict(success=False, message='No such trip found'), status=400)
            
            receipts = DiasporaReceipts.objects.filter(trip_id=trip_id)
            
            
            title = f'{trip.trip_name} '

            thead = [
                "Date",
                "Client's Name",
                "Marketer",
                "Plot Number",
                "Amount Receipted",
                "Cash Price",
                "Purchase price",
                "Total Paid",
                "BALANCE LCY",
                "Agreement Sent",
                "Agreement Signed",
                "Account",
                "Region"
            ]

            tbody = []
            for receipt in receipts:
                cashprice = 0
                purchaseprice = 0
                totalpaid = 0
                balance_lcy = 0
                agreement_sent = "NO"
                agreement_signed = "NO"
                marketer= User.objects.filter(employee_no=receipt.marketer).first()
                offerletter = OfferLetterMain.objects.filter(booking_id=receipt.booking_id).first()
                if offerletter:
                    if offerletter.lead_file is not None:
                        leadfile = LeadFile.objects.filter(lead_file_no=offerletter.lead_file).first()
                        if leadfile:
                            cashprice = leadfile.selling_price
                            purchaseprice = leadfile.purchase_price
                            totalpaid = leadfile.total_paid
                            balance_lcy = leadfile.balance_lcy
                            agreement_sent = leadfile.sale_agreement_sent
                            agreement_signed = leadfile.sale_agreement_signed
                            if leadfile.sale_agreement_sent == True:
                                agreement_sent = "YES"
                            if leadfile.sale_agreement_signed == True:
                                agreement_signed = "YES"
                        

                
                
                tbody.append({
                    "date": receipt.created_at.strftime('%Y-%m-%d %H:%M:%S') if receipt.created_at else None,
                    "client_name": receipt.client_name,
                    "marketer": marketer.fullnames if marketer else "Unknown",
                    "plot_number": receipt.plot_no,
                    "amount_receipted": "{:,.2f}".format(receipt.amount) if receipt.amount else 0,
                    "cash_price": "{:,.2f}".format(cashprice) if cashprice else 0,
                    "purchase_price": "{:,.2f}".format(purchaseprice) if purchaseprice else 0,
                    "total_paid": "{:,.2f}".format(totalpaid) if totalpaid else 0,
                    "balance_lcy": "{:,.2f}".format(balance_lcy) if balance_lcy else 0,
                    "agreement_sent": "YES" if agreement_sent == "YES" else "NO",
                    "agreement_signed": "YES" if agreement_signed == "YES" else "NO",
                    "account": receipt.acc_no,
                    "region": receipt.region
                })
                
            tfooter = {
                "date": "",
                "total_clients": receipts.count(),
                "total_marketers": User.objects.filter(employee_no__in=receipts.values_list('marketer', flat=True)).count(),
                "plot_number": "",
                "total_amount_receipted": "{:,.2f}".format(receipts.aggregate(Sum('amount'))['amount__sum'] or 0),
                "total_cash_price": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('selling_price'))['selling_price__sum'] or 0),
                "total_purchase_price": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('purchase_price'))['purchase_price__sum'] or 0),
                "total_paid": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('total_paid'))['total_paid__sum'] or 0),
                "total_balance_lcy": "{:,.2f}".format(LeadFile.objects.filter(
                    lead_file_no__in=OfferLetterMain.objects.filter(
                        booking_id__in=receipts.values_list('booking_id', flat=True)
                    ).values_list('lead_file', flat=True)
                ).aggregate(Sum('balance_lcy'))['balance_lcy__sum'] or 0),
                "agreement_sent": "",
                "agreement_signed": "",
                "account": "",
                "region": "",
}

            

            if export_type == 'excel':
                df = pd.DataFrame(tbody)
                excel_file = BytesIO()
                writer = pd.ExcelWriter(excel_file, engine='xlsxwriter')
                df.to_excel(writer, sheet_name='Sheet1', index=False, startrow=2)

                workbook = writer.book
                worksheet = writer.sheets['Sheet1']

                # Title
                title_format = workbook.add_format({'bold': True, 'align': 'center', 'font_size': 14})
                worksheet.merge_range(0, 0, 0, len(thead)-1, title, title_format)

                # Header
                # header_format = workbook.add_format({'bold': True, 'align': 'center', 'border': 1})
                # for col_num, header in enumerate(thead):
                #     worksheet.write(1, col_num, header, header_format)

                # Footer
                footer_format = workbook.add_format({'bold': True, 'border': 1})
                footer_row = len(tbody) + 2
                for col_num, (key, value) in enumerate(tfooter.items()):
                    worksheet.write(footer_row, col_num, value, footer_format)

                # Column width
                for i, col in enumerate(df.columns):
                    max_len = max(df[col].astype(str).apply(len).max(), len(thead[i]))
                    worksheet.set_column(i, i, max_len + 2)

                writer.close()
                excel_file.seek(0)

                response = HttpResponse(
                    excel_file.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="{trip.trip_name}_receipts.xlsx"'
                return response

            # Else return JSON
            return Response({
                'success': True,
                'message': 'Receipts retrieved successfully',
                'data': {
                    'title': title,
                    'thead': thead,
                    'tbody': tbody,
                    'tfooter': tfooter,
                }
            }, status=200)

        except Exception as e:
            return Response(dict(success=False, message=str(e)), status=500)
    

class GenerateLeadFromLinkView(views.APIView):
    permission_classes = [AllowAny]
    http_method_names = ['post']
    
    @swagger_auto_schema(
        tags=['Leads'],
        operation_summary="Generate link for diaspora marketer",
        responses={200: 'Lead lead Link generated successfully.'}
    )
    def post(self, request): 
        data = request.data.copy()
        marketer_no = data.get('marketer')
        lead_source_ref = data.get('ref_code')

        try:
            with transaction.atomic():
                
                lead_source = LeadSource.objects.filter(ref_code=lead_source_ref).first()
                if not lead_source: 
                    return Response("No such leadsource was found!!")
                
                marketer = User.objects.get(employee_no=marketer_no, status='Active')
                if not marketer: 
                    return Response("No such sales manager is found!!")
                
                from urllib.parse import urlparse, parse_qs

                lead_source_link = lead_source.link
                query = urlparse(lead_source_link).query
                params = parse_qs(query) 
                encoded_code = params.get("ls", [None])[0]  # get first "ls" value

                if not encoded_code:
                    return Response(dict(message="Invalid lead source link!", success=False), status=400)

                import base64        
                ref_code = base64.b64decode(encoded_code).decode("utf-8")

                __params = {
                    "leadSourceName":lead_source.name,
                    "marketerCode": marketer.mkcode,
                    "refCode": ref_code,
                    "marketerName":marketer.fullnames
                }

                # Convert dict to JSON string 
                json_string = json.dumps(__params)

                encoded_string = base64.b64encode(json_string.encode('utf-8')).decode('utf-8')

                new_link = lead_source_link.split('=')[0] + '=' + encoded_string

                email_context = {
                    'lead_source_name':lead_source.name,
                    'lead_link':new_link,
                    'mkcode':marketer.mkcode,
                    'marketer': marketer.fullnames,
                    'year': str(timezone.now().year),
                }

                # handle cc mails 
                cc_emails = ['<EMAIL>']
                if lead_source.manager is not None: 
                    cc_emails.append(lead_source.manager.email)


                email_template = 'leads/lead_form_link.html'
                send_email_task.delay(
                    subject=f"Diaspora Lead Form Link",
                    recipients=normalize_emails([marketer.email]),
                    template_name=email_template,
                    context=deepcopy(email_context),
                    cc=normalize_emails(cc_emails)
                    )
                
                return Response(dict(message='Link generated and sent to marketer successfully', success=True), status=200)                
                 
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
                
  

class AddLeadFromLinkView(views.APIView):
    permission_classes = [AllowAny]
    http_method_names = ['post']
    
    @swagger_auto_schema(
        tags=['Leads'],
        operation_summary="Post Lead from Link",
        responses={200: 'Lead added successfully.'}
    )
    def post(self, request): 
        data = request.data.copy()
        marketer_code = data.get('marketer_code')
        lead_source_ref_code = data.get('lead_source_ref_code')


        try:
            with transaction.atomic():

                user = User.objects.get(mkcode=marketer_code, status='Active')
                if not user: 
                    return Response("No such sales manager is found!!")
                
                # Check if phone number already exists
                if data.get('phone'):
                    existingPropspect = Prospects.objects.filter(phone=data['phone']).first()
                    if existingPropspect is not None:
                        #create an engagement for the existing prospect
                        Engagement.objects.create(
                            client_type='Prospect',
                            engagement_type='Contact-add',
                            subject=f" Follow-up on existing prospect",
                            description=f"{user.fullnames} was Following up on this prospect",
                            created_by=user,
                            prospect=existingPropspect
                        )
                        mk='Unknown'
                        if existingPropspect.marketer:
                            mk=f' Marketer {existingPropspect.marketer.fullnames}'
                        if existingPropspect.department:
                            mk = f' Department {existingPropspect.department.dp_name}'

                        return Response(
                            dict(success=False, message=f'You already exists as lead  ({existingPropspect.name}) under {mk}'), 
                            status=400
                        )
                    existingCustomer = Customer.objects.filter(phone=data['phone']).first()
                    if existingCustomer is not None:
                        #create an engagement for the existing customer
                        Engagement.objects.create(
                            client_type='Customer',
                            engagement_type='Contact-add',
                            subject=f"Follow-up on existing customer",
                            description=f"{user.fullnames} was Following up on this customer",
                            created_by=user,
                            customer=existingCustomer
                        )
                        return Response(
                            dict(success=False, message=f'You already exists as customer ({existingCustomer.customer_name}) under sales manager {existingCustomer.marketer.fullnames}'), 
                            status=400
                        )
                

                data['department'] = None
                data['department_member'] = None
                data['lead_type'] = 'personal'
                data['marketer'] = user.employee_no
                
                lead_source = LeadSource.objects.filter(ref_code=lead_source_ref_code).first()
                if not lead_source:
                    return Response(dict(success=False, message='No such Lead source found'), status=400)  
                
                phone= data.get('phone')
                if phone:
                    # Remove spaces, dashes, and leading plus
                    phone = phone.replace(" ", "").replace("-", "")
                    # Ensure phone starts with '+'
                    if not phone.startswith("+"):
                        # If phone starts with '0', replace with country code (assume Kenya +254)
                        if phone.startswith("0"):
                            phone = "+254" + phone[1:]
                        # If phone starts with country code without '+', add '+'
                        elif phone[:3] == "254":
                            phone = "+" + phone
                        else:
                            # Default: add '+' at the start
                            phone = "+" + phone
                    # Validate length (international numbers are usually 10-15 digits after '+')
                    digits_only = phone[1:]
                    if not digits_only.isdigit():
                        return Response(dict(success=False, message='Phone number must contain only digits after +'), status=400)
                    if len(digits_only) < 10:
                        return Response(dict(success=False, message='Phone number is too short'), status=400)
                    if len(digits_only) > 15:
                        return Response(dict(success=False, message='Phone number is too long'), status=400)
                    data['phone'] = phone

                data['lead_source'] = lead_source.leadsource_id
                data['lead_source_category'] = lead_source.lead_source_subcategory.lead_source_category.id   
                data['lead_source_subcategory'] = lead_source.lead_source_subcategory.id    

                serializer = ProspectsSerializer(data=data)
                serializer.is_valid(raise_exception=True)    
                lead = serializer.save()


                # send email to marketer

                marketing_allocation_context = {
                    'marketer': lead.marketer.fullnames,
                    'marketer_department': lead.marketer.department.dp_name,
                    'prospect_name': lead.name,
                    'prospect_phone': lead.phone,
                    'prospect_alt_number': lead.alternate_phone if lead.alternate_phone else 'N/A',
                    'prospect_email': lead.email if lead.email else 'N/A',
                    'prospect_category': lead.category if lead.category else 'N/A',
                    'prospect_city': lead.city if lead.city else 'N/A',
                    'prospect_country': lead.country if lead.country else 'N/A',
                    'prospect_comment': lead.comment if lead.comment else 'N/A',
                    'department': lead.department.dp_name if lead.department else 'CONVERSION',
                    'allocator': lead.department_member.fullnames if lead.department_member else request.user.fullnames,
                    'year': str(timezone.now().year)
                }
                marketing_allocation_template = 'leads/marketer_lead_allocation.html'
                recipients_list = [lead.marketer.email] 
                send_email_task.delay(
                    subject="Prospect Allocation",
                    recipients=normalize_emails([email for email in recipients_list if email]),
                    template_name=marketing_allocation_template,
                    context=deepcopy(marketing_allocation_context),
                    )
                

                return Response(dict(success=True, message='Lead saved successfully', data=serializer.data), status=201)  
        
        except Exception as e:
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)


class ProspectsFilterView(AdvancedReportExportMixin, viewsets.ViewSet):
    """
    A ViewSet for filtering prospects/leads with comprehensive filter options and export capabilities.
    Provides advanced filtering capabilities for lead_source, marketer, department,
    date range, status, category, pipeline_level, and all other prospect fields.
    
    Export Features:
    - PDF reports with custom templates
    - Excel exports with multiple sheets
    - Advanced Excel reports with statistics
    """
    
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    # Export configuration
    export_template_name = 'exports/leads_report.html'
    export_filename_prefix = 'leads_report'
    export_title = 'Prospects/Leads Data Report'
    
    # Serializer configuration for export functionality
    serializer_class = ProspectsSerializer

    def get_serializer(self, *args, **kwargs):
        """Return serializer instance for export functionality"""
        return self.serializer_class(*args, **kwargs)
    
    def get_serializer_class(self):
        """Return serializer class for export functionality"""
        return self.serializer_class

    def get_queryset(self):
        """Get filtered queryset based on user permissions"""
        if getattr(self, 'swagger_fake_view', False):
            return Prospects.objects.none()
        
        base_queryset = Prospects.objects.all().order_by('-date')
        # queryset = leads_permission_filters(self.request.user, base_queryset)
        # if queryset is False:
        #     raise PermissionDenied("You do not have permission to view these prospects records.")
        queryset=base_queryset
        return queryset

    def get_export_queryset(self, request):
        """Get filtered queryset for export - completely custom filtering to avoid 'replace' error"""
        from django_filters.rest_framework import DjangoFilterBackend
        
        # Get base queryset
        queryset = self.get_queryset()
        
        # Apply ProspectsFilter manually to avoid any backend issues
        try:
            prospects_filter = ProspectsFilter(request.GET, queryset=queryset)
            filtered_queryset = prospects_filter.qs
        except Exception as e:
            # If filtering fails, fall back to base queryset
            print(f"Filtering error: {e}")
            filtered_queryset = queryset
        
        return filtered_queryset

    def get_export_data(self, queryset):
        """Transform queryset into export-ready data."""
        # Use list(queryset) to convert queryset to a list of instances
        serializer = self.get_serializer(list(queryset), many=True)
        return serializer.data

    def get_export_statistics(self, queryset, request):
        """Generate leads-specific statistics for exports."""
        total_count = queryset.count()
        
        # Status statistics
        active_count = queryset.filter(status='ACTIVE').count()
        converted_count = queryset.filter(status='CONVERTED').count()
        dropped_count = queryset.filter(status='DROPPED').count()
        
        # Verification statistics
        verified_count = queryset.filter(verification_status='Verified').count()
        unverified_count = queryset.filter(verification_status='Unverified').count()
        
        # Pipeline statistics
        pipeline_stats = {}
        pipeline_levels = queryset.values_list('pipeline_level', flat=True).distinct()
        for level in pipeline_levels:
            if level:
                pipeline_stats[f'pipeline_{level}'] = queryset.filter(pipeline_level=level).count()
        
        # Date statistics
        from django.utils import timezone
        current_year = timezone.now().year
        current_year_count = queryset.filter(date__year=current_year).count()
        
        stats = {
            'total_prospects': total_count,
            'active_prospects': active_count,
            'converted_prospects': converted_count,
            'dropped_prospects': dropped_count,
            'verified_prospects': verified_count,
            'unverified_prospects': unverified_count,
            'registered_this_year': current_year_count,
            'export_date': timezone.now(),
            'applied_filters': self.get_applied_filters(request),
        }
        
        # Add pipeline statistics
        stats.update(pipeline_stats)
        
        return stats
    
    def get_applied_filters(self, request):
        """Extract leads-specific applied filters."""
        filters = {}
        
        # Leads-specific filter parameters
        leads_filter_params = [
            'lead_source', 'marketer', 'department', 'pipeline_level',
            'status', 'category', 'verification_status', 'country',
            'date_from', 'date_to', 'first_name', 'last_name',
            'phone_number', 'email', 'group_by', 'group_count'
        ]
        
        for param in leads_filter_params:
            if request.query_params.get(param):
                filters[param] = request.query_params.get(param)
        
        return filters

    @swagger_auto_schema(
        tags=['Leads Filter'],
        operation_description="""
Filter prospects/leads with comprehensive filtering options and export capabilities.

## Data Filtering Examples

### Basic Filtering
**Single Value Filters:**
- Basic lead source filter: `/api/leads/filter/?lead_source=123`
- Marketer filter: `/api/leads/filter/?marketer=EMP001`
- Status filter: `/api/leads/filter/?status=Active`
- Category filter: `/api/leads/filter/?category=Hot`
- Pipeline level: `/api/leads/filter/?pipeline_level=New`
- Verification status: `/api/leads/filter/?is_verified=true`
- Location filter: `/api/leads/filter/?country=Kenya&city=Nairobi`

**Date Range Filtering:**
- Date range: `/api/leads/filter/?date_from_date=2024-01-01&date_to_date=2024-12-31`
- Current month: `/api/leads/filter/?date_from_date=2024-01-01&date_to_date=2024-01-31`

### Multiple Value Filtering
**Comma-separated Values (AND logic for same field):**
- Multiple lead sources: `/api/leads/filter/?lead_source=123,456,789`
- Multiple marketers: `/api/leads/filter/?marketer=EMP001,EMP002,EMP003`
- Multiple departments: `/api/leads/filter/?department=Digital,Telemarketing,Diaspora`
- Multiple categories: `/api/leads/filter/?category=Hot,Warm`
- Multiple pipeline levels: `/api/leads/filter/?pipeline_level=New,Qualified Leads,Nurturing`

**Complex Multi-field Filtering (AND logic across fields):**
- Multi-criteria: `/api/leads/filter/?lead_source=123,456&marketer=EMP001,EMP002&status=Active&category=Hot,Warm&date_from_date=2024-01-01`
- Location + source: `/api/leads/filter/?lead_source=123&country=Kenya&city=Nairobi&is_verified=true`

### Data Grouping
**Basic Grouping (returns grouped data with records):**
- Group by lead source: `/api/leads/filter/?group_by=lead_source`
- Group by marketer: `/api/leads/filter/?group_by=marketer`
- Group by department: `/api/leads/filter/?group_by=department`
- Group by status: `/api/leads/filter/?group_by=status`
- Group by pipeline level: `/api/leads/filter/?group_by=pipeline_level`

**Count-only Grouping (returns statistics only):**
- Lead source counts: `/api/leads/filter/?group_by=lead_source&group_count=true`
- Marketer performance: `/api/leads/filter/?group_by=marketer&group_count=true`
- Status distribution: `/api/leads/filter/?group_by=status&group_count=true`

**Filtered Grouping (combine filtering with grouping):**
- Active leads by source: `/api/leads/filter/?status=Active&group_by=lead_source&group_count=true`
- Q1 2024 by marketer: `/api/leads/filter/?date_from_date=2024-01-01&date_to_date=2024-03-31&group_by=marketer`
- Hot leads by pipeline: `/api/leads/filter/?category=Hot&group_by=pipeline_level&group_count=true`

## Export Functionality

### PDF Exports
**Basic PDF Export:**
- All leads: `/api/leads/filter/export_pdf/`
- Filtered leads: `/api/leads/filter/export_pdf/?status=Active&category=Hot`
- Date range: `/api/leads/filter/export_pdf/?date_from_date=2024-01-01&date_to_date=2024-03-31`

**PDF with Grouping:**
- Grouped PDF: `/api/leads/filter/export_pdf/?group_by=marketer&status=Active`
- Count summary: `/api/leads/filter/export_pdf/?group_by=status&group_count=true`

### Excel Exports
**Basic Excel Export:**
- Standard Excel: `/api/leads/filter/export_excel/`
- Filtered Excel: `/api/leads/filter/export_excel/?lead_source=123,456&marketer=EMP001`
- Multi-criteria: `/api/leads/filter/export_excel/?status=Active&category=Hot,Warm&is_verified=true`

**Excel with Grouping:**
- Grouped Excel: `/api/leads/filter/export_excel/?group_by=department&lead_source=123`
- Performance report: `/api/leads/filter/export_excel/?group_by=marketer&group_count=true&date_from_date=2024-01-01`

### Advanced Excel Exports (with enhanced formatting and charts)
**Advanced Reports:**
- Comprehensive report: `/api/leads/filter/export_advanced_excel/`
- Filtered advanced: `/api/leads/filter/export_advanced_excel/?status=Active&category=Hot&date_from_date=2024-01-01`
- Marketer performance: `/api/leads/filter/export_advanced_excel/?group_by=marketer&group_count=true`
- Department analysis: `/api/leads/filter/export_advanced_excel/?group_by=department&lead_source=123,456,789`

## Response Examples

### Standard Data Response
```json
{
  "count": 150,
  "next": "http://api/leads/filter/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "John Doe",
      "phone": "+254712345678",
      "email": "<EMAIL>",
      "status": "Active",
      "category": "Hot",
      "pipeline_level": "New",
      "lead_source": {"name": "Website", "id": 123},
      "marketer": {"fullnames": "Jane Smith", "employee_no": "EMP001"}
    }
  ],
  "filter_summary": {
    "applied_filters": {
      "status": "Active",
      "category": "Hot"
    }
  }
}
```

### Grouped Response (group_count=false)
```json
{
  "group_by": "marketer",
  "total_records": 500,
  "groups": [
    {
      "group_name": "Jane Smith (EMP001)",
      "group_value": "EMP001",
      "count": 45,
      "records": [/* prospect objects */]
    }
  ]
}
```

### Count Statistics Response (group_count=true)
```json
{
  "group_by": "status",
  "group_count": true,
  "total_records": 500,
  "statistics": {
    "total": 500,
    "active": 350,
    "verified": 280,
    "converted": 125,
    "groups": 3
  },
  "groups": [
    {"group_name": "Active", "count": 350},
    {"group_name": "Dormant", "count": 100},
    {"group_name": "Converted", "count": 50}
  ]
}
```
        """,
        manual_parameters=[
            openapi.Parameter(
                name='lead_source',
                in_=openapi.IN_QUERY,
                description='Filter by one or more lead source IDs. For multiple IDs, use comma-separated values (e.g., 123,456,789)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='lead_source_category',
                in_=openapi.IN_QUERY,
                description='Filter by lead source category ID (exact match)',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='lead_source_subcategory',
                in_=openapi.IN_QUERY,
                description='Filter by lead source subcategory ID (exact match)',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='marketer',
                in_=openapi.IN_QUERY,
                description='Filter by one or more marketer employee numbers. For multiple marketers, use comma-separated values (e.g., EMP001,EMP002,EMP003)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='department',
                in_=openapi.IN_QUERY,
                description='Filter by one or more department names. For multiple departments, use comma-separated values (e.g., Digital,Telemarketing,Diaspora)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='department_member',
                in_=openapi.IN_QUERY,
                description='Filter by department member (allocator) employee numbers. For multiple, use comma-separated values',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='lead_type',
                in_=openapi.IN_QUERY,
                description='Filter by lead type',
                type=openapi.TYPE_STRING,
                enum=['personal', 'allocated'],
                required=False,
            ),
            openapi.Parameter(
                name='status',
                in_=openapi.IN_QUERY,
                description='Filter by prospect status',
                type=openapi.TYPE_STRING,
                enum=['Active', 'Dormant'],
                required=False,
            ),
            openapi.Parameter(
                name='category',
                in_=openapi.IN_QUERY,
                description='Filter by prospect category',
                type=openapi.TYPE_STRING,
                enum=['Hot', 'Warm', 'Cold'],
                required=False,
            ),
            openapi.Parameter(
                name='pipeline_level',
                in_=openapi.IN_QUERY,
                description='Filter by pipeline level',
                type=openapi.TYPE_STRING,
                enum=['New', 'Qualified Leads', 'Nurturing', 'Site Visits', 'Booking', 'Offer Letter', 'Sale Agreement', 'Converted', 'Lost', 'Completed'],
                required=False,
            ),
            openapi.Parameter(
                name='is_verified',
                in_=openapi.IN_QUERY,
                description='Filter by verification status',
                type=openapi.TYPE_BOOLEAN,
                required=False,
            ),
            openapi.Parameter(
                name='is_converted',
                in_=openapi.IN_QUERY,
                description='Filter by conversion status',
                type=openapi.TYPE_BOOLEAN,
                required=False,
            ),
            openapi.Parameter(
                name='date_from_date',
                in_=openapi.IN_QUERY,
                description='Filter prospects created from this date onwards (YYYY-MM-DD format)',
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
            ),
            openapi.Parameter(
                name='date_to_date',
                in_=openapi.IN_QUERY,
                description='Filter prospects created up to this date (YYYY-MM-DD format)',
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
            ),
            openapi.Parameter(
                name='name',
                in_=openapi.IN_QUERY,
                description='Filter by prospect name (partial match, case insensitive)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='phone',
                in_=openapi.IN_QUERY,
                description='Filter by phone number (partial match)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='email',
                in_=openapi.IN_QUERY,
                description='Filter by email address (partial match)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='city',
                in_=openapi.IN_QUERY,
                description='Filter by city (partial match, case insensitive)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='country',
                in_=openapi.IN_QUERY,
                description='Filter by country (partial match, case insensitive)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='project',
                in_=openapi.IN_QUERY,
                description='Filter by project ID (exact match)',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='ordering',
                in_=openapi.IN_QUERY,
                description='Order results by field. Prefix with "-" for descending order. Available fields: name, date, category, status, pipeline_level',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                description='Page number for pagination',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                description='Number of results per page (max 100)',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='group_by',
                in_=openapi.IN_QUERY,
                description='Group results by field. Available fields: lead_source, marketer, department, status, category, pipeline_level, country, city, lead_type, is_verified, is_converted',
                type=openapi.TYPE_STRING,
                enum=['lead_source', 'marketer', 'department', 'status', 'category', 'pipeline_level', 'country', 'city', 'lead_type', 'is_verified', 'is_converted'],
                required=False,
            ),
            openapi.Parameter(
                name='group_count',
                in_=openapi.IN_QUERY,
                description='When group_by is used, return only count statistics instead of detailed records (true/false)',
                type=openapi.TYPE_BOOLEAN,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Successfully filtered prospects",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'count': openapi.Schema(type=openapi.TYPE_INTEGER, description='Total number of prospects matching filters (only for non-grouped results)'),
                        'next': openapi.Schema(type=openapi.TYPE_STRING, description='URL for next page of results (only for non-grouped results)'),
                        'previous': openapi.Schema(type=openapi.TYPE_STRING, description='URL for previous page of results (only for non-grouped results)'),
                        'group_by': openapi.Schema(type=openapi.TYPE_STRING, description='Field used for grouping (only for grouped results)'),
                        'group_count': openapi.Schema(type=openapi.TYPE_BOOLEAN, description='Whether this is a count-only grouping (only for grouped results)'),
                        'total_records': openapi.Schema(type=openapi.TYPE_INTEGER, description='Total number of records (for grouped results)'),
                        'statistics': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            description='Overall statistics (only for group_count=true)',
                            properties={
                                'total': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'active': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'verified': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'converted': openapi.Schema(type=openapi.TYPE_INTEGER),
                                'groups': openapi.Schema(type=openapi.TYPE_INTEGER)
                            }
                        ),
                        'groups': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            description='Grouped data (format varies based on group_count parameter)',
                            items=openapi.Schema(type=openapi.TYPE_OBJECT)
                        ),
                        'results': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            description='Prospect records (only for non-grouped results)',
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'id': openapi.Schema(type=openapi.TYPE_INTEGER),
                                    'name': openapi.Schema(type=openapi.TYPE_STRING),
                                    'phone': openapi.Schema(type=openapi.TYPE_STRING),
                                    'email': openapi.Schema(type=openapi.TYPE_STRING),
                                    'city': openapi.Schema(type=openapi.TYPE_STRING),
                                    'country': openapi.Schema(type=openapi.TYPE_STRING),
                                    'status': openapi.Schema(type=openapi.TYPE_STRING),
                                    'category': openapi.Schema(type=openapi.TYPE_STRING),
                                    'pipeline_level': openapi.Schema(type=openapi.TYPE_STRING),
                                    'lead_type': openapi.Schema(type=openapi.TYPE_STRING),
                                    'date': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME),
                                    'lead_source': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'marketer': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'department': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'is_verified': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                    'is_converted': openapi.Schema(type=openapi.TYPE_BOOLEAN),
                                }
                            )
                        ),
                        'filter_summary': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'applied_filters': openapi.Schema(type=openapi.TYPE_OBJECT)
                            }
                        )
                    }
                )
            ),
            400: openapi.Response(description="Bad request - Invalid filter parameters"),
            403: openapi.Response(description="Permission denied"),
        }
    )
    def list(self, request):
        """
        List prospects with comprehensive filtering options and grouping capabilities.
        
        Filter Examples:
        - /api/leads/filter/?lead_source=123 - Filter by single lead source ID
        - /api/leads/filter/?lead_source=123,456,789 - Filter by multiple lead source IDs
        - /api/leads/filter/?marketer=EMP001 - Filter by single marketer employee number
        - /api/leads/filter/?marketer=EMP001,EMP002,EMP003 - Filter by multiple marketer employee numbers
        - /api/leads/filter/?department=Digital,Telemarketing - Filter by multiple departments
        - /api/leads/filter/?date_from_date=2024-01-01&date_to_date=2024-12-31 - Filter by date range
        - /api/leads/filter/?status=Active&category=Hot - Filter by status and category
        - /api/leads/filter/?pipeline_level=New&is_verified=true - Filter by pipeline level and verification status
        - /api/leads/filter/?country=Kenya&city=Nairobi - Filter by location
        - /api/leads/filter/?lead_source=123,456&marketer=EMP001,EMP002&status=Active&ordering=-date - Complex filtering with multiple criteria
        
        Grouping Examples:
        - /api/leads/filter/?group_by=status - Group results by status
        - /api/leads/filter/?group_by=marketer&group_count=true - Get count by marketer
        - /api/leads/filter/?lead_source=123,456&group_by=pipeline_level - Filter and group by pipeline level
        - /api/leads/filter/?department=Digital&group_by=category&group_count=true - Department leads grouped by category with counts
        """
        from django_filters.rest_framework import DjangoFilterBackend
        from rest_framework import filters
        from rest_framework.pagination import PageNumberPagination
        from django.db.models import Count, Q, Case, When, Value, CharField
        from django.db.models.functions import Coalesce
        
        # Get parameters
        group_by = request.query_params.get('group_by')
        group_count = request.query_params.get('group_count', 'false').lower() == 'true'
        
        # Define available grouping fields with their database paths
        grouping_fields = {
            'lead_source': 'lead_source__name',
            'lead_source_id': 'lead_source__leadsource_id',
            'marketer': 'marketer__fullnames',
            'marketer_id': 'marketer__employee_no',
            'department': 'department__dp_name',
            'status': 'status',
            'category': 'category',
            'pipeline_level': 'pipeline_level',
            'country': 'country',
            'city': 'city',
            'lead_type': 'lead_type',
            'is_verified': 'is_verified',
            'is_converted': 'is_converted'
        }
        
        # Get queryset
        queryset = self.get_queryset()
        
        # Apply filters using the ProspectsFilter
        filter_backend = DjangoFilterBackend()
        filtered_queryset = filter_backend.filter_queryset(request, queryset, self)
        
        # Apply search if provided (only if search parameter exists)
        if request.query_params.get('search'):
            search_backend = filters.SearchFilter()
            filtered_queryset = search_backend.filter_queryset(request, filtered_queryset, self)
        
        # Handle grouping
        if group_by and group_by in grouping_fields:
            group_field = grouping_fields[group_by]
            
            if group_count:
                # Return grouped counts
                grouped_data = (
                    filtered_queryset
                    .values(group_field)
                    .annotate(
                        count=Count('id'),
                        group_name=Case(
                            When(**{f'{group_field}__isnull': True}, then=Value('Not Specified')),
                            When(**{f'{group_field}__exact': ''}, then=Value('Not Specified')),
                            default=group_field,
                            output_field=CharField()
                        )
                    )
                    .order_by('-count')
                )
                
                # Calculate additional statistics
                total_count = filtered_queryset.count()
                active_count = filtered_queryset.filter(status='Active').count()
                verified_count = filtered_queryset.filter(is_verified=True).count()
                converted_count = filtered_queryset.filter(is_converted=True).count()
                
                return Response({
                    'group_by': group_by,
                    'group_count': True,
                    'total_records': total_count,
                    'statistics': {
                        'total': total_count,
                        'active': active_count,
                        'verified': verified_count,
                        'converted': converted_count,
                        'groups': len(grouped_data)
                    },
                    'groups': [
                        {
                            'group_value': item['group_name'] or 'Not Specified',
                            'count': item['count'],
                            'percentage': round((item['count'] / total_count * 100), 2) if total_count > 0 else 0
                        }
                        for item in grouped_data
                    ],
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'lead_source', 'lead_source_category', 'lead_source_subcategory',
                                'marketer', 'department', 'department_member', 'lead_type',
                                'status', 'category', 'pipeline_level', 'is_verified', 'is_converted',
                                'date_from_date', 'date_to_date', 'project'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
            else:
                # Return grouped detailed records
                grouped_data = {}
                
                # Get distinct group values
                group_values = (
                    filtered_queryset
                    .values_list(group_field, flat=True)
                    .distinct()
                    .order_by(group_field)
                )
                
                for group_value in group_values:
                    group_name = group_value or 'Not Specified'
                    
                    # Filter records for this group
                    if group_value is None or group_value == '':
                        group_queryset = filtered_queryset.filter(
                            Q(**{f'{group_field}__isnull': True}) | Q(**{f'{group_field}__exact': ''})
                        )
                    else:
                        group_queryset = filtered_queryset.filter(**{group_field: group_value})
                    
                    # Apply ordering to group
                    ordering_backend = filters.OrderingFilter()
                    ordering_fields = ['name', 'date', 'category', 'status', 'pipeline_level', 'city', 'country']
                    group_queryset = ordering_backend.filter_queryset(request, group_queryset, self)
                    
                    # Serialize group data
                    serializer = ProspectsSerializer(group_queryset, many=True)
                    
                    grouped_data[group_name] = {
                        'count': group_queryset.count(),
                        'records': serializer.data
                    }
                
                return Response({
                    'group_by': group_by,
                    'group_count': False,
                    'total_records': filtered_queryset.count(),
                    'groups': grouped_data,
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'lead_source', 'lead_source_category', 'lead_source_subcategory',
                                'marketer', 'department', 'department_member', 'lead_type',
                                'status', 'category', 'pipeline_level', 'is_verified', 'is_converted',
                                'date_from_date', 'date_to_date', 'project'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
        
        # Standard filtering without grouping
        # Apply ordering
        ordering_backend = filters.OrderingFilter()
        ordering_fields = ['name', 'date', 'category', 'status', 'pipeline_level', 'city', 'country']
        filtered_queryset = ordering_backend.filter_queryset(request, filtered_queryset, self)
        
        # Set up pagination
        paginator = PageNumberPagination()
        paginator.page_size = min(int(request.query_params.get('page_size', 20)), 100)
        page = paginator.paginate_queryset(filtered_queryset, request)
        
        # Serialize data
        serializer = ProspectsSerializer(page, many=True)
        
        # Return paginated response with filter summary
        response_data = paginator.get_paginated_response(serializer.data)
        
        # Add filter summary to response
        filter_summary = {
            'applied_filters': {},
            'total_without_pagination': filtered_queryset.count()
        }
        
        # Extract applied filters from request
        filter_params = [
            'lead_source', 'lead_source_category', 'lead_source_subcategory',
            'marketer', 'department', 'department_member', 'lead_type',
            'status', 'category', 'pipeline_level', 'is_verified', 'is_converted',
            'date_from_date', 'date_to_date', 'project'
        ]
        
        for param in filter_params:
            if request.query_params.get(param):
                filter_summary['applied_filters'][param] = request.query_params.get(param)
        
        response_data.data['filter_summary'] = filter_summary
        
        return response_data

    # Make filter class available to DjangoFilterBackend
    filterset_class = ProspectsFilter
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'phone', 'alternate_phone', 'email', 'city', 'country', 'comment']
    ordering_fields = ['name', 'date', 'category', 'status', 'pipeline_level', 'city', 'country']
