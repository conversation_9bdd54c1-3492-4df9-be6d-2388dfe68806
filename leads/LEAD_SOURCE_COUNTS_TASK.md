# Lead Source Counts Update Task

## Overview
This task automatically updates the lead source counts every 5 minutes. It calculates and updates the following metrics for each LeadSource:

- **active_leads**: Count of prospects with status='Active'
- **dormant_leads**: Count of prospects with status='Dormant'
- **ongoing_sales**: Count of lead files with lead_file_status_dropped=False
- **dropped_sales**: Count of lead files with lead_file_status_dropped=True  
- **completed_sales**: Count of lead files with balance_lcy < 10

## Files Created/Modified

### 1. `leads/tasks.py`
Contains the Celery task `update_lead_source_counts` that performs the count calculations and updates.

**Features:**
- Uses Django aggregation for efficient database queries
- Includes error handling and retry logic
- Comprehensive logging
- Updates `last_updated` timestamp on each LeadSource

### 2. `config/celery.py`
Added the new task to the Celery beat schedule to run every 5 minutes.

### 3. `leads/management/commands/update_lead_source_counts.py`
Django management command for manual execution of the task.

**Usage:**
```bash
python manage.py update_lead_source_counts
python manage.py update_lead_source_counts --verbose
```

### 4. `leads/tests.py`
Added test cases to verify the task functionality.

## Database Relationships

The task uses the following relationships:

### For Prospects Counts:
- `Prospects.lead_source` → `LeadSource`
- Filters by `Prospects.status` ('Active' or 'Dormant')

### For Sales Counts:
- `LeadFile.customer_lead_source` → `LeadSource` 
- `LeadFile.engagement` → `LeadSource`
- Uses OR condition to count sales from both fields
- Filters by:
  - `lead_file_status_dropped` (False for ongoing, True for dropped)
  - `balance_lcy < 10` for completed sales

## Performance Considerations

- Uses Django's `aggregate()` with `Count()` for efficient database queries
- Processes all LeadSources in a single task execution
- Includes error handling to prevent one failed update from stopping others
- Uses `Q` objects for complex filtering conditions

## Monitoring

The task includes comprehensive logging:
- Info level: Task start/completion with summary statistics
- Debug level: Individual LeadSource update details  
- Error level: Individual failures and task-level exceptions

## Error Handling

- Individual LeadSource update failures are logged but don't stop the task
- Task-level failures trigger automatic retry with exponential backoff
- Maximum 3 retries with 60-second base delay

## Manual Execution

To run the task manually:

### Using Celery:
```python
from leads.tasks import update_lead_source_counts
result = update_lead_source_counts.delay()
```

### Using Django Management Command:
```bash
python manage.py update_lead_source_counts --verbose
```

## Schedule

The task runs automatically every 5 minutes via Celery Beat as configured in `config/celery.py`.