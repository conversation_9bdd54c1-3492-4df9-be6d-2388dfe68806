# Leads Filter API Documentation

## Overview
The `ProspectsFilterView` provides comprehensive filtering capabilities for the Prospects/Leads model with advanced filter options and proper API documentation.

## Endpoint
```
GET /api/leads/filter/
```

## Available Filters

### 1. Lead Source Filters
- **Parameter**: `lead_source`
- **Type**: String (comma-separated integers)
- **Description**: Filter by one or more lead source IDs
- **Examples**: 
  - `/api/leads/filter/?lead_source=123` - Single lead source
  - `/api/leads/filter/?lead_source=123,456,789` - Multiple lead sources

- **Parameter**: `lead_source_category`
- **Type**: Integer
- **Description**: Filter by lead source category ID (exact match)
- **Example**: `/api/leads/filter/?lead_source_category=5`

- **Parameter**: `lead_source_subcategory`
- **Type**: Integer
- **Description**: Filter by lead source subcategory ID (exact match)
- **Example**: `/api/leads/filter/?lead_source_subcategory=10`

### 2. People Filters
- **Parameter**: `marketer`
- **Type**: String (comma-separated employee numbers)
- **Description**: Filter by one or more marketer employee numbers
- **Examples**: 
  - `/api/leads/filter/?marketer=EMP001` - Single marketer
  - `/api/leads/filter/?marketer=EMP001,EMP002,EMP003` - Multiple marketers

- **Parameter**: `department`
- **Type**: String (comma-separated department names)
- **Description**: Filter by one or more department names
- **Examples**: 
  - `/api/leads/filter/?department=Digital` - Single department
  - `/api/leads/filter/?department=Digital,Telemarketing,Diaspora` - Multiple departments

- **Parameter**: `department_member`
- **Type**: String (comma-separated employee numbers)
- **Description**: Filter by department member (allocator) employee numbers
- **Example**: `/api/leads/filter/?department_member=EMP001,EMP002`

### 3. Lead Classification Filters
- **Parameter**: `lead_type`
- **Type**: String
- **Options**: `personal`, `allocated`
- **Description**: Filter by lead type
- **Example**: `/api/leads/filter/?lead_type=personal`

- **Parameter**: `status`
- **Type**: String
- **Options**: `Active`, `Dormant`
- **Description**: Filter by prospect status
- **Example**: `/api/leads/filter/?status=Active`

- **Parameter**: `category`
- **Type**: String
- **Options**: `Hot`, `Warm`, `Cold`
- **Description**: Filter by prospect category
- **Example**: `/api/leads/filter/?category=Hot`

- **Parameter**: `pipeline_level`
- **Type**: String
- **Options**: `New`, `Qualified Leads`, `Nurturing`, `Site Visits`, `Booking`, `Offer Letter`, `Sale Agreement`, `Converted`, `Lost`, `Completed`
- **Description**: Filter by pipeline level
- **Example**: `/api/leads/filter/?pipeline_level=New`

### 4. Boolean Filters
- **Parameter**: `is_verified`
- **Type**: Boolean
- **Description**: Filter by verification status
- **Examples**: 
  - `/api/leads/filter/?is_verified=true`
  - `/api/leads/filter/?is_verified=false`

- **Parameter**: `is_converted`
- **Type**: Boolean
- **Description**: Filter by conversion status
- **Examples**: 
  - `/api/leads/filter/?is_converted=true`
  - `/api/leads/filter/?is_converted=false`

### 5. Date Range Filters
- **Parameters**: 
  - `date_from_date` (Date from)
  - `date_to_date` (Date to)
- **Type**: Date (YYYY-MM-DD format)
- **Description**: Filter prospects created within a date range
- **Examples**: 
  - `/api/leads/filter/?date_from_date=2024-01-01`
  - `/api/leads/filter/?date_to_date=2024-12-31`
  - `/api/leads/filter/?date_from_date=2024-01-01&date_to_date=2024-12-31`

### 6. Contact Information Filters
- **Parameter**: `name`
- **Type**: String
- **Description**: Filter by prospect name (partial match, case insensitive)
- **Example**: `/api/leads/filter/?name=John`

- **Parameter**: `phone`
- **Type**: String
- **Description**: Filter by phone number (partial match)
- **Example**: `/api/leads/filter/?phone=254712`

- **Parameter**: `email`
- **Type**: String
- **Description**: Filter by email address (partial match)
- **Example**: `/api/leads/filter/?email=<EMAIL>`

### 7. Location Filters
- **Parameter**: `city`
- **Type**: String
- **Description**: Filter by city (partial match, case insensitive)
- **Example**: `/api/leads/filter/?city=Nairobi`

- **Parameter**: `country`
- **Type**: String
- **Description**: Filter by country (partial match, case insensitive)
- **Example**: `/api/leads/filter/?country=Kenya`

### 8. Project Filter
- **Parameter**: `project`
- **Type**: Integer
- **Description**: Filter by project ID (exact match)
- **Example**: `/api/leads/filter/?project=5`

## Pagination & Ordering
- **page**: Page number for pagination
- **page_size**: Number of results per page (max 100, default 20)
- **ordering**: Order results by field. Prefix with `-` for descending order
  - Available fields: `name`, `date`, `category`, `status`, `pipeline_level`, `city`, `country`

## Complex Filter Examples

### Example 1: Hot prospects from specific lead sources
```
/api/leads/filter/?lead_source=123,456&category=Hot&status=Active
```

### Example 2: Prospects assigned to multiple marketers in Digital department
```
/api/leads/filter/?marketer=EMP001,EMP002&department=Digital&lead_type=allocated
```

### Example 3: New prospects from Kenya created in 2024
```
/api/leads/filter/?country=Kenya&pipeline_level=New&date_from_date=2024-01-01&date_to_date=2024-12-31
```

### Example 4: Verified prospects ready for booking
```
/api/leads/filter/?is_verified=true&pipeline_level=Site Visits,Booking&category=Hot,Warm&ordering=-date
```

### Example 5: Complex multi-criteria filter with pagination
```
/api/leads/filter/?lead_source=123,456,789&marketer=EMP001,EMP002&status=Active&category=Hot&country=Kenya&city=Nairobi&is_verified=true&page=1&page_size=50&ordering=-date
```

## Response Format

### Success Response (200)
```json
{
  "count": 250,
  "next": "http://api/leads/filter/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "name": "John Doe",
      "phone": "+254712345678",
      "email": "<EMAIL>",
      "city": "Nairobi",
      "country": "Kenya",
      "status": "Active",
      "category": "Hot",
      "pipeline_level": "New",
      "lead_type": "personal",
      "date": "2024-01-15T10:30:00Z",
      "lead_source": {
        "leadsource_id": 123,
        "name": "Website"
      },
      "marketer": {
        "employee_no": "EMP001",
        "fullnames": "Jane Smith"
      },
      "department": {
        "dp_name": "Digital",
        "dp_id": 1
      },
      "is_verified": true,
      "is_converted": false
    }
  ],
  "filter_summary": {
    "applied_filters": {
      "lead_source": "123,456",
      "marketer": "EMP001,EMP002",
      "status": "Active",
      "category": "Hot"
    },
    "total_without_pagination": 250
  }
}
```

### Error Responses
- **400**: Bad request - Invalid filter parameters
- **403**: Permission denied - User lacks access rights

## Technical Implementation Features

### Multiple Value Filters
- Uses `BaseInFilter` for comma-separated values
- Automatically converts to SQL `IN` clauses
- Handles both integers and strings appropriately

### Date Filtering
- Supports both datetime and date-only filtering
- Flexible date range queries
- Proper timezone handling

### Permission Integration
- Respects existing `leads_permission_filters()`
- Ensures users only see prospects they have access to

### Performance Optimizations
- Efficient database queries with proper indexing
- Paginated responses to handle large datasets
- Optimized serialization for API responses

### Search Integration
- Combined with DjangoFilterBackend for precise filtering
- SearchFilter for text-based searches across multiple fields
- OrderingFilter for flexible result sorting

## API Description in Swagger/OpenAPI

The API includes comprehensive Swagger documentation with:

1. **Parameter Descriptions**: Each filter parameter documented with type, format, and examples
2. **Response Schema**: Complete response structure with example data
3. **Error Handling**: Documented error responses with status codes
4. **Filter Examples**: Practical usage examples in operation descriptions
5. **Enum Values**: Complete lists of available choices for select fields

## Integration with Existing Views

The enhanced filtering capabilities complement the existing `ProspectsView` while providing dedicated filtering functionality with comprehensive documentation and multiple value support.