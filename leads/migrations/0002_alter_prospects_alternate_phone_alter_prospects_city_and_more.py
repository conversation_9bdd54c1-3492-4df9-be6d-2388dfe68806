# Generated by Django 5.1.7 on 2025-06-17 09:20

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0002_initial'),
        ('leads', '0001_initial'),
        ('users', '0005_user_team_alter_user_team_tl'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='prospects',
            name='alternate_phone',
            field=models.CharField(blank=True, default=None, max_length=20),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='city',
            field=models.CharField(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='comment',
            field=models.TextField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='country',
            field=models.Char<PERSON>ield(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.<PERSON>er<PERSON><PERSON>(
            model_name='prospects',
            name='customer',
            field=models.Char<PERSON>ield(blank=True, default=None, max_length=255, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='department',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_department', to='users.departments'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='department_member',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_department_member', to=settings.AUTH_USER_MODEL, to_field='employee_no'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='email',
            field=models.EmailField(blank=True, default=None, max_length=254, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='is_converted',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='is_verified',
            field=models.BooleanField(blank=True, default=False, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='lead_source',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources', to='leads.leadsource', to_field='leadsource_id'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='lead_source_category',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_category_p', to='leads.leadsourcecategory'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='lead_source_subcategory',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_sub_category_p', to='leads.leadsourcesubcategory'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='marketer',
            field=models.ForeignKey(default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no'),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='no_of_sales',
            field=models.IntegerField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name='prospects',
            name='project',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_project', to='inventory.project'),
        ),
    ]
