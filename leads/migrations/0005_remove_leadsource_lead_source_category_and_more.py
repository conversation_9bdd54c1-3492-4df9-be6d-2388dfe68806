# Generated by Django 5.1.7 on 2025-06-29 13:37

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('leads', '0004_leadsource_managing_team'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='leadsource',
            name='lead_source_category',
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='active_leads',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='completed_sales',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='dormant_leads',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='dropped_sales',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='last_updated',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='lead_source_subcategory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_sub_category', to='leads.leadsourcesubcategory', to_field='cat_lead_source_id'),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='ongoing_sales',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsource',
            name='sales',
            field=models.IntegerField(default=0, null=True),
        ),
        migrations.AlterField(
            model_name='leadsourcesubcategory',
            name='lead_source_category',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='leads.leadsourcecategory', to_field='category_id'),
        ),
    ]
