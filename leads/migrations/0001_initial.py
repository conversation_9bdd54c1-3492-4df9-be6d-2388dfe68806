# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0001_initial'),
        ('users', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='LeadSourceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='DiasporaRegions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('manager_name', models.CharField(blank=True, max_length=255, null=True)),
                ('manager', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='diaspora_regions', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.CreateModel(
            name='DiasporaTrips',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trip_name', models.CharField(max_length=255)),
                ('trip_date', models.DateTimeField(auto_now_add=True)),
                ('trip_notes', models.TextField(blank=True, null=True)),
                ('target_MIB', models.IntegerField(blank=True, null=True)),
                ('visiting_country', models.CharField(blank=True, max_length=255, null=True)),
                ('diaspora_region', models.CharField(blank=True, max_length=255, null=True)),
                ('manager', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='man_diaspora_trips', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.CreateModel(
            name='DiasporaTripsMarketers',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('DiasporaTrips', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trip_diaspora_trips_marketers', to='leads.diasporatrips')),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='marketer_diaspora_trips_marketers', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.CreateModel(
            name='LeadSource',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('leadsource_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('qr_code', models.ImageField(blank=True, null=True, upload_to='uploads/lead_source_qr_codes/')),
                ('ref_code', models.CharField(max_length=50, unique=True)),
                ('link', models.URLField(blank=True, null=True)),
                ('sales', models.IntegerField(default=0)),
                ('ongoing_sales', models.IntegerField(default=0)),
                ('dropped_sales', models.IntegerField(default=0)),
                ('completed_sales', models.IntegerField(default=0)),
                ('active_leads', models.IntegerField(default=0)),
                ('dormant_leads', models.IntegerField(default=0)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('manager_name', models.CharField(blank=True, max_length=255, null=True)),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='manager_lead_sources', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('lead_source_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_category', to='leads.leadsourcecategory')),
            ],
        ),
        migrations.AddField(
            model_name='diasporatrips',
            name='lead_source',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ls_diaspora_trips', to='leads.leadsource', to_field='leadsource_id'),
        ),
        migrations.CreateModel(
            name='LeadSourceSubCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cat_lead_source_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('manager_name', models.CharField(blank=True, max_length=255, null=True)),
                ('lead_source_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='subcategories', to='leads.leadsourcecategory')),
                ('manager', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='lead_source_subcategories', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
        ),
        migrations.AddField(
            model_name='leadsource',
            name='lead_source_subcategory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_sub_category', to='leads.leadsourcesubcategory'),
        ),
        migrations.CreateModel(
            name='Prospects',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('lead_type', models.CharField(choices=[('personal', 'personal'), ('allocated', 'allocated')], max_length=30)),
                ('name', models.CharField(max_length=255)),
                ('phone', models.CharField(blank=True, max_length=20, null=True, unique=True)),
                ('alternate_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('city', models.CharField(blank=True, max_length=255, null=True)),
                ('country', models.CharField(blank=True, max_length=255, null=True)),
                ('comment', models.TextField(blank=True, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Dormant', 'Dormant')], default='Active', max_length=50)),
                ('category', models.CharField(choices=[('Hot', 'Hot'), ('Warm', 'Warm'), ('Cold', 'Cold')], default='Warm', max_length=255)),
                ('pipeline_level', models.CharField(choices=[('New', 'New'), ('Qualified Leads', 'Qualified Leads'), ('Nurturing', 'Nurturing'), ('Site Visits', 'Site Visits'), ('Booking', 'Booking'), ('Offer Letter', 'Offer Letter'), ('Sale Agreement', 'Sale Agreement'), ('Converted', 'Converted'), ('Lost', 'Lost'), ('Completed', 'Completed')], default='New', max_length=50)),
                ('is_verified', models.BooleanField(default=False)),
                ('is_converted', models.BooleanField(default=False)),
                ('customer', models.CharField(blank=True, max_length=255, null=True)),
                ('no_of_sales', models.IntegerField(default=0)),
                ('department', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_department', to='users.departments')),
                ('department_member', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_department_member', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('lead_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources', to='leads.leadsource', to_field='leadsource_id')),
                ('lead_source_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_category_p', to='leads.leadsourcecategory')),
                ('lead_source_subcategory', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='lead_sources_sub_category_p', to='leads.leadsourcesubcategory')),
                ('marketer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prospect_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('project', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_project', to='inventory.project')),
            ],
        ),
        migrations.CreateModel(
            name='prospectFlags',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('flag_date', models.DateTimeField(auto_now_add=True)),
                ('flag_notes', models.TextField(blank=True, null=True)),
                ('flag_type', models.CharField(choices=[('Warning', 'Warning'), ('Danger', 'Danger'), ('Info', 'Info')], default='Info', max_length=50)),
                ('flag_status', models.CharField(choices=[('Active', 'Active'), ('Resolved', 'Resolved')], default='Active', max_length=50)),
                ('flag_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='flags_flagger', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('prospect', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='flags_prospects', to='leads.prospects')),
            ],
        ),
        migrations.CreateModel(
            name='Feedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_date', models.DateTimeField(auto_now_add=True)),
                ('feedback_type', models.CharField(choices=[('Positive', 'Positive'), ('Negative', 'Negative'), ('Neutral', 'Neutral')], max_length=50)),
                ('feedback_notes', models.TextField(blank=True, null=True)),
                ('feedback_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_feedback', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('prospect', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='feedbacks_prospects', to='leads.prospects')),
            ],
        ),
        migrations.CreateModel(
            name='Engagements',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('engagement_date', models.DateTimeField(auto_now_add=True)),
                ('engagement_type', models.CharField(choices=[('Call', 'Call'), ('Email', 'Email'), ('Meeting', 'Meeting')], max_length=50)),
                ('notes', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Closed', 'Closed')], default='Active', max_length=50)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('follow_up_notes', models.TextField(blank=True, null=True)),
                ('follow_up_status', models.CharField(choices=[('Pending', 'Pending'), ('Completed', 'Completed')], default='Pending', max_length=50)),
                ('follow_up_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='follow_up_engagements', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('lead_source', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='engagements_lead_source', to='leads.leadsource', to_field='leadsource_id')),
                ('prospect', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='engagements_prospects', to='leads.prospects')),
            ],
        ),
        migrations.CreateModel(
            name='Complaints',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('complaint_date', models.DateTimeField(auto_now_add=True)),
                ('complaint_notes', models.TextField(blank=True, null=True)),
                ('complaint_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_complaints', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('prospect', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='prospect_complaints', to='leads.prospects')),
            ],
        ),
    ]
