from django_filters import rest_framework as django_filters

from leads.models import Prospects

class ProspectsFilter(django_filters.FilterSet):
    """Custom filter for Prospects"""
    
    # Filter by marketer ID
    marketer = django_filters.CharFilter(field_name='marketer')
    
    # Filter for null/not null marketer
    marketer__isnull = django_filters.BooleanFilter(
        field_name='marketer',
        lookup_expr='isnull'
    )
    
    # Filter by department name
    department__dp_name = django_filters.CharFilter(
        field_name='department__dp_name',
        lookup_expr='exact'
    )
    
    # Filter by department name
    lead_source__id = django_filters.NumberFilter(
        field_name='lead_source__id',
        lookup_expr='exact'
    )
    
    class Meta:
        model = Prospects
        fields = [
            'name', 'phone', 'alternate_phone', 'email', 
            'city', 'country', 'comment', 'date', 
            'status', 'category', 'is_verified', 'is_converted', 
            'customer', 'no_of_sales',
        ]