from django_filters import rest_framework as django_filters
from django import forms
from leads.models import Prospects

class MultipleLeadSourceFilter(django_filters.BaseInFilter, django_filters.NumberFilter):
    """
    Custom filter to handle multiple lead source IDs
    Accepts comma-separated values like: ?lead_source=123,456,789
    """
    pass

class MultipleMarketerFilter(django_filters.BaseInFilter, django_filters.CharFilter):
    """
    Custom filter to handle multiple marketer employee numbers
    Accepts comma-separated values like: ?marketer=EMP001,EMP002,EMP003
    """
    pass

class MultipleDepartmentFilter(django_filters.BaseInFilter, django_filters.CharFilter):
    """
    Custom filter to handle multiple department names
    Accepts comma-separated values like: ?department=Digital,Telemarketing,Diaspora
    """
    pass

class ProspectsFilter(django_filters.FilterSet):
    """Enhanced custom filter for Prospects with comprehensive filtering options"""
    
    # Basic text filters
    name = django_filters.CharFilter(lookup_expr='icontains')
    phone = django_filters.CharFilter(lookup_expr='icontains')
    alternate_phone = django_filters.CharFilter(lookup_expr='icontains')
    email = django_filters.CharFilter(lookup_expr='icontains')
    city = django_filters.CharFilter(lookup_expr='icontains')
    country = django_filters.CharFilter(lookup_expr='icontains')
    comment = django_filters.CharFilter(lookup_expr='icontains')
    
    # Multiple lead source IDs filter
    lead_source = MultipleLeadSourceFilter(
        field_name='lead_source__leadsource_id',
        lookup_expr='in',
        help_text='Filter by one or more lead source IDs. Use comma-separated values: 123,456,789'
    )
    
    # Lead source category filter
    lead_source_category = django_filters.NumberFilter(
        field_name='lead_source_category__category_id',
        lookup_expr='exact'
    )
    
    # Lead source subcategory filter  
    lead_source_subcategory = django_filters.NumberFilter(
        field_name='lead_source_subcategory__cat_lead_source_id',
        lookup_expr='exact'
    )
    
    # Multiple marketer filter
    marketer = MultipleMarketerFilter(
        field_name='marketer__employee_no',
        lookup_expr='in',
        help_text='Filter by one or more marketer employee numbers. Use comma-separated values: EMP001,EMP002,EMP003'
    )
    
    # Filter for null/not null marketer
    marketer__isnull = django_filters.BooleanFilter(
        field_name='marketer',
        lookup_expr='isnull'
    )
    
    # Multiple department filter
    department = MultipleDepartmentFilter(
        field_name='department__dp_name',
        lookup_expr='in',
        help_text='Filter by one or more department names. Use comma-separated values: Digital,Telemarketing,Diaspora'
    )
    
    # Department member (allocator) filter
    department_member = MultipleMarketerFilter(
        field_name='department_member__employee_no',
        lookup_expr='in',
        help_text='Filter by one or more department member employee numbers'
    )
    
    # Choice filters
    lead_type = django_filters.ChoiceFilter(
        choices=[('personal', 'personal'), ('allocated', 'allocated')]
    )
    
    status = django_filters.ChoiceFilter(
        choices=[('Active', 'Active'), ('Dormant', 'Dormant')]
    )
    
    category = django_filters.ChoiceFilter(
        choices=[('Hot', 'Hot'), ('Warm', 'Warm'), ('Cold', 'Cold')]
    )
    
    pipeline_level = django_filters.ChoiceFilter(
        choices=[
            ('New', 'New'), 
            ('Qualified Leads', 'Qualified Leads'), 
            ('Nurturing', 'Nurturing'), 
            ('Site Visits', 'Site Visits'), 
            ('Booking', 'Booking'), 
            ('Offer Letter', 'Offer Letter'), 
            ('Sale Agreement', 'Sale Agreement'),
            ('Converted', 'Converted'),
            ('Lost', 'Lost'),
            ('Completed', 'Completed')
        ]
    )
    
    # Boolean filters
    is_verified = django_filters.BooleanFilter()
    is_converted = django_filters.BooleanFilter()
    
    # Date range filters
    date_from = django_filters.DateTimeFilter(
        field_name='date',
        lookup_expr='gte',
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local'})
    )
    date_to = django_filters.DateTimeFilter(
        field_name='date',
        lookup_expr='lte',
        widget=forms.DateTimeInput(attrs={'type': 'datetime-local'})
    )
    
    # Date only filters (ignoring time)
    date_from_date = django_filters.DateFilter(
        field_name='date__date',
        lookup_expr='gte',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    date_to_date = django_filters.DateFilter(
        field_name='date__date',
        lookup_expr='lte',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    
    # Project filter
    project = django_filters.NumberFilter(
        field_name='project__id',
        lookup_expr='exact'
    )
    
    class Meta:
        model = Prospects
        fields = [
            'name', 'phone', 'alternate_phone', 'email', 
            'city', 'country', 'comment', 'lead_source',
            'lead_source_category', 'lead_source_subcategory',
            'marketer', 'marketer__isnull', 'department', 'department_member',
            'lead_type', 'status', 'category', 'pipeline_level',
            'is_verified', 'is_converted', 'project',
            'date_from', 'date_to', 'date_from_date', 'date_to_date'
        ]