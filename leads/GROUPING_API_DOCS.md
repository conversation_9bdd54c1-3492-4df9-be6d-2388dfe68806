# Leads Grouping API Documentation

## Overview
The Leads Filter API now supports powerful grouping capabilities that allow you to analyze your leads data by various dimensions. You can group leads by any supported field and get either detailed records or aggregated counts.

## Grouping Parameters

### `group_by` Parameter
- **Type**: String
- **Required**: No
- **Available Values**: 
  - `lead_source` - Group by lead source name
  - `marketer` - Group by marketer full name
  - `department` - Group by department name
  - `status` - Group by prospect status (Active/Dormant)
  - `category` - Group by prospect category (Hot/Warm/Cold)
  - `pipeline_level` - Group by pipeline level
  - `country` - Group by country
  - `city` - Group by city
  - `lead_type` - Group by lead type (personal/allocated)
  - `is_verified` - Group by verification status
  - `is_converted` - Group by conversion status

### `group_count` Parameter
- **Type**: Boolean
- **Required**: No
- **Default**: false
- **Description**: When true, returns only aggregated counts and statistics. When false, returns detailed records for each group.

## Usage Examples

### 1. Basic Grouping (Detailed Records)
```bash
GET /api/leads/filter/?group_by=status
```

**Response Format:**
```json
{
  "group_by": "status",
  "group_count": false,
  "total_records": 150,
  "groups": {
    "Active": {
      "count": 120,
      "records": [
        {
          "id": 1,
          "name": "John Doe",
          "status": "Active",
          "category": "Hot",
          ...
        }
      ]
    },
    "Dormant": {
      "count": 30,
      "records": [...]
    }
  },
  "filter_summary": {...}
}
```

### 2. Count-Only Grouping (Statistics)
```bash
GET /api/leads/filter/?group_by=marketer&group_count=true
```

**Response Format:**
```json
{
  "group_by": "marketer",
  "group_count": true,
  "total_records": 200,
  "statistics": {
    "total": 200,
    "active": 180,
    "verified": 150,
    "converted": 45,
    "groups": 8
  },
  "groups": [
    {
      "group_value": "John Smith",
      "count": 45,
      "percentage": 22.5
    },
    {
      "group_value": "Jane Doe",
      "count": 38,
      "percentage": 19.0
    },
    {
      "group_value": "Not Specified",
      "count": 12,
      "percentage": 6.0
    }
  ],
  "filter_summary": {...}
}
```

## Combined Filtering and Grouping

### 3. Filter by Lead Source and Group by Category
```bash
GET /api/leads/filter/?lead_source=123,456&group_by=category&group_count=true
```

### 4. Department Analysis with Date Range
```bash
GET /api/leads/filter/?department=Digital&date_from_date=2024-01-01&date_to_date=2024-12-31&group_by=pipeline_level&group_count=true
```

### 5. Marketer Performance Analysis
```bash
GET /api/leads/filter/?status=Active&is_verified=true&group_by=marketer&group_count=true
```

### 6. Lead Source Effectiveness
```bash
GET /api/leads/filter/?category=Hot,Warm&group_by=lead_source&group_count=true
```

## Advanced Grouping Examples

### 7. Geographic Distribution
```bash
GET /api/leads/filter/?group_by=country&group_count=true
```

### 8. Pipeline Analysis by Department
```bash
GET /api/leads/filter/?department=Digital,Telemarketing&group_by=pipeline_level
```

### 9. Verification Status by Lead Type
```bash
GET /api/leads/filter/?group_by=is_verified&lead_type=allocated&group_count=true
```

### 10. Monthly Lead Source Performance
```bash
GET /api/leads/filter/?date_from_date=2024-10-01&date_to_date=2024-10-31&group_by=lead_source&group_count=true
```

## Response Formats

### Detailed Grouping Response (`group_count=false`)
```json
{
  "group_by": "category",
  "group_count": false,
  "total_records": 100,
  "groups": {
    "Hot": {
      "count": 25,
      "records": [
        {
          "id": 1,
          "name": "John Doe",
          "phone": "+254712345678",
          "email": "<EMAIL>",
          "category": "Hot",
          "status": "Active",
          "pipeline_level": "New",
          "lead_source": {
            "leadsource_id": 123,
            "name": "Website"
          },
          "marketer": {
            "employee_no": "EMP001",
            "fullnames": "Jane Smith"
          },
          "department": {
            "dp_name": "Digital"
          },
          "is_verified": true,
          "is_converted": false,
          "date": "2024-10-15T10:30:00Z"
        }
      ]
    },
    "Warm": {
      "count": 45,
      "records": [...]
    },
    "Cold": {
      "count": 30,
      "records": [...]
    }
  },
  "filter_summary": {
    "applied_filters": {
      "status": "Active"
    }
  }
}
```

### Count-Only Grouping Response (`group_count=true`)
```json
{
  "group_by": "department",
  "group_count": true,
  "total_records": 200,
  "statistics": {
    "total": 200,
    "active": 180,
    "verified": 150,
    "converted": 45,
    "groups": 4
  },
  "groups": [
    {
      "group_value": "Digital",
      "count": 80,
      "percentage": 40.0
    },
    {
      "group_value": "Telemarketing",
      "count": 60,
      "percentage": 30.0
    },
    {
      "group_value": "Diaspora",
      "count": 45,
      "percentage": 22.5
    },
    {
      "group_value": "Not Specified",
      "count": 15,
      "percentage": 7.5
    }
  ],
  "filter_summary": {
    "applied_filters": {
      "status": "Active",
      "date_from_date": "2024-01-01"
    }
  }
}
```

## Business Use Cases

### 1. Team Performance Analysis
```bash
# Get detailed breakdown of each marketer's leads
GET /api/leads/filter/?group_by=marketer&status=Active
```

### 2. Lead Source ROI Analysis
```bash
# Count leads by source to identify top performers
GET /api/leads/filter/?group_by=lead_source&group_count=true&is_converted=true
```

### 3. Pipeline Health Check
```bash
# See distribution across pipeline stages
GET /api/leads/filter/?group_by=pipeline_level&group_count=true&status=Active
```

### 4. Geographic Market Analysis
```bash
# Analyze lead distribution by country
GET /api/leads/filter/?group_by=country&group_count=true&category=Hot,Warm
```

### 5. Department Allocation Review
```bash
# Review how leads are distributed across departments
GET /api/leads/filter/?group_by=department&lead_type=allocated&group_count=true
```

### 6. Quality Assessment
```bash
# Check verification rates by lead source
GET /api/leads/filter/?group_by=is_verified&lead_source=123,456&group_count=true
```

### 7. Conversion Tracking
```bash
# Track conversion rates by category
GET /api/leads/filter/?group_by=is_converted&category=Hot&group_count=true
```

## Statistics Provided

### For Count-Only Grouping (`group_count=true`)
- **Total Records**: Total number of leads matching filters
- **Active Count**: Number of active leads
- **Verified Count**: Number of verified leads
- **Converted Count**: Number of converted leads
- **Groups Count**: Number of distinct groups
- **Percentage**: Each group's percentage of total

### For Detailed Grouping (`group_count=false`)
- **Group Count**: Number of records in each group
- **Full Records**: Complete lead data for each group

## Performance Considerations

### Optimization Tips
1. **Use Count-Only for Large Datasets**: When analyzing thousands of records, use `group_count=true`
2. **Combine with Filters**: Apply filters first to reduce dataset size before grouping
3. **Limit Group Fields**: Some fields like `marketer` or `lead_source` may create many groups

### Recommended Practices
- Use date filters to limit scope when analyzing historical data
- Combine status filters with grouping for active lead analysis
- Use department filters when analyzing team performance

## Error Handling

### Invalid Group Field
```json
{
  "error": "Invalid group_by field. Available options: lead_source, marketer, department, status, category, pipeline_level, country, city, lead_type, is_verified, is_converted"
}
```

### Empty Results
When no records match the filters, grouping will return empty groups array but maintain the response structure.

## Integration with Existing Filters

Grouping works seamlessly with all existing filters:
- Multiple value filters (lead_source, marketer, department)
- Date range filters
- Choice filters (status, category, pipeline_level)
- Boolean filters (is_verified, is_converted)
- Text filters (name, phone, email, city, country)

The grouping functionality provides powerful analytics capabilities while maintaining the flexibility and performance of the existing filtering system.