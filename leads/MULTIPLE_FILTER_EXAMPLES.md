# Multiple Lead Source & Marketer Filtering - Test Examples

## Basic Usage Examples

### 1. Single Lead Source (Backward Compatible)
```
GET /api/leads/filter/?lead_source=123
```
This will filter prospects where lead_source__leadsource_id = 123

### 2. Multiple Lead Sources
```
GET /api/leads/filter/?lead_source=123,456,789
```
This will filter prospects where lead_source__leadsource_id IN (123, 456, 789)

### 3. Single Marketer (Backward Compatible)
```
GET /api/leads/filter/?marketer=EMP001
```
This will filter prospects where marketer__employee_no = 'EMP001'

### 4. Multiple Marketers
```
GET /api/leads/filter/?marketer=EMP001,EMP002,EMP003
```
This will filter prospects where marketer__employee_no IN ('EMP001', 'EMP002', 'EMP003')

### 5. Multiple Departments
```
GET /api/leads/filter/?department=Digital,Telemarketing,Diaspora
```
This will filter prospects where department__dp_name IN ('Digital', 'Telemarketing', 'Diaspora')

## Advanced Lead-Specific Combinations

### 6. Multiple Lead Sources + Pipeline Level
```
GET /api/leads/filter/?lead_source=123,456&pipeline_level=New&category=Hot
```

### 7. Marketer Teams + Status + Date Range
```
GET /api/leads/filter/?marketer=EMP001,EMP002&status=Active&date_from_date=2024-01-01&date_to_date=2024-12-31
```

### 8. Department Allocation Tracking
```
GET /api/leads/filter/?department=Digital,Telemarketing&lead_type=allocated&department_member=EMP005,EMP006
```

### 9. Verification and Conversion Tracking
```
GET /api/leads/filter/?is_verified=true&is_converted=false&category=Hot,Warm&pipeline_level=Site Visits,Booking
```

### 10. Location-Based Filtering
```
GET /api/leads/filter/?country=Kenya&city=Nairobi&lead_source=123,456&marketer=EMP001,EMP002
```

## Complex Business Use Cases

### 11. Regional Performance Analysis
```
GET /api/leads/filter/?lead_source=123,456,789&country=Kenya,Uganda,Tanzania&status=Active&date_from_date=2024-01-01&ordering=-date
```

### 12. Team Performance Tracking
```
GET /api/leads/filter/?department=Digital&marketer=EMP001,EMP002,EMP003&category=Hot,Warm&pipeline_level=New,Qualified Leads,Nurturing&page_size=100
```

### 13. Conversion Funnel Analysis
```
GET /api/leads/filter/?lead_source=123,456&pipeline_level=Booking,Offer Letter,Sale Agreement&is_verified=true&date_from_date=2024-01-01
```

### 14. Lead Source Effectiveness
```
GET /api/leads/filter/?lead_source=123,456,789&category=Hot&status=Active&is_converted=false&pipeline_level=Site Visits,Booking
```

### 15. Diaspora Team Analysis
```
GET /api/leads/filter/?department=Diaspora&lead_type=personal&country=USA,UK,Canada&category=Hot,Warm&ordering=-date&page_size=50
```

## Technical Implementation Details

### Enhanced Filter Classes:
1. **MultipleLeadSourceFilter**: Handles comma-separated lead source IDs
   - Field: `lead_source__leadsource_id`
   - Lookup: `in`
   - Type: Integer list

2. **MultipleMarketerFilter**: Handles comma-separated marketer employee numbers
   - Field: `marketer__employee_no`
   - Lookup: `in`
   - Type: String list

3. **MultipleDepartmentFilter**: Handles comma-separated department names
   - Field: `department__dp_name`
   - Lookup: `in`
   - Type: String list

### Additional Lead-Specific Filters:
- **Lead Source Hierarchy**: category and subcategory filtering
- **Pipeline Management**: All pipeline levels with choice validation
- **Boolean Tracking**: Verification and conversion status
- **Date Range**: Both datetime and date-only filtering
- **Contact Information**: Name, phone, email with partial matching
- **Location**: City and country with case-insensitive partial matching
- **Project Association**: Direct project ID filtering

### SQL Query Examples:
- Single: `WHERE lead_source.leadsource_id = 123`
- Multiple: `WHERE lead_source.leadsource_id IN (123, 456, 789)`
- Complex: `WHERE lead_source.leadsource_id IN (123, 456) AND marketer.employee_no IN ('EMP001', 'EMP002') AND status = 'Active'`

### Response Format with Filter Summary:
```json
{
  "filter_summary": {
    "applied_filters": {
      "lead_source": "123,456,789",
      "marketer": "EMP001,EMP002",
      "department": "Digital,Telemarketing",
      "status": "Active",
      "category": "Hot",
      "pipeline_level": "New"
    },
    "total_without_pagination": 156
  }
}
```

## Error Handling & Validation

### Invalid Lead Source IDs:
Non-numeric values in lead_source parameter are filtered out gracefully.

### Invalid Choice Values:
Choice fields (status, category, pipeline_level) validate against predefined options.

### Date Format Validation:
Date parameters must follow YYYY-MM-DD format for date filters.

### Permission Filtering:
All results respect user permissions through `leads_permission_filters()`.

## Performance Considerations

### Database Optimization:
- Uses efficient SQL IN clauses for multiple value filters
- Proper indexing on frequently filtered fields
- Pagination to handle large result sets

### API Response Optimization:
- Structured response with filter summary
- Minimal data serialization for list views
- Efficient queryset evaluation