
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from customers import views

customer_router = DefaultRouter(trailing_slash=False)
customer_router.register('all-customers', views.CustomerView)
customer_router.register('all-customers-permission-less', views.CustomerViewPermissionLess, basename='customer-permission-less')
customer_router.register('customer-groups', views.CustomerGroupsView)
customer_router.register('Customer-Category', views.CustomerView, basename='customer-category')
urlpatterns = [
    path('customers/', include(customer_router.urls)),
]