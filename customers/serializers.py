from rest_framework import serializers
from customers.models import Customer,CustomerGroups
from users.models import User


class CustomerSerializer(serializers.ModelSerializer):
    """
    Serializer for Customer
    """
    class Meta:
        
        model = Customer
        fields = '__all__'
        
    def to_representation(self, instance):
        rep = super().to_representation(instance)

        # Replace IDs with nested objects
        rep['marketer'] = instance.marketer.fullnames if instance.marketer else None
        rep['lead_source_display'] = instance.lead_source.name if instance.lead_source else None
        rep['lead_source_subcategory_display'] = instance.lead_source.lead_source_subcategory.name if instance.lead_source and instance.lead_source.lead_source_subcategory else None
        return rep




class CustomerGroupsSerializer(serializers.ModelSerializer):
    """
    Serializer for CustomerGroups
    """
    class Meta:
        model = CustomerGroups
        fields = '__all__'



