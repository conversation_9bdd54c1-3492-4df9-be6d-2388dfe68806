# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0001_initial'),
        ('leads', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='customer',
            name='lead_source',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_lead_source', to='leads.leadsource'),
        ),
        migrations.AddField(
            model_name='customer',
            name='marketer',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer_marketer', to=settings.AUTH_USER_MODEL, to_field='employee_no'),
        ),
        migrations.AddField(
            model_name='customergroups',
            name='customer_no',
            field=models.ForeignKey(max_length=50, on_delete=django.db.models.deletion.CASCADE, related_name='customer_groups', to='customers.customer'),
        ),
    ]
