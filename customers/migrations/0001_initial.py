# Generated by Django 5.1.7 on 2025-06-04 12:50

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('customer_no', models.Char<PERSON>ield(max_length=50, primary_key=True, serialize=False)),
                ('customer_name', models.Char<PERSON>ield(max_length=255)),
                ('national_id', models.CharField(blank=True, max_length=50, null=True)),
                ('passport_no', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('kra_pin', models.Char<PERSON>ield(blank=True, max_length=50, null=True)),
                ('dob', models.DateField(blank=True, default=None, max_length=50, null=True)),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female')], max_length=50, null=True)),
                ('marital_status', models.<PERSON><PERSON><PERSON><PERSON>(blank=True, choices=[('Single', 'Single'), ('Married', 'Married'), ('Divorced', 'Divorced'), ('Widowed', 'Widowed')], max_length=50, null=True)),
                ('phone', models.CharField(blank=True, max_length=50, null=True)),
                ('alternative_phone', models.CharField(blank=True, max_length=50, null=True)),
                ('primary_email', models.CharField(blank=True, max_length=255, null=True)),
                ('alternative_email', models.CharField(blank=True, max_length=255, null=True)),
                ('address', models.CharField(blank=True, max_length=255, null=True)),
                ('customer_type', models.CharField(choices=[('Individual', 'Individual'), ('Group', 'Group')], default='Individual', max_length=15)),
                ('country_of_residence', models.CharField(blank=True, max_length=255, null=True)),
                ('date_of_registration', models.DateField(blank=True, default=None, null=True)),
                ('otp', models.CharField(blank=True, default=None, max_length=10, null=True)),
                ('otp_generated_at', models.DateTimeField(blank=True, default=None, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='CustomerGroups',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('member_no', models.CharField(max_length=50)),
                ('customer_name', models.CharField(max_length=255)),
                ('national_id', models.CharField(blank=True, max_length=50, null=True)),
                ('passport_no', models.CharField(blank=True, max_length=50, null=True)),
                ('kra_pin', models.CharField(blank=True, max_length=50, null=True)),
                ('dob', models.DateField(blank=True, null=True)),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female')], max_length=50, null=True)),
                ('phone', models.CharField(blank=True, max_length=50, null=True)),
                ('alternative_phone', models.CharField(blank=True, max_length=50, null=True)),
                ('primary_email', models.CharField(blank=True, max_length=50, null=True)),
                ('alternative_email', models.CharField(blank=True, max_length=50, null=True)),
                ('country_of_residence', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
    ]
