from django.db import models

from leads.models import LeadSource

# Create your models here.

GENDER_CHOICES = [
    ('Male', 'Male'),
    ('Female', 'Female'),
]

MARITAL_CHOICES = [
    ('Single', 'Single'),
    ('Married', 'Married'),
    ('Divorced', 'Divorced'),
    ('Widowed', 'Widowed'),
]

CUSTOMER_TYPE_CHOICES = [
    ('Individual', 'Individual'),
    ('Group', 'Group'),
]

class Customer(models.Model):
    customer_no = models.CharField(max_length=50, null=False, primary_key=True) 
    customer_name = models.CharField(max_length=255,null=False)
    national_id = models.CharField(max_length=50, blank=True, null=True)
    passport_no = models.CharField(max_length=50, blank=True, null=True)
    kra_pin = models.Char<PERSON>ield(max_length=50, blank=True, null=True)
    dob = models.DateField(max_length=50, blank=True, null=True, default=None)
    gender = models.Cha<PERSON><PERSON><PERSON>(max_length=50, choices=GENDER_CHOICES, blank=True, null=True)
    marital_status = models.CharField(max_length=50, choices=MARITAL_CHOICES, blank=True, null=True )
    phone = models.CharField(max_length=50, blank=True, null=True)
    alternative_phone = models.CharField(max_length=50, blank=True, null=True)
    primary_email = models.CharField(max_length=255, blank=True, null=True)
    alternative_email = models.CharField(max_length=255, blank=True, null=True)
    address = models.CharField(max_length=255, blank=True, null=True)
    customer_type = models.CharField(max_length=15, choices=CUSTOMER_TYPE_CHOICES, default='Individual')
    country_of_residence = models.CharField(max_length=255, blank=True, null=True)
    date_of_registration = models.DateField(blank=True, null=True, default=None)
    lead_source = models.ForeignKey(LeadSource, on_delete=models.CASCADE, related_name='customer_lead_source', null=True, blank=True)
    marketer = models.ForeignKey('users.User', to_field='employee_no', on_delete=models.CASCADE, related_name='customer_marketer', null=True, blank=True,default=None)
    otp = models.CharField(max_length=10, blank=True, null=True,default=None)
    otp_generated_at = models.DateTimeField(blank=True, null=True,default=None)

    class Meta:
        ordering = ['-date_of_registration']

    def __str__(self):
        return f'{self.customer_no} - {self.customer_name}'
    

class CustomerGroups(models.Model):
    customer_no = models.ForeignKey(Customer, max_length=50,to_field='customer_no',null=False,on_delete=models.CASCADE, related_name='customer_groups')
    member_no = models.CharField(max_length=50, null=False)
    customer_name = models.CharField(max_length=255, null=False)
    national_id = models.CharField(null=True, blank=True,max_length=50)
    passport_no = models.CharField(null=True, blank=True, max_length=50)
    kra_pin = models.CharField(null=True, blank=True, max_length=50)
    dob = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=50, choices=GENDER_CHOICES, null=True, blank=True)
    phone = models.CharField(max_length=50, null=True, blank=True)
    alternative_phone = models.CharField(max_length=50, null=True, blank=True)
    primary_email = models.CharField(max_length=50, null=True, blank=True)
    alternative_email = models.CharField(max_length=50, null=True, blank=True)
    country_of_residence = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        ordering = ['-customer_no']

    def __str__(self):
        return f'{self.customer_name} - {self.customer_no} - {self.member_no}'
