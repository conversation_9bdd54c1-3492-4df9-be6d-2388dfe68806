from django_filters import rest_framework as filters
from customers.models import Customer
from django import forms

class MultipleLeadSourceFilter(filters.BaseInFilter, filters.NumberFilter):
    """
    Custom filter to handle multiple lead source IDs
    Accepts comma-separated values like: ?lead_source=123,456,789
    """
    pass

class MultipleMarketerFilter(filters.BaseInFilter, filters.CharFilter):
    """
    Custom filter to handle multiple marketer employee numbers
    Accepts comma-separated values like: ?marketer=EMP001,EMP002,EMP003
    """
    pass

class MultipleLeadSourceSubCategoryFilter(filters.BaseInFilter, filters.NumberFilter):
    """
    Custom filter to handle multiple lead source subcategory IDs
    Accepts comma-separated values like: ?lead_source_subcategory=123,456,789
    """
    pass

class CustomerFilter(filters.FilterSet):
    customer_name = filters.CharFilter(lookup_expr='icontains')
    customer_no = filters.CharFilter(lookup_expr='exact')
    national_id = filters.CharFilter(lookup_expr='exact')
    phone = filters.CharFilter(lookup_expr='icontains')
    
    # Multiple marketer filter
    marketer = MultipleMarketerFilter(
        field_name='marketer__employee_no',
        lookup_expr='in',
        help_text='Filter by one or more marketer employee numbers. Use comma-separated values: EMP001,EMP002,EMP003'
    )
    
    # Multiple lead source IDs filter
    lead_source = MultipleLeadSourceFilter(
        field_name='lead_source__leadsource_id', 
        lookup_expr='in',
        help_text='Filter by one or more lead source IDs. Use comma-separated values: 123,456,789'
    )
    # Multiple leadsource_subcategories
    lead_source_subcategory = MultipleLeadSourceSubCategoryFilter(
        field_name='lead_source__lead_source_subcategory__cat_lead_source_id',
        lookup_expr='in',
        help_text='Filter by one or more lead source subcategory IDs. Use comma-separated values: 123,456,789'
    )

    customer_type = filters.ChoiceFilter(choices=[('Individual', 'Individual'), ('Group', 'Group')])
    country_of_residence = filters.CharFilter(lookup_expr='icontains')
    
    # Date range filters for date_of_registration
    date_of_registration_from = filters.DateFilter(
        field_name='date_of_registration', 
        lookup_expr='gte',
        widget=forms.DateInput(attrs={'type': 'date'})
    )
    date_of_registration_to = filters.DateFilter(
        field_name='date_of_registration', 
        lookup_expr='lte',
        widget=forms.DateInput(attrs={'type': 'date'})
    )

    class Meta:
        model = Customer
        fields = [
            'customer_name', 'customer_no', 'national_id', 'phone', 
            'marketer', 'lead_source', 'lead_source_subcategory', 'customer_type', 'country_of_residence',
            'date_of_registration_from', 'date_of_registration_to'
        ]
