from django_filters import rest_framework as filters
from customers.models import Customer

class CustomerFilter(filters.FilterSet):
    customer_name = filters.CharFilter(lookup_expr='icontains')
    customer_no = filters.CharFilter(lookup_expr='exact')
    national_id = filters.CharFilter(lookup_expr='exact')
    phone = filters.CharFilter(lookup_expr='icontains')
    marketer = filters.CharFilter(lookup_expr='exact')
    lead_source = filters.CharFilter(lookup_expr='exact')

    class Meta:
        model = Customer
        fields = ['customer_name', 'customer_no', 'national_id', 'phone', 'marketer', 'lead_source']
