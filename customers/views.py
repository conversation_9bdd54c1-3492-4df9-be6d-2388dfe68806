from django.shortcuts import render

# Create your views here.
from customers.filterset import CustomerFilter
from customers.models import Customer,CustomerGroups
from customers.serializers import CustomerSerializer,CustomerGroupsSerializer
from rest_framework.response import Response
from rest_framework import mixins, viewsets
from rest_framework.permissions import IsAuth<PERSON>icated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.conf import settings
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from config.perm_filters import customers_permission_filters
from rest_framework.exceptions import PermissionDenied
from django.db.models import Sum,Count, Q, F, Case, When, IntegerField
from sales.models import LeadFile

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve', ]
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


# Create your views here.
class CustomerView(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for Customer with only list and retrieve actions
    """
    queryset = Customer.objects.none()
    serializer_class = CustomerSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['customer_no', 'customer_name', 'national_id', 'phone','marketer__employee_no', 'lead_source__leadsource_id', 'alternative_phone', 'marketer__fullnames']
    filterset_fields = ['customer_no', 'customer_name', 'national_id', 'phone', 'marketer', 'lead_source', 'alternative_phone']
    ordering_fields = ['customer_no', 'customer_name', 'national_id', 'phone', 'marketer',  'lead_source']

    ordering = ['-customer_no']

    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = Customer.objects.all()
        queryset = customers_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have rights to view these sales records or you may have more that one right .")
        
        return queryset

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return CustomerSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Customer'], operation_description="List, filter, search, order Customer",
                         manual_parameters=[
            openapi.Parameter(
                name='category',in_=openapi.IN_QUERY,description='Returns customers based on categorization: ACTIVE, COMPLETED, DROPPED.',
                type=openapi.TYPE_STRING,enum=['ACTIVE', 'COMPLETED', 'DROPPED','ALL'],required=False,default='ALL',
            )
        ], )
    def list(self, request, *args, **kwargs):
        category = request.query_params.get("category", "ALL").upper()

        if category not in ["ACTIVE", "COMPLETED", "DROPPED", "ALL"]:
            return Response({"error": "Invalid category. Must be one of: ALL, ACTIVE, COMPLETED, DROPPED."}, status=400)

        queryset = self.get_queryset()

        # # Annotate with total balance across all related lead files
        # queryset = queryset.annotate(
        #     total_balance=Sum('lead_file_customer__balance_lcy'),
        #     total_leads=Count('lead_file_customer', distinct=True),
        #     dropped_leads=Count('lead_file_customer', filter=Q(lead_file_customer__lead_file_status_dropped=True), distinct=True)
        # )

        # if category == "ACTIVE":
        #     queryset = queryset.filter(total_balance__gt=0)

        # elif category == "COMPLETED":
        #     queryset = queryset.filter(total_balance__lte=0)

        # elif category == "DROPPED":
        #     queryset = queryset.filter(total_leads=F('dropped_leads'))
        # elif category == "DROPPED":
        #     pass

        # print(queryset.query)  # Print the SQL query for preview
        self.queryset = queryset
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Customer'], operation_description="Retrieve Customer")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class CustomerViewPermissionLess(viewsets.ModelViewSet):
    """
    ViewSet for Customer with only list and retrieve actions
    """
    queryset = Customer.objects.only('customer_no', 'customer_name', 'primary_email', 'national_id', 'phone', 'kra_pin', 'passport_no')
    serializer_class = CustomerSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['customer_no', 'customer_name', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no', 'marketer__fullnames']
    filterset_fields = ['customer_no', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no']
    ordering_fields = ['customer_no', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no']
    http_method_names = ['get']
    
    @swagger_auto_schema( tags=['Customer'],operation_description="List, filter,search,order customers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Customer'],operation_description="customers by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
      

    

# Create your views here.
class CustomerGroupsView(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for CustomerGroups with only list and retrieve actions
    """
    queryset = CustomerGroups.objects.all()
    serializer_class = CustomerGroupsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = []
    search_fields = []
    ordering_fields = []


    def get_serializer_class(self):
        if self.action == 'retrieve':
            return CustomerGroupsSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Customer'], operation_description="List, filter, search, order CustomerGroups members")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Customer'], operation_description="Retrieve CustomerGroups members")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

   
    

 

