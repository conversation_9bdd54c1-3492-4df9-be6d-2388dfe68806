from django.shortcuts import render

# Create your views here.
from customers.filterset import CustomerFilter
from customers.models import Customer,CustomerGroups
from customers.serializers import CustomerSerializer,CustomerGroupsSerializer
from rest_framework.response import Response
from rest_framework import mixins, viewsets
from rest_framework.permissions import IsAuth<PERSON>icated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.conf import settings
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from config.perm_filters import customers_permission_filters
from rest_framework.exceptions import PermissionDenied
from django.db.models import Sum,Count, Q, F, Case, When, IntegerField
from sales.models import LeadFile
from utils.export_mixins import ReportExportMixin, AdvancedReportExportMixin

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve', ]
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


# Create your views here.
class CustomerView(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for Customer with only list and retrieve actions
    """
    queryset = Customer.objects.none()
    serializer_class = CustomerSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['customer_no', 'customer_name', 'national_id', 'phone','marketer__employee_no', 'lead_source__leadsource_id', 'alternative_phone', 'marketer__fullnames']
    filterset_class = CustomerFilter
    ordering_fields = ['customer_no', 'customer_name', 'national_id', 'phone', 'marketer',  'lead_source']

    ordering = ['-customer_no']

    
    def get_queryset(self):
        if getattr(self, 'swagger_fake_view', False):  # <-- important check
            return self.queryset.none()
        base_queryset = Customer.objects.all()
        queryset = customers_permission_filters(self.request.user, base_queryset)
        if queryset is False:
            raise PermissionDenied("You do not have rights to view these sales records or you may have more that one right .")
        
        return queryset

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return CustomerSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Customer'], operation_description="List, filter, search, order Customer",
                         manual_parameters=[
            openapi.Parameter(
                name='category',in_=openapi.IN_QUERY,description='Returns customers based on categorization: ACTIVE, COMPLETED, DROPPED.',
                type=openapi.TYPE_STRING,enum=['ACTIVE', 'COMPLETED', 'DROPPED','ALL'],required=False,default='ALL',
            )
        ], )
    def list(self, request, *args, **kwargs):
        category = request.query_params.get("category", "ALL").upper()

        if category not in ["ACTIVE", "COMPLETED", "DROPPED", "ALL"]:
            return Response({"error": "Invalid category. Must be one of: ALL, ACTIVE, COMPLETED, DROPPED."}, status=400)

        queryset = self.get_queryset()

        # # Annotate with total balance across all related lead files
        # queryset = queryset.annotate(
        #     total_balance=Sum('lead_file_customer__balance_lcy'),
        #     total_leads=Count('lead_file_customer', distinct=True),
        #     dropped_leads=Count('lead_file_customer', filter=Q(lead_file_customer__lead_file_status_dropped=True), distinct=True)
        # )

        # if category == "ACTIVE":
        #     queryset = queryset.filter(total_balance__gt=0)

        # elif category == "COMPLETED":
        #     queryset = queryset.filter(total_balance__lte=0)

        # elif category == "DROPPED":
        #     queryset = queryset.filter(total_leads=F('dropped_leads'))
        # elif category == "DROPPED":
        #     pass

        # print(queryset.query)  # Print the SQL query for preview
        self.queryset = queryset
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Customer'], operation_description="Retrieve Customer")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    

class CustomerViewPermissionLess(viewsets.ModelViewSet):
    """
    ViewSet for Customer with only list and retrieve actions
    """
    queryset = Customer.objects.only('customer_no', 'customer_name', 'primary_email', 'national_id', 'phone', 'kra_pin', 'passport_no')
    serializer_class = CustomerSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['customer_no', 'customer_name', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no', 'marketer__fullnames']
    filterset_fields = ['customer_no', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no']
    ordering_fields = ['customer_no', 'primary_email', 'national_id', 'phone','kra_pin', 'passport_no']
    http_method_names = ['get']
    
    @swagger_auto_schema( tags=['Customer'],operation_description="List, filter,search,order customers")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Customer'],operation_description="customers by id")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
      

    

# Create your views here.
class CustomerGroupsView(viewsets.ReadOnlyModelViewSet):
    """
    Viewset for CustomerGroups with only list and retrieve actions
    """
    queryset = CustomerGroups.objects.all()
    serializer_class = CustomerGroupsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = []
    search_fields = []
    ordering_fields = []


    def get_serializer_class(self):
        if self.action == 'retrieve':
            return CustomerGroupsSerializer
        return super().get_serializer_class()
    

    @swagger_auto_schema(tags=['Customer'], operation_description="List, filter, search, order CustomerGroups members")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    
    @swagger_auto_schema(tags=['Customer'], operation_description="Retrieve CustomerGroups members")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)


class CustomerfilterView(AdvancedReportExportMixin, viewsets.ViewSet):
    """
    A ViewSet for filtering customers with comprehensive filter options and export capabilities.
    Provides advanced filtering capabilities for lead_source, marketer, 
    date_of_registration (from and to), customer_type, and country_of_residence.
    
    Export Features:
    - PDF reports with custom templates
    - Excel exports with multiple sheets
    - Advanced Excel reports with statistics
    """
    
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    # Export configuration
    export_template_name = 'exports/customer_report.html'
    export_filename_prefix = 'customer_report'
    export_title = 'Customer Data Report'
    
    # Serializer configuration for export functionality
    serializer_class = CustomerSerializer

    def get_serializer(self, *args, **kwargs):
        """Return serializer instance for export functionality"""
        return self.serializer_class(*args, **kwargs)
    
    def get_serializer_class(self):
        """Return serializer class for export functionality"""
        return self.serializer_class

    def get_queryset(self):
        """Get filtered queryset based on user permissions"""
        if getattr(self, 'swagger_fake_view', False):
            return Customer.objects.none()
        
        base_queryset = Customer.objects.all()
        # queryset = customers_permission_filters(self.request.user, base_queryset)
        # if queryset is False:
        #     raise PermissionDenied("You do not have rights to view these customer records.")
        queryset=base_queryset
        return queryset

    def get_export_queryset(self, request):
        """Get filtered queryset for export - completely custom filtering to avoid 'replace' error"""
        from django_filters.rest_framework import DjangoFilterBackend
        
        # Get base queryset
        queryset = self.get_queryset()
        
        # Apply CustomerFilter manually to avoid any backend issues
        try:
            customer_filter = CustomerFilter(request.GET, queryset=queryset)
            filtered_queryset = customer_filter.qs
        except Exception as e:
            # If filtering fails, fall back to base queryset
            print(f"Filtering error: {e}")
            filtered_queryset = queryset
        
        return filtered_queryset

    def get_export_data(self, queryset):
        """Transform queryset into export-ready data."""
        # Use list(queryset) to convert queryset to a list of instances
        serializer = self.get_serializer(list(queryset), many=True)
        return serializer.data

    def get_export_statistics(self, queryset, request):
        """Generate customer-specific statistics for exports."""
        total_count = queryset.count()
        individual_count = queryset.filter(customer_type='Individual').count()
        group_count = queryset.filter(customer_type='Group').count()
        
        # Gender statistics
        male_count = queryset.filter(gender='Male').count()
        female_count = queryset.filter(gender='Female').count()
        
        # Registration statistics by year
        from django.utils import timezone
        current_year = timezone.now().year
        current_year_count = queryset.filter(date_of_registration__year=current_year).count()
        
        return {
            'total_customers': total_count,
            'individual_customers': individual_count,
            'group_customers': group_count,
            'male_customers': male_count,
            'female_customers': female_count,
            'registered_this_year': current_year_count,
            'export_date': timezone.now(),
            'applied_filters': self.get_applied_filters(request),
        }
    
    def get_applied_filters(self, request):
        """Extract customer-specific applied filters."""
        filters = {}
        
        # Customer-specific filter parameters
        customer_filter_params = [
            'lead_source', 'marketer', 'customer_type', 'country_of_residence',
            'date_of_registration_from', 'date_of_registration_to',
            'customer_name', 'phone', 'gender', 'marital_status',
            'national_id', 'primary_email', 'group_by', 'group_count'
        ]
        
        for param in customer_filter_params:
            if request.query_params.get(param):
                filters[param] = request.query_params.get(param)
        
        return filters

    @swagger_auto_schema(
        tags=['Customer Filters'],
        operation_description="""Filter customers with advanced filtering options and export capabilities.
        
        🔍 **DATA FILTERING EXAMPLES:**
        
        **Basic Filtering:**
        - Filter by single lead source: `/api/customers/filter/?lead_source=123`
        - Filter by multiple lead sources: `/api/customers/filter/?lead_source=123,456,789`
        - Filter by single lead source subcategory: `/api/customers/filter/?lead_source_subcategory=456`
        - Filter by multiple lead source subcategories: `/api/customers/filter/?lead_source_subcategory=456,789`
        - Filter by marketer: `/api/customers/filter/?marketer=EMP001,EMP002`
        - Filter by customer type: `/api/customers/filter/?customer_type=Individual`
        - Filter by country: `/api/customers/filter/?country_of_residence=Kenya`
        - Filter by gender: `/api/customers/filter/?gender=Female`
        - Filter by date range: `/api/customers/filter/?date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31`
        
        **Advanced Filtering:**
        - Multiple filters: `/api/customers/filter/?customer_type=Individual&gender=Female&country_of_residence=Kenya`
        - Search with filters: `/api/customers/filter/?search=John&customer_type=Individual`
        - Ordered results: `/api/customers/filter/?ordering=-date_of_registration&customer_type=Group`
        - Paginated results: `/api/customers/filter/?page=2&page_size=50&marketer=EMP001`
        
        **Grouping & Analytics:**
        - Group by customer type: `/api/customers/filter/?group_by=customer_type&group_count=true`
        - Group by marketer with counts: `/api/customers/filter/?group_by=marketer&group_count=true`
        - Filtered grouping: `/api/customers/filter/?country_of_residence=Kenya&group_by=lead_source&group_count=true`
        - Gender distribution: `/api/customers/filter/?group_by=gender&group_count=true`
        - Demographic analysis: `/api/customers/filter/?customer_type=Individual&group_by=marital_status&group_count=true`
        
        📊 **EXPORT FUNCTIONALITY:**
        
        **PDF Export (Professional Reports):**
        - Export all customers: `/api/customers/filter/export_pdf/`
        - Export filtered data: `/api/customers/filter/export_pdf/?customer_type=Individual&country_of_residence=Kenya`
        - Custom template: `/api/customers/filter/export_pdf/?template=custom_customer_report.html`
        - Complex filters: `/api/customers/filter/export_pdf/?lead_source=123,456&marketer=EMP001&date_of_registration_from=2024-01-01`
        
        **Excel Export (Spreadsheet Format):**
        - Basic Excel export: `/api/customers/filter/export_excel/`
        - Custom sheet name: `/api/customers/filter/export_excel/?sheet_name=Active Customers`
        - Without metadata: `/api/customers/filter/export_excel/?include_metadata=false`
        - Filtered Excel: `/api/customers/filter/export_excel/?customer_type=Group&sheet_name=Group Customers`
        
        **Advanced Excel Export (Multi-sheet with Statistics):**
        - Full advanced export: `/api/customers/filter/export_advanced_excel/`
        - With filters: `/api/customers/filter/export_advanced_excel/?gender=Female&customer_type=Individual`
        - Marketer analysis: `/api/customers/filter/export_advanced_excel/?marketer=EMP001,EMP002&group_by=lead_source`
        - Geographic analysis: `/api/customers/filter/export_advanced_excel/?country_of_residence=Kenya,Uganda`
        
        **Real-world Use Cases:**
        1. **Marketing Campaign Analysis:** `/api/customers/filter/?lead_source=123&group_by=country_of_residence&group_count=true`
        2. **Marketer Performance:** `/api/customers/filter/export_advanced_excel/?marketer=EMP001&date_of_registration_from=2024-01-01`
        3. **Demographic Report:** `/api/customers/filter/export_pdf/?group_by=gender&customer_type=Individual`
        4. **Regional Analysis:** `/api/customers/filter/?country_of_residence=Kenya&group_by=marketer&group_count=true`
        5. **Monthly Registration Report:** `/api/customers/filter/export_excel/?date_of_registration_from=2024-10-01&date_of_registration_to=2024-10-31&sheet_name=October 2024`
        
        💡 **Export Features:**
        - PDF: Professional layouts, company branding, filter documentation, statistics summaries
        - Excel: Multi-sheet workbooks, auto-sized columns, formatted headers, metadata sheets
        - Advanced Excel: Additional statistics sheet with demographics, registration trends, and performance metrics
        - All exports respect the same filters and permissions as the API endpoint
        """,
        manual_parameters=[
            openapi.Parameter(
                name='lead_source',
                in_=openapi.IN_QUERY,
                description='Filter by one or more lead source IDs. For multiple IDs, use comma-separated values (e.g., 123,456,789)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='lead_source_subcategory',
                in_=openapi.IN_QUERY,
                description='Filter by one or more lead source subcategory IDs. For multiple IDs, use comma-separated values (e.g., 123,456,789)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='marketer',
                in_=openapi.IN_QUERY,
                description='Filter by one or more marketer employee numbers. For multiple marketers, use comma-separated values (e.g., EMP001,EMP002,EMP003)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='date_of_registration_from',
                in_=openapi.IN_QUERY,
                description='Filter customers registered from this date onwards (YYYY-MM-DD format)',
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
            ),
            openapi.Parameter(
                name='date_of_registration_to',
                in_=openapi.IN_QUERY,
                description='Filter customers registered up to this date (YYYY-MM-DD format)',
                type=openapi.TYPE_STRING,
                format=openapi.FORMAT_DATE,
                required=False,
            ),
            openapi.Parameter(
                name='customer_type',
                in_=openapi.IN_QUERY,
                description='Filter by customer type',
                type=openapi.TYPE_STRING,
                enum=['Individual', 'Group'],
                required=False,
            ),
            openapi.Parameter(
                name='country_of_residence',
                in_=openapi.IN_QUERY,
                description='Filter by country of residence (partial match, case insensitive)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='customer_name',
                in_=openapi.IN_QUERY,
                description='Filter by customer name (partial match, case insensitive)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='phone',
                in_=openapi.IN_QUERY,
                description='Filter by phone number (partial match)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='gender',
                in_=openapi.IN_QUERY,
                description='Filter by gender',
                type=openapi.TYPE_STRING,
                enum=['Male', 'Female'],
                required=False,
            ),
            openapi.Parameter(
                name='marital_status',
                in_=openapi.IN_QUERY,
                description='Filter by marital status',
                type=openapi.TYPE_STRING,
                enum=['Single', 'Married', 'Divorced', 'Widowed'],
                required=False,
            ),
            openapi.Parameter(
                name='national_id',
                in_=openapi.IN_QUERY,
                description='Filter by national ID (exact match)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='primary_email',
                in_=openapi.IN_QUERY,
                description='Filter by primary email (partial match)',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='ordering',
                in_=openapi.IN_QUERY,
                description='Order results by field. Prefix with "-" for descending order. Available fields: customer_no, customer_name, date_of_registration',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='page',
                in_=openapi.IN_QUERY,
                description='Page number for pagination',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='page_size',
                in_=openapi.IN_QUERY,
                description='Number of results per page (max 100)',
                type=openapi.TYPE_INTEGER,
                required=False,
            ),
            openapi.Parameter(
                name='group_by',
                in_=openapi.IN_QUERY,
                description='Group results by field. Available fields: lead_source, lead_source_subcategory, marketer, customer_type, country_of_residence, date_of_registration, gender, marital_status',
                type=openapi.TYPE_STRING,
                enum=['lead_source', 'lead_source_subcategory', 'marketer', 'customer_type', 'country_of_residence', 'date_of_registration', 'gender', 'marital_status'],
                required=False,
            ),
            openapi.Parameter(
                name='group_count',
                in_=openapi.IN_QUERY,
                description='When group_by is used, return only count statistics instead of detailed records (true/false)',
                type=openapi.TYPE_BOOLEAN,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Successfully filtered customers",
                schema=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'count': openapi.Schema(type=openapi.TYPE_INTEGER, description='Total number of customers matching filters'),
                        'next': openapi.Schema(type=openapi.TYPE_STRING, description='URL for next page of results'),
                        'previous': openapi.Schema(type=openapi.TYPE_STRING, description='URL for previous page of results'),
                        'results': openapi.Schema(
                            type=openapi.TYPE_ARRAY,
                            items=openapi.Schema(
                                type=openapi.TYPE_OBJECT,
                                properties={
                                    'customer_no': openapi.Schema(type=openapi.TYPE_STRING),
                                    'customer_name': openapi.Schema(type=openapi.TYPE_STRING),
                                    'customer_type': openapi.Schema(type=openapi.TYPE_STRING),
                                    'country_of_residence': openapi.Schema(type=openapi.TYPE_STRING),
                                    'date_of_registration': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE),
                                    'lead_source': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'lead_source_subcategory': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'marketer': openapi.Schema(type=openapi.TYPE_OBJECT),
                                    'phone': openapi.Schema(type=openapi.TYPE_STRING),
                                    'primary_email': openapi.Schema(type=openapi.TYPE_STRING),
                                }
                            )
                        )
                    }
                )
            ),
            400: openapi.Response(description="Bad request - Invalid filter parameters"),
            403: openapi.Response(description="Permission denied"),
        }
    )
    def list(self, request):
        """
        List customers with filtering options and grouping capabilities.
        
        Filter Examples:
        - /api/customers/filter/?lead_source=123 - Filter by single lead source ID
        - /api/customers/filter/?lead_source=123,456,789 - Filter by multiple lead source IDs
        - /api/customers/filter/?marketer=EMP001 - Filter by single marketer employee number
        - /api/customers/filter/?marketer=EMP001,EMP002,EMP003 - Filter by multiple marketer employee numbers
        - /api/customers/filter/?date_of_registration_from=2024-01-01&date_of_registration_to=2024-12-31 - Filter by date range
        - /api/customers/filter/?customer_type=Individual - Filter by customer type
        - /api/customers/filter/?country_of_residence=Kenya - Filter by country (partial match)
        - /api/customers/filter/?gender=Female&marital_status=Single - Filter by demographics
        - /api/customers/filter/?customer_name=John&customer_type=Individual&ordering=-date_of_registration - Multiple filters with ordering
        - /api/customers/filter/?lead_source=123,456&customer_type=Individual&marketer=EMP001,EMP002 - Complex filtering with multiple lead sources and marketers
        -

        Grouping Examples:
        - /api/customers/filter/?group_by=customer_type - Group results by customer type
        - /api/customers/filter/?group_by=marketer&group_count=true - Get count by marketer
        - /api/customers/filter/?country_of_residence=Kenya&group_by=lead_source&group_count=true - Filter and group by lead source
        - /api/customers/filter/?group_by=lead_source_subcategory&group_count=true - Analyze customer lead source subcategory distribution
        - /api/customers/filter/?group_by=gender&group_count=true - Analyze customer gender distribution
        - /api/customers/filter/?group_by=marital_status&customer_type=Individual&group_count=true - Marital status analysis for individuals
        - /api/customers/filter/?group_by=country_of_residence&group_count=true - Geographic distribution analysis
        """
        from django_filters.rest_framework import DjangoFilterBackend
        from rest_framework import filters
        from rest_framework.pagination import PageNumberPagination
        from django.db.models import Count, Q, Case, When, Value, CharField
        from django.db.models.functions import Coalesce
        
        # Get parameters
        group_by = request.query_params.get('group_by')
        group_count = request.query_params.get('group_count', 'false').lower() == 'true'
        
        # Define available grouping fields with their database paths
        grouping_fields = {
            'lead_source': 'lead_source__name',
            'lead_source_id': 'lead_source__leadsource_id',
            'lead_source_subcategory': 'lead_source__lead_source_subcategory__name',
            'lead_source_subcategory_id': 'lead_source__lead_source_subcategory__cat_lead_source_id',
            'marketer': 'marketer__fullnames',
            'marketer_id': 'marketer__employee_no',
            'customer_type': 'customer_type',
            'country_of_residence': 'country_of_residence',
            'date_of_registration': 'date_of_registration',
            'gender': 'gender',
            'marital_status': 'marital_status'
        }
        
        # Get queryset
        queryset = self.get_queryset()
        
        # Apply filters using the CustomerFilter
        filter_backend = DjangoFilterBackend()
        filtered_queryset = filter_backend.filter_queryset(request, queryset, self)
        
        # Apply search if provided (only if search parameter exists)
        if request.query_params.get('search'):
            search_backend = filters.SearchFilter()
            filtered_queryset = search_backend.filter_queryset(request, filtered_queryset, self)
        
        # Handle grouping
        if group_by and group_by in grouping_fields:
            group_field = grouping_fields[group_by]
            
            if group_count:
                # Return grouped counts
                grouped_data = (
                    filtered_queryset
                    .values(group_field)
                    .annotate(
                        count=Count('customer_no'),
                        group_name=Case(
                            When(**{f'{group_field}__isnull': True}, then=Value('Not Specified')),
                            When(**{f'{group_field}__exact': ''}, then=Value('Not Specified')),
                            default=group_field,
                            output_field=CharField()
                        )
                    )
                    .order_by('-count')
                )
                
                # Calculate additional statistics
                total_count = filtered_queryset.count()
                individual_count = filtered_queryset.filter(customer_type='Individual').count()
                group_customer_count = filtered_queryset.filter(customer_type='Group').count()
                male_count = filtered_queryset.filter(gender='Male').count()
                female_count = filtered_queryset.filter(gender='Female').count()
                
                return Response({
                    'group_by': group_by,
                    'group_count': True,
                    'total_records': total_count,
                    'statistics': {
                        'total': total_count,
                        'individual': individual_count,
                        'group_customers': group_customer_count,
                        'male': male_count,
                        'female': female_count,
                        'groups': len(grouped_data)
                    },
                    'groups': [
                        {
                            'group_value': item['group_name'] or 'Not Specified',
                            'count': item['count'],
                            'percentage': round((item['count'] / total_count * 100), 2) if total_count > 0 else 0
                        }
                        for item in grouped_data
                    ],
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'lead_source', 'lead_source_subcategory', 'marketer', 'customer_type', 'country_of_residence',
                                'date_of_registration_from', 'date_of_registration_to'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
            else:
                # Return grouped detailed records
                grouped_data = {}
                
                # Get distinct group values
                group_values = (
                    filtered_queryset
                    .values_list(group_field, flat=True)
                    .distinct()
                    .order_by(group_field)
                )
                
                for group_value in group_values:
                    group_name = group_value or 'Not Specified'
                    
                    # Filter records for this group
                    if group_value is None or group_value == '':
                        group_queryset = filtered_queryset.filter(
                            Q(**{f'{group_field}__isnull': True}) | Q(**{f'{group_field}__exact': ''})
                        )
                    else:
                        group_queryset = filtered_queryset.filter(**{group_field: group_value})
                    
                    # Apply ordering to group
                    ordering_backend = filters.OrderingFilter()
                    ordering_fields = ['customer_no', 'customer_name', 'date_of_registration', 'customer_type']
                    group_queryset = ordering_backend.filter_queryset(request, group_queryset, self)
                    
                    # Serialize group data
                    serializer = CustomerSerializer(group_queryset, many=True)
                    
                    grouped_data[group_name] = {
                        'count': group_queryset.count(),
                        'records': serializer.data
                    }
                
                return Response({
                    'group_by': group_by,
                    'group_count': False,
                    'total_records': filtered_queryset.count(),
                    'groups': grouped_data,
                    'filter_summary': {
                        'applied_filters': {
                            param: request.query_params.get(param)
                            for param in [
                                'lead_source', 'lead_source_subcategory', 'marketer', 'customer_type', 'country_of_residence',
                                'date_of_registration_from', 'date_of_registration_to'
                            ]
                            if request.query_params.get(param)
                        }
                    }
                })
        
        # Standard filtering without grouping
        # Apply ordering
        ordering_backend = filters.OrderingFilter()
        ordering_fields = ['customer_no', 'customer_name', 'date_of_registration', 'customer_type']
        filtered_queryset = ordering_backend.filter_queryset(request, filtered_queryset, self)
        
        # Set up pagination
        paginator = PageNumberPagination()
        paginator.page_size = min(int(request.query_params.get('page_size', 20)), 100)
        page = paginator.paginate_queryset(filtered_queryset, request)
        
        # Serialize data
        serializer = CustomerSerializer(page, many=True)
        
        # Return paginated response with filter summary
        response_data = paginator.get_paginated_response(serializer.data)
        
        # Add filter summary to response
        filter_summary = {
            'applied_filters': {},
            'total_without_pagination': filtered_queryset.count()
        }
        
        # Extract applied filters from request
        filter_params = ['lead_source', 'lead_source_subcategory', 'marketer', 'date_of_registration_from',
                        'date_of_registration_to', 'customer_type', 'country_of_residence']
        
        for param in filter_params:
            if request.query_params.get(param):
                filter_summary['applied_filters'][param] = request.query_params.get(param)
        
        response_data.data['filter_summary'] = filter_summary
        
        return response_data

    # Make filter class available to DjangoFilterBackend
    filterset_class = CustomerFilter
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['customer_name', 'customer_no', 'phone', 'primary_email']
    ordering_fields = ['customer_no', 'customer_name', 'date_of_registration', 'customer_type']

   
    

 

