from os import read
from rest_framework import serializers
from .models import Engagement
from customers.serializers import CustomerSerializer
from leads.serializers import ProspectsSerializer
from sales.serializers import LeadFileSerializer
from users.serializers import UserSerializer 

class EngagementSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = Engagement
        fields = '__all__'
        read_only_fields = ['engagement_id', 'created_at', 'updated_at']
        
    def to_representation(self, instance):
        """Customize how instance is serialized when doing GET"""
        rep = super().to_representation(instance)

        # Replace IDs with nested objects
        rep['customer'] = instance.customer.customer_name if instance.customer else None
        rep['prospect'] = instance.prospect.name if instance.prospect else None
        rep['sale'] = instance.sale.plot.plotId if instance.sale else None
        rep['created_by'] = instance.created_by.fullnames if instance.created_by else None

        return rep
    
    
