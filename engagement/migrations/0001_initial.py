# Generated by Django 5.1.7 on 2025-07-25 09:32

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Engagement',
            fields=[
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', help_text='Current status of the client in their journey', max_length=20)),
                ('engagement_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('engagement_type', models.CharField(choices=[('Call', 'Call'), ('Email', 'Email'), ('Meeting', 'Meeting'), ('SMS', 'SMS'), ('Chat', 'Chat'), ('Visit', 'Visit'), ('Event', 'Event'), ('Follow-up', 'Follow-up'), ('Contact-add', 'Contact-add')], max_length=50)),
                ('subject', models.CharField(max_length=255)),
                ('description', models.TextField()),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('set_reminder', models.BooleanField(default=False)),
                ('reminder_time', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='engagements_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='engagements', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_engagements', to='leads.prospects')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_engagements', to='sales.leadfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
