from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer
from leads.models import Prospects
from sales.models import LeadFile
from engagement.utils import generate_engagement_id



class Engagement(models.Model):
    ENGAGEMENT_TYPE_CHOICES = [
        ('Call', 'Call'),
        ('Email', 'Email'),
        ('Meeting', 'Meeting'),
        ('SMS', 'SMS'),
        ('Chat', 'Chat'),
        ('Visit', 'Visit'),
        ('Event', 'Event'),
        ('Follow-up', 'Follow-up'),
        ('Contact-add', 'Contact-add'),
    ]
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]
    
   
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect',help_text="Current status of the client in their journey")
    engagement_id = models.CharField(max_length=50, unique=True, primary_key=True)
    engagement_type = models.CharField(max_length=50, choices=ENGAGEMENT_TYPE_CHOICES)
    subject = models.CharField(max_length=255)
    description = models.TextField()
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(blank=True, null=True)
    set_reminder = models.BooleanField(default=False)
    reminder_time = models.DateTimeField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='engagements_created', to_field='employee_no')

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, related_name='engagements', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_engagements',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_engagements', to_field='lead_file_no', null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure engagement_id is unique"""
        
        if not self.engagement_id:
            self.engagement_id = generate_engagement_id()
            while Engagement.objects.filter(engagement_id=self.engagement_id).exists():
                self.engagement_id = generate_engagement_id()
        print(f"Saving Engagement: {self.subject} with ID: {self.engagement_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.engagement_id} - {self.subject}"

    class Meta:
        ordering = ['-created_at']

