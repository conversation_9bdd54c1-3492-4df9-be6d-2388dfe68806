from celery import shared_task
from django.db import transaction
from decimal import Decimal
from datetime import datetime
import logging
from inventory.models import Project, Plot
from customers.models import Customer, LeadSource
from users.models import User
from sales.models import LeadFile
from leads.models import LeadSourceCategory
from .helpers import retrieve_lead_file

logger = logging.getLogger(__name__)


def normalize_payload(payload: dict) -> dict:
    """Normalize ERP keys to consistent naming + cleanup values."""
    normalized = {}

    # Normalize keys: some ERP responses send Plot_No vs Plot_NO
    key_map = {
        "Plot_NO": "Plot_No",
        "plot_no": "Plot_No",
        "Customer_Id": "Customer_Id",
        "customer_id": "Customer_Id",
        "ProjectId": "ProjectId",
        "project_id": "ProjectId",
        "Marketer": "Marketer",
    }

    for k, v in payload.items():
        normalized[key_map.get(k, k)] = v

    return normalized


def parse_decimal(val):
    if val in (None, "", "null"):
        return Decimal(0)
    try:
        return Decimal(str(val).replace(",", ""))
    except Exception:
        return Decimal(0)


def parse_date(val):
    if not val:
        return None
    try:
        return datetime.strptime(val, "%Y-%m-%d").date()
    except Exception:
        return None


@shared_task(bind=True, max_retries=3)
def sync_leadfile_from_erp(self, lead_no: str):
    payload, err = retrieve_lead_file(lead_no)
    if err:
        logger.error(f"ERP fetch error for {lead_no}: {err}")
        raise self.retry(exc=Exception(str(err)), countdown=2 ** self.request.retries)

    if not payload:
        return f"LeadFile {lead_no}: No data returned from ERP"

    payload = normalize_payload(payload)

    try:
        with transaction.atomic():
            # --- FK lookups ---
            project = Project.objects.filter(projectId=payload.get("Project_No")).first()
            if not project:
                raise ValueError(f"Project {payload.get('Project_No')} not found in CRM")

            plot = Plot.objects.filter(plotId=payload.get("Plot_No")).first()
            if not plot:
                raise ValueError(f"Plot {payload.get('Plot_No')} not found in CRM")

            customer = Customer.objects.filter(customer_no=payload.get("Member_No")).first()
            if not customer:
                raise ValueError(f"Customer {payload.get('Member_No')} not found in CRM")

            marketer = User.objects.filter(employee_no=payload.get("Sales_Person_Code")).first()
            if not marketer:
                raise ValueError(f"User {payload.get('Sales_Person_Code')} not found in CRM")

            lead_source = LeadSource.objects.filter(name=payload.get("Lead_Source")).first()
            cat_lead_source = LeadSourceCategory.objects.filter(name=payload.get("Campaign_Source_of_Lead")).first()

            # --- LeadFile upsert ---
            LeadFile.objects.update_or_create(
                lead_file_no=lead_no,
                defaults={
                    "plot": plot,
                    "plot_no": payload.get("Plot_No"),
                    "project": project,
                    "marketer": marketer,
                    "purchase_price": parse_decimal(payload.get("Purchase_Price")),
                    "selling_price": parse_decimal(payload.get("Selling_Price")),
                    "balance_lcy": parse_decimal(payload.get("Balance_LCY")),
                    "customer_id": customer,
                    "customer_name": payload.get("Customer_Name") or "",
                    "purchase_type": payload.get("Purchase_Type") or "Cash",
                    "commission_threshold": parse_decimal(payload.get("Commission_Threshold")),
                    "deposit_threshold": parse_decimal(payload.get("Deposit_Threshold")),
                    "discount": parse_decimal(payload.get("Discount")),
                    "completion_date": parse_date(payload.get("Completion_Date")),
                    "no_of_installments": payload.get("No_Of_Installments"),
                    "installment_amount": parse_decimal(payload.get("Installment_Amount")),
                    "sale_agreement_sent": payload.get("Sale_Agreement_Sent"),
                    "sale_agreement_signed": payload.get("Sale_Agreement_Signed"),
                    "total_paid": parse_decimal(payload.get("Total_Paid")),
                    "transfer_cost_charged": parse_decimal(payload.get("Transfer_Cost_Charged")),
                    "transfer_cost_paid": parse_decimal(payload.get("Transfer_Cost_Paid")),
                    "overpayments": parse_decimal(payload.get("Overpayments")),
                    "refunds": parse_decimal(payload.get("Refunds")),
                    "refundable_amount": parse_decimal(payload.get("Refundable_Amount")),
                    "penalties_accrued": parse_decimal(payload.get("Penalties_Accrued")),
                    "customer_lead_source": lead_source,
                    "cat_lead_source": cat_lead_source,
                    "booking_id": payload.get("Booking_Id"),
                    "booking_date": parse_date(payload.get("Booking_Date")),
                    "additional_deposit_date": parse_date(payload.get("Additional_Deposit_Date")),
                    "title_status": payload.get("Title_Status"),
                    "credit_officer_id": payload.get("Credit_Officer_Id"),
                    "lead_type": payload.get("Lead_Type") or "new",
                }
            )
            return f"LeadFile {lead_no} synced successfully"

    except Exception as e:
        logger.exception(f"Error syncing {lead_no}: {e}")
        raise self.retry(exc=Exception(str(e)), countdown=2 ** self.request.retries)
