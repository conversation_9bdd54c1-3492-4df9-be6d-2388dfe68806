from celery import shared_task
from django.db import transaction
from decimal import Decimal
from datetime import datetime
import logging
from inventory.models import Project, Plot
from customers.models import Customer, LeadSource
from users.models import User
from sales.models import LeadFile
from leads.models import LeadSourceCategory
from .helpers import retrieve_lead_file,retrieve_installment_schedules
from receipts.models import InstallmentSchedule

logger = logging.getLogger(__name__)


def normalize_payload(payload: dict) -> dict:
    """Normalize ERP keys to consistent naming + cleanup values."""
    normalized = {}
    key_map = {
        "Plot_NO": "Plot_No",
        "plot_no": "Plot_No",
        "Customer_Id": "Customer_Id",
        "customer_id": "Customer_Id",
        "ProjectId": "Project_No",   # <-- map to the key you later read
        "project_id": "Project_No",
        "Marketer": "Marketer",
    }
    for k, v in payload.items():
        normalized[key_map.get(k, k)] = v
    return normalized


def parse_decimal(val):
    if val in (None, "", "null"):
        return Decimal(0)
    try:
        return Decimal(str(val).replace(",", ""))
    except Exception:
        return Decimal(0)


def parse_date(val):
    if not val:
        return None
    try:
        return datetime.strptime(val, "%Y-%m-%d").date()
    except Exception:
        return None
def _normalize_paid(val):
    if isinstance(val, bool):
        return val
    if val is None:
        return None
    s = str(val).strip().lower()
    if s in {"yes", "y", "true", "1"}:
        return True
    if s in {"no", "n", "false", "0"}:
        return False
    return None

@shared_task(bind=True, max_retries=3)
def sync_leadfile_from_erp(self, lead_no: str):
    # --- one-time initial delay so ERP has time to update ---
    if self.request.retries == 0:
        # reschedule this task to run 60s later, then exit
        raise self.retry(countdown=60)

    # helper: backoff after real errors (60s, 120s, 240s)
    def _backoff(n: int) -> int:
        # n is next retry number
        return 60 * (2 ** max(n - 1, 0))

    payload, err = retrieve_lead_file(lead_no)
    if err:
        logger.error(f"ERP fetch error for {lead_no}: {err}")
        raise self.retry(exc=Exception(str(err)), countdown=_backoff(self.request.retries + 1))

    if not payload:
        return f"LeadFile {lead_no}: No data returned from ERP"

    payload = normalize_payload(payload)

    try:
        with transaction.atomic():
            # --- FK lookups ---
            project = Project.objects.filter(projectId=payload.get("Project_No")).first()
            if not project:
                raise ValueError(f"Project {payload.get('Project_No')} not found in CRM")

            plot = Plot.objects.filter(plotId=payload.get("Plot_No")).first()
            if not plot:
                raise ValueError(f"Plot {payload.get('Plot_No')} not found in CRM")

            customer = Customer.objects.filter(customer_no=payload.get("Member_No")).first()
            if not customer:
                raise ValueError(f"Customer {payload.get('Member_No')} not found in CRM")

            marketer = User.objects.filter(employee_no=payload.get("Sales_Person_Code")).first()
            if not marketer:
                raise ValueError(f"User {payload.get('Sales_Person_Code')} not found in CRM")

            lead_source = LeadSource.objects.filter(name=payload.get("Lead_Source")).first()
            cat_lead_source = LeadSourceCategory.objects.filter(name=payload.get("Campaign_Source_of_Lead")).first()

            # --- LeadFile upsert ---
            LeadFile.objects.update_or_create(
                lead_file_no=lead_no,
                defaults={
                    "plot": plot,
                    "plot_no": payload.get("Plot_No"),
                    "project": project,
                    "marketer": marketer,
                    "purchase_price": parse_decimal(payload.get("Purchase_Price")),
                    "selling_price": parse_decimal(payload.get("Property_Price")),
                    "balance_lcy": parse_decimal(payload.get("ActualBalance")),
                    "customer_id": customer,
                    "customer_name": payload.get("Member_Name") or "",
                    "purchase_type": payload.get("Purchase_Type") or "Cash",
                    "commission_threshold": parse_decimal(payload.get("Threshold_Amount")),
                    "deposit_threshold": parse_decimal(payload.get("Deposit_Threshold")),
                    "discount": parse_decimal(payload.get("Discount_Amount")),
                    "completion_date": parse_date(payload.get("Completion_Date")),
                    "no_of_installments": payload.get("No_Of_Installments"),
                    "installment_amount": parse_decimal(payload.get("Installment_Amount")),
                    "sale_agreement_sent": payload.get("Sale_Agrmt_Sent_to_Customer"),
                    "sale_agreement_signed": payload.get("Sale_Agreement_Signed"),
                    "total_paid": parse_decimal(payload.get("Total_Paid")),
                    "transfer_cost_charged": parse_decimal(payload.get("Transfer_Cost_Charged")),
                    "transfer_cost_paid": parse_decimal(payload.get("Transfer_Cost_Paid_Amount")),
                    "overpayments": parse_decimal(payload.get("Over_payments")),
                    "refunds": parse_decimal(payload.get("Total_Refunds")),
                    "refundable_amount": parse_decimal(payload.get("Total_Refunds")),
                    "penalties_accrued": parse_decimal(payload.get("Accrued_Penalties")),
                    "customer_lead_source": lead_source,
                    "cat_lead_source": cat_lead_source,
                    "booking_id": payload.get("Booking_Id"),
                    "booking_date": parse_date(payload.get("Booking_Date")),
                    "additional_deposit_date": parse_date(payload.get("Additional_Deposit_Date")),
                    "title_status": payload.get("Registry_File_Movement_Status"),
                    "credit_officer_id": payload.get("Credit_Officer_Id"),
                    "lead_type": payload.get("Special_Category"),
                    "lead_file_dropped_status": payload.get("Dropped"),
                }
            )
            return f"LeadFile {lead_no} synced successfully"

    except Exception as e:
        # Unexpected/transient DB or parsing error -> retry
        logger.exception("Unexpected error syncing %s", lead_no)
        try:
            raise self.retry(exc=e, countdown=_backoff(self.request.retries + 1))
        except MaxRetriesExceededError:
            logger.exception("Max retries exceeded while syncing %s", lead_no)
            raise




@shared_task(bind=True, max_retries=3)
def sync_installment_schedule_from_erp(self, lead_no: str):
    # --- one-time initial delay so ERP has time to update ---
    if self.request.retries == 0:
        # reschedule this task to run 60s later, then exit
        raise self.retry(countdown=60)

    # helper: backoff after real errors (60s, 120s, 240s)
    def _backoff(n: int) -> int:
        # n is next retry number
        return 60 * (2 ** max(n - 1, 0))

    # ---- helpers (reuse your parse_date/parse_decimal if already defined globally) ----

    def _normalize_paid(val):
        if isinstance(val, bool):
            return val
        if val is None:
            return None
        s = str(val).strip().lower()
        if s in {"yes", "y", "true", "1"}:
            return True
        if s in {"no", "n", "false", "0"}:
            return False
        return None

    # Map BC → internal keys (based on your sample row)
    def _normalize_installment_row(row: dict) -> dict:
        """
        Accepts either BC keys (Document_No, Line_No, ...) or already-internal keys
        and returns a dict with internal keys normalized and types parsed.
        """
        # If already internal, just copy
        if "leadfile_no" in row and "installment_no" in row:
            data = dict(row)
        else:
            data = {
                "leadfile_no": row.get("Document_No"),
                "member_no": row.get("MemberNo"),
                "line_no": row.get("Line_No"),
                "installment_no": row.get("Installment_No"),
                "installment_amount": row.get("Installment_Amount"),
                "remaining_Amount": row.get("Remaining_Amount"),
                "due_date": row.get("Due_Date"),
                "paid": row.get("Paid"),
                "plot_No": row.get("Plot_No"),
                "plot_Name": row.get("Plot_Name"),
                "amount_Paid": row.get("Amount_Paid"),
                "penaties_Accrued": row.get("Accrued_Penalty"),
            }

        # Parse/normalize types
        data["due_date"] = parse_date(data.get("due_date"))
        data["installment_amount"] = parse_decimal(data.get("installment_amount"))
        data["remaining_Amount"] = parse_decimal(data.get("remaining_Amount"))
        data["amount_Paid"] = parse_decimal(data.get("amount_Paid"))
        data["paid"] = _normalize_paid(data.get("paid"))
        return data

    # ---- fetch from ERP ----
    payload, err = retrieve_installment_schedules(lead_no)
    if err:
        logger.error(f"ERP fetch error for {lead_no}: {err}")
        raise self.retry(exc=Exception(str(err)), countdown=_backoff(self.request.retries + 1))

    if not payload:
        return f"LeadFile {lead_no}: No installment schedules returned from ERP"

    # ERP can return a single dict or a list
    items = payload if isinstance(payload, list) else [payload]

    created_count = 0
    updated_count = 0

    try:
        with transaction.atomic():
            for row in items:
                data = _normalize_installment_row(row)

                # Natural key to avoid collisions: leadfile + installment + line
                if not (data.get("leadfile_no") and data.get("installment_no") and data.get("line_no")):
                    # if any required key is missing, skip this row
                    logger.warning(
                        "Skipping installment row (missing key). lead=%s data=%s",
                        lead_no, {k: data.get(k) for k in ("leadfile_no","installment_no","line_no")}
                    )
                    continue

                obj, created = InstallmentSchedule.objects.update_or_create(
                    leadfile_no=data["leadfile_no"],
                    installment_no=data["installment_no"],
                    line_no=data["line_no"],
                    defaults={
                        "member_no": data.get("member_no"),
                        "installment_amount": data.get("installment_amount"),
                        "remaining_Amount": data.get("remaining_Amount"),
                        "due_date": data.get("due_date"),
                        "paid": data.get("paid"),
                        "plot_No": data.get("plot_No"),
                        "plot_Name": data.get("plot_Name"),
                        "amount_Paid": data.get("amount_Paid"),
                        "penaties_Accrued": data.get("penaties_Accrued"),
                        # add "updated_at": timezone.now() if your model has it
                    }
                )
                if created:
                    created_count += 1
                else:
                    updated_count += 1

        return f"LeadFile {lead_no}: installment schedules synced (created {created_count}, updated {updated_count})"

    except Exception as e:
        logger.exception(f"Error syncing installments for {lead_no}: {e}")
        raise self.retry(exc=Exception(str(e)), countdown=_backoff(self.request.retries + 1))

