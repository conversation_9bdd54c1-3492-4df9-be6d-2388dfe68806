# Generated by Django 5.1.7 on 2025-06-27 13:05

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='FailedApiCall',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.URLField()),
                ('request_method', models.Char<PERSON>ield(max_length=10)),
                ('request_headers', models.JSONField()),
                ('request_body', models.JSONField(blank=True, null=True)),
                ('status_code', models.IntegerField()),
                ('response_body', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('retry_count', models.IntegerField(default=0)),
                ('max_retries', models.IntegerField(default=3)),
                ('resolved', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
        ),
    ]
