from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from leads.management.commands.refreshUserCodes import generate_marketer_code
from rest_framework.views import APIView
import json
from rest_framework.decorators import api_view
from rest_framework.decorators import api_view, permission_classes
from django.utils.decorators import method_decorator
from rest_framework.permissions import AllowAny
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.response import Response
from rest_framework import status

from users.helpers import deactivate_user_email
from users.serializers import UserSerializer,DepartmentsSerializer,TeamsSerializer
from users.models import User,Departments,Teams
import os
import random
import logging

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class UserSyncAPIView(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync user from external system",
    operation_description="Sync user from external system based on emp_no. If the record exists, it updates; otherwise, creates a new record.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["emp_no", "fullname", "company_email", "gender", "status"],
        properties={
            'emp_no': openapi.Schema(type=openapi.TYPE_STRING, description="Employee Number (used as unique identifier)"),
            'fullname': openapi.Schema(type=openapi.TYPE_STRING, description="Full name of user"),
            'company_email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'personal_email': openapi.Schema(type=openapi.TYPE_STRING, format='email'),
            'phone_number': openapi.Schema(type=openapi.TYPE_STRING),
            'gender': openapi.Schema(type=openapi.TYPE_STRING),
            'department': openapi.Schema(type=openapi.TYPE_STRING),
            'office': openapi.Schema(type=openapi.TYPE_STRING),
            'status': openapi.Schema(type=openapi.TYPE_STRING, enum=["Active", "Frozen"]),
            'team': openapi.Schema(type=openapi.TYPE_STRING),
            'region': openapi.Schema(type=openapi.TYPE_STRING),
            'is_marketer': openapi.Schema(type=openapi.TYPE_STRING),
            'Manager': openapi.Schema(type=openapi.TYPE_STRING),
            'ERP_user_id': openapi.Schema(type=openapi.TYPE_STRING, description="ERP User ID"),
        }
    ),
    responses={
        200: openapi.Response("User created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        data["email"] = data.get("company_email")
        data["erp_user_id"] = data.get("ERP_user_id")
        data["employee_no"] = data.get("emp_no")
        data["is_marketer"] = True if data.get("is_marketer") == 'Yes' else False
        
        serializer = None


        # Clean and transform
        if data.get("status") == "Active":
            data["status"] = "Active"
            data["is_active"] = True
            data["is_staff"] = True
            data["is_available"] = True
        else:
            data["status"] = "Inactive"
            prefix = User.objects.filter(email__startswith="inactive.").count() + 1
            data["email"] = f'inactive.{prefix}.{data["email"]}'
            data["is_active"] = False
            data["is_staff"] = False
            data["is_available"] = False

        if data.get("office") == "ABSA":
            data["office"] = "HQ"
        elif data.get("office") == "KAREN":
            data["office"] = "Global"
        else:
            data["office"] = "HQ"

        # if data.get("department") == "CONVERSION":
        #     data["department"] = "Marketing"
        # elif data.get("department") == "CUSTOMER SERVICE":
            # data["department"] = "Customer Exp"
        

        try:
            department = Departments.objects.get(dp_name=data["department"])
            data["department"] = department.dp_name
        except Departments.DoesNotExist:
            if data.get("is_marketer") in ["Yes","yes", True, 'true']:
                data["department"] = "CONVERSION"
            else:
                data["department"] = 'UNKNOWN'
        try:
            team = Teams.objects.get(team=data["team"])
            data["team"] = team.team
            data["tl_code"] = team.tl_code
            data["tl_name"] = team.tl_name
        except Teams.DoesNotExist:
            data["team"] = None
        
        data["group"] = None

        # Update or Create
        try:
            user = User.objects.get(employee_no=data["emp_no"])
            if data.get('company_email') == '' and user.email:
                deactivate_email = deactivate_user_email(user.email)
                data['email'] = deactivate_email
                data['status'] = "Inactive"

            elif data.get('company_email') and user.email != data.get('company_email'):
                data['email'] = data.get('company_email')
            
            elif data.get('company_email') and user.email.startswith('newuser'):
                data['email'] = data.get('company_email')
                data['status'] = 'Active'
                data["is_active"] = True
                data["is_staff"] = True
                data["is_available"] = True
                

            if user.mkcode == "" or not user.mkcode.startswith('SM-'):
                marketer_code = generate_marketer_code(user)
                data["mkcode"] = marketer_code
            
            if data.get("is_marketer") in ["Yes","yes","1",'true']:
                data["department"] = "CONVERSION"
            

            serializer = UserSerializer(user, data=data, partial=True)

            __user = serializer.save()
            return Response({"message": "User updated successfully"}, status=status.HTTP_200_OK)
        
        except User.DoesNotExist:
            if not data.get("user_id"):
                data["user_id"] = str(random.randint(1000000, 9999999))
            if not data.get("fullname"):
                return Response({"error": "Fullname is required"}, status=status.HTTP_400_BAD_REQUEST)
            
            names = data["fullname"].split()

            data["first_name"] = names[0]
            data["last_name"] = names[-1] if len(names) > 1 else ""
            base_username = f"{data['first_name']}.{data['last_name']}".lower()
            import re
            sanitized_base_username = re.sub(r'[^a-zA-Z0-9]', '', base_username)
            username = sanitized_base_username
            counter = 1
            while User.objects.filter(username=username).exists():
                username = f"{sanitized_base_username}{counter}"
                counter += 1
            
            data["username"] = username
            data["fullnames"] = data.pop("fullname")
            data["password"] = "password123"  # Default password for new users

            if data.get('company_email') == '': 
                mkcode = data["employee_no"].split('/')[-1]
                data["email"] = f'newuser{mkcode}@optiven.co.ke'
                data["status"] = "Inactive"
                data["mkcode"] = f'SM-{mkcode}'
            
           
            # if not data.get("mkcode"):    
            #     last_mkcode = User.objects.filter(mkcode__isnull=False).order_by('-mkcode').first()
            #     data["mkcode"] = str(last_mkcode.mkcode + 1) if last_mkcode and isinstance(last_mkcode.mkcode, int) else "1"

            # print("Creating new user with data:", data)
            serializer = UserSerializer(data=data)


            if serializer.is_valid():
                __user = serializer.save()

                marketer_code = generate_marketer_code(__user)
                data["mkcode"] = marketer_code
                
                return Response({"message": "User created or updated successfully"}, status=status.HTTP_200_OK)

        except Exception as e:
            print("Serializer errors:", serializer.errors, '\n Error Exception:', str(e))
            logger.error(f"Error in UserSyncAPIView: {str(e)}")
            return Response({"error": "An error occurred", "details": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
            # return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    # def new_method(self, data):
    #     names = data["fullname"].split()
    #     return names


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class UserUpdateSyncAPIView(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync user updates from external system",
    operation_description="Sync user update from external system based on emp_no. If the record exists, it updates; otherwise, creates a new record.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["emp_no", "department", "teams_code"],
        properties={
            'employee_no': openapi.Schema(type=openapi.TYPE_STRING, description="Employee Number (used as unique identifier)"),
            'department': openapi.Schema(type=openapi.TYPE_STRING),
            'teams_code': openapi.Schema(type=openapi.TYPE_STRING),
        }
    ),
    responses={
        200: openapi.Response("User updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        data["employee_no"] = data.get("emp_no")
        data["department"] = data.get("department")
        data["team"] = data.get("teams_code")
        
        serializer = None

        # Update or Create
        try:
            user = User.objects.get(employee_no=data["employee_no"])            
            serializer = UserSerializer(user, data=data, partial=True)

        except User.DoesNotExist:
            serializer = UserSerializer(data=data)

        if serializer.is_valid():
            serializer.save()
            return Response({"message": "User updated successfully"}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])    
class DepartmentsSyncAPIView(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync Departments from ERP system",
    operation_description="Sync DEPARTMENTS from external system based on . If the record exists, it updates; otherwise, creates a new record.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=[ "dp_name"],
        properties={
            'dp_id': openapi.Schema(type=openapi.TYPE_INTEGER, description="Department ID (used as unique identifier)"),
            'dp_name': openapi.Schema(type=openapi.TYPE_STRING, description="department name"),
            'dep_head_id': openapi.Schema(type=openapi.TYPE_STRING, format='department head id*(employee no)*'),
            
        }
    ),
    responses={
        200: openapi.Response("User created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        # update the department head
        
        try:
            user = User.objects.get(employee_no=data["dep_head_id"])
            data["dep_head_name"] = user.fullnames
            data["dep_head_id"] = user.employee_no
        except User.DoesNotExist:
            data["dep_head_name"] = None
            data["dep_head_id"] = None
            # return Response({"error": f"Department head not found for  {data["dep_head_id"]}"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            department = Departments.objects.get(dp_name=data["dp_name"])
            serializer = DepartmentsSerializer(department, data=data, partial=True)
        except Departments.DoesNotExist:
            serializer = DepartmentsSerializer(data=data)

        
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Department created or updated successfully", "data": serializer.data}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])    
class TeamsSyncAPIView(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync Teams from ERP system",
    operation_description="Sync Teams from external system based on . If the record exists, it updates; otherwise, creates a new record.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["tm_id","team", "tl_code", "inactive"],
        properties={
            
            'team': openapi.Schema(type=openapi.TYPE_STRING, description="team name"),
            'tl_code': openapi.Schema(type=openapi.TYPE_STRING, format='Team leader head id*(employee no)*'),
            'inactive': openapi.Schema(type=openapi.TYPE_BOOLEAN, description="Inactive status"),
            'office': openapi.Schema(type=openapi.TYPE_STRING, description="office"),
            
        }
    ),
    responses={
        200: openapi.Response("User created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        data = request.data.copy()
        # update the department head
        try:
            user = User.objects.get(employee_no=data["tl_code"])
            data["tl_name"] = user.fullnames
            data["tl_code"] = user.employee_no
        except User.DoesNotExist:
            data["tl_name"] = None
            data["tl_code"] = None
            # return Response({"error": f"Team head not found for  {data["tl_code"]}"}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            Team = Teams.objects.get(team=data["team"])
            serializer = TeamsSerializer(Team, data=data, partial=True)
        except Teams.DoesNotExist:
            serializer = TeamsSerializer(data=data)

        
        if serializer.is_valid():
            serializer.save()
            return Response({"message": "Team created or updated successfully", "data": serializer.data}, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)