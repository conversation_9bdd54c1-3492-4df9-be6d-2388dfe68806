from rest_framework import viewsets
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.permissions import AllowAny, IsAuthenticated
from receipts.models import InstallmentSchedule
from receipts.serializers import InstallmentScheduleSerializer
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'retrieve', 'partial_update', 'create']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator
class InstallmentScheduleViewSet(viewsets.ModelViewSet):
    queryset = InstallmentSchedule.objects.all()
    serializer_class = InstallmentScheduleSerializer
    permission_classes = [AllowAny]  # TODO: Change to [IsAuthenticated] in production
    http_method_names = ['get',]
    filterset_fields = ['paid', 'plot_No', 'plot_Name','member_no', 'leadfile_no']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['member_no', 'leadfile_no', 'plot_No']
    ordering_fields = ['due_date', 'installment_no', 'member_no']

    @swagger_auto_schema(
        tags=["SALES"],
        operation_summary="List all installment schedules",
        operation_description="Get a list of all installment schedules",
        responses={
            200: InstallmentScheduleSerializer(many=True),
            400: "Bad Request",
            401: "Unauthorized",
        }
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    

    @swagger_auto_schema(
        tags=["SALES"],
        operation_summary="Get a specific installment schedule",
        operation_description="Get details of a specific installment schedule by IS_id",
        responses={
            200: InstallmentScheduleSerializer,
            404: "Not Found",
            401: "Unauthorized",
        }
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
