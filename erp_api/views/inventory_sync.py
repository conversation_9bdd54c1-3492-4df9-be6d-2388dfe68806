import os
from rest_framework.views import APIView
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from inventory.models import Plot, Project
from inventory.serializers import PlotSerializer, ProjectsSyncSerializer

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class ProjectSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync project from external system",
    operation_description="Sync project from external system based on project_id. If the project exists, it updates; otherwise, creates a new project.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["projectId", "name"],
        properties={
            'projectId': openapi.Schema(type=openapi.TYPE_STRING, description="Project ID (used as unique identifier for syncing -  previously ERP_id)"),
            'name': openapi.Schema(type=openapi.TYPE_STRING, description="Name of the project"),
            'link': openapi.Schema(type=openapi.TYPE_STRING, description="link of the project"),
            'priority': openapi.Schema(type=openapi.TYPE_NUMBER, description="Priority of the project"),
            'tier': openapi.Schema(type=openapi.TYPE_STRING, description="Tier of the project", enum=["1", "2", "3", "4", "5"]),
            'bank': openapi.Schema(type=openapi.TYPE_STRING, description="Bank associated with the project"),
            'account_no': openapi.Schema(type=openapi.TYPE_STRING, description="Account number associated with the project"),
            'website_link': openapi.Schema(type=openapi.TYPE_STRING, description="Website link of the project"),
            'initials': openapi.Schema(type=openapi.TYPE_STRING, description="Initials of the project"),
            'visibiliy': openapi.Schema(type=openapi.TYPE_STRING, description="Visibility of the project"),
            'description': openapi.Schema(type=openapi.TYPE_STRING, description="Description of the project"),

        }
    ),
    responses={
        200: openapi.Response("Project created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():

                data = request.data.copy()
                # print()
                
                project_id = data.get('projectId')
 
                project, created = Project.objects.update_or_create(
                    projectId=project_id,
                    defaults={
                        'name': data.get('name'),
                        'link': data.get('link'),
                        'description': data.get('description'),
                        'priority': data.get('priority'),
                        'tier': data.get('tier'),
                        'bank': data.get('bank'),
                        'account_no': data.get('account_no'),
                        'website_link': data.get('website_link'),
                        'initials': data.get('initials'),
                        'visibiliy': data.get('visibiliy')
                    }
                )
                serializer = ProjectsSyncSerializer(project, many=False)
                if created:
                    return Response({"message": "Project created successfully", 'data':serializer.data}, status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "Project updated successfully", 'data':serializer.data}, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class PlotsSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync plots from external system",
    operation_description="Sync plots from external system based on plotId. If the plot exists, it updates; otherwise, creates a new plot.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['project',"plotId", "plot_no", "plot_size", "plot_type", "plot_status", "location", "cash_price", "threshold_price"],
        properties={
            'plotId': openapi.Schema(type=openapi.TYPE_STRING, description="Plot ID (used as unique identifier for syncing -  previously unit_id)"),
            'plot_no': openapi.Schema(type=openapi.TYPE_STRING, description="Plot number"),
            'plot_size': openapi.Schema(type=openapi.TYPE_NUMBER, description="Plot size"),
            'plot_type': openapi.Schema(type=openapi.TYPE_STRING, description="Plot type"),
            'plot_status': openapi.Schema(type=openapi.TYPE_STRING, description="Plot status"),
            'location': openapi.Schema(type=openapi.TYPE_STRING, description="Location of the plot"),
            'cash_price': openapi.Schema(type=openapi.TYPE_STRING, description="Cash price of the plot"),
            'threshold_price': openapi.Schema(type=openapi.TYPE_STRING, description="Threshold price of the plot"),
            'project': openapi.Schema(type=openapi.TYPE_STRING, description="Project_id)"),
            'lr_no': openapi.Schema(type=openapi.TYPE_STRING, description="LR number"),
            'view': openapi.Schema(type=openapi.TYPE_STRING, description="View of the plot"),
            'erp_status': openapi.Schema(type=openapi.TYPE_STRING, description="ERP status of the plot"),
        }
    ),
    responses={
        200: openapi.Response("Plot created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)
        
        try:
            with transaction.atomic():
                data = request.data.copy()
                plot_id = data.get('plotId')
                project = data.get('project')
                cash_price =  float(data.get('cash_price').replace(',', '')) if data.get('cash_price') else None
                threshold_price =  float(data.get('threshold_price').replace(',', '')) if data.get('threshold_price') else None

                # Get the project instance based on the projectId in the request
                project_instance = Project.objects.filter(projectId=project).first()
                if not project_instance:
                    return Response({"error": "Project not found"}, status=status.HTTP_400_BAD_REQUEST)

                # Create or update the plot
                plot, created = Plot.objects.update_or_create(
                    plotId=plot_id,
                    defaults={
                        'plot_no': data.get('plot_no'),
                        'plot_size': data.get('plot_size'),
                        'plot_type': data.get('plot_type'),
                        'plot_status': data.get('plot_status'),
                        'location': data.get('location'),
                        'cash_price': cash_price,
                        'threshold_price': threshold_price,
                        'project': project_instance,
                        'lr_no': data.get('lr_no'),
                        'view': data.get('view'),
                        'erp_status': data.get('erp_status')
                    }
                )
                serializer = PlotSerializer(plot, many=False)
                if created:
                    return Response({"message": "Plot created successfully", 'data':serializer.data}, status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "Plot updated successfully", 'data':serializer.data}, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)