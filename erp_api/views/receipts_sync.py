# import os
# from rest_framework.views import APIView
# from rest_framework import status, permissions
# from rest_framework.response import Response
# from rest_framework.decorators import permission_classes
# from rest_framework.permissions import AllowAny

# from django.db import transaction, IntegrityError
# from django.views.decorators.csrf import csrf_exempt
# from django.utils.decorators import method_decorator
# from drf_yasg.utils import swagger_auto_schema
# from drf_yasg import openapi

# from receipts.models import PostedReceipt, CancelledReceipt
# from datetime import datetime
# from django.db import models
# import requests
# from requests_ntlm import HttpNtlmAuth
# from urllib.parse import quote


# # --- OData config via envs (recommended) ---
# ODATA_HOST = os.getenv("BC_ODATA_HOST", "http://optiven-server:8048")
# BC_TENANT = os.getenv("BC_TENANT_PATH", "optiven/ODataV4")
# BC_COMPANY = quote(os.getenv("BC_COMPANY", "Optiven R.E"))
# AUTH = HttpNtlmAuth(os.getenv("BC_NTLM_USER", "Dennis.Mwendwa"),
#                     os.getenv("BC_NTLM_PASS", "Den9@24!#"))
# HEADERS = {
#     "User-Agent": "OptivenERP/1.0",
#     "Accept": "application/json",
#     "Content-Type": "application/json",
# }

# def retrieve_lead_file(pk: str):
#     """
#     Return full Lead_File_Card payload (dict) and an error string if any.
#     """
#     url = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/Lead_File_Card('{pk}')"
#     try:
#         r = requests.get(url, auth=AUTH, headers=HEADERS, timeout=12)
#         if not r.ok:
#             return None, f"OData error {r.status_code}: {r.text[:300]}"
#         return r.json(), None
#     except requests.exceptions.RequestException as e:
#         return None, f"Network error contacting OData: {e}"




# @method_decorator(csrf_exempt, name='dispatch')
# @permission_classes([AllowAny])
# class ReceiptSyncAPIView(APIView):
#     def get_client_ip(self, request):
#         x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
#         if x_forwarded_for:
#             return x_forwarded_for.split(',')[0]
#         return request.META.get('REMOTE_ADDR')
    
#     def parse_date(self, date_str):
#         if not date_str:
#             return None
#         try:
#             return datetime.strptime(date_str, "%m/%d/%y").strftime("%Y-%m-%d")
#         except ValueError:
#             try:
#                 return datetime.strptime(date_str, "%Y-%m-%d").strftime("%Y-%m-%d")
#             except ValueError:
#                 return None
    
#     @swagger_auto_schema(
#         tags=["Data Sync From ERP System"],
#         operation_summary="Sync Receipts from external system",
#         operation_description="Sync Receipts from external system based on line_no. If the Receipt exists, it updates; otherwise, creates a new Receipt. Note: A receipt number cannot be repeated for the same type (Posted/Cancelled).",
#         request_body=openapi.Schema(
#             type=openapi.TYPE_OBJECT,
#             required=["id", "Type", "Receipt_No"],
#             properties={
#                 'id': openapi.Schema(type=openapi.TYPE_INTEGER, description="Line number from ERP system"),
#                 'Receipt_No': openapi.Schema(type=openapi.TYPE_STRING, description="Receipt Number (must be unique for each type)"),
#                 'Date_Posted': openapi.Schema(type=openapi.TYPE_STRING, description="Date Posted (MM/DD/YY or YYYY-MM-DD)"),
#                 'Payment_date': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Date (MM/DD/YY or YYYY-MM-DD)"),
#                 'Bank_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Bank Name"),
#                 'Bank_Account': openapi.Schema(type=openapi.TYPE_STRING, description="Bank Account"),
#                 'Customer_Id': openapi.Schema(type=openapi.TYPE_STRING, description="Customer ID"),
#                 'Customer_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Customer Name"),
#                 'Pay_mode': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Mode"),
#                 'Lead_file_no': openapi.Schema(type=openapi.TYPE_STRING, description="Lead File Number"),
#                 'Project_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Project Name"),
#                 'Plot_NO': openapi.Schema(type=openapi.TYPE_STRING, description="Plot Number"),
#                 'Marketer': openapi.Schema(type=openapi.TYPE_STRING, description="Marketer"),
#                 'Teams': openapi.Schema(type=openapi.TYPE_STRING, description="Teams"),
#                 'Regions': openapi.Schema(type=openapi.TYPE_STRING, description="Regions"),
#                 'Deposit_Threshold': openapi.Schema(type=openapi.TYPE_STRING, description="Deposit Threshold"),
#                 'Transaction_type': openapi.Schema(type=openapi.TYPE_STRING, description="Transaction Type"),
#                 'transfer_receipt': openapi.Schema(type=openapi.TYPE_STRING, description="Transfer Receipt"),
#                 'Amount_LCY': openapi.Schema(type=openapi.TYPE_NUMBER, description="Amount in Local Currency"),
#                 'Balance_LCY': openapi.Schema(type=openapi.TYPE_NUMBER, description="Balance in Local Currency"),
#                 'Type': openapi.Schema(type=openapi.TYPE_STRING, description="Type (Posted/Cancelled)"),
#                 'status': openapi.Schema(type=openapi.TYPE_STRING, description="Status"),
#                 'POSTED_DATE1': openapi.Schema(type=openapi.TYPE_STRING, description="Posted Date (MM/DD/YY or YYYY-MM-DD)"),
#                 'PAYMENT_DATE1': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Date (MM/DD/YY or YYYY-MM-DD)"),
#                 'Narration': openapi.Schema(type=openapi.TYPE_STRING, description="Narration"),
#                 'assigned_to': openapi.Schema(type=openapi.TYPE_STRING, description="Assigned To"),
#             }
#         ),
#         responses={
#             200: openapi.Response("Receipt created or updated successfully"),
#             400: openapi.Response("Validation error"),
#             403: openapi.Response("Unauthorized access (IP not allowed)"),
#             409: openapi.Response("Receipt number already exists for this type"),
#         }
#     )
#     def post(self, request, *args, **kwargs):
#         allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
#         ip = self.get_client_ip(request)
#         if ip != allowed_ip:
#             return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

#         try:
#             with transaction.atomic():
#                 data = request.data.copy()
                
#                 # Convert all date fields
#                 date_fields = ['Date_Posted', 'Payment_date', 'POSTED_DATE1', 'PAYMENT_DATE1']
#                 for field in date_fields:
#                     data[field] = self.parse_date(data.get(field))

#                 line_no = data.get('id')  # Use id from ERP as line_no
#                 receipt_no = data.get('Receipt_No')
#                 receipt_type = data.get('Type')

                
#                 if not receipt_no:
#                     return Response({"error": "Receipt number is required"}, status=status.HTTP_400_BAD_REQUEST)

#                 # Determine which model to use based on Type
#                 if receipt_type == 'Posted':
#                     model = PostedReceipt
#                 elif receipt_type == 'Cancelled':
#                     model = CancelledReceipt
#                 else:
#                     return Response({"error": "Invalid receipt type. Must be 'Posted' or 'Cancelled'"}, 
#                                   status=status.HTTP_400_BAD_REQUEST)

#                 try:
#                     # Auto-increment line_no if not provided or already exists
#                     if not line_no:
#                         # Get the max line_no for this model and increment
#                         max_line_no = model.objects.aggregate(max_no=models.Max('line_no'))['max_no'] or 0
#                         line_no = max_line_no + 1
#                     elif model.objects.filter(line_no=line_no).exists():
#                         # If line_no exists, increment from max
#                         max_line_no = model.objects.aggregate(max_no=models.Max('line_no'))['max_no'] or 0
#                         line_no = max_line_no + 1
#                     # Create or update the receipt using line_no as the primary key
#                     receipt, created = model.objects.update_or_create(
#                         line_no=line_no,
#                         defaults={
#                             'Receipt_No': receipt_no,
#                             'Date_Posted': data.get('Date_Posted'),
#                             'Payment_date': data.get('Payment_date'),
#                             'Bank_Name': data.get('Bank_Name'),
#                             'Bank_Account': data.get('Bank_Account'),
#                             'Customer_Id': data.get('Customer_Id'),
#                             'Customer_Name': data.get('Customer_Name'),
#                             'Pay_mode': data.get('Pay_mode'),
#                             'Lead_file_no': data.get('Lead_file_no'),
#                             'Project_Name': data.get('Project_Name'),
#                             'Plot_NO': data.get('Plot_NO'),
#                             'Marketer': data.get('Marketer'),
#                             'Teams': data.get('Teams'),
#                             'Regions': data.get('Regions'),
#                             'Deposit_Threshold': data.get('Deposit_Threshold'),
#                             'Transaction_type': data.get('Transaction_type'),
#                             'transfer_receipt': data.get('transfer_receipt'),
#                             'Amount_LCY': data.get('Amount_LCY'),
#                             'Balance_LCY': data.get('Balance_LCY'),
#                             'Type': receipt_type,
#                             'status': data.get('status'),
#                             'POSTED_DATE1': data.get('POSTED_DATE1'),
#                             'PAYMENT_DATE1': data.get('PAYMENT_DATE1'),
#                             'Narration': data.get('Narration'),
#                             'assigned_to': data.get('assigned_to'),
#                         }
#                     )

#                     if created:
#                         return Response({"message": f"{receipt_type} receipt created successfully"}, 
#                                       status=status.HTTP_201_CREATED)
#                     else:
#                         return Response({"message": f"{receipt_type} receipt updated successfully"}, 
#                                       status=status.HTTP_200_OK)
#                 except IntegrityError:
#                     return Response(
#                         {"error": f"Receipt number {receipt_no} already exists for type {receipt_type}"},
#                         status=status.HTTP_409_CONFLICT
#                     )
                
#         except Exception as e:
#             return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST) 


import os
import requests
from urllib.parse import quote
from requests_ntlm import HttpNtlmAuth

from datetime import datetime
from django.db import models, transaction, IntegrityError
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from receipts.models import PostedReceipt, CancelledReceipt
from erp_api.tasks import sync_leadfile_from_erp

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class ReceiptSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    def parse_date(self, date_str):
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, "%m/%d/%y").strftime("%Y-%m-%d")
        except ValueError:
            try:
                return datetime.strptime(date_str, "%Y-%m-%d").strftime("%Y-%m-%d")
            except ValueError:
                return None
    
    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Receipts from external system",
        operation_description="Sync Receipts from external system based on line_no. If the Receipt exists, it updates; otherwise, creates a new Receipt. Note: A receipt number cannot be repeated for the same type (Posted/Cancelled).",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["id", "Type", "Receipt_No"],
            properties={
                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description="Line number from ERP system"),
                'Receipt_No': openapi.Schema(type=openapi.TYPE_STRING, description="Receipt Number (must be unique for each type)"),
                'Date_Posted': openapi.Schema(type=openapi.TYPE_STRING, description="Date Posted"),
                'Payment_date': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Date"),
                'Bank_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Bank Name"),
                'Bank_Account': openapi.Schema(type=openapi.TYPE_STRING, description="Bank Account"),
                'Customer_Id': openapi.Schema(type=openapi.TYPE_STRING, description="Customer ID"),
                'Customer_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Customer Name"),
                'Pay_mode': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Mode"),
                'Lead_file_no': openapi.Schema(type=openapi.TYPE_STRING, description="Lead File Number"),
                'Project_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Project Name"),
                'Plot_NO': openapi.Schema(type=openapi.TYPE_STRING, description="Plot Number"),
                'Marketer': openapi.Schema(type=openapi.TYPE_STRING, description="Marketer"),
                'Teams': openapi.Schema(type=openapi.TYPE_STRING, description="Teams"),
                'Regions': openapi.Schema(type=openapi.TYPE_STRING, description="Regions"),
                'Deposit_Threshold': openapi.Schema(type=openapi.TYPE_STRING, description="Deposit Threshold"),
                'Transaction_type': openapi.Schema(type=openapi.TYPE_STRING, description="Transaction Type"),
                'transfer_receipt': openapi.Schema(type=openapi.TYPE_STRING, description="Transfer Receipt"),
                'Amount_LCY': openapi.Schema(type=openapi.TYPE_NUMBER, description="Amount in Local Currency"),
                'Balance_LCY': openapi.Schema(type=openapi.TYPE_NUMBER, description="Balance in Local Currency"),
                'Type': openapi.Schema(type=openapi.TYPE_STRING, description="Type (Posted/Cancelled)"),
                'status': openapi.Schema(type=openapi.TYPE_STRING, description="Status"),
                'POSTED_DATE1': openapi.Schema(type=openapi.TYPE_STRING, description="Posted Date"),
                'PAYMENT_DATE1': openapi.Schema(type=openapi.TYPE_STRING, description="Payment Date"),
                'Narration': openapi.Schema(type=openapi.TYPE_STRING, description="Narration"),
                'assigned_to': openapi.Schema(type=openapi.TYPE_STRING, description="Assigned To"),
            }
        ),
        responses={
            200: openapi.Response("Receipt created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            409: openapi.Response("Receipt number already exists for this type"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
                
                # Convert all date fields
                date_fields = ['Date_Posted', 'Payment_date', 'POSTED_DATE1', 'PAYMENT_DATE1']
                for field in date_fields:
                    data[field] = self.parse_date(data.get(field))

                line_no = data.get('line_no')
                receipt_no = data.get('Receipt_No')
                receipt_type = data.get('Type')

                if not receipt_no:
                    return Response({"error": "Receipt number is required"}, status=status.HTTP_400_BAD_REQUEST)

                # Select model
                if receipt_type == 'Posted':
                    model = PostedReceipt
                elif receipt_type == 'Cancelled':
                    model = CancelledReceipt
                else:
                    return Response({"error": "Invalid receipt type. Must be 'Posted' or 'Cancelled'"}, 
                                  status=status.HTTP_400_BAD_REQUEST)

                try:
                    ## Handle line_no uniqueness
                    # if not line_no or model.objects.filter(line_no=line_no).exists():
                    #     max_line_no = model.objects.aggregate(max_no=models.Max('line_no'))['max_no'] or 0
                    #     line_no = max_line_no + 1
                    
                    # Save receipt
                    receipt, created = model.objects.update_or_create(
                        line_no=line_no,
                        defaults={**data, "Type": receipt_type}
                    )

                    # 🔑 Async LeadFile sync (non-blocking)
                    lead_no = data.get("Lead_file_no")
                    if lead_no:
                        sync_leadfile_from_erp.delay(lead_no)

                    if created:
                        return Response({"message": f"{receipt_type} receipt created successfully"}, 
                                      status=status.HTTP_201_CREATED)
                    else:
                        return Response({"message": f"{receipt_type} receipt updated successfully"}, 
                                      status=status.HTTP_200_OK)
                except IntegrityError:
                    return Response(
                        {"error": f"Receipt number {receipt_no} already exists for type {receipt_type}"},
                        status=status.HTTP_409_CONFLICT
                    )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)


