import secrets
import string
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.decorators import permission_classes
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import os
from datetime import datetime

from leads.models import LeadSourceCategory, LeadSourceSubCategory, LeadSource
from leads.serializers import LeadSourceCategorySerializer, LeadSourceSubCategorySerializer, LeadSourceSerializer
from users.models import User

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class LeadSourceCategorySyncAPIView(APIView):
    """
    API View for syncing Lead Source Categories from ERP system.
    """
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Lead Source Category from ERP system",
        operation_description="Sync Lead Source Category from ERP system based on category_id. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["category_id", "category_name"],
            properties={
                'category_id': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Category ID from ERP (used as unique identifier)"
                ),
                'category_name': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name of the category"
                ),
                'description': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Description of the category"
                ),
            }
        ),
        responses={
            200: openapi.Response("Category created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
        }
    )
    def post(self, request, *args, **kwargs):
        # Check if request is from allowed IP
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response(
                {"error": f"{ip} is Unauthorized"},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        
        # Validate required fields
        if not data.get('category_id'):
            return Response(
                {"error": "category_id is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        if not data.get('category_name'):
            return Response(
                {"error": "category_name is required"},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Map ERP fields to CRM fields
            defaults = {
                'name': data['category_name'],
                'description': data.get('description')
            }

            # Try to find and update existing record, or create new one
            category, created = LeadSourceCategory.objects.update_or_create(
                category_id=data['category_id'],
                defaults=defaults
            )

            # Serialize the result
            serializer = LeadSourceCategorySerializer(category)
            
            message = "Category created successfully" if created else "Category updated successfully"
            return Response({
                "message": message,
                "data": serializer.data
            }, status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class LeadSourceSubCategorySyncAPIView(APIView):
    """
    API View for syncing Lead Source Sub Categories from ERP system.
    """
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Lead Source Sub Category from ERP system",
        operation_description="Sync Lead Source Sub Category from ERP system based on cat_lead_source_id. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["cat_lead_source_id", "category_id", "sub_cat_name"],
            properties={
                'cat_lead_source_id': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Sub Category ID from ERP (used as unique identifier)"
                ),
                'category_id': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Parent Category ID"
                ),
                'sub_cat_name': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name of the sub category"
                ),
                'manager_id': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Employee number of the manager"
                ),
                'manager_name': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name of the manager"
                ),
            }
        ),
        responses={
            200: openapi.Response("Sub Category created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            404: openapi.Response("Parent category not found"),
        }
    )
    def post(self, request, *args, **kwargs):
        # Check if request is from allowed IP
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response(
                {"error": f"{ip} is Unauthorized"},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        
        # Validate required fields
        required_fields = ['cat_lead_source_id', 'category_id', 'sub_cat_name']
        for field in required_fields:
            if not data.get(field):
                return Response(
                    {"error": f"{field} is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )

        try:
            # Find parent category
            try:
                parent_category = LeadSourceCategory.objects.get(category_id=data['category_id'])
            except LeadSourceCategory.DoesNotExist:
                return Response(
                    {"error": f"Parent category with ID {data['category_id']} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Find manager if manager_id is provided
            manager = None
            if data.get('manager_id'):
                try:
                    manager = User.objects.get(employee_no=data['manager_id'])
                except User.DoesNotExist:
                    # We'll continue without the manager if not found
                    pass

            # Map ERP fields to CRM fields
            defaults = {
                'lead_source_category': parent_category,
                'name': data['sub_cat_name'],
                'manager': manager,
                'manager_name': data.get('manager_name')
            }

            # Try to find and update existing record, or create new one
            subcategory, created = LeadSourceSubCategory.objects.update_or_create(
                cat_lead_source_id=data['cat_lead_source_id'],
                defaults=defaults
            )

            # Serialize the result
            serializer = LeadSourceSubCategorySerializer(subcategory)
            
            message = "Sub Category created successfully" if created else "Sub Category updated successfully"
            return Response({
                "message": message,
                "data": serializer.data
            }, status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class LeadSourceSyncAPIView(APIView):
    """
    API View for syncing Lead Sources from ERP system.
    """
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Lead Source from ERP system",
        operation_description="Sync Lead Source from ERP system based on leadsource_id. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["leadsource_id", "sub_category_id", "title", "reff_code"],
            properties={
                'leadsource_id': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Lead Source ID from ERP (used as unique identifier)"
                ),
                'sub_category_id': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Sub Category ID"
                ),
                'title': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Name/Title of the lead source"
                ),
                'description': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Description of the lead source"
                ),
                'QR': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="QR code URL"
                ),
                'reff_code': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Reference code"
                ),
                'link': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Associated link"
                ),
                'sales': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Total sales"
                ),
                'ongiong_sales': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Ongoing sales"
                ),
                'completed_sales': openapi.Schema(
                    type=openapi.TYPE_INTEGER,
                    description="Completed sales"
                ),
                'last_update': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Last update timestamp"
                ),
            }
        ),
        responses={
            200: openapi.Response("Lead Source created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            404: openapi.Response("Required related object not found"),
        }
    )
    def post(self, request, *args, **kwargs):
        # Check if request is from allowed IP
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response(
                {"error": f"{ip} is Unauthorized"},
                status=status.HTTP_403_FORBIDDEN
            )

        data = request.data.copy()
        
        # Validate required fields
        required_fields = ['leadsource_id', 'sub_category_id', 'title', ]
        for field in required_fields:
            if not data.get(field):
                return Response(
                    {"error": f"{field} is required"},
                    status=status.HTTP_400_BAD_REQUEST
                )
        # Check if a LeadSource with the same title already exists
        existing_lead_source = LeadSource.objects.filter(name=data['title'], leadsource_id=data['leadsource_id'],).first()
        if existing_lead_source:
           # Use the provided reff_code from ERP
            ref_code = existing_lead_source.ref_code 
            link = existing_lead_source.link 

            if existing_lead_source.link == "":
                import base64
                encoded_string = base64.b64encode(ref_code.encode('utf-8')).decode('utf-8')
                link = f'https://engage360.optiven.co.ke/lead-form/?ls={encoded_string}'

        else:
            # Create a new ref_code for the new record
            def generate_random_string(size=7):
                characters = string.ascii_letters + string.digits  # Uppercase, lowercase, and digits
                generate_code = ''.join(secrets.choice(characters) for _ in range(size))
                return 'LS-' + generate_code

            ref_code = generate_random_string()

            import base64
            encoded_string = base64.b64encode(ref_code.encode('utf-8')).decode('utf-8')
            link = f'https://engage360.optiven.co.ke/lead-form/?ls={encoded_string}'
            # lead_source.ref_code = base64_bytes.decode('ascii')


            # last = LeadSource.objects.order_by('-ref_code').first()
            # if last and last.ref_code and last.ref_code.startswith("LS") and last.ref_code[2:].isdigit():
            #     print (f"Last ref_code: {last.ref_code}")
            #     next_num = int(last.ref_code[2:]) + 1
            # else:
            #     next_num = 1
            # ref_code = f"LS{next_num:03d}"
            # print (f"Last ref_code3: {last.ref_code}")
        try:
            # Find subcategory and its associated category
            try:
                subcategory = LeadSourceSubCategory.objects.select_related('lead_source_category').get(
                    cat_lead_source_id=data['sub_category_id']
                )
            except LeadSourceSubCategory.DoesNotExist:
                return Response(
                    {"error": f"Sub category with ID {data['sub_category_id']} not found"},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Map ERP fields to CRM fields
            defaults = {
                # 'lead_source_category': subcategory.lead_source_category,
                'lead_source_subcategory': subcategory,
                'name': data['title'],
                'description': data.get('description'),
                'ref_code': ref_code,
                'link': link,
                'sales': data.get('sales', 0),
                'ongoing_sales': data.get('ongiong_sales', 0),  # Note: handling ERP's spelling
                'completed_sales': data.get('completed_sales', 0),
            }

            # Handle QR code if provided
            if data.get('QR'):
                defaults['qr_code'] = data['QR']

            # Handle last_update if provided
            if data.get('last_update'):
                try:
                    # You might need to adjust the datetime format based on what ERP sends
                    defaults['last_updated'] = datetime.strptime(data['last_update'], '%Y-%m-%d %H:%M:%S')
                except (ValueError, TypeError):
                    pass  # Use auto_now if parsing fails

            # Try to find and update existing record, or create new one
            lead_source, created = LeadSource.objects.update_or_create(
                leadsource_id=data['leadsource_id'],
                name=data['title'], 
                ref_code=ref_code,  # Ensure name is used for uniqueness
                defaults=defaults
            )

            # Serialize the result
            serializer = LeadSourceSerializer(lead_source)
            
            message = "Lead Source created successfully" if created else "Lead Source updated successfully"
            return Response({
                "message": message
            }, status=status.HTTP_200_OK if not created else status.HTTP_201_CREATED)

        except Exception as e:
            return Response(
                {"error": str(e)},
                status=status.HTTP_400_BAD_REQUEST
            ) 