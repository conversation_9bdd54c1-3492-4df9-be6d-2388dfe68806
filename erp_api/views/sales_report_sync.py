from decimal import Decimal
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.decorators import permission_classes
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
import os
from datetime import datetime
from django.db import transaction
from sales.models import (
    CashOnCash,
    MarketersTarget,
    CommissionLine,
    CommissionHeader
)
from sales.serializers import (
    CashOnCashSerializer,
    MarketersTargetSerializer,
    CommissionLineSerializer,
    CommissionHeaderSerializer
)
from users.models import User


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])

class CashOnCashSyncAPIVIEW(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync cash_on_cash from external system",
        operation_description=(
            "Sync cash_on_cash from external system based on `Period`, `marketer`, and `plot_no`. "
            "If the record exists, it updates it; otherwise, it creates a new record."
        ),
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["Period", "marketer", "plot_no"],
            properties={
                'Period': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='date',
                    description="Commission period (Date)"
                ),
                'marketer': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Marketer's employee number (ForeignKey to User model)"
                ),
                'Transaction_date': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='date',
                    description="Date of transaction"
                ),
                'client_no': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Client number"
                ),
                'client_name': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Client's full name"
                ),
                'plot_no': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Plot number"
                ),
                'amount': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Transaction amount"
                ),
                'bonus': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Bonus amount"
                ),
                'Regional_Category': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Regional category"
                ),
            }
        ),
        responses={
            200: openapi.Response("Cash_on_Cash updated successfully"),
            201: openapi.Response("Cash_on_Cash created successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            500: openapi.Response("Server error"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)

        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()

                period = data.get('Period')
                marketer = data.get('marketer')  # Expected to be employee_no
                plot_no = data.get('plot_no')

                instance, created = CashOnCash.objects.update_or_create(
                    Period=period,
                    marketer_id=marketer,
                    plot_no=plot_no,
                    defaults={
                        'Transaction_date': data.get('Transaction_date'),
                        'client_no': data.get('client_no'),
                        'client_name': data.get('client_name'),
                        'amount': Decimal(data.get('amount').replace(",", "")),
                        'bonus': Decimal(data.get('bonus').replace(",", "")),
                        'Regional_Category': data.get('Regional_Category'),
                    }
                )

                serializer = CashOnCashSerializer(instance)
                message = "Record created successfully" if created else "Record updated successfully"
                return Response({"message": message, 'data': serializer.data},
                                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class MarketerTargetSyncAPIVIEW(APIView):
    """
    This API view is for syncing the marketer target data from an external system.
    It allows for the creation and updating of target records based on the provided data.
    """

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync marketer target from external system",
        operation_description=(
            "Sync marketer target from external system based on `marketer` and `period_end_date`. "
            "If the record exists, it updates it; otherwise, it creates a new record."
        ),
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['marketer', 'period_start_date'],
            properties={
                'marketer': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    description="Marketer's employee number (ForeignKey to User model)"
                ),
                'period_start_date': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='date',
                    description="Start date of the commission period"
                ),
                'period_end_date': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    format='date',
                    description="End date of the commission period"
                ),
                'monthly_target': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    # format='decimal',
                    description="Monthly sales target"
                ),
                'daily_target': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    # format='decimal',
                    description="Daily sales target"
                ),
                'MIB_achieved': openapi.Schema(
                    type=openapi.TYPE_STRING,
                    # format='decimal',
                    description="Total MIB (Made In Bonus) achieved"
                ),
                'MIB_Perfomance': openapi.Schema(  # if this is not a typo
                    type=openapi.TYPE_STRING,
                    # format='decimal',
                    description="MIB performance as a percentage or value"
                ),
            }
        ),
        responses={
            200: openapi.Response("Marketer target updated successfully"),
            201: openapi.Response("Marketer target created successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            500: openapi.Response("Server error"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)

        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)
        try:
            with transaction.atomic():
                data = request.data.copy()

                marketer = data.get('marketer')  # Expected to be employee_no
                period_start_date = data.get('period_start_date')

                if User.objects.filter(employee_no=marketer).exists():
                    instance, created = MarketersTarget.objects.update_or_create(
                        marketer_no_id=marketer,
                        period_start_date=period_start_date,
                        defaults={
                            'period_end_date': data.get('period_end_date'),
                            'monthly_target': Decimal(data.get('monthly_target').replace(",", "")),
                            'daily_target': Decimal(data.get('daily_target').replace(",", "")),
                            'MIB_achieved': Decimal(data.get('MIB_achieved').replace(",", "")),
                            'MIB_Perfomance': Decimal(data.get('MIB_Perfomance').replace(",", "")),
                        }
                    )

                    serializer = MarketersTargetSerializer(instance)
                    message = "Record created successfully" if created else "Record updated successfully"
                    return Response({"message": message, 'data': serializer.data},
                                    status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)
                else: 
                    pass


        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])

class CommissionLinesSyncAPIVIEW(APIView):
    """
    This API view is for syncing the commission lines data from an external system.
    It allows for the creation and updating of commission line records based on the provided data.
    """

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync commission lines from external system",
        operation_description=(
            "Sync commission lines from external system based on `period_start_date`, `marketer_no`, and `plot_number`. "
            "If the record exists, it updates it; otherwise, it creates a new record."
        ),
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['marketer_no', 'period_start_date', 'plot_number'],
            properties={
                'marketer_no': openapi.Schema(type=openapi.TYPE_STRING, description="Marketer's employee number"),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'transaction_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'plot_number': openapi.Schema(type=openapi.TYPE_STRING),
                'new_deposits_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'installments_collected': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Commission line updated successfully"),
            201: openapi.Response("Commission line created successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            500: openapi.Response("Server error"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)
        try:
            with transaction.atomic():
                data = request.data.copy()
                employee_no = data.get("marketer_no")
                try:
                    employee = User.objects.get(employee_no=employee_no)
                except User.DoesNotExist:
                    return Response({"error": f"Employee with number {employee_no} does not exist."},
                                    status=status.HTTP_400_BAD_REQUEST)
                plot_number = data.get("plot_number")
                period_start_date = data.get("period_start_date")

                instance, created = CommissionLine.objects.update_or_create(
                    marketer_no=employee,
                    plot_number=plot_number,
                    period_start_date=period_start_date,
                    defaults={
                        'period_end_date': data.get('period_end_date'),
                        'transaction_date': data.get('transaction_date'),
                        'new_deposits_collected': Decimal(data.get('new_deposits_collected', 0).replace(",", "")),
                        'installments_collected': Decimal(data.get('installments_collected', 0).replace(",", "")),
                    }
                )

                serializer = CommissionLineSerializer(instance)
                message = "Record created successfully" if created else "Record updated successfully"
                return Response({"message": message, 'data': serializer.data},
                                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class CommissionHeaderSyncAPIVIEW(APIView):
    """
    This API view is for syncing the commission headers data from an external system.
    It allows for the creation and updating of commission header records based on the provided data.
    """

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync commission headers from external system",
        operation_description=(
            "Sync commission headers from external system based on `period_start_date`, `emp_no`. "
            "If the record exists, it updates it; otherwise, it creates a new record."
        ),
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['emp_no', 'period_start_date'],
            properties={
                'emp_no': openapi.Schema(type=openapi.TYPE_STRING, description="Employee number"),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'role': openapi.Schema(type=openapi.TYPE_STRING),
                'Deposit_amount': openapi.Schema(type=openapi.TYPE_STRING),
                'deposit_perc': openapi.Schema(type=openapi.TYPE_STRING),
                'deposit_commission': openapi.Schema(type=openapi.TYPE_STRING),
                'installment_amount': openapi.Schema(type=openapi.TYPE_STRING),
                'installment_perc': openapi.Schema(type=openapi.TYPE_STRING),
                'installment_commission': openapi.Schema(type=openapi.TYPE_STRING),
                'Total_commission': openapi.Schema(type=openapi.TYPE_STRING),
                'Tl_gained_comm_from_members': openapi.Schema(type=openapi.TYPE_STRING),
                'rm_achieved_MIB': openapi.Schema(type=openapi.TYPE_STRING),
                'rm_commission_rate': openapi.Schema(type=openapi.TYPE_STRING),
                'rm_commission_amount': openapi.Schema(type=openapi.TYPE_STRING),
                'commision_payable_TL': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Commission header updated successfully"),
            201: openapi.Response("Commission header created successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            500: openapi.Response("Server error"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)

        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)
        try:
            with transaction.atomic():
                data = request.data.copy()
        # Extracting the required fields from the request data
                emp_no = data.get("emp_no")
                period_start_date = data.get("period_start_date")
                instance, created = CommissionHeader.objects.update_or_create(
                    emp_no_id=emp_no,
                    period_start_date=period_start_date,
                    defaults={
                        'period_end_date': data.get('period_end_date'),
                        'role': data.get('role'),
                        'Deposit_amount': Decimal(data.get('Deposit_amount', 0).replace(",", "")),
                        'deposit_perc': Decimal(data.get('deposit_perc', 0).replace(",", "")),
                        'deposit_commission': Decimal(data.get('deposit_commission', 0).replace(",", "")),
                        'installment_amount': Decimal(data.get('installment_amount', 0).replace(",", "")),
                        'installment_perc': Decimal(data.get('installment_perc', 0).replace(",", "")),
                        'installment_commission': Decimal(data.get('installment_commission', 0).replace(",", "")),
                        'Total_commission': Decimal(data.get('Total_commission', 0).replace(",", "")),
                        'Tl_gained_comm_from_members': Decimal(data.get('Tl_gained_comm_from_members', 0).replace(",", "")),
                        'rm_achieved_MIB': Decimal(data.get('rm_achieved_MIB', 0).replace(",", "")),
                        'rm_commission_rate': Decimal(data.get('rm_commission_rate', 0).replace(",", "")),
                        'rm_commission_amount': Decimal(data.get('rm_commission_amount', 0).replace(",", "")),
                        'commisison_payable_TL': Decimal(data.get('commision_payable_TL', 0).replace(",", "")),
                    }
                )
                serializer = CommissionHeaderSerializer(instance)
                message = "Record created successfully" if created else "Record updated successfully"
                return Response(
                    {"message": message, "data": serializer.data},
                    status=status.HTTP_201_CREATED if created else status.HTTP_200_OK
                )
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


        

