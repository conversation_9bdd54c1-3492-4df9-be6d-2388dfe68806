import os
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from erp_api.models import FailedApiCall
from django.views.decorators.csrf import csrf_exempt
from rest_framework.decorators import permission_classes


@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class FailedApiWebhookAPIView(APIView):

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get("HTTP_X_FORWARDED_FOR")
        if x_forwarded_for:
            return x_forwarded_for.split(",")[0]
        return request.META.get("REMOTE_ADDR")

    @swagger_auto_schema(
        tags=["ERP > CRM Sync Error Handling"],
        operation_summary="Log failed API call from ERP sync",
        operation_description="Stores the failed API request for later retries.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["endpoint", "method", "status_code"],
            properties={
                'endpoint': openapi.Schema(type=openapi.TYPE_STRING, description="API URL that failed"),
                'method': openapi.Schema(type=openapi.TYPE_STRING, description="HTTP method used (GET, POST, etc.)"),
                'headers': openapi.Schema(type=openapi.TYPE_OBJECT, description="Headers sent with the request"),
                'body': openapi.Schema(type=openapi.TYPE_OBJECT, description="Request body payload"),
                'status_code': openapi.Schema(type=openapi.TYPE_INTEGER, description="HTTP response code received"),
                'response': openapi.Schema(type=openapi.TYPE_STRING, description="Response text or error message"),
            }
        ),
        responses={
            201: openapi.Response(description="Error logged successfully"),
            403: openapi.Response(description="IP not allowed"),
            400: openapi.Response(description="Invalid data"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)

        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        data = request.data

        try:
            FailedApiCall.objects.create(
                endpoint=data.get("endpoint"),
                request_method=data.get("method"),
                request_headers=data.get("headers", {}),
                request_body=data.get("body", {}),
                status_code=data.get("status_code", 500),
                response_body=data.get("response", ""),
            )
            return Response({"message": "Failed API call logged successfully"}, status=status.HTTP_201_CREATED)
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
