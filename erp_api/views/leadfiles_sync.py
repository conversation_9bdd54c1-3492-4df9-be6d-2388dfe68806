import os
from rest_framework.views import APIView
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from customers.models import Customer, CustomerGroups
from sales.serializers import LeadFileSerializer
from leads.models import LeadSource,LeadSourceCategory
from users.models import User
from inventory.models import Project, Plot
from sales.models import LeadFile
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class LeadfilesSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync leadfiles from external system",
    operation_description="Sync leadfiles from external system based on leadfile no. If the leadfiles exists, it updates; otherwise, creates a new leadfiles.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["lead_file_no", "lead_file_status_dropped","plot","project","marketer","balance_lcy","customer_id", ],
        properties={
            'lead_file_no': openapi.Schema(type=openapi.TYPE_STRING, description="Customer No (used as unique identifier for syncing -  previously ERP_id)"),
            'lead_file_status_dropped': openapi.Schema(type=openapi.TYPE_STRING, description="customer_name"),
            'plot': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'plot_no': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'project': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'marketer': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'purchase_price': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'selling_price': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'balance_lcy': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'customer_id': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'customer_name': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'purchase_type': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'commission_threshold': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'deposit_threshold': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'discount': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'completion_date': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'no_of_installments': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'installment_amount': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'sale_agreement_sent': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'sale_agreement_signed': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'total_paid': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'transfer_cost_charged': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'transfer_cost_paid': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'overpayments': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'refunds': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'refundable_amount': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'penalties_accrued': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'customer_lead_source': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'cat_lead_source': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'booking_id': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'booking_date': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'additional_deposit_date': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'title_status': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'credit_officer_id': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'lead_type': openapi.Schema(type=openapi.TYPE_STRING, description=""),

    
            
        }
    ),
    responses={
        200: openapi.Response("leadfile created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():

                data = request.data.copy()
                
                lead_file_no = data.get('lead_file_no')
                
                try:
                    plot_instance = Plot.objects.get(plot_no=data.get('plot_no'))
                    plot = plot_instance
                    plot_no = plot_instance.plot_no
                except Plot.DoesNotExist:
                    plot = None
                    plot_no = data.get('plot_no')
                    return Response({"error": f"Plot with plotNO {data.get('plot_no')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                try:
                    project = Project.objects.get(projectId=data.get('project'))
                except Project.DoesNotExist:
                    project = None 
                    return Response({"error": f"Project with projectId {data.get('project')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    customer = Customer.objects.get(customer_no=data.get('customer_id'))
                    customer.name = data.get('customer_name')
                    customer.save(update_fields=['customer_name'])
                except Customer.DoesNotExist:
                    customer = None
                    return Response({"error": f"Customer with customer_no {data.get('customer_id')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    marketer = User.objects.get(employee_no=data.get('marketer'))
                except User.DoesNotExist:
                    marketer = User.objects.get(employee_no="OL/HR/789")
                    
                    # return Response({"error": f"User with employee_no {data.get('marketer')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                
                try:
                    lead_source = LeadSource.objects.get(leadsource_id=data.get('customer_lead_source'))
                except LeadSource.DoesNotExist:
                   
                    lead_source = LeadSource.objects.get(leadsource_id="1")
                    # return Response({"error": f"LeadSource with leadsource_id: {data.get('customer_lead_source')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                # try:
                #     lead_source_category = LeadSourceCategory.objects.get(category_id=data.get('cat_lead_source'))
                # except LeadSourceCategory.DoesNotExist:
                #     lead_source_category = "1"
                #     # return Response({"error": f"LeadSourceCategory with category_id: {data.get('cat_lead_source')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)

                if data.get('booking_date')=="":
                    data['booking_date'] = None
                if data.get('additional_deposit_date')=="":
                    data['additional_deposit_date'] = None
                if data.get('completion_date')=="":
                    data['completion_date'] = None
                if data.get('booking_date') is not None:
                    data['booking_date'] = datetime.strptime(data.get('booking_date'), "%Y-%m-%d").strftime("%Y-%m-%d")
                if data.get('additional_deposit_date') is not None:
                    data['additional_deposit_date'] = datetime.strptime(data.get('additional_deposit_date'), "%Y-%m-%d").strftime("%Y-%m-%d")
                if data.get('completion_date') is not None:
                    data['completion_date'] = datetime.strptime(data.get('completion_date'), "%Y-%m-%d").strftime("%Y-%m-%d")
                
                if data.get('lead_file_status_dropped')=="No":
                    data['lead_file_status_dropped'] = False
                elif data.get('lead_file_status_dropped')=="Yes":
                    data['lead_file_status_dropped'] = True
                purchase_price = float(data.get('purchase_price').replace(',', '')) if data.get('purchase_price') else None
                selling_price = float(data.get('selling_price').replace(',', '')) if data.get('selling_price') else None
                balance_lcy = float(data.get('balance_lcy').replace(',', '')) if data.get('balance_lcy') else None
                commission_threshold = float(data.get('commission_threshold').replace(',', '')) if data.get('commission_threshold') else None
                deposit_threshold = float(data.get('deposit_threshold').replace(',', '')) if data.get('deposit_threshold') else None
                discount = float(data.get('discount').replace(',', '')) if data.get('discount') else None
                total_paid = float(data.get('total_paid').replace(',', '')) if data.get('total_paid') else None
                transfer_cost_charged = float(data.get('transfer_cost_charged').replace(',', '')) if data.get('transfer_cost_charged') else None
                transfer_cost_paid = float(data.get('transfer_cost_paid').replace(',', '')) if data.get('transfer_cost_paid') else None
                overpayments = float(data.get('overpayments').replace(',', '')) if data.get('overpayments') else None
                refunds = float(data.get('refunds').replace(',', '')) if data.get('refunds') else None
                refundable_amount = float(data.get('refundable_amount').replace(',', '')) if data.get('refundable_amount') else None
                penalties_accrued = float(data.get('penalties_accrued').replace(',', '')) if data.get('penalties_accrued') else None
                installment_amount = float(data.get('installment_amount').replace(',', '')) if data.get('installment_amount') else None
                no_of_installments = float(data.get('no_of_installments').replace(',', '')) if data.get('no_of_installments') else None
                
                sale, created = LeadFile.objects.update_or_create(
                    lead_file_no=lead_file_no,
                    defaults={ 
                        # 'customer_name': data.get('customer_name'),
                        'lead_file_status_dropped': data.get('lead_file_status_dropped'),
                        'plot': plot,
                        'plot_no': plot_no,
                        'project': project,
                        'marketer': marketer,
                        'purchase_price': purchase_price,
                        'selling_price': selling_price,
                        'balance_lcy': balance_lcy,
                        'customer_id': customer,
                        'customer_name': customer.customer_name,
                        'purchase_type': data.get('purchase_type'),
                        'commission_threshold': commission_threshold,
                        'deposit_threshold': deposit_threshold,
                        'discount': discount,
                        'completion_date': data.get('completion_date'),
                        'no_of_installments': no_of_installments,
                        'installment_amount': installment_amount,
                        'sale_agreement_sent': data.get('sale_agreement_sent'),
                        'sale_agreement_signed': data.get('sale_agreement_signed'),
                        'total_paid': total_paid,
                        'transfer_cost_charged': transfer_cost_charged,
                        'transfer_cost_paid': transfer_cost_paid,
                        'overpayments': overpayments,
                        'refunds': refunds,
                        'refundable_amount': refundable_amount,
                        'penalties_accrued': penalties_accrued,
                        'customer_lead_source': lead_source,
                        'cat_lead_source': lead_source.lead_source_subcategory.lead_source_category if lead_source.lead_source_subcategory else None,
                        'booking_id': data.get('booking_id'),
                        'booking_date': data.get('booking_date'),
                        'additional_deposit_date': data.get('additional_deposit_date'),
                        'title_status': data.get('title_status'),
                        'credit_officer_id': data.get('credit_officer_id'),
                        'lead_type': data.get('lead_type'),
                        
                        
                    }
                )
                serializer = LeadFileSerializer(sale, many=False)
                if created:
                    return Response({"message": "sale created successfully", 'data':serializer.data}, status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "sale updated successfully", 'data':serializer.data}, status=status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
