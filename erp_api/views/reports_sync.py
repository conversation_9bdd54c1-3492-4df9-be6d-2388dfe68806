from decimal import Decimal
import os
from rest_framework.views import APIView
from rest_framework import status
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny
from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from sales.models import HOSGMTarget, TeamTarget, PortfolioHeader, PortfolioLine
from sales.serializers import (
    HOSGMTargetSerializer, 
    TeamTargetSerializer,
    PortfolioHeaderSerializer,
    PortfolioLineSerializer
)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class HOSGMTargetSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync HOS/GM Target from ERP system",
        operation_description="Sync HOS/GM Target from ERP system based on line_no. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["line_no", "marketer_no", "marketer_name", "Title", "status", "period_start_date", 
                     "period_end_date", "monthly_target", "daily_target", "MIB_achieved", "MIB_Perfomance"],
            properties={
                'line_no': openapi.Schema(type=openapi.TYPE_INTEGER),
                'marketer_no': openapi.Schema(type=openapi.TYPE_STRING),
                'marketer_name': openapi.Schema(type=openapi.TYPE_STRING),
                'Title': openapi.Schema(type=openapi.TYPE_STRING),
                'status': openapi.Schema(type=openapi.TYPE_STRING),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'monthly_target': openapi.Schema(type=openapi.TYPE_STRING),
                'daily_target': openapi.Schema(type=openapi.TYPE_STRING),
                'MIB_achieved': openapi.Schema(type=openapi.TYPE_STRING),
                'MIB_Perfomance': openapi.Schema(type=openapi.TYPE_STRING),
                'commission_rate': openapi.Schema(type=openapi.TYPE_STRING),
                'commission_payable': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Target created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
                
                # Rename 'Title' to 'title' to match model field
                if 'Title' in data:
                    data['title'] = data.pop('Title')

                target, created = HOSGMTarget.objects.update_or_create(
                    line_no=data['line_no'],
                    defaults={
                        'marketer_no': data['marketer_no'],
                        'marketer_name': data['marketer_name'],
                        'title': data['title'],
                        'status': data['status'],
                        'period_start_date': data['period_start_date'],
                        'period_end_date': data['period_end_date'],
                        'monthly_target': Decimal(data.get('monthly_target', '0').replace(",", "")) if data.get('monthly_target') else 0,
                        'daily_target': Decimal(data.get('daily_target', '0').replace(",", "")) if data.get('daily_target') else 0,
                        'MIB_achieved': Decimal(data.get('MIB_achieved', '0').replace(",", "")) if data.get('MIB_achieved') else 0,
                        'MIB_Perfomance': Decimal(data.get('MIB_Perfomance', '0').replace(",", "")) if data.get('MIB_Perfomance') else 0,
                        'commission_rate': Decimal(data.get('commission_rate', '0').replace(",", "")) if data.get('commission_rate') else 0,
                        'commission_payable': Decimal(data.get('commission_payable', '0').replace(",", "")) if data.get('commission_payable') else 0
                    }
                )

                serializer = HOSGMTargetSerializer(target)
                message = "HOS/GM Target created successfully" if created else "HOS/GM Target updated successfully"
                return Response({
                    "message": message,
                    "data": serializer.data
                }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class TeamTargetSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Team Target from ERP system",
        operation_description="Sync Team Target from ERP system based on line_no. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["line_no", "team"],
            properties={
                'line_no': openapi.Schema(type=openapi.TYPE_INTEGER),
                'team': openapi.Schema(type=openapi.TYPE_STRING),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'monthly_target': openapi.Schema(type=openapi.TYPE_STRING),
                'daily_target': openapi.Schema(type=openapi.TYPE_STRING),
                'MIB_achieved': openapi.Schema(type=openapi.TYPE_STRING),
                'MIB_Perfomance': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Team Target created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
                
                target, created = TeamTarget.objects.update_or_create(
                    line_no=data['line_no'],
                    defaults={
                        'team': data['team'],
                        'period_start_date': data.get('period_start_date'),
                        'period_end_date': data.get('period_end_date'),
                        'monthly_target': Decimal(data.get('monthly_target', '0').replace(",", "")) if data.get('monthly_target') else 0,
                        'daily_target': Decimal(data.get('daily_target', '0').replace(",", "")) if data.get('daily_target') else 0,
                        'MIB_achieved': Decimal(data.get('MIB_achieved', '0').replace(",", "")) if data.get('MIB_achieved') else 0,
                        'MIB_Perfomance': Decimal(data.get('MIB_Perfomance', '0').replace(",", "")) if data.get('MIB_Perfomance') else 0,
                    }
                )

                serializer = TeamTargetSerializer(target)
                message = "Team Target created successfully" if created else "Team Target updated successfully"
                return Response({
                    "message": message,
                    "data": serializer.data
                }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class PortfolioHeaderSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Portfolio Header from ERP system",
        operation_description="Sync Portfolio Header from ERP system based on line_no. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[ "marketer_no", "period_start_date", "period_end_date", "total_purchases", "team", "region"],
            properties={
                
                'marketer_no': openapi.Schema(type=openapi.TYPE_STRING),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'total_purchases': openapi.Schema(type=openapi.TYPE_STRING),
                'team': openapi.Schema(type=openapi.TYPE_STRING),
                'region': openapi.Schema(type=openapi.TYPE_STRING),
                'total_collections': openapi.Schema(type=openapi.TYPE_STRING),
                'total_to_collect': openapi.Schema(type=openapi.TYPE_STRING),
                'total_installments_due': openapi.Schema(type=openapi.TYPE_STRING),
                'total_installments_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'total_overdue': openapi.Schema(type=openapi.TYPE_STRING),
                'total_overdue_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'total_previous_unpaid': openapi.Schema(type=openapi.TYPE_STRING),
                'portfolio_balance': openapi.Schema(type=openapi.TYPE_STRING),
                'marketer_target': openapi.Schema(type=openapi.TYPE_STRING),
                'MIB_perfomance': openapi.Schema(type=openapi.TYPE_STRING),
                'locality': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Portfolio Header created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
                
                header, created = PortfolioHeader.objects.update_or_create(
                    marketer_no=data['marketer_no'],
                    period_start_date=data['period_start_date'],
                    period_end_date=data['period_end_date'],
                    
                    defaults={
                        'total_purchases': Decimal(data.get('total_purchases', '0').replace(",", "")) if data.get('total_purchases') else 0,
                        'team': data.get('team', ''),
                        'region': data.get('region', ''),
                        'total_collections': Decimal(data.get('total_collections', '0').replace(",", "")) if data.get('total_collections') else 0,
                        'total_to_collect': Decimal(data.get('total_to_collect', '0').replace(",", "")) if data.get('total_to_collect') else 0,
                        'total_installments_due': Decimal(data.get('total_installments_due', '0').replace(",", "")) if data.get('total_installments_due') else 0,
                        'total_installments_collected': Decimal(data.get('total_installments_collected', '0').replace(",", "")) if data.get('total_installments_collected') else 0,
                        'total_overdue': Decimal(data.get('total_overdue', '0').replace(",", "")) if data.get('total_overdue') else 0,
                        'total_overdue_collected': Decimal(data.get('total_overdue_collected', '0').replace(",", "")) if data.get('total_overdue_collected') else 0,
                        'total_previous_unpaid': Decimal(data.get('total_previous_unpaid', '0').replace(",", "")) if data.get('total_previous_unpaid') else 0,
                        'portfolio_balance': Decimal(data.get('portfolio_balance', '0').replace(",", "")) if data.get('portfolio_balance') else 0,
                        'marketer_target': Decimal(data.get('marketer_target', '0').replace(",", "")) if data.get('marketer_target') else 0,
                        'MIB_perfomance': Decimal(data.get('MIB_perfomance', '0').replace(",", "")) if data.get('MIB_perfomance') else 0,
                        'locality': data.get('locality', '')
                    }
                )

                serializer = PortfolioHeaderSerializer(header)
                message = "Portfolio Header created successfully" if created else "Portfolio Header updated successfully"
                return Response({
                    "message": message,
                    "data": serializer.data
                }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class PortfolioLineSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')

    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Portfolio Line from ERP system",
        operation_description="Sync Portfolio Line from ERP system based on no. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[ "marketer_no", "period_start_date", "period_end_date", "lead_file_no", "plot_name"],
            properties={
                
                'marketer_no': openapi.Schema(type=openapi.TYPE_STRING),
                'period_start_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'period_end_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'lead_file_no': openapi.Schema(type=openapi.TYPE_STRING),
                'plot_name': openapi.Schema(type=openapi.TYPE_STRING),
                'installments_due': openapi.Schema(type=openapi.TYPE_STRING),
                'installments_due_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'overdue_collections': openapi.Schema(type=openapi.TYPE_STRING),
                'overdue_collections_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'previous_unpaid': openapi.Schema(type=openapi.TYPE_STRING),
                'total_to_collect': openapi.Schema(type=openapi.TYPE_STRING),
                'customer_number': openapi.Schema(type=openapi.TYPE_STRING),
                'total_previously_collected': openapi.Schema(type=openapi.TYPE_STRING),
                'penalties_accrued': openapi.Schema(type=openapi.TYPE_STRING),
                'current_balance': openapi.Schema(type=openapi.TYPE_STRING),
                'due_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'completion_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'booking_date': openapi.Schema(type=openapi.TYPE_STRING, format='date'),
                'total_collected': openapi.Schema(type=openapi.TYPE_STRING),
            }
        ),
        responses={
            200: openapi.Response("Portfolio Line created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
               
                line, created = PortfolioLine.objects.update_or_create(
                    
                    lead_file_no=data['lead_file_no'],
                    period_start_date=data['period_start_date'],
                    defaults={
                        'marketer_no': data['marketer_no'],
                        'period_end_date': data['period_end_date'],
                        'plot_name': data['plot_name'],
                        'installments_due': Decimal(data.get('installments_due', '0').replace(",", "")) if data.get('installments_due') else 0,
                        'installments_due_collected': Decimal(data.get('installments_due_collected', '0').replace(",", "")) if data.get('installments_due_collected') else 0,
                        'overdue_collections': Decimal(data.get('overdue_collections', '0').replace(",", "")) if data.get('overdue_collections') else 0,
                        'overdue_collections_collected': Decimal(data.get('overdue_collections_collected', '0').replace(",", "")) if data.get('overdue_collections_collected') else 0,
                        'previous_unpaid': Decimal(data.get('previous_unpaid', '0').replace(",", "")) if data.get('previous_unpaid') else 0,
                        'total_to_collect': Decimal(data.get('total_to_collect', '0').replace(",", "")) if data.get('total_to_collect') else 0,
                        'customer_number': data.get('customer_number', ''),
                        'total_previously_collected': Decimal(data.get('total_previously_collected', '0').replace(",", "")) if data.get('total_previously_collected') else 0,
                        'penalties_accrued': Decimal(data.get('penalties_accrued', '0').replace(",", "")) if data.get('penalties_accrued') else 0,
                        'current_balance': Decimal(data.get('current_balance', '0').replace(",", "")) if data.get('current_balance') else 0,
                        'due_date': data.get('due_date'),
                        'completion_date': data.get('completion_date'),
                        'booking_date': data.get('booking_date'),
                        'total_collected': Decimal(data.get('total_collected', '0').replace(",", "")) if data.get('total_collected') else 0
                    }
                )

                serializer = PortfolioLineSerializer(line)
                message = "Portfolio Line created successfully" if created else "Portfolio Line updated successfully"
                return Response({
                    "message": message,
                    "data": serializer.data
                }, status=status.HTTP_201_CREATED if created else status.HTTP_200_OK)

        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST) 