# views.py
import os
import json
import logging
from decimal import Decimal, InvalidOperation
from datetime import datetime
from urllib.parse import quote, urlencode

import requests
from requests_ntlm import HttpNtlmAuth

from django.db import transaction, models, IntegrityError
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from receipts.models import PostedReceipt, CancelledReceipt
from rest_framework.permissions import AllowAny

logger = logging.getLogger(__name__)

# ---- ERP / OData config ----
ODATA_HOST = os.getenv("BC_ODATA_HOST", "http://***********:8048")
BC_TENANT = os.getenv("BC_TENANT_PATH", "optiven/ODataV4")
BC_COMPANY = quote(os.getenv("BC_COMPANY", "Optiven R.E"))
BC_RECEIPTS_ENTITY = os.getenv("BC_RECEIPTS_ENTITY", "receiptlines")  # entity set name

AUTH = HttpNtlmAuth(
    os.getenv("BC_NTLM_USER", "OPTIVEN\\Dennis.Mwendwa"),
    os.getenv("BC_NTLM_PASS", "Den9@24!#"),
)

HEADERS = {
    "User-Agent": "OptivenERP/1.0",
    "Accept": "application/json",
    "Content-Type": "application/json",
}

# ----------------- helpers -----------------
def _parse_date(date_str):
    if not date_str:
        return None
    for fmt in ("%m/%d/%y", "%Y-%m-%d", "%m/%d/%Y"):
        try:
            d = datetime.strptime(str(date_str), fmt)
            return d.strftime("%Y-%m-%d")
        except ValueError:
            continue
    return None

def _to_decimal(val):
    if val in (None, "", "null"):
        return None
    try:
        if isinstance(val, str):
            val = val.replace(",", "").strip()
        return Decimal(val)
    except (InvalidOperation, ValueError, TypeError):
        return None

def _client_ip(request):
    xff = request.META.get("HTTP_X_FORWARDED_FOR")
    return xff.split(",")[0] if xff else request.META.get("REMOTE_ADDR")

def _odata_iterate_collection(base_url, params, auth, headers, timeout=20):
    """Iterate OData v4 collection following @odata.nextLink."""
    def make_url(url, params_dict):
        return f"{url}?{urlencode(params_dict)}" if params_dict else url
    url = make_url(base_url, params)
    while True:
        logger.info(f"Pulling OData page: {url}")
        r = requests.get(url, auth=auth, headers=headers, timeout=timeout)
        req_url = getattr(getattr(r, "request", None), "url", url)
        if not r.ok:
            logger.error("OData error %s: %s", r.status_code, r.text[:300])
            raise RuntimeError(f"OData GET failed. URL={req_url} STATUS={r.status_code} BODY={r.text[:300]}")
        payload = r.json()
        for item in payload.get("value", []):
            yield item
        next_link = payload.get("@odata.nextLink")
        if not next_link:
            break
        url = next_link

def _sanitize_select(select):
    """
    Accepts list/str/None and removes 'string' (swagger demo).
    Returns a comma-joined string or None to skip $select entirely.
    """
    if not select:
        return None
    if isinstance(select, str):
        if select.strip().lower() == "string":
            return None
        cleaned = [s.strip() for s in select.split(",") if s.strip() and s.strip().lower() != "string"]
        return ",".join(cleaned) if cleaned else None
    if isinstance(select, list):
        cleaned = [s.strip() for s in select if s and s.strip().lower() != "string"]
        return ",".join(cleaned) if cleaned else None
    return None

def _iterate_with_select_retry(base_url, odata_params, auth, headers, timeout=20):
    """
    Try to iterate with provided params; on error and if $select is present,
    retry once without $select.
    """
    try:
        for row in _odata_iterate_collection(base_url, odata_params, auth, headers, timeout):
            yield row
    except RuntimeError as e:
        if "$select" in odata_params:
            logger.warning("OData failed with $select (%s). Retrying without $select.", e)
            params_no_select = dict(odata_params)
            params_no_select.pop("$select", None)
            for row in _odata_iterate_collection(base_url, params_no_select, auth, headers, timeout):
                yield row
        else:
            raise

def pick(row, *candidates):
    for k in candidates:
        if k in row and row.get(k) not in (None, "", "NULL"):
            return row.get(k)
    return None

def as_bool(v):
    if isinstance(v, bool):
        return v
    if v is None:
        return False
    s = str(v).strip().lower()
    return s in ("1", "true", "t", "yes", "y")

def _filter_to_model_fields(model, data: dict) -> dict:
    """Keep only keys that are concrete fields on model."""
    model_fields = {f.name for f in model._meta.get_fields() if hasattr(f, "attname")}
    return {k: v for k, v in data.items() if k in model_fields}

def _log_dropped_keys(model, data: dict):
    model_fields = {f.name for f in model._meta.get_fields() if hasattr(f, "attname")}
    dropped = sorted(set(data.keys()) - model_fields)
    if dropped:
        logger.debug("Dropping unknown fields for %s: %s", model.__name__, ", ".join(dropped))

# --- allocate line_no safely with per-row inner transactions and retry ---
def _allocate_line_nos_with_retry(model, to_create, retry_limit=20):
    """
    Assign unique line_no values (max(line_no)+1) and insert one-by-one.
    Each insert has its own inner transaction. Collisions retry.
    Returns number_created.
    """
    if not to_create:
        return 0

    created = 0
    next_ln = (model.objects.aggregate(m=models.Max("line_no"))["m"] or 0)

    for obj in to_create:
        attempts = 0
        while True:
            attempts += 1
            if getattr(obj, "line_no", None) in (None, "", 0):
                next_ln += 1
                obj.line_no = next_ln

            try:
                with transaction.atomic():
                    obj.save(force_insert=True)
                created += 1
                break
            except IntegrityError:
                if attempts >= retry_limit:
                    raise
                next_ln = max(next_ln, int(obj.line_no or 0))
                obj.line_no = None
                continue

    return created

def _bulk_upsert(model, objs, batch_size=500):
    """
    Upsert by (Receipt_No, Type).
    Inserts: per-row with retry-safe line_no allocator (inner transactions).
    Updates: bulk_update in a small transaction.
    Returns (created_count, updated_count)
    """
    if not objs:
        return (0, 0)

    fields = [f.name for f in model._meta.get_fields() if hasattr(f, "attname")]
    updatable_fields = [f for f in fields if f not in ("id", "line_no")]

    keys = [(o.Receipt_No, o.Type) for o in objs]
    by_key_obj = {(o.Receipt_No, o.Type): o for o in objs}

    existing_map = {}

    def chunked(seq, n=1000):
        for i in range(0, len(seq), n):
            yield seq[i:i+n]

    all_receipt_nos = list({k[0] for k in keys})
    for nos in chunked(all_receipt_nos, 1000):
        for row in model.objects.filter(Receipt_No__in=nos):
            existing_map[(row.Receipt_No, row.Type)] = row

    to_create, to_update = [], []
    for k in keys:
        if k in existing_map:
            src = by_key_obj[k]
            dst = existing_map[k]
            changed = False
            for f in updatable_fields:
                new_val = getattr(src, f, None)
                if getattr(dst, f, None) != new_val:
                    setattr(dst, f, new_val)
                    changed = True
            if changed:
                to_update.append(dst)
        else:
            to_create.append(by_key_obj[k])

    created_count = 0
    if to_create:
        created_count = _allocate_line_nos_with_retry(model, to_create)

    updated_count = 0
    if to_update:
        with transaction.atomic():
            for chunk in chunked(to_update, batch_size):
                model.objects.bulk_update(chunk, updatable_fields, batch_size=batch_size)
        updated_count = len(to_update)

    return (created_count, updated_count)

class ERPIReceiptsBulkSync(APIView):
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=["ERP Integration"],
        operation_summary="Pull first N receipts (no filters) and upsert",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "page_size": openapi.Schema(type=openapi.TYPE_INTEGER, description="Max rows to pull (default 1000)"),
                "select": openapi.Schema(
                    type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_STRING),
                    description="Optional OData $select field list (use exact ERP keys)"
                ),
                "preview_limit": openapi.Schema(type=openapi.TYPE_INTEGER, description="How many records to print/return in preview (default 5)"),
            },
        ),
        responses={200: "Sync summary with RAW + mapped previews"}
    )
    def post(self, request):
        # --- allowlist by IP (optional; uncomment to enforce) ---
        # allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")
        # ip = _client_ip(request)
        # if ip != allowed_ip:
        #     return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        page_size = int(request.data.get("page_size") or 70000)
        select = request.data.get("select") or None
        preview_limit = int(request.data.get("preview_limit") or 5)

        # Endpoint (no filters)
        base_url = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/{BC_RECEIPTS_ENTITY}"
        odata_params = {"$top": page_size}

        # Sanitize $select to avoid "?$select=string"
        safe_select = _sanitize_select(select)
        if safe_select:
            odata_params["$select"] = safe_select

        posted_objs, cancelled_objs = [], []
        raw_count = 0
        pulled_preview_raw, pulled_preview_mapped = [], []

        try:
            # iterate (with auto-retry removing $select if BC rejects it)
            for row in _iterate_with_select_retry(base_url, odata_params, AUTH, HEADERS):
                raw_count += 1
                if raw_count > page_size:
                    break

                # PREVIEW RAW
                if len(pulled_preview_raw) < preview_limit:
                    pulled_preview_raw.append({k: row.get(k) for k in list(row.keys())})

                # Receipt number (No/Receipt_No variants)
                receipt_no = str(pick(
                    row, "Receipt_No", "Receipt No.", "Receipt No", "ReceiptNo",
                    "Document_No", "Document No.", "No", "No_", "Receipt_No_", "No"
                ) or "").strip()
                if not receipt_no:
                    continue

                # STRICT boolean rules:
                # - Posted == True and Cancelled == False  -> Posted
                # - Posted == True and Cancelled == True   -> Cancelled
                # - else                                   -> skip
                posted_flag = as_bool(row.get("Posted"))
                cancel_flag = as_bool(row.get("Cancelled"))
                if not posted_flag:
                    continue
                rtype = "Cancelled" if cancel_flag else "Posted"

                # Map fields (DO NOT take incoming line_no; assign on insert)
                mapped = {
                    "Receipt_No": receipt_no,
                    "Date_Posted": _parse_date(pick(row, "Date_Posted", "Date Posted", "Posting_Date", "Posting Date")),
                    "Payment_date": _parse_date(pick(row, "Payment_Date", "Payment Date")),
                    "Bank_Name": pick(row, "Rcpt_Bank_Name", "Rcpt Bank Name", "Bank_Name", "Bank Name"),
                    "Bank_Account": pick(row, "Bank_Account", "Bank Account"),
                    "Customer_Id": pick(row, "Cust_No", "Cust No.", "Customer_Id", "Customer_No", "Customer No."),
                    "Customer_Name": pick(row, "Account_Name", "Customer_Name"),
                    "Pay_mode": pick(row, "Pay_Mode", "Pay Mode", "Pay_mode"),
                    "Lead_file_no": pick(row, "Lead_File_No", "Lead File No.", "Lead_file_no"),
                    "Project_Name": pick(row, "Project_Name", "Project Name"),
                    "Plot_NO": pick(row, "Plot_No", "Plot No.", "Plot_NO", "Plot No"),
                    "Marketer": pick(row, "Sales_Person_Code", "Sales Person Code"),
                    "Teams": pick(row, "Shortcut_Dimension_2_Code", "Teams", "Team"),
                    "Regions": pick(row, "Global_Dimension_1_Code", "Regions", "Region"),
                    "Deposit_Threshold": (str(pick(row, "Deposit_Threshold", "Deposit Threshold")) or "").replace(",", "")
                        if pick(row, "Deposit_Threshold", "Deposit Threshold") else None,
                    "Transaction_type": pick(row, "Transaction_Type", "Transaction Type", "Transaction_type"),
                    "transfer_receipt": pick(row, "Transfer_Receipt", "Transfer Receipt"),
                    "Amount_LCY": _to_decimal(pick(row, "Amount_LCY", "Amount (LCY)", "Amount")),
                    "Balance_LCY": _to_decimal(pick(row, "LF_Balance", "Balance_LCY", "Balance")),
                    "Type": rtype,
                    "status": pick(row, "status", "Status"),
                    "POSTED_DATE1": _parse_date(pick(row, "POSTED_DATE1", "Date Posted", "Posting Date")),
                    "PAYMENT_DATE1": _parse_date(pick(row, "PAYMENT_DATE1", "Payment_Date", "Payment Date")),
                    "Narration": pick(row, "Narration", "Description"),
                    "assigned_to": pick(row, "assigned_to", "AssignedTo"),
                    "created_at":datetime.now(),
                    "updated_at":datetime.now(),
                    # Only meaningful for cancelled; filter will drop for PostedReceipt if not a field there
                    "cancellation_date": _parse_date(pick(
                        row,
                        "Cancellation_Date", "Cancelled_Date", "Canceled_Date",
                        "Cancellation Date", "Cancelled Date", "Canceled Date"
                    )),
                }

                # PREVIEW MAPPED (pre-filter)
                if len(pulled_preview_mapped) < preview_limit:
                    pulled_preview_mapped.append(mapped.copy())

                # Filter by model to avoid unexpected kwargs
                if rtype == "Posted":
                    _log_dropped_keys(PostedReceipt, mapped)
                    data = _filter_to_model_fields(PostedReceipt, mapped)
                    posted_objs.append(PostedReceipt(**data))
                else:
                    _log_dropped_keys(CancelledReceipt, mapped)
                    data = _filter_to_model_fields(CancelledReceipt, mapped)
                    cancelled_objs.append(CancelledReceipt(**data))

        except Exception as e:
            logger.exception("Error pulling from OData")
            return Response({"error": f"Failed reading OData: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- Upsert (no giant atomic; small transactions inside helpers) ---
        try:
            c_post, u_post = _bulk_upsert(PostedReceipt, posted_objs)
            c_can, u_can  = _bulk_upsert(CancelledReceipt, cancelled_objs)
        except IntegrityError as e:
            logger.exception("DB upsert failed (integrity)")
            return Response({"error": f"DB upsert failed: {e}"}, status=status.HTTP_409_CONFLICT)
        except Exception as e:
            logger.exception("DB upsert failed")
            return Response({"error": f"DB upsert failed: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Print to console
        if pulled_preview_raw:
            print("\n[ERP RAW PREVIEW]")
            print(json.dumps(pulled_preview_raw, indent=2, default=str))
        if pulled_preview_mapped:
            print("\n[MAPPED PREVIEW]  (pre-filter)")
            print(json.dumps(pulled_preview_mapped, indent=2, default=str))

        summary = {
            "erp_rows_scanned": raw_count,
            "posted": {"created": c_post, "updated": u_post, "total_in_batch": len(posted_objs)},
            "cancelled": {"created": c_can, "updated": u_can, "total_in_batch": len(cancelled_objs)},
            "preview": {"raw": pulled_preview_raw, "mapped": pulled_preview_mapped},
        }
        return Response(summary, status=status.HTTP_200_OK)


