import os
from rest_framework.views import APIView
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from django.db import transaction, IntegrityError
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from receipts.models import InstallmentSchedule
from receipts.serializers import InstallmentScheduleSerializer
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class InstallmentScheduleSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    def parse_date(self, date_str):
        if not date_str:
            return None
        try:
            return datetime.strptime(date_str, "%m/%d/%y").strftime("%Y-%m-%d")
        except ValueError:
            try:
                return datetime.strptime(date_str, "%Y-%m-%d").strftime("%Y-%m-%d")
            except ValueError:
                return None
    
    @swagger_auto_schema(
        tags=["Data Sync From ERP System"],
        operation_summary="Sync Installment Schedule from external system",
        operation_description="Sync Installment Schedule from external system based on IS_id. If the record exists, it updates; otherwise, creates a new record.",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=[ "member_no", "leadfile_no", "line_no", "installment_no", "installment_amount", 
                     "remaining_Amount", "due_date", "paid", "plot_No", "plot_Name", "amount_Paid", "penaties_Accrued"],
            properties={
                
                'member_no': openapi.Schema(type=openapi.TYPE_STRING, description="Member Number"),
                'leadfile_no': openapi.Schema(type=openapi.TYPE_STRING, description="Lead File Number"),
                'line_no': openapi.Schema(type=openapi.TYPE_INTEGER, description="Line Number"),
                'installment_no': openapi.Schema(type=openapi.TYPE_INTEGER, description="Installment Number"),
                'installment_amount': openapi.Schema(type=openapi.TYPE_STRING, description="Installment Amount"),
                'remaining_Amount': openapi.Schema(type=openapi.TYPE_STRING, description="Remaining Amount"),
                'due_date': openapi.Schema(type=openapi.TYPE_STRING, description="Due Date (MM/DD/YY or YYYY-MM-DD)"),
                'paid': openapi.Schema(type=openapi.TYPE_STRING, description="Paid Status (Yes/No)"),
                'plot_No': openapi.Schema(type=openapi.TYPE_STRING, description="Plot Number"),
                'plot_Name': openapi.Schema(type=openapi.TYPE_STRING, description="Plot Name"),
                'amount_Paid': openapi.Schema(type=openapi.TYPE_STRING, description="Amount Paid"),
                'penaties_Accrued': openapi.Schema(type=openapi.TYPE_INTEGER, description="Penalties Accrued"),
            }
        ),
        responses={
            200: openapi.Response("Installment Schedule created or updated successfully"),
            400: openapi.Response("Validation error"),
            403: openapi.Response("Unauthorized access (IP not allowed)"),
            409: openapi.Response("Record already exists"),
        }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():
                data = request.data.copy()
                
                # Convert date field
                data['due_date'] = self.parse_date(data.get('due_date'))

                leadfile_no = data.get('leadfile_no')
                if not leadfile_no:
                    return Response({"error": "leadfile_no is required"}, status=status.HTTP_400_BAD_REQUEST)

                
                schedule, created = InstallmentSchedule.objects.update_or_create(
                    
                    leadfile_no=leadfile_no,
                    member_no=data.get('member_no'),
                    installment_no=data.get('installment_no'),
                    
                    defaults={
                        'line_no': data.get('line_no'),
                        'installment_amount': data.get('installment_amount'),
                        'remaining_Amount': data.get('remaining_Amount'),
                        'due_date': data.get('due_date'),
                        'paid': data.get('paid'),
                        'plot_No': data.get('plot_No'),
                        'plot_Name': data.get('plot_Name'),
                        'amount_Paid': data.get('amount_Paid'),
                        'penaties_Accrued': data.get('penaties_Accrued'),
                    }
                )
                serializer = InstallmentScheduleSerializer(schedule, many=False)

                if created:
                    return Response({"message": "Installment schedule created successfully", 'data':serializer.data}, 
                                  status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "Installment schedule updated successfully", 'data':serializer.data}, 
                                  status=status.HTTP_200_OK)
                
                
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST) 