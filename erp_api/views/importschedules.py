# views.py
import os
import json
import logging
from datetime import datetime, date
from decimal import Decimal
from urllib.parse import quote, urlencode

import requests
from requests_ntlm import HttpNtlmAuth

from django.db import transaction
from django.utils import timezone
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status

from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from receipts.models import InstallmentSchedule

logger = logging.getLogger(__name__)

# ---------------- OData / BC config ----------------
ODATA_HOST = os.getenv("BC_ODATA_HOST", "http://***********:8048")
BC_TENANT = os.getenv("BC_TENANT_PATH", "optiven/ODataV4")
BC_COMPANY = quote(os.getenv("BC_COMPANY", "Optiven R.E"))
BC_SCHEDULES_ENTITY = os.getenv("BC_SCHEDULES_ENTITY", "installmentschedules")

AUTH = HttpNtlmAuth(
    os.getenv("BC_NTLM_USER", "OPTIVEN\\Dennis.Mwendwa"),
    os.getenv("BC_NTLM_PASS", "Den9@24!#"),
)

HEADERS = {
    "User-Agent": "OptivenERP/1.0",
    "Accept": "application/json",
    "Content-Type": "application/json",
}

# ---------------- helpers ----------------
def _parse_date(v):
    if not v:
        return None
    s = str(v).strip()
    for fmt in ("%Y-%m-%d", "%m/%d/%Y", "%m/%d/%y", "%d/%m/%Y", "%d-%m-%Y"):
        try:
            return datetime.strptime(s, fmt).date()
        except ValueError:
            continue
    try:
        return datetime.fromisoformat(s).date()
    except Exception:
        return None

def _to_int(v):
    try:
        return int(str(v).strip())
    except Exception:
        return None

def _to_str_amount(v):
    """Your model stores amounts as CharField; save normalized numeric text."""
    if v is None:
        return ""
    if isinstance(v, (int, float, Decimal)):
        return str(v)
    s = str(v).strip()
    return s.replace(",", "")

def _to_paid_str(v) -> str:
    s = str(v).strip().lower()
    return "True" if s in ("1", "true", "t", "yes", "y") else "False"

def _pick(row, *candidates):
    for k in candidates:
        if k in row and row.get(k) not in (None, "", "NULL"):
            return row.get(k)
    return None

def _chunked(seq, n=1000):
    for i in range(0, len(seq), n):
        yield seq[i:i+n]

def _sanitize_select(select):
    """Drop swagger-demo junk like 'string'."""
    if not select:
        return None
    if isinstance(select, str):
        cleaned = [s.strip() for s in select.split(",") if s.strip() and s.strip().lower() != "string"]
        return ",".join(cleaned) if cleaned else None
    if isinstance(select, list):
        cleaned = [s.strip() for s in select if s and s.strip().lower() != "string"]
        return ",".join(cleaned) if cleaned else None
    return None

def _odata_iterate_collection(base_url, params, auth, headers, timeout=20):
    """Iterate OData v4 collection following @odata.nextLink."""
    def make_url(url, params_dict):
        return f"{url}?{urlencode(params_dict)}" if params_dict else url
    url = make_url(base_url, params)
    while True:
        logger.info(f"Pulling OData page: {url}")
        r = requests.get(url, auth=auth, headers=headers, timeout=timeout)
        req_url = getattr(getattr(r, "request", None), "url", url)
        if not r.ok:
            logger.error("OData error %s: %s", r.status_code, r.text[:300])
            raise RuntimeError(f"OData GET failed. URL={req_url} STATUS={r.status_code} BODY={r.text[:300]}")
        payload = r.json()
        for item in payload.get("value", []):
            yield item
        next_link = payload.get("@odata.nextLink")
        if not next_link:
            break
        url = next_link

def _iterate_with_select_retry(base_url, odata_params, auth, headers, timeout=20):
    """Retry once without $select if BC rejects it."""
    try:
        for row in _odata_iterate_collection(base_url, odata_params, auth, headers, timeout):
            yield row
    except RuntimeError as e:
        if "$select" in odata_params:
            logger.warning("OData failed with $select (%s). Retrying without $select.", e)
            params2 = dict(odata_params)
            params2.pop("$select", None)
            for row in _odata_iterate_collection(base_url, params2, auth, headers, timeout):
                yield row
        else:
            raise

# ---------------- BULK UPSERT core (create + update) ----------------
def _bulk_upsert_installment_schedules(normalized_rows, batch_size=1000):
    """
    Upsert into InstallmentSchedule using natural key:
    (leadfile_no, member_no, installment_no)
    - INSERT requires incoming line_no; we do not auto-assign.
    - UPDATE never modifies existing line_no.
    """
    if not normalized_rows:
        return (0, 0, 0)

    # Prefetch existing rows by leadfile_no chunks
    keys = [(r["leadfile_no"], r["member_no"], r["installment_no"]) for r in normalized_rows]
    existing_by_key = {}
    for chunk in _chunked(list({k[0] for k in keys}), 1000000):
        for row in InstallmentSchedule.objects.filter(leadfile_no__in=chunk):
            existing_by_key[(row.leadfile_no, row.member_no, row.installment_no)] = row

    to_insert = []
    to_update = []
    skipped_missing_line_on_insert = 0
    now = timezone.now()

    for data in normalized_rows:
        key = (data["leadfile_no"], data["member_no"], data["installment_no"])
        existing = existing_by_key.get(key)
        if existing:
            # UPDATE mutable fields (keep line_no)
            existing.installment_amount = data["installment_amount"]
            existing.remaining_Amount   = data["remaining_Amount"]
            existing.due_date           = data["due_date"]
            existing.paid               = data["paid"]
            existing.plot_No            = data["plot_No"]
            existing.plot_Name          = data["plot_Name"]
            existing.amount_Paid        = data["amount_Paid"]
            existing.penaties_Accrued   = data["penaties_Accrued"]
            existing.updated_at         = now
            to_update.append(existing)
        else:
            # INSERT requires incoming line_no
            if data["line_no"] in (None, ""):
                skipped_missing_line_on_insert += 1
                continue
            to_insert.append(InstallmentSchedule(**data))

    created = updated = 0
    with transaction.atomic():
        if to_insert:
            for chunk in _chunked(to_insert, batch_size):
                InstallmentSchedule.objects.bulk_create(chunk, batch_size=batch_size)
                created += len(chunk)

        if to_update:
            fields_to_update = [
                "installment_amount",
                "remaining_Amount",
                "due_date",
                "paid",
                "plot_No",
                "plot_Name",
                "amount_Paid",
                "penaties_Accrued",
                "updated_at",
            ]
            for chunk in _chunked(to_update, batch_size):
                InstallmentSchedule.objects.bulk_update(chunk, fields_to_update, batch_size=batch_size)
                updated += len(chunk)

    return created, updated, skipped_missing_line_on_insert

# -------------------- ERP → DB: pull & upsert --------------------
class ERPInstallmentScheduleBulkSync(APIView):
    """
    POST /api/installment-schedules/sync-from-erp
    Body:
    {
      "page_size": 1000,
      "select": ["Document_No","MemberNo","Installment_No","Line_No","Due_Date","Paid",
                 "Installment_Amount","Remaining_Amount","Amount_Paid","Plot_No","Plot_Name","Accrued_Penalty"],
      "preview_limit": 5
    }

    Pulls first N rows from BC OData and upserts into installment_schedule.
    """
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=["ERP Integration"],
        operation_summary="Pull from ERP (OData) and upsert into installment_schedule",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                "page_size": openapi.Schema(type=openapi.TYPE_INTEGER, description="Max rows to pull (default 1000)"),
                "select": openapi.Schema(
                    type=openapi.TYPE_ARRAY, items=openapi.Items(type=openapi.TYPE_STRING),
                    description="Optional OData $select list (exact ERP keys)."
                ),
                "preview_limit": openapi.Schema(type=openapi.TYPE_INTEGER, description="How many records to preview (default 5)"),
            },
        ),
        responses={200: "Sync summary with RAW + mapped previews"}
    )
    def post(self, request):
        page_size = int(request.data.get("page_size") or 1000000)
        select = request.data.get("select") or None
        preview_limit = int(request.data.get("preview_limit") or 5)

        base_url = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/{BC_SCHEDULES_ENTITY}"
        odata_params = {"$top": page_size}
        safe_select = _sanitize_select(select)
        if safe_select:
            odata_params["$select"] = safe_select

        raw_count = 0
        preview_raw, preview_mapped = [], []
        normalized_rows = []
        invalid_key_rows = 0

        try:
            for row in _iterate_with_select_retry(base_url, odata_params, AUTH, HEADERS):
                raw_count += 1
                if raw_count > page_size:
                    break

                if len(preview_raw) < preview_limit:
                    preview_raw.append({k: row.get(k) for k in list(row.keys())})

                # Natural key from ERP sample:
                # Document_No -> leadfile_no
                # MemberNo    -> member_no
                # Installment_No -> installment_no
                leadfile_no = str(_pick(row, "Document_No", "Document No.", "Lead_File_No", "Lead File No.", "leadfile_no") or "").strip()
                member_no   = str(_pick(row, "MemberNo", "Member_No", "Member No.", "member_no") or "").strip()
                inst_no     = _to_int(_pick(row, "Installment_No", "Installment No.", "installment_no"))
                if not (leadfile_no and member_no and inst_no is not None):
                    invalid_key_rows += 1
                    continue

                line_no = _to_int(_pick(row, "Line_No", "Line No.", "AuxiliaryIndex1", "id", "line_no"))

                mapped = {
                    "leadfile_no": leadfile_no,
                    "member_no": member_no,
                    "installment_no": inst_no,
                    "line_no": line_no,  # used on inserts only

                    "installment_amount": _to_str_amount(_pick(row, "Installment_Amount", "Installment Amount", "Amount", "installment_amount")),
                    "remaining_Amount":   _to_str_amount(_pick(row, "Remaining_Amount", "Remaining Amount", "Remaining", "remaining_Amount")),
                    "due_date":           _parse_date(_pick(row, "Due_Date", "Due Date", "due_date")),
                    "paid":               _to_paid_str(_pick(row, "Paid", "IsPaid", "paid")),
                    "plot_No":            str(_pick(row, "Plot_No", "Plot No.", "PlotNo", "plot_No") or "")[:20],
                    "plot_Name":          str(_pick(row, "Plot_Name", "Plot Name", "plot_Name") or "")[:10],
                    "amount_Paid":        _to_str_amount(_pick(row, "Amount_Paid", "Amount Paid", "amount_Paid")),
                    # Accrued_Penalty in ERP -> penaties_Accrued in model
                    "penaties_Accrued":   _to_int(_pick(row, "Accrued_Penalty", "Penalties", "Penalties Accrued", "penaties_Accrued")) or 0,
                }

                if len(preview_mapped) < preview_limit:
                    p = mapped.copy()
                    if isinstance(p["due_date"], date):
                        p["due_date"] = p["due_date"].isoformat()
                    preview_mapped.append(p)

                normalized_rows.append(mapped)

        except Exception as e:
            logger.exception("Error pulling from OData")
            return Response({"error": f"Failed reading OData: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Upsert into DB
        try:
            created, updated, skipped_missing_line_on_insert = _bulk_upsert_installment_schedules(normalized_rows)
        except Exception as e:
            logger.exception("DB upsert failed")
            return Response({"error": f"DB upsert failed: {e}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # Console previews for quick inspection (optional)
        if preview_raw:
            print("\n[ERP RAW PREVIEW]")
            print(json.dumps(preview_raw, indent=2, default=str))
        if preview_mapped:
            print("\n[MAPPED PREVIEW]")
            print(json.dumps(preview_mapped, indent=2, default=str))

        return Response(
            {
                "erp_rows_scanned": raw_count,
                "created": created,
                "updated": updated,
                "invalid_key_rows": invalid_key_rows,
                "skipped_missing_line_on_insert": skipped_missing_line_on_insert,
                "preview": {"raw": preview_raw, "mapped": preview_mapped},
            },
            status=status.HTTP_200_OK,
        )
