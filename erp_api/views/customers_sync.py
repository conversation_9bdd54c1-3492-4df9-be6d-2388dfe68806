import os
from rest_framework.views import APIView
from rest_framework import status, permissions
from rest_framework.response import Response
from rest_framework.decorators import permission_classes
from rest_framework.permissions import AllowAny

from django.db import transaction
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from customers.models import Customer, CustomerGroups
from customers.serializers import CustomerSerializer, CustomerGroupsSerializer
from leads.models import LeadSource
from users.models import User
from datetime import datetime

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class CustomerSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync Customer from external system",
    operation_description="Sync Customer from external system based on customer_no. If the Customer exists, it updates; otherwise, creates a new Customer.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["customer_no", "customer_name", ],
        properties={
            'customer_no': openapi.Schema(type=openapi.TYPE_STRING, description="Customer No (used as unique identifier for syncing -  previously ERP_id)"),
            'customer_name': openapi.Schema(type=openapi.TYPE_STRING, description="customer_name"),
            'national_id': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'passport_no': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'kra_pin': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'dob': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'gender': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'marital_status': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'phone': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'alternative_phone': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'primary_email': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'alternative_email': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'address': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'customer_type': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'country_of_residence': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'date_of_registration': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'lead_source': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'marketer': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            
        }
    ),
    responses={
        200: openapi.Response("Customer created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():

                data = request.data.copy()
                
                customer_no = data.get('customer_no')
                try:
                    lead_source = LeadSource.objects.get(leadsource_id=data.get('lead_source'))
                except LeadSource.DoesNotExist:
                    lead_source = LeadSource.objects.get(leadsource_id=40)
                
                try:
                    marketer = User.objects.get(employee_no=data.get('marketer'))
                except User.DoesNotExist:
                    marketer = User.objects.get(employee_no='EMP00255') 
                    # return Response({"error": f"Marketer with employee_no {data.get('marketer')} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                if data.get('dob')=="":
                    data['dob'] = None
                else:
                    data['dob'] = datetime.strptime(data.get('dob'), "%m/%d/%y").strftime("%Y-%m-%d")
                
                reg_date = data.get('date_of_registration')
                reg_date = datetime.strptime(reg_date, "%m/%d/%y").strftime("%Y-%m-%d")

                customer, created = Customer.objects.update_or_create(
                    customer_no=customer_no,
                    defaults={
                        'customer_name': data.get('customer_name'),
                        'national_id': data.get('national_id'),
                        'passport_no': data.get('passport_no'),
                        'kra_pin': data.get('kra_pin'),
                        'dob': data.get('dob'),
                        'gender': data.get('gender'),
                        'marital_status': data.get('marital_status'),
                        'phone': data.get('phone'),
                        'alternative_phone': data.get('alternative_phone'),
                        'primary_email': data.get('primary_email'),
                        'alternative_email': data.get('alternative_email'),
                        'address': data.get('address'),
                        'customer_type': data.get('customer_type'),
                        'country_of_residence': data.get('country_of_residence'),
                        'date_of_registration': reg_date,
                        'lead_source': lead_source,
                        'marketer': marketer
                    }
                )
                serializer = CustomerSerializer(customer, many=False)
                if created:
                    return Response({"message": "customer created successfully", 'data':serializer.data}, status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "customer updated successfully", 'data':serializer.data}, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)

@method_decorator(csrf_exempt, name='dispatch')
@permission_classes([AllowAny])
class CustomerGroupsSyncAPIView(APIView):
    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            return x_forwarded_for.split(',')[0]
        return request.META.get('REMOTE_ADDR')
    
    @swagger_auto_schema(
    tags=["Data Sync From ERP System"],
    operation_summary="Sync CustomerGroups from external system",
    operation_description="Sync CustomerGroups from external system based on customer_no. If the Customer exists, it updates; otherwise, creates a new Customer.",
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=["customer_no", "member_no", ],
        properties={
            'customer_no': openapi.Schema(type=openapi.TYPE_STRING, description="Customer No (used as unique identifier for syncing -  previously ERP_id)"),
            'member_no': openapi.Schema(type=openapi.TYPE_STRING, description="member_no"),
            'customer_name': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'national_id': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'passport_no': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'kra_pin': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'dob': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'gender': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'phone': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'primary_email': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            'country_of_residence': openapi.Schema(type=openapi.TYPE_STRING, description="")
            # 'marital_status': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            # 'alternative_phone': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            # 'alternative_email': openapi.Schema(type=openapi.TYPE_STRING, description=""),
            
        }
    ),
    responses={
        200: openapi.Response("Customer created or updated successfully"),
        400: openapi.Response("Validation error"),
        403: openapi.Response("Unauthorized access (IP not allowed)"),
    }
    )
    def post(self, request, *args, **kwargs):
        allowed_ip = os.getenv("ERP_ALLOWED_IP", "127.0.0.1")  
        ip = self.get_client_ip(request)
        if ip != allowed_ip:
            return Response({"error": f"{ip} is Unauthorized"}, status=status.HTTP_403_FORBIDDEN)

        try:
            with transaction.atomic():

                data = request.data.copy()
                
                customer_no = data.get('customer_no')
                try:
                    customer = Customer.objects.get(customer_no=customer_no)
                except Customer.DoesNotExist:
                    customer = None 
                    return Response({"error": f"Customer with customer_no {customer_no} does not exist"}, status=status.HTTP_400_BAD_REQUEST)
                
                if data.get('dob')=="":
                    data['dob'] = None
                else:
                    data['dob'] = datetime.strptime(data.get('dob'), "%m/%d/%y").strftime("%Y-%m-%d")

                customergroup, created = CustomerGroups.objects.update_or_create(
                    customer_no=customer,
                    member_no=data.get('member_no'),
                    defaults={
                        'customer_name': data.get('customer_name'),
                        'national_id': data.get('national_id'),
                        'passport_no': data.get('passport_no'),
                        'kra_pin': data.get('kra_pin'),
                        'dob': data.get('dob'),
                        'gender': data.get('gender'),
                        'phone': data.get('phone'),
                        'alternative_phone': data.get('alternative_phone'),
                        'primary_email': data.get('primary_email'),
                        'alternative_email': data.get('alternative_email'),
                        'country_of_residence': data.get('country_of_residence'),
                    }
                )
                serializer = CustomerGroupsSerializer(customergroup, many=False)
                if created:
                    return Response({"message": "customer group created successfully", 'data':serializer.data}, status=status.HTTP_201_CREATED)
                else:
                    return Response({"message": "customer group updated successfully", 'data':serializer.data}, status=status.HTTP_200_OK)
                
        except Exception as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
