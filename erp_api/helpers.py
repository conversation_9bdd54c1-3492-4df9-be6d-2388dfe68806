import os
import requests
from urllib.parse import quote
from requests_ntlm import HttpNtlmAuth
import logging
logger = logging.getLogger(__name__)


ODATA_HOST = os.getenv("BC_ODATA_HOST", "http://***********:8048")
BC_TENANT = os.getenv("BC_TENANT_PATH", "optiven/ODataV4")
BC_COMPANY = quote(os.getenv("BC_COMPANY", "Optiven R.E"))
AUTH = HttpNtlmAuth(os.getenv("BC_NTLM_USER", "Dennis.Mwendwa"),
                    os.getenv("BC_NTLM_PASS", "Den9@24!#"))
HEADERS = {
    "User-Agent": "OptivenERP/1.0",
    "Accept": "application/json",
    "Content-Type": "application/json",
}

def retrieve_lead_file(pk: str):
    """
    Return full Lead_File_Card payload (dict) and an error string if any.
    """
    url = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/Lead_File_Card('{pk}')"
    logger.info(f"Fetching lead file {pk} from ERP: {url}")
    try:
        r = requests.get(url, auth=AUTH, headers=HEADERS, timeout=12)
        logger.info(f"ERP response {r.status_code}: {r.text[:200]}...")
        if not r.ok:
            return None, f"OData error {r.status_code}: {r.text[:200]}"
        return r.json(), None
    except requests.exceptions.RequestException as e:
        logger.error(f"Network error contacting OData: {e}")
        return None, f"Network error contacting OData: {e}"

