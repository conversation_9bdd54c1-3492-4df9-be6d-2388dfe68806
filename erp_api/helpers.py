import os
import requests
from urllib.parse import quote
from requests_ntlm import HttpNtlmAuth
import logging
logger = logging.getLogger(__name__)


ODATA_HOST = os.getenv("BC_ODATA_HOST", "http://***********:8048")
BC_TENANT = os.getenv("BC_TENANT_PATH", "optiven/ODataV4")
BC_COMPANY = quote(os.getenv("BC_COMPANY", "Optiven R.E"))
AUTH = HttpNtlmAuth(os.getenv("BC_NTLM_USER", "Dennis.Mwendwa"),
                    os.getenv("BC_NTLM_PASS", "Den9@24!#"))
HEADERS = {
    "User-Agent": "OptivenERP/1.0",
    "Accept": "application/json",
    "Content-Type": "application/json",
}
ENTITY_SET = "installmentschedules"
def retrieve_lead_file(pk: str):
    """
    Return full Lead_File_Card payload (dict) and an error string if any.
    """
    url = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/Lead_File_Card('{pk}')"
    logger.info(f"Fetching lead file {pk} from ERP: {url}")
    try:
        r = requests.get(url, auth=AUTH, headers=HEADERS, timeout=12)
        logger.info(f"ERP response {r.status_code}: {r.text[:200]}...")
        if not r.ok:
            return None, f"OData error {r.status_code}: {r.text[:200]}"
        return r.json(), None
    except requests.exceptions.RequestException as e:
        logger.error(f"Network error contacting OData: {e}")
        return None, f"Network error contacting OData: {e}"



_FIELD_MAP = {
    "Document_No": "leadfile_no",
    "MemberNo": "member_no",
    "Line_No": "line_no",
    "Installment_No": "installment_no",
    "Installment_Amount": "installment_amount",
    "Remaining_Amount": "remaining_Amount",
    "Due_Date": "due_date",
    "Paid": "paid",
    "Plot_No": "plot_No",
    "Plot_Name": "plot_Name",
    "Amount_Paid": "amount_Paid",
    "Accrued_Penalty": "penaties_Accrued",
}

_SELECT = ",".join(_FIELD_MAP.keys())  # only pull what we need

def _map_bc_row(row: dict) -> dict:
    """Map Business Central field names to internal keys expected by the task."""
    out = {}
    for bc_name, internal in _FIELD_MAP.items():
        out[internal] = row.get(bc_name)
    return out

def retrieve_installment_schedules(lead_no: str):
    """
    Return (list[dict], error_str|None).
    Filters BC OData by Document_No eq 'LF...'
    """
    base = f"{ODATA_HOST}/{BC_TENANT}/Company('{BC_COMPANY}')/{ENTITY_SET}"
    params = {
        "$select": _SELECT,
        "$filter": f"Document_No eq '{lead_no}'",
    }
    logger.info("Fetching installment schedules for leadfile %s from ERP: %s", lead_no, base)
    try:
        r = requests.get(base, auth=AUTH, headers=HEADERS, params=params, timeout=15)
        logger.info("ERP response %s: %s...", r.status_code, r.text[:200])
        if not r.ok:
            return None, f"OData error {r.status_code}: {r.text[:200]}"
        data = r.json().get("value", [])
        # Map each BC row to your internal field naming
        mapped = [_map_bc_row(x) for x in data]
        return mapped, None
    except requests.exceptions.RequestException as e:
        logger.error("Network error contacting OData: %s", e)
        return None, f"Network error contacting OData: {e}"