from django.db import models

class FailedApiCall(models.Model):
    endpoint = models.URLField()
    request_method = models.CharField(max_length=10)  # GET, POST, etc.
    request_headers = models.JSONField()
    request_body = models.JSONField(null=True, blank=True)
    status_code = models.IntegerField()
    response_body = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    retry_count = models.IntegerField(default=0)
    max_retries = models.IntegerField(default=3)
    resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.endpoint} - {self.status_code}"
