from django.urls import path
from .views.user_sync import UserSyncAPIView,DepartmentsSyncAPIView,TeamsSyncAPIView, UserUpdateSyncAPIView
from .views.inventory_sync import ProjectSyncAPIView,PlotsSyncAPIView
from .views.leads_sync import LeadSourceCategorySyncAPIView, LeadSourceSubCategorySyncAPIView, LeadSourceSyncAPIView
from .views.customers_sync import CustomerSyncAPIView, CustomerGroupsSyncAPIView
from .views.sales_report_sync import CashOnCashSyncAPIVIEW
from .views.reports_sync import (
    HOSGMTargetSyncAPIView,
    TeamTargetSyncAPIView,
    PortfolioHeaderSyncAPIView,
    PortfolioLineSyncAPIView
)
from .views.leadfiles_sync import LeadfilesSyncAPIView
from .views.sales_report_sync import MarketerTargetSyncAPIVIEW
from .views.sales_report_sync import CommissionLinesSync<PERSON>IVIE<PERSON>
from .views.sales_report_sync import CommissionHeaderSync<PERSON><PERSON><PERSON>E<PERSON>
from .views.receipts_sync import ReceiptSyncAPIView
from .views.installmentschedule import InstallmentScheduleViewSet
from .views.installmentschedule_sync import InstallmentScheduleSyncAPIView
from .views.failed_calls import FailedApiWebhookAPIView

from rest_framework.routers import DefaultRouter
router = DefaultRouter()

urlpatterns = [
    path('sync-user/', UserSyncAPIView.as_view(), name='sync-user'),
    path('sync-user-update/', UserUpdateSyncAPIView.as_view(), name='sync-user-update'),
    path('sync-departments/', DepartmentsSyncAPIView.as_view(), name='sync-departments'),
    path('sync-teams/', TeamsSyncAPIView.as_view(), name='sync-teams'),
    path('sync-projects/', ProjectSyncAPIView.as_view(), name='sync-projects'),
    path('sync-plots/', PlotsSyncAPIView.as_view(), name='sync-plots'),
    path('sync-lead-category/', LeadSourceCategorySyncAPIView.as_view(), name='sync-lead-category'),
    path('sync-lead-subcategory/', LeadSourceSubCategorySyncAPIView.as_view(), name='sync-lead-subcategory'),
    path('sync-lead-source/', LeadSourceSyncAPIView.as_view(), name='sync-lead-source'),
    path('sync-customers/', CustomerSyncAPIView.as_view(), name='sync-customers'),
    path('sync-customer-group/', CustomerGroupsSyncAPIView.as_view(), name='sync-customer-group'),
    path('sync-cash_on_cash/',CashOnCashSyncAPIVIEW.as_view(),name='sync-cash_on_cash'),
    path('sync/hos-gm-target/', HOSGMTargetSyncAPIView.as_view(), name='sync-hos-gm-target'),
    path('sync/team-target/', TeamTargetSyncAPIView.as_view(), name='sync-team-target'),
    path('sync/portfolio-header/', PortfolioHeaderSyncAPIView.as_view(), name='sync-portfolio-header'),
    path('sync/portfolio-line/', PortfolioLineSyncAPIView.as_view(), name='sync-portfolio-line'),
    path('sync-lead_files/', LeadfilesSyncAPIView.as_view(), name='sync-lead_files'),
    path('sync-marketers_target/',MarketerTargetSyncAPIVIEW.as_view(),name='sync-marketers_target'),
    path('sync-commission_lines/',CommissionLinesSyncAPIVIEW.as_view(),name='sync-commission_lines'),
    path('sync-commission_header/',CommissionHeaderSyncAPIVIEW.as_view(),name='sync-commission_header'),
    path('sync-receipts/', ReceiptSyncAPIView.as_view(), name='sync-receipts'),
    path('sync-installment-schedules/', InstallmentScheduleSyncAPIView.as_view(), name='sync-installment-schedules'),
    path('webhook/log-failed-call/', FailedApiWebhookAPIView.as_view(), name='log_failed_api_call'),
]

router.register(r'installment-schedules', InstallmentScheduleViewSet, basename='installmentschedule')

urlpatterns += router.urls