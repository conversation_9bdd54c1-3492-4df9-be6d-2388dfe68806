from rest_framework import viewsets, filters
from rest_framework.response import Response
from rest_framework.permissions import AllowAny,IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from inventory.tasks import send_email_task
from .models import *
from django.conf import settings
from .serializers import *
from drf_yasg import openapi
import MySQLdb.cursors
from django.db import connections
import json,os
from rest_framework.viewsets import ViewSet
from rest_framework.views import APIView
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.shortcuts import get_object_or_404
from utils.pdf import generate_pdf
from rest_framework.exceptions import NotFound
from copy import deepcopy



def swagger_tags(tags):
    def decorator(cls):
        methods = ['list', 'retrieve', 'create', 'partial_update', 'destroy']
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                decorated_method = swagger_auto_schema(tags=tags)(method)
                setattr(cls, method_name, decorated_method)
        return cls
    return decorator
def get_offer_letter_context(offer_letter_id):
    try:
        offer = OfferLetterMain.objects.get(id=offer_letter_id)
    except OfferLetterMain.DoesNotExist:
        return {"error": "Offer letter not found"}
    try:
        booking = offer.booking_id
    except PlotBooking.DoesNotExist:
        return {"error": "Booking not found for this offer letter"}
    if not booking:
        return {"error": "Booking not found for this offer letter"}

    # Serialized data to be used as context in both API and HTML
    context = OfferLetterFullDetailSerializer(offer).data
    return context

def fetch_one(query, error_message, params=None, dict_cursor=False):
    try:
        conn = connections["reports"]
        conn.ensure_connection()
        cursor_class = MySQLdb.cursors.DictCursor if dict_cursor else None
        with conn.connection.cursor(cursor_class) as cur:
            cur.execute(query, params)
            return cur.fetchone()
    except Exception as e:
        raise Exception(f"{error_message}: {str(e)}")

def normalize_emails(field):
        if not field:
            return []
        if isinstance(field, str):
            # This handles comma-separated strings
            return [email.strip() for email in field.split(",") if email.strip()]
        if isinstance(field, list):
            return [email.strip() for email in field if isinstance(email, str)]
        raise ValueError(f"Invalid email field: {field}")

@swagger_tags(['Offer Letters'])
class OfferLetterMainViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterMain.objects.all()
    serializer_class = OfferLetterMainSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    http_method_names = ['get', 'post', 'patch', 'delete']
    required_fields = ['plot_number', 'booking_id', 'date']
    filterset_fields = ['plot_number', 'booking_id', 'date']
    search_fields = ['plot_number', 'customer_type','is_completed']
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        booking_id= data.get('booking_id')
        if booking_id:
            try:
                booking= PlotBooking.objects.get(booking_id=booking_id)
            except PlotBooking.DoesNotExist:
                return Response({"error": "Booking ID does not exist"}, status=404)
        data['plot_number'] = booking.plots

        # Ensure booking_id is set to a serializable value (e.g., booking.pk)
        data['booking_id'] = booking.pk

        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(serializer.data, status=200)
        
    def partial_update(self, request, *args, **kwargs):
        # Custom logic before update
        instance = self.get_object()
        data = request.data.copy()
        booking_id = data.get('booking_id')
        is_completed = data.get('is_completed', False)
        offer_letter_id = instance.id
        if booking_id:
            try:
                booking = PlotBooking.objects.get(id=booking_id)
            except PlotBooking.DoesNotExist:
                return Response({"error": "Booking ID does not exist"}, status=404)
            
        status_before_update = instance.is_completed
        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
    
        # Custom logic after update
        # print(f"Offer Letter {offer_letter_id} updated successfully.")
        # print(instance.booking_id.marketer.email if instance.booking_id.marketer else 'No marketer email')
        if is_completed == True and status_before_update == False:
            #generate pdf and send email logic here
            
            offer_letter_template= "offerletter/offer_letter_template.html"
            document_name = f"Offer_Letter_{instance.booking_id.pk}_{uuid4()}.pdf"
            context = get_offer_letter_context(offer_letter_id)
            offer_letter_pdf = generate_pdf(offer_letter_template, context, output_filename=document_name)
            
            #get email recipients
            if instance.customer_type == 'individual':
                to = [context['individuals'][0]['email']]
                client_name = context['individuals'][0]['first_name'] + ' ' + context['individuals'][0]['last_name']
            elif instance.customer_type == 'company':
                to = [context['companies'][0]['email']]
                client_name = context['companies'][0]['company_name']
            elif instance.customer_type == 'group':
                to = [context['groups'][0]['email']]
                client_name = context['groups'][0]['group_name']
            elif instance.customer_type == 'partner':
                to = [context['partners'][0]['email']]
                client_name = context['partners'][0]['first_name'] + ' ' + context['partners'][0]['last_name']
            else:
                to = ['<EMAIL>']
                client_name = 'Optiven Client'


            marketeremail=instance.booking_id.marketer.email if instance.booking_id.marketer else ''
            cc = [marketeremail, '<EMAIL>','<EMAIL>','<EMAIL>', '<EMAIL>']
            offerletteremail_template = "offerletter/offer_letter_email_template.html"
            email_context = {
                'client_name': client_name,
                'plot': instance.plot_number,
                'customer_type': instance.customer_type,
                'booking_id': instance.booking_id.booking_id,
                'date': instance.date,
                'year': datetime.now().year,
            }
            send_email_task.delay(
                            subject=f"OfferLetter Received for {instance.plot_number}",
                            recipients=normalize_emails([email for email in to if email]),
                            template_name=offerletteremail_template,
                            context=deepcopy(email_context),
                            attachments=[offer_letter_pdf],
                            cc= normalize_emails(cc if cc else []),
                            )
            return Response({"message": "Offer letter processed successfully."}, status=200)
        else:    
            return Response(serializer.data, status=200)
    
class OfferLetterCompanyViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterCompany.objects.all()
    serializer_class = OfferLetterCompanySerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'company_name']
    search_fields = ['offer_letter', 'company_name']


class OfferLetterCompanyDirectorViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterCompanyDirector.objects.all()
    serializer_class = OfferLetterCompanyDirectorSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['company', 'first_name', 'last_name']
    search_fields = ['company', 'first_name', 'last_name']


class OfferLetterGroupViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterGroup.objects.all()
    serializer_class = OfferLetterGroupSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'group_name']
    search_fields = ['offer_letter', 'group_name']


class OfferLetterGroupMemberViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterGroupMember.objects.all()
    serializer_class = OfferLetterGroupMemberSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['group', 'first_name', 'last_name']
    search_fields = ['group', 'first_name', 'last_name']


class OfferLetterIndividualViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterIndividual.objects.all()
    serializer_class = OfferLetterIndividualSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'first_name', 'last_name']
    search_fields = ['offer_letter', 'first_name', 'last_name']


class OfferLetterPartnerViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterPartner.objects.all()
    serializer_class = OfferLetterPartnerSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'first_name', 'last_name']
    search_fields = ['offer_letter', 'first_name', 'last_name']


class OfferLetterNextOfKinViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterNextOfKin.objects.all()
    serializer_class = OfferLetterNextOfKinSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'partner', 'individual', 'full_name']
    search_fields = ['offer_letter', 'partner', 'individual', 'full_name']


class OfferLetterPaymentsPlanViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterPaymentsPlan.objects.all()
    serializer_class = OfferLetterPaymentsPlanSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter', 'plot_no']
    search_fields = ['offer_letter', 'plot_no']


class OfferLetterPricingViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterPricing.objects.all()
    serializer_class = OfferLetterPricingSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['Project_No']
    search_fields = ['Project_No','Payment_Model', 'Size_Category','Plot_Type', 'view']


class OfferLetterTermsConditionsViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterTermsConditions.objects.all()
    serializer_class = OfferLetterTermsConditionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter']
    search_fields = ['offer_letter']

class OfferLetterReviewViewSet(viewsets.ModelViewSet):
    queryset = OfferLetterReview.objects.all()
    serializer_class = OfferLetterReviewSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    filterset_fields = ['offer_letter']
    search_fields = ['offer_letter']
        
class PlotPaymentsOptionsViewSet(ViewSet):
    queryset = None 
    # if os.environ.get('DEBUG', 'false').lower() == 'true':
    #     permission_classes = [IsAuthenticated]
    # else:
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=['Offer Letters'],
        operation_description="Returns plot payment options.",
        manual_parameters=[
           openapi.Parameter(name='PLOT_NO', in_=openapi.IN_QUERY, description='Filter by plot number.',
                type=openapi.TYPE_STRING, required=True, default='ALL'),
        ]
    )
    def list(self, request):
        """
        List all plot payment options.
        """
        plot_no = request.query_params.get('PLOT_NO', 'ALL')
        if plot_no != 'ALL':
            getmonth_sql = """
            SELECT `plot_type`,`project_id`,`cash_price`,`threshold_price`,`plot_size`,`view` FROM `inventory_plot` WHERE `plot_no` = %s;"""
           
            row = fetch_one(getmonth_sql, "Error fetching current marketing month", params=(plot_no,), dict_cursor=True)
        
            if row is None:
                return Response({"error": "Plot not found"}, status=404)
            else:
                plot_size = float(row['plot_size']) if row and row['plot_size'] is not None else None
                project_id = row['project_id'] if row else None
                cash_price = row['cash_price'] if row else None
                threshold_price = row['threshold_price'] if row else None
                plot_type = row['plot_type'] if row else None
                view = row['view'] if row else None


                classify_plot_sql = """SELECT `category`, `definition` FROM inventory_plotsizecategory WHERE %s BETWEEN start_size AND end_size LIMIT 1;"""


            classify_row = fetch_one(classify_plot_sql, "Error classifying plot", params=(plot_size,), dict_cursor=True)
            
            if classify_row is None:
                return Response({"error": "Plot size classification not found"}, status=404)
            else:
                category = classify_row['category'] if classify_row else None
                definition = classify_row['definition'] if classify_row else None
                
            #get pricing
            query = """SELECT f.*,pr.name as Project FROM offer_letter_pricing f JOIN inventory_project pr ON
            f.`Project_No_id`= pr.projectId WHERE `Project_No_id`=%s AND `Size_Category_id`=%s AND `Plot_Type`=%s"""
            # if view == 'Ridge View':
            #     query += " AND `view`='Ridge View'"
            # elif view == 'Ocean View':
            #     query += " AND `view`='Ocean View'"
            if view and view != '' and view is not None:
                query += " AND `view`=%s "
                params = (project_id, category, plot_type, view)
            else:
                params = (project_id, category, plot_type)

            query += """LIMIT 1;"""
            row = fetch_one(query, "Error fetching pricing", params=params, dict_cursor=True)

            if row is None:
                print(query % (project_id, f"'{category}'", f"'{plot_type}'"), "Error fetching pricing")
                return Response({"error": "Pricing not found"}, status=404)
            else:
                
                print("Pricing Row: ", row)
                
                
                project_name = row['Project'] if row else None
                
                from decimal import Decimal
                from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger

                def decimal_to_float(val):
                    if isinstance(val, Decimal):
                        return float(val)
                    return val
                print("Monthly_Interest: ", row.get('Monthly_Interest', 0), "Threshold: ", row.get('Threshold', 0))

                def generate_payment_plan_html(row, from_plots_cash_price, from_plots_threshold, p):
                    value = ""
                    monthly_interest = decimal_to_float(row.get('Monthly_Interest', 0))
                    months_available = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 24]
                    Payment_Model = row.get('Payment_Model')

                    # Cash Price Option
                    if from_plots_cash_price:
                        cp = decimal_to_float(from_plots_cash_price)
                        response = {
                            "plot_no": p,
                            "no_of_ins": 1,
                            "cash_price": cp,
                            "deposit": decimal_to_float(from_plots_threshold),
                            "monthly_install": cp,
                            "statement": f"30 Days Cash Price @ Ksh {decimal_to_float(from_plots_threshold)}"
                        }
                        cash_price = json.dumps([response])
                    # Installment Options
                    for months_available in months_available:
                        key = f'_{months_available}M_IN'
                        if row.get(key) is not None and row.get(key) >= 0:
                            if Payment_Model == 'Standard':
                                cp = decimal_to_float(from_plots_cash_price) + ((monthly_interest if monthly_interest is not None else 0.0) * months_available)
                            else:
                                interest = row.get(f'_{months_available}M_IN', 0)
                                cp = decimal_to_float(from_plots_cash_price) + (float(interest) if interest is not None else 0.0)
                            monthly_install = (cp - decimal_to_float(from_plots_threshold)) / months_available
                            response = {
                                "plot_no": p,
                                "no_of_ins": months_available,
                                "cash_price": cp,
                                "deposit": decimal_to_float(from_plots_threshold),
                                "monthly_install": monthly_install
                            }
                            value += json.dumps([response])
                    
                    return cash_price + value



                return Response({
                    "plot_no": plot_no,
                    "project_id": project_id,
                    "project_name": project_name,
                    "plot_type": plot_type,
                    "cash_price": cash_price,
                    "view": view,
                    "plot_size_definition": definition,
                    "Options": generate_payment_plan_html(row, cash_price, threshold_price, plot_no)
                
                })
        else:
            return Response({"error": "Plot number is required"}, status=400)
        
class OfferLetterCategoryViewSet(ViewSet):
    queryset = None 
    permission_classes = [AllowAny]

    @swagger_auto_schema(
        tags=['Offer Letters'],
        operation_description="Returns Offer Letters Based On categories.",
        manual_parameters=[
           openapi.Parameter(name='CATEGORY', in_=openapi.IN_QUERY, description='Filter by category.', enum=['ACTIVE', 'COMPLETE', 'ALL'],
                type=openapi.TYPE_STRING, required=True, default='ACTIVE'),
           openapi.Parameter(name='search', in_=openapi.IN_QUERY, description='Search by plot number.', type=openapi.TYPE_STRING, required=False),
           openapi.Parameter(name='page', in_=openapi.IN_QUERY, description='Page number.', type=openapi.TYPE_INTEGER, required=False, default=1),
           openapi.Parameter(name='page_size', in_=openapi.IN_QUERY, description='Page size.', type=openapi.TYPE_INTEGER, required=False, default=20),
        ]
    )
    def list(self, request):
        category = request.query_params.get('CATEGORY', 'ALL')
        search = request.query_params.get('search', '').strip()

        if category == 'ALL':
            offerletters = OfferLetterMain.objects.filter(is_completed=True)
        elif category == 'ACTIVE':
            offerletters = OfferLetterMain.objects.filter(is_completed=True, lead_file__isnull=True)
        elif category == 'COMPLETE':
            offerletters = OfferLetterMain.objects.filter(is_completed=True, lead_file__isnull=False)
        else:
            offerletters = OfferLetterMain.objects.none()

        # Apply search filter
        if search:
            offerletters = offerletters.filter(
                models.Q(plot_number__icontains=search))

        
        # Pagination
        page = request.query_params.get('page', 1)
        page_size = request.query_params.get('page_size', 20)
        try:
            page = int(page)
        except ValueError:
            page = 1
        try:
            page_size = int(page_size)
        except ValueError:
            page_size = 20

        paginator = Paginator(list(offerletters), page_size)
        try:
            paged_data = paginator.page(page)
        except PageNotAnInteger:
            paged_data = paginator.page(1)
        except EmptyPage:
            paged_data = paginator.page(paginator.num_pages)

        serializer = OfferLetterMainSerializer(paged_data, many=True)
        return Response({
            "count": paginator.count,
            "num_pages": paginator.num_pages,
            "current_page": paged_data.number,
            "results": serializer.data
        }, status=200)
       
class OfferLetterDetailsAPIView(APIView):
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]

    @swagger_auto_schema(
        tags=["Offer Letters"],
        operation_description="Get full details for an offer letter ID.",
        manual_parameters=[
            openapi.Parameter(
                'offer_letter_id', openapi.IN_QUERY, type=openapi.TYPE_INTEGER,
                required=True, description="ID of the OfferLetterMain"
            )
        ]
    )
    def get(self, request):
        offer_letter_id = request.query_params.get('offer_letter_id')
        if not offer_letter_id:
            return Response({"error": "Missing offer_letter_id"}, status=400)

        context = get_offer_letter_context(offer_letter_id)
        return Response(context)


from django.shortcuts import render
from datetime import datetime

def test_offer_letter(request):
    offer_letter_id = request.GET.get("offer_letter_id")
    if not offer_letter_id:
        return render(request, "offerletter/error.html", {"error": "Missing offer_letter_id"})

    try:
        context = get_offer_letter_context(offer_letter_id)
    except Exception as e:
        return render(request, "offerletter/error.html", {"error": str(e)})

    return render(request, "offerletter/offer_letter_template.html", context)

