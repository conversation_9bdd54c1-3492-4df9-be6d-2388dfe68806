from rest_framework.routers import DefaultRouter
from django.urls import path
from .views import *

router = DefaultRouter()
router.register(r'offer-letters', OfferLetterMainViewSet)
router.register(r'offer-letter-company', OfferLetterCompanyViewSet)
router.register(r'offer-letter-company-directors', OfferLetterCompanyDirectorViewSet)
router.register(r'offer-letter-groups', OfferLetterGroupViewSet)
router.register(r'offer-letter-group-members', OfferLetterGroupMemberViewSet)
router.register(r'offer-letter-individuals', OfferLetterIndividualViewSet)
router.register(r'offer-letter-partners', OfferLetterPartnerViewSet)
router.register(r'offer-letter-next-of-kin', OfferLetterNextOfKinViewSet)
router.register(r'offer-letter-payments', OfferLetterPaymentsPlanViewSet)
router.register(r'offer-letter-pricing', OfferLetterPricingViewSet)
router.register(r'offer-letter-terms', OfferLetterTermsConditionsViewSet)
router.register(r'offer-letter-reviews', OfferLetterReviewViewSet)
router.register(r'plots-payment-options', PlotPaymentsOptionsViewSet, basename='plots-payment-options')
router.register(r'offer-letter-categories', OfferLetterCategoryViewSet, basename='offer-letter-categories')


urlpatterns = router.urls + [
    path('offer-letter-details/', OfferLetterDetailsAPIView.as_view(), name='offer-letter-details'),
    path("test-offer/", test_offer_letter, name="test-offer"),
]
