from django.db import models
from django.db.models import <PERSON><PERSON><PERSON><PERSON>
from inventory.models import Plot, Project, PlotBooking,PlotSizeCategory
# Main Offer Letter
class OfferLetterMain(models.Model):
    step = models.CharField(max_length=5)
    is_completed = models.BooleanField(default=False)
    plot_number = models.CharField(max_length=225, null=True, blank=True)
    booking_id = models.ForeignKey(PlotBooking, on_delete=models.CASCADE, null=True, blank=True)
    customer_type = models.CharField(max_length=20, null=True, blank=True)
    date = models.DateTimeField(auto_now_add=True)
    acc_payment_conf = models.BooleanField(default=False)
    lead_file = models.CharField(max_length=255, null=True, blank=True)
    customers = models.CharField(max_length=255, null=True, blank=True)

    class Meta:
        db_table = 'offer_Letter_main'


# Company
class OfferLetterCompany(models.Model):
    offer_letter = models.Foreign<PERSON>ey(OfferLetterMain, on_delete=models.CASCADE)
    company_name = models.CharField(max_length=150, null=True, blank=True)
    company_registration_number = models.CharField(max_length=100, null=True, blank=True)
    company_country_code = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.CharField(max_length=150, null=True, blank=True)
    address = models.CharField(max_length=200, null=True, blank=True)
    country = models.CharField(max_length=50, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    company_kra = models.CharField(max_length=30, null=True, blank=True)
    preferred_contact = JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Company'


class OfferLetterCompanyDirector(models.Model):
    company = models.ForeignKey(OfferLetterCompany, on_delete=models.CASCADE)
    director_id = models.IntegerField()
    first_name = models.CharField(max_length=150)
    last_name = models.CharField(max_length=150)
    country_codes = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=30)
    email = models.CharField(max_length=150)
    national_id = models.CharField(max_length=30)
    created_at = models.DateField()

    class Meta:
        db_table = 'offer_Letter_Company_directors'
        unique_together = ('company', 'director_id')


# Group
class OfferLetterGroup(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    group_name = models.CharField(max_length=100, null=True, blank=True)
    group_code = models.CharField(max_length=7, null=True, blank=True)
    group_phone = models.CharField(max_length=20, null=True, blank=True)
    group_email = models.CharField(max_length=150, null=True, blank=True)
    Group_KRA_PIN = models.CharField(max_length=30, null=True, blank=True)
    Group_country = models.CharField(max_length=200, null=True, blank=True)
    Group_city = models.CharField(max_length=30, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Group'


class OfferLetterGroupMember(models.Model):
    group = models.ForeignKey(OfferLetterGroup, on_delete=models.CASCADE)
    member_id = models.IntegerField()
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    country_codes = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=30)
    email = models.CharField(max_length=150)
    national_id = models.CharField(max_length=20)
    created_at = models.DateField()

    class Meta:
        db_table = 'offer_Letter_Group_members'
        unique_together = ('group', 'member_id')


# Individual
class OfferLetterIndividual(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    country_code = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.CharField(max_length=150, null=True, blank=True)
    national_id = models.CharField(max_length=50, null=True, blank=True)
    country = models.CharField(max_length=50, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    KRA_Pin = models.CharField(max_length=30, null=True, blank=True)
    DOB = models.DateField(null=True, blank=True)
    preferred_contact = JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Individual'


# Partner
class OfferLetterPartner(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    first_name = models.CharField(max_length=100, null=True, blank=True)
    last_name = models.CharField(max_length=100, null=True, blank=True)
    country_code = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=20, null=True, blank=True)
    email = models.CharField(max_length=150, null=True, blank=True)
    national_id = models.CharField(max_length=50, null=True, blank=True)
    KRA_Pin = models.CharField(max_length=30, null=True, blank=True)
    country = models.CharField(max_length=50, null=True, blank=True)
    city = models.CharField(max_length=50, null=True, blank=True)
    preferred_contact = JSONField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Partner'


# Next of Kin
class OfferLetterNextOfKin(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    individual = models.ForeignKey(OfferLetterIndividual, on_delete=models.CASCADE, null=True, blank=True)
    partner = models.ForeignKey(OfferLetterPartner, on_delete=models.CASCADE, null=True, blank=True)
    full_name = models.CharField(max_length=150)
    relationship = models.CharField(max_length=50)
    country_code = models.CharField(max_length=7, null=True, blank=True)
    phone = models.CharField(max_length=20)
    email = models.CharField(max_length=150, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Next_of_Kin'


# Payment Plan
class OfferLetterPaymentsPlan(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    plot_no = models.CharField(max_length=10)
    no_of_instalments = models.IntegerField()
    total_cash_price = models.DecimalField(max_digits=15, decimal_places=2)
    monthly_installments = models.DecimalField(max_digits=15, decimal_places=2)
    deposit = models.DecimalField(max_digits=15, decimal_places=2)
    created_at = models.DateField(null=True, blank=True)

    class Meta:
        db_table = 'offer_Letter_payments_plan'


# Pricing
class OfferLetterPricing(models.Model):
    Project_No = models.ForeignKey(Project, on_delete=models.CASCADE, related_name='offer_letter_pricing', null=True, blank=True)
    Payment_Model = models.CharField(max_length=255, null=True, blank=True)
    Size_Category = models.ForeignKey(PlotSizeCategory, on_delete=models.CASCADE, to_field='category', related_name='offer_letter_pricing', null=True, blank=True)
    Plot_Type = models.CharField(max_length=255, null=True, blank=True)
    view = models.CharField(max_length=20, null=True, blank=True)
    Deposit = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    Monthly_Interest = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)

    # Monthly installments
    _2M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _3M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _4M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _5M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _6M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _7M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _8M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _9M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _10M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _11M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _12M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _15M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)
    _24M_IN = models.DecimalField(max_digits=20, decimal_places=2, null=True, blank=True)

    class Meta:
        db_table = 'offer_letter_pricing'
        ordering =['Project_No']


# Terms & Conditions
class OfferLetterTermsConditions(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE)
    content = models.TextField()
    acceptance_date = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Terms_Conditions'


# Review
class OfferLetterReview(models.Model):
    offer_letter = models.ForeignKey(OfferLetterMain, on_delete=models.CASCADE, related_name='reviews')
    individual = models.ForeignKey(OfferLetterIndividual, on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    partner = models.ForeignKey(OfferLetterPartner, on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    group = models.ForeignKey(OfferLetterGroup, on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    company = models.ForeignKey(OfferLetterCompany, on_delete=models.CASCADE, related_name='reviews', null=True, blank=True)
    status = models.CharField(max_length=10, choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Pending')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        db_table = 'offer_Letter_Review'
