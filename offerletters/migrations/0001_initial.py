import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('inventory', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='OfferLetterMain',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('step', models.CharField(max_length=5)),
                ('is_completed', models.BooleanField(default=False)),
                ('customer_type', models.CharField(blank=True, max_length=20, null=True)),
                ('date', models.DateTimeField(auto_now_add=True)),
                ('acc_payment_conf', models.CharField(blank=True, max_length=5, null=True)),
                ('lead_file', models.CharField(blank=True, max_length=255, null=True)),
                ('booking_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.plotbooking')),
                ('plot_number', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.plot')),
            ],
            options={
                'db_table': 'offer_Letter_main',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterIndividual',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('country_code', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.CharField(blank=True, max_length=150, null=True)),
                ('national_id', models.CharField(blank=True, max_length=50, null=True)),
                ('country', models.CharField(blank=True, max_length=50, null=True)),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('KRA_Pin', models.CharField(blank=True, max_length=30, null=True)),
                ('DOB', models.DateField(blank=True, null=True)),
                ('preferred_contact', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_Individual',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterGroup',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_name', models.CharField(blank=True, max_length=100, null=True)),
                ('group_code', models.CharField(blank=True, max_length=7, null=True)),
                ('group_phone', models.CharField(blank=True, max_length=20, null=True)),
                ('group_email', models.CharField(blank=True, max_length=150, null=True)),
                ('Group_KRA_PIN', models.CharField(blank=True, max_length=30, null=True)),
                ('Group_country', models.CharField(blank=True, max_length=200, null=True)),
                ('Group_city', models.CharField(blank=True, max_length=30, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_Group',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('company_name', models.CharField(blank=True, max_length=150, null=True)),
                ('company_registration_number', models.CharField(blank=True, max_length=100, null=True)),
                ('company_country_code', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.CharField(blank=True, max_length=150, null=True)),
                ('address', models.CharField(blank=True, max_length=200, null=True)),
                ('country', models.CharField(blank=True, max_length=50, null=True)),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('company_kra', models.CharField(blank=True, max_length=30, null=True)),
                ('preferred_contact', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_Company',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterPartner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=100, null=True)),
                ('last_name', models.CharField(blank=True, max_length=100, null=True)),
                ('country_code', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(blank=True, max_length=20, null=True)),
                ('email', models.CharField(blank=True, max_length=150, null=True)),
                ('national_id', models.CharField(blank=True, max_length=50, null=True)),
                ('country', models.CharField(blank=True, max_length=50, null=True)),
                ('city', models.CharField(blank=True, max_length=50, null=True)),
                ('preferred_contact', models.JSONField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_Partner',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterNextOfKin',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('full_name', models.CharField(max_length=150)),
                ('relationship', models.CharField(max_length=50)),
                ('country_code', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(max_length=20)),
                ('email', models.CharField(blank=True, max_length=150, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('individual', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerletterindividual')),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerletterpartner')),
            ],
            options={
                'db_table': 'offer_Letter_Next_of_Kin',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterPaymentsPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('plot_no', models.CharField(max_length=10)),
                ('no_of_instalments', models.IntegerField()),
                ('total_cash_price', models.DecimalField(decimal_places=2, max_digits=15)),
                ('monthly_installments', models.DecimalField(decimal_places=2, max_digits=15)),
                ('deposit', models.DecimalField(decimal_places=2, max_digits=15)),
                ('created_at', models.DateField(blank=True, null=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_payments_plan',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterPricing',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('Payment_Model', models.CharField(blank=True, max_length=255, null=True)),
                ('Plot_Type', models.CharField(blank=True, max_length=255, null=True)),
                ('view', models.CharField(blank=True, max_length=20, null=True)),
                ('Deposit', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('Monthly_Interest', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_2M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_3M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_4M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_5M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_6M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_7M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_8M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_9M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_10M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_11M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_12M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_15M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('_24M_IN', models.DecimalField(blank=True, decimal_places=2, max_digits=20, null=True)),
                ('Project_No', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer_letter_pricing', to='inventory.project')),
                ('Size_Category', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='offer_letter_pricing', to='inventory.plotsizecategory')),
            ],
            options={
                'db_table': 'offer_letter_pricing',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('Pending', 'Pending'), ('Approved', 'Approved'), ('Rejected', 'Rejected')], default='Pending', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('company', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='offerletters.offerlettercompany')),
                ('group', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='offerletters.offerlettergroup')),
                ('individual', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='offerletters.offerletterindividual')),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='offerletters.offerlettermain')),
                ('partner', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reviews', to='offerletters.offerletterpartner')),
            ],
            options={
                'db_table': 'offer_Letter_Review',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterTermsConditions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('content', models.TextField()),
                ('acceptance_date', models.DateTimeField(auto_now_add=True)),
                ('offer_letter', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettermain')),
            ],
            options={
                'db_table': 'offer_Letter_Terms_Conditions',
            },
        ),
        migrations.CreateModel(
            name='OfferLetterCompanyDirector',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('director_id', models.IntegerField()),
                ('first_name', models.CharField(max_length=150)),
                ('last_name', models.CharField(max_length=150)),
                ('country_codes', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(max_length=30)),
                ('email', models.CharField(max_length=150)),
                ('national_id', models.CharField(max_length=30)),
                ('created_at', models.DateField()),
                ('company', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettercompany')),
            ],
            options={
                'db_table': 'offer_Letter_Company_directors',
                'unique_together': {('company', 'director_id')},
            },
        ),
        migrations.CreateModel(
            name='OfferLetterGroupMember',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('member_id', models.IntegerField()),
                ('first_name', models.CharField(max_length=255)),
                ('last_name', models.CharField(max_length=255)),
                ('country_codes', models.CharField(blank=True, max_length=7, null=True)),
                ('phone', models.CharField(max_length=30)),
                ('email', models.CharField(max_length=150)),
                ('national_id', models.CharField(max_length=20)),
                ('created_at', models.DateField()),
                ('group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='offerletters.offerlettergroup')),
            ],
            options={
                'db_table': 'offer_Letter_Group_members',
                'unique_together': {('group', 'member_id')},
            },
        ),
    ]
