from rest_framework import serializers
from django.db import models
from .models import (
    OfferLetterMain,
    OfferLetterCompany,
    OfferLetterCompanyDirector,
    OfferLetterGroup,
    OfferLetterGroupMember,
    OfferLetterIndividual,
    OfferLetterPartner,
    OfferLetterNextOfKin,
    OfferLetterPaymentsPlan,
    OfferLetterPricing,
    OfferLetterTermsConditions,
    OfferLetterReview
)

class OfferLetterMainSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterMain
        fields = '__all__'


class OfferLetterCompanySerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterCompany
        fields = '__all__'


class OfferLetterCompanyDirectorSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterCompanyDirector
        fields = '__all__'


class OfferLetterGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterGroup
        fields = '__all__'


class OfferLetterGroupMemberSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterGroupMember
        fields = '__all__'


class OfferLetterIndividualSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterIndividual
        fields = '__all__'


class OfferLetterPartnerSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterPartner
        fields = '__all__'


class OfferLetterNextOfKinSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterNextOfKin
        fields = '__all__'


class OfferLetterPaymentsPlanSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterPaymentsPlan
        fields = '__all__'


class OfferLetterPricingSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterPricing
        fields = '__all__'


class OfferLetterTermsConditionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterTermsConditions
        fields = '__all__'


class OfferLetterReviewSerializer(serializers.ModelSerializer):
    class Meta:
        model = OfferLetterReview
        fields = '__all__'

class OfferLetterIndividualDetailSerializer(serializers.ModelSerializer):
    next_of_kin = serializers.SerializerMethodField()

    class Meta:
        model = OfferLetterIndividual
        fields = ['id', 'first_name', 'last_name', 'country_code', 'phone', 
                'email', 'national_id', 'country', 'city', 'KRA_Pin','DOB','preferred_contact','created_at',
                'offer_letter','next_of_kin']

    def get_next_of_kin(self, obj):
        kins = OfferLetterNextOfKin.objects.filter(
            models.Q(individual_id=obj.id) | models.Q(offer_letter=obj.offer_letter)
        )
        return OfferLetterNextOfKinSerializer(kins, many=True).data
class OfferLetterPartnerDetailSerializer(serializers.ModelSerializer):
    next_of_kin = serializers.SerializerMethodField()

    class Meta:
        model = OfferLetterPartner
        fields = ['id', 'first_name', 'last_name', 'country_code', 'phone', 
                'email', 'national_id', 'country', 'city', 'preferred_contact','created_at',
                'offer_letter','next_of_kin']

    def get_next_of_kin(self, obj):
        kins = OfferLetterNextOfKin.objects.filter(
            models.Q(partner=obj.id) | models.Q(offer_letter=obj.offer_letter)
        )
        return OfferLetterNextOfKinSerializer(kins, many=True).data
class OfferLetterCompanyDetailSerializer(serializers.ModelSerializer):
    directors = serializers.SerializerMethodField()

    class Meta:
        model = OfferLetterCompany
        fields = ['id', 'company_name', 'company_registration_number', 'company_country_code', 'phone', 
                'email', 'address', 'country', 'city', 'preferred_contact','company_kra',
                'offer_letter','created_at', 'directors']

    def get_directors(self, obj):
        directors = OfferLetterCompanyDirector.objects.filter(company=obj)
        return OfferLetterCompanyDirectorSerializer(directors, many=True).data
class OfferLetterGroupDetailSerializer(serializers.ModelSerializer):
    members = serializers.SerializerMethodField()

    class Meta:
        model = OfferLetterGroup
        fields = ['group_name','group_code','group_phone','group_email','Group_KRA_PIN',
                'Group_country','Group_city','created_at','offer_letter','members']

    def get_members(self, obj):
        members = OfferLetterGroupMember.objects.filter(group=obj)
        return OfferLetterGroupMemberSerializer(members, many=True).data


class OfferLetterFullDetailSerializer(serializers.ModelSerializer):
    payment_plan = serializers.SerializerMethodField()
    terms_conditions = serializers.SerializerMethodField()
    individuals = serializers.SerializerMethodField()
    partners = serializers.SerializerMethodField()
    companies = serializers.SerializerMethodField()
    groups = serializers.SerializerMethodField()

    class Meta:
        model = OfferLetterMain
        fields = ['step','is_completed','plot_number','booking_id','customer_type','date','acc_payment_conf','lead_file',
            'payment_plan', 'terms_conditions', 'individuals',
            'partners', 'companies', 'groups'
        ]
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['marketer'] = instance.booking_id.marketer.fullnames if instance.booking_id and instance.booking_id.marketer else None
        representation['lead_source'] = self.get_lead_source(instance)
        representation['customer_exists'] = self.check_customer_exists(instance)
        return representation
    def get_lead_source(self, instance):
        if instance.booking_id.customer is not None:
            return instance.booking_id.customer.lead_source.name if instance.booking_id.customer.lead_source else None
        elif instance.booking_id.lead is not None:
            return instance.booking_id.lead.lead_source.name if instance.booking_id.lead.lead_source else None
        return None

    def check_customer_exists(self, instance):
        from customers.models import Customer

        phone = None
        email = None
        national_id = None
        Kra_pin = None

        if instance.customer_type == 'individual':
            ind = OfferLetterIndividual.objects.filter(offer_letter=instance).first()
            if ind:
                phone = ind.phone
                email = ind.email
                national_id = ind.national_id
                Kra_pin = ind.KRA_Pin

        elif instance.customer_type == 'partner':
            partner = OfferLetterPartner.objects.filter(offer_letter=instance).first()
            if partner:
                phone = partner.phone
                email = partner.email
                national_id = partner.national_id
                Kra_pin = partner.KRA_Pin

        elif instance.customer_type == 'company':
            company = OfferLetterCompany.objects.filter(offer_letter=instance).first()
            if company:
                phone = company.phone
                email = company.email
                national_id = company.company_registration_number
                

        elif instance.customer_type == 'group':
            group = OfferLetterGroup.objects.filter(offer_letter=instance).first()
            if group:
                phone = group.group_phone
                email = group.group_email
                Kra_pin = group.Group_KRA_PIN

        # Check existence in Customer model
        exists = False
        if phone or email:
            # Only include non-empty values in the query
            query = models.Q()
            if phone:
                query |= models.Q(phone=phone)
            if email:
                query |= models.Q(primary_email=email)
            if national_id:
                query |= models.Q(national_id=national_id)
            if Kra_pin:
                query |= models.Q(kra_pin=Kra_pin)
            if query:
                exists = Customer.objects.filter(query).first()
            else:
                exists = False
            if exists:
                exists = {'status': True,
                    'customer_no': exists.customer_no,
                    'customer_name': exists.customer_name,
                    'phone': exists.phone,
                    'email': exists.primary_email,
                    'national_id': exists.national_id,
                    'kra_pin': exists.kra_pin
                }
        return exists
        

    def get_payment_plan(self, obj):
        plans = OfferLetterPaymentsPlan.objects.filter(offer_letter=obj)
        return OfferLetterPaymentsPlanSerializer(plans, many=True).data

    def get_terms_conditions(self, obj):
        terms = OfferLetterTermsConditions.objects.filter(offer_letter=obj)
        return OfferLetterTermsConditionsSerializer(terms, many=True).data

    def get_individuals(self, obj):
        if obj.customer_type != 'individual':
            return []
        qs = OfferLetterIndividual.objects.filter(offer_letter=obj)
        return OfferLetterIndividualDetailSerializer(qs, many=True).data

    def get_partners(self, obj):
        if obj.customer_type != 'partner':
            return []
        qs = OfferLetterPartner.objects.filter(offer_letter=obj)
        return OfferLetterPartnerDetailSerializer(qs, many=True).data

    def get_companies(self, obj):
        if obj.customer_type != 'company':
            return []
        qs = OfferLetterCompany.objects.filter(offer_letter=obj)
        return OfferLetterCompanyDetailSerializer(qs, many=True).data

    def get_groups(self, obj):
        if obj.customer_type != 'group':
            return []
        qs = OfferLetterGroup.objects.filter(offer_letter=obj)
        return OfferLetterGroupDetailSerializer(qs, many=True).data
