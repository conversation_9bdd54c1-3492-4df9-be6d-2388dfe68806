from django.db import models
from customers.models import Customer
from sales.models import LeadFile
from users.models import User, Teams, Departments
from leads.models import Prospects
import uuid

def generate_ticket_id():
    """
    Generate a unique ticket ID with format: TKT-XXXXXXXXXX
    """
    return f"TKT-{uuid.uuid4().hex[:10].upper()}"

class TicketCategory(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
class TicketSource(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    
 
 
    
class Ticket(models.Model):
    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
        ('escalated', 'Escalated'),
    ]

    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]
    ticket_id = models.CharField(max_length=20, unique=True, default=generate_ticket_id)
    title = models.CharField(max_length=255)
    description = models.TextField()
    customer = models.ForeignKey(Customer, related_name='customer_ticket', on_delete=models.SET_NULL, null=True, blank=True)
    sales = models.ForeignKey(LeadFile, related_name='sales_tickets', on_delete=models.SET_NULL, null=True, blank=True)
    user = models.ForeignKey(User, related_name='tickets_assigned', to_field='employee_no', on_delete=models.SET_NULL, null=True, blank=True)
    escalate_to = models.ForeignKey(User, related_name='tickets_escalated_to', to_field='employee_no', on_delete=models.SET_NULL, null=True, blank=True)
    category = models.ForeignKey(TicketCategory, on_delete=models.SET_NULL, null=True)
    department = models.ForeignKey(Departments, on_delete=models.SET_NULL, null=True)
    team = models.ForeignKey(Teams, on_delete=models.SET_NULL, null=True)
    prospect = models.ForeignKey(Prospects, on_delete=models.SET_NULL, null=True, blank=True)
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    source = models.ForeignKey(TicketSource, on_delete=models.SET_NULL, null=True)
    
    def save(self, *args, **kwargs):
        """
        Override save to auto-generate ticket_id and sync customer_no with customer relationship
        """
        # Auto-generate ticket_id if not provided
        if not self.ticket_id:
            self.ticket_id = generate_ticket_id()
            # Ensure uniqueness by checking if ID already exists
            while Ticket.objects.filter(ticket_id=self.ticket_id).exists():
                self.ticket_id = generate_ticket_id()
        super().save(*args, **kwargs)
    def __str__(self):
        return f"{self.ticket_id} - {self.title}"

    class Meta:
        ordering = ['-created_at']


class TicketEscalation(models.Model):
    ticket = models.ForeignKey(Ticket, related_name='escalated_ticket', on_delete=models.CASCADE)
    user = models.ForeignKey(User, related_name='escalated_ticket_user', to_field='employee_no', on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)



class TicketMessage(models.Model):
    ticket = models.ForeignKey(Ticket, related_name='messages', on_delete=models.CASCADE)
    sender = models.ForeignKey(User,  to_field='employee_no', on_delete=models.CASCADE)
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    
class TicketAttachment(models.Model):
    ticket = models.ForeignKey(Ticket, related_name='attachments', on_delete=models.CASCADE)
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE)
    file = models.FileField(upload_to='ticket_attachments/')
    uploaded_at = models.DateTimeField(auto_now_add=True)
    
class TicketActionLog(models.Model):
    ACTION_CHOICES = [
        ('created', 'Created'),
        ('assigned', 'Assigned'),
        ('updated', 'Updated'),
        ('escalated', 'Escalated'),
        ('resolved', 'Resolved'),
        ('reopened', 'Reopened'),
        ('closed', 'Closed'),
        ('commented', 'Commented'),
    ]

    ticket = models.ForeignKey(Ticket, related_name='actions', on_delete=models.CASCADE)
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)
    comment = models.TextField(blank=True, null=True)  # Optional comment or reason for escalation
    timestamp = models.DateTimeField(auto_now_add=True)
