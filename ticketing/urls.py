from django.urls import path, include
from rest_framework import routers

from .views import (
    TicketCategoryViewSet,
    TicketViewSet,
    TicketMessageViewSet,
    TicketAttachmentViewSet,
    TicketActionLogViewSet,
    TicketSourceViewSet
)

router = routers.DefaultRouter()
router.register(r'tickets-categories', TicketCategoryViewSet, basename='ticketcategory')
router.register(r'tickets-sources', TicketSourceViewSet, basename='ticketsource')
router.register(r'tickets', TicketViewSet, basename='ticket')
router.register(r'tickets-messages', TicketMessageViewSet, basename='ticketmessage')
router.register(r'tickets-attachments', TicketAttachmentViewSet, basename='ticketattachment')
router.register(r'tickets-action-logs', TicketActionLogViewSet, basename='ticketactionlog')

urlpatterns = [
    path('', include(router.urls)),
]