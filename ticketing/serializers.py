from customers.models import Customer
from reminders.models import Reminder
from reminders.serializer import <PERSON>minder<PERSON>erializer
from rest_framework import serializers
from sales.models import LeadFile
from ticketing.models import (
    TicketCategory, Ticket, TicketEscalation, TicketMessage, TicketAttachment, TicketActionLog,TicketSource
)

class TicketCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketCategory
        fields = ['id', 'name', 'description']
class TicketSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketSource
        fields = ['id', 'name', 'description']

class TicketAttachmentSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = TicketAttachment
        fields = ['id', 'ticket', 'uploaded_by', 'file', 'uploaded_at']

class TicketMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketMessage
        fields = ['id', 'ticket', 'sender', 'message', 'created_at']

    

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['ticket_id'] = instance.ticket.ticket_id if instance.ticket else ''
        representation['sender_name'] = instance.sender.fullnames if instance.sender else ''

        return representation

class TicketActionLogSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = TicketActionLog
        fields = ['id', 'ticket', 'action', 'performed_by', 'comment', 'timestamp']


class TicketEscalationSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketEscalation
        fields = '__all__'
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['ticket_id'] = instance.ticket.ticket_id if instance.ticket else ''
        representation['user_name'] = instance.user.fullnames if instance.user else ''

        return representation

class TicketSerializer(serializers.ModelSerializer):
   
    class Meta:
        model = Ticket
        ref_name = "TicketingTicketSerializer"
        fields ='__all__'


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['source_name'] = instance.source.name if instance.source else ''
        representation['category_name'] = instance.category.name if instance.category else ''
        representation['prospect_name'] = instance.prospect.name if instance.prospect else ''
        representation['user_name'] = instance.user.fullnames if instance.user else ''


        # Default values
        customer_name = ""
        customer = ""
        prospect_name = ""
        sales_number = ""

        # Safe lookup using try/except
        try:
            if instance.sales and instance.sales.customer_id:
                customer_name = instance.sales.customer_id.customer_name or ""
                customer = instance.sales.customer_id.customer_no or ""
                sales_number = instance.sales.lead_file_no or ""
        except Exception:
            pass

        try:
            if not customer_name and instance.customer:
                customer_name = instance.customer.customer_name or ""
                customer = instance.customer.customer_no or ""
        except Exception:
            pass

        try:
            if instance.prospect:
                prospect_name = instance.prospect.name or ""
        except Exception:
            pass

        # Add computed fields
        representation["customer_name"] = customer_name
        representation["customer"] = customer
        representation["sales_number"] = sales_number 
        representation["prospect_name"] = prospect_name


        attachments = TicketAttachment.objects.filter(ticket=instance)
        representation['attachments'] = TicketAttachmentSerializer(attachments, many=True).data

        escalations = TicketEscalation.objects.filter(ticket=instance)
        representation['escalations'] = TicketEscalationSerializer(escalations, many=True).data

        reminders = Reminder.objects.filter(ticket=instance)
        representation['reminders'] = ReminderSerializer(reminders, many=True).data

        return representation

