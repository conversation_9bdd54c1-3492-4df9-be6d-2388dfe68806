from customers.models import Customer
from reminders.models import Reminder
from reminders.serializer import <PERSON>minder<PERSON>erializer
from rest_framework import serializers
from sales.models import LeadFile
from ticketing.models import (
    TicketCategory, Ticket, TicketEscalation, TicketMessage, TicketAttachment, TicketActionLog,TicketSource
)

class TicketCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketCategory
        fields = ['id', 'name', 'description']
class TicketSourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketSource
        fields = ['id', 'name', 'description']

class TicketAttachmentSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = TicketAttachment
        fields = ['id', 'ticket', 'uploaded_by', 'file', 'uploaded_at']

class TicketMessageSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketMessage
        fields = ['id', 'ticket', 'sender', 'message', 'created_at']

    

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['ticket_id'] = instance.ticket.ticket_id if instance.ticket else ''
        representation['sender_name'] = instance.sender.fullnames if instance.sender else ''

        return representation

class TicketActionLogSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = TicketActionLog
        fields = ['id', 'ticket', 'action', 'performed_by', 'comment', 'timestamp']


class TicketEscalationSerializer(serializers.ModelSerializer):
    class Meta:
        model = TicketEscalation
        fields = '__all__'
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['ticket_id'] = instance.ticket.ticket_id if instance.ticket else ''
        representation['user_name'] = instance.user.fullnames if instance.user else ''

        return representation

class TicketSerializer(serializers.ModelSerializer):
   
    class Meta:
        model = Ticket
        ref_name = "TicketingTicketSerializer"
        fields ='__all__'


    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['source_name'] = instance.source.name if instance.source else ''
        representation['category_name'] = instance.category.name if instance.category else ''
        representation['prospect_name'] = instance.prospect.name if instance.prospect else ''
        representation['user_name'] = instance.user.fullnames if instance.user else ''

        try:
            representation['sales_number'] = instance.sales.lead_file_no if instance.sales else ''
            representation['customer_name'] = instance.sales.customer_id.customer_name if instance.sales and instance.sales.customer_id else ''
        except (AttributeError, LeadFile.DoesNotExist):
            representation['sales_number'] = ""

        try:
            representation['customer'] = instance.sales.customer_id.customer_no if instance.sales and instance.sales.customer_id else ''
        except (AttributeError, Customer.DoesNotExist):
            representation['customer'] = ""
        

        attachments = TicketAttachment.objects.filter(ticket=instance)
        representation['attachments'] = TicketAttachmentSerializer(attachments, many=True).data

        escalations = TicketEscalation.objects.filter(ticket=instance)
        representation['escalations'] = TicketEscalationSerializer(escalations, many=True).data

        reminders = Reminder.objects.filter(ticket=instance)
        representation['reminders'] = ReminderSerializer(reminders, many=True).data

        return representation

