from reminders.models import <PERSON><PERSON><PERSON>
from reminders.views import ReminderViewSet
from rest_framework import viewsets, filters, status, mixins
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated,AllowAny
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON>ars<PERSON>, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from django.db import transaction
from ticketing.models import (
    TicketCategory, Ticket, TicketEscalation, TicketMessage, TicketAttachment, 
    TicketActionLog, TicketSource
)
from ticketing.serializers import (
    TicketCategorySerializer, TicketSerializer, TicketMessageSerializer,
    TicketAttachmentSerializer, TicketActionLogSerializer, TicketSourceSerializer
)
from users.models import User
import os

def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        methods = ['list', 'retrieve', 'create', 'partial_update', 'destroy']
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                decorated_method = swagger_auto_schema(tags=tags)(method)
                setattr(cls, method_name, decorated_method)
        return cls
    return decorator

class TicketCategoryViewSet(viewsets.ModelViewSet):
    queryset = TicketCategory.objects.all()
    serializer_class = TicketCategorySerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['name']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List, filter, and search Ticket Categories"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket Category"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket Category"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket Category"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket Category"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class TicketSourceViewSet(viewsets.ModelViewSet):
    queryset = TicketSource.objects.all()
    serializer_class = TicketSourceSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['name']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List, filter, and search Ticket Sources"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket Source"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket Source"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket Source"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket Source"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class TicketViewSet(viewsets.ModelViewSet):
    queryset = Ticket.objects.all()
    serializer_class = TicketSerializer
    permission_classes = [IsAuthenticated]
    # if os.environ.get('DEBUG', 'false').lower() == 'true':
    #     permission_classes = [IsAuthenticated]
    # else:
    #     permission_classes = [AllowAny]
    filter_backends = [filters.SearchFilter, DjangoFilterBackend]
    search_fields = ['ticket_id','status', 'priority', 'category', 'prospect__name', 'sales__lead_file_no', 'customer__customer_name','title', 'description']
    filterset_fields = ['ticket_id','status','priority', 'category', 'prospect','sales','customer','prospect__name', 'sales__lead_file_no', 'customer__customer_name','title', 'description']
    http_method_names = ['get', 'post', 'patch', 'delete']

    parser_classes = [MultiPartParser, FormParser]

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List, filter, and search Tickets"
    )
    def list(self, request, *args, **kwargs):
        all_tickets = self.queryset
        tickets_status = {
            'open': all_tickets.filter(status='open').count(),
            'in_progress': all_tickets.filter(status='in_progress').count(),
            'escalated': all_tickets.filter(status='escalated').count(),
            'resolved': all_tickets.filter(status='resolved').count(),
            'closed': all_tickets.filter(status='closed').count(),
            'total': all_tickets.count(),
        }
        
        response = super().list(request, *args, **kwargs)
        if isinstance(response.data, dict):
            response.data['tickets_status'] = tickets_status
        return response

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket"
    )
    def create(self, request, *args, **kwargs): 
        file = request.FILES.get('file')

        try:
            with transaction.atomic():
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                ticket = serializer.save()  # Get the actual instance

                if file:
                    TicketAttachment.objects.create(
                        ticket=ticket,
                        file=file,
                        uploaded_by=request.user,
                    )

                if request.data.get("add_reminder"):

                    # Validate reminder_date and reminder_time
                    if not request.data.get('reminder_date'):
                        return Response({"detail": "reminder_date is required."}, status=400)
                    
                    if not request.data.get('reminder_time'):
                        return Response({"detail": "reminder_time is required."}, status=400)
                    
                    if not request.data.get('reminder_type'):
                        return Response({"detail": "reminder type is required."}, status=400)
                    
                    if not request.data.get('reminder_priority'):
                        return Response({"detail": "reminder priority is required."}, status=400)

                    # 3️⃣ Call ReminderViewSet.create() to also create a reminder
                    reminder_data = {
                        "reminder_type": request.data.get("reminder_type"),
                        "reminder_date": request.data.get("reminder_date"),
                        "reminder_time": request.data.get("reminder_time"),
                        "priority": request.data.get("reminder_priority"),
                        "created_by": request.user,
                        "client_type": None,
                        "title": f"Reminder for Ticket {ticket.ticket_id}: {ticket.title}",
                        "description": ticket.description,
                        "ticket": ticket,
                        # "customer": request.data.get("customer"),
                        # "prospect": request.data.get("prospect"),
                        # "sale": request.data.get("sale"),
                    }

                    reminder = Reminder.objects.create(**reminder_data)
                    if not reminder:
                        return Response(dict(success=True, message='Failed to create reminder'), status=400)
                    
                
                headers = self.get_success_headers(serializer.data)
                return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)
                
        except Exception as e:
            return Response(dict(success=False,message='could not create ticket', error=f'{e}'), status=500)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket"
    )
    def partial_update(self, request, *args, **kwargs):
        try:
            with transaction.atomic():
                data = request.data.copy()

                ticket = self.get_object()
                add_reminder = data.get("add_reminder")

                if add_reminder and str(add_reminder).lower() in ["true", "1", "yes", "on", True]:

                    # Validate reminder_date and reminder_time
                    if not request.data.get('reminder_date'):
                        return Response({"detail": "reminder_date is required."}, status=400)
                    
                    if not request.data.get('reminder_time'):
                        return Response({"detail": "reminder_time is required."}, status=400)
                    
                    if not request.data.get('reminder_type'):
                        return Response({"detail": "reminder type is required."}, status=400)
                    
                    if not request.data.get('reminder_priority'):
                        return Response({"detail": "reminder priority is required."}, status=400)

                    # 3️⃣ Call ReminderViewSet.create() to also create a reminder
                    reminder_data = {
                        "reminder_type": request.data.get("reminder_type"),
                        "reminder_date": request.data.get("reminder_date"),
                        "reminder_time": request.data.get("reminder_time"),
                        "priority": request.data.get("reminder_priority"),
                        "created_by": request.user,
                        "client_type": None,
                        "title": f"Reminder for Ticket {ticket.ticket_id}",
                        "description": request.data.get("reminder_descriptions"),
                        "ticket": ticket,
                        # "customer": request.data.get("customer"),
                        # "prospect": request.data.get("prospect"),
                        # "sale": request.data.get("sale"),
                    }

                    reminder = Reminder.objects.create(**reminder_data)
                    if not reminder:
                        return Response(dict(success=True, message='Failed to create reminder'), status=400)
                    
                    TicketActionLog.objects.create(
                        ticket=ticket,
                        action='Created',
                        performed_by=request.user,
                        comment=f'{request.user.fullnames} a reminder to Ticket ID {ticket.ticket_id}'
                    )
                    
                    return Response(dict(success=True, message='Reminder created successfully'), status=200)

                if data.get('status') == 'escalated':
                    if not ticket:
                        return Response({'error': 'Ticket not found'}, status=status.HTTP_404_NOT_FOUND)

                    # Check if already escalated to this user
                    escalate_to = data.get('escalate_to')
                    if not escalate_to:
                        return Response({'error': 'Escalate to employee field is required for escalation'}, status=status.HTTP_400_BAD_REQUEST)

                    escalations = TicketEscalation.objects.filter(ticket=ticket)
                    last_entry = escalations.last()

                    if last_entry and str(last_entry.user.id) == str(escalate_to):
                        return Response({'message': 'Employee is currently assigned to handle the ticket'}, status=status.HTTP_400_BAD_REQUEST)

                    # Create escalation
                    escalate_to_instance = User.objects.filter(employee_no=escalate_to).first()
                    if not escalate_to_instance:
                        return Response({'error': 'Employee not found'}, status=status.HTTP_404_NOT_FOUND)

                    escalated_ticket = TicketEscalation.objects.create(ticket=ticket, user=escalate_to_instance)

                    TicketActionLog.objects.create(
                        ticket=ticket,
                        action='escalated',
                        performed_by=request.user,
                        comment=f'Ticket ID {ticket.ticket_id} was escalated to {escalate_to_instance.fullnames} by {request.user.fullnames}'
                    )

                    return super().partial_update(request, *args, **kwargs)
                
                
                
                else:
                    TicketActionLog.objects.create(
                        ticket=ticket,
                        action=data.get('status'),
                        performed_by=request.user,
                        comment=f'Ticket ID {ticket.ticket_id} was set to {data.get('status')} by {request.user.fullnames}'
                    )
                
                    return super().partial_update(request, *args, **kwargs)

        except Exception as e:
            return Response(dict(error=f'{e}', message='Something went wrong'), status=500)

    


    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class TicketMessageViewSet(viewsets.ModelViewSet):
    queryset = TicketMessage.objects.all()
    serializer_class = TicketMessageSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['ticket']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List and filter Ticket Messages"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket Message"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket Message"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket Message"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket Message"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class TicketAttachmentViewSet(viewsets.ModelViewSet):
    queryset = TicketAttachment.objects.all()
    serializer_class = TicketAttachmentSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['ticket']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List and filter Ticket Attachments"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket Attachment"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket Attachment"
    )
    def create(self, request, *args, **kwargs):

        try:
            with transaction.atomic():
                file = request.FILES.get('file')
                data = request.data.copy()
                ticket_id = data.get('ticket')

                ticket = Ticket.objects.filter(ticket_id=ticket_id).first()

                if ticket:
                    TicketAttachment.objects.create(
                        ticket=ticket,
                        file=file,
                        uploaded_by=request.user,
                    )
                    return Response(dict(success=True, message='File uploaded successfully'), status=201)
                else: 
                    return Response(dict(success=False, message='Ticket not found'), status=400)
        
        except Exception as e: 
            return Response(dict(success=False, message='Something went wrong', error=f'{e}'), status=500)
        
    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket Attachment"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket Attachment"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class TicketActionLogViewSet(viewsets.ModelViewSet):
    queryset = TicketActionLog.objects.all()
    serializer_class = TicketActionLogSerializer
    if os.environ.get('DEBUG', 'false').lower() == 'true':
        permission_classes = [IsAuthenticated]
    else:
        permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['ticket', 'action']
    http_method_names = ['get', 'post', 'patch', 'delete']

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="List and filter Ticket Action Logs"
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Retrieve a Ticket Action Log"
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Create a Ticket Action Log"
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Update a Ticket Action Log"
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)

    @swagger_auto_schema(
        tags=['Tickets'],
        operation_description="Delete a Ticket Action Log"
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

