# Generated by Django 5.1.7 on 2025-06-27 14:43

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0002_initial'),
        ('sales', '0003_alter_marketerstarget_unique_together'),
        ('ticketing', '0006_alter_ticketmessage_sender'),
    ]

    operations = [
        migrations.AddField(
            model_name='ticket',
            name='sales',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales_tickets', to='sales.leadfile'),
        ),
        migrations.AlterField(
            model_name='ticket',
            name='customer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_ticket', to='customers.customer'),
        ),
        migrations.AlterField(
            model_name='ticket',
            name='status',
            field=models.CharField(choices=[('open', 'Open'), ('in_progress', 'In Progress'), ('resolved', 'Resolved'), ('closed', 'Closed'), ('escalated', 'Escalated')], default='open', max_length=20),
        ),
    ]
