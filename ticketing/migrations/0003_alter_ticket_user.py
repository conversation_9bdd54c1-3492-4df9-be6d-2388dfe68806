# Generated by Django 5.1.7 on 2025-06-26 21:05

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models
import ticketing.models

class Migration(migrations.Migration):

    dependencies = [
        ('ticketing', '0002_ticketsource_ticket_source'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='ticket',
            name='user',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tickets_assigned', to=settings.AUTH_USER_MODEL, to_field='employee_no'),
        ),
        migrations.AddField(
            model_name='ticket',
            name='ticket_id',
            field=models.CharField(default=ticketing.models.generate_ticket_id, max_length=20, unique=True),
        ),
    ]
