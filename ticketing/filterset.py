import django_filters
from .models import Ticket


class TicketFilterSet(django_filters.FilterSet):
    # Custom filter for partial title matching (case-insensitive)
    title_contains = django_filters.CharFilter(
        field_name='title',
        lookup_expr='icontains',
        help_text="Filter tickets by title containing the specified text (case-insensitive)"
    )
    
    # Custom filter for exact title matching
    title_exact = django_filters.CharFilter(
        field_name='title',
        lookup_expr='exact',
        help_text="Filter tickets by exact title match"
    )
    
    # Custom filter for title starting with text
    title_startswith = django_filters.CharFilter(
        field_name='title',
        lookup_expr='istartswith',
        help_text="Filter tickets by title starting with the specified text (case-insensitive)"
    )
    
    # Custom filter for description containing text
    description_contains = django_filters.CharFilter(
        field_name='description',
        lookup_expr='icontains',
        help_text="Filter tickets by description containing the specified text (case-insensitive)"
    )

    class Meta:
        model = Ticket
        fields = {
            'ticket_id': ['exact', 'icontains'],
            'status': ['exact'],
            'priority': ['exact'],
            'category': ['exact'],
            'prospect': ['exact'],
            'sales': ['exact'],
            'customer': ['exact'],
            'prospect__name': ['exact', 'icontains'],
            'sales__lead_file_no': ['exact', 'icontains'],
            'customer__customer_name': ['exact', 'icontains'],
            'module': ['exact', 'icontains'],
            'is_ict_issue': ['exact'],
        }