# ERP-API

### Project Server Setup

1 Setup SSH Access

2 Install coolify `curl -fsSL https://cdn.coollabs.io/coolify/install.sh | bash`

3 Make sure docker compose and docker is installed `docker version` and `docker compose version`


#### Countinous Development
To run the application on local machine leverage the `docker-compose.dev.yaml` file to run the application in docker with all dependencies

```bash
docker compose -f docker-compose.dev.yaml up --build
```
All Environment variables used during the development process are stored in `.env.dev`

To add a depency in project run `poetry add Django` for example.

After adding dependencies of the project run the command 
`poetry export -f requirements.txt > requirements.txt`
 this updates the requirements.txt file with the updated dependencies.



commands:  run celery   
```--
celery -A config worker --loglevel=info --pool=solo
```
celery beat logs

celery -A config beat --loglevel=INFO





python manage.py migrate --run-syncdb


python manage.py collectstatic --noinput && python manage.py makemigrations && python manage.py migrate && celery -A config beat --loglevel=INFO


SELECT setval(
    pg_get_serial_sequence('accounts_use', 'id'), 
    COALESCE((SELECT MAX(id) FROM accounts_use), 1) + 1,
    false
);


OR 


ALTER SEQUENCE accounts_organisationdepartment_id_seq RESTART WITH 51;




To push code from the development branch to the main branch while ensuring a smooth merge process, follow these steps:

