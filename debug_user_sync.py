#!/usr/bin/env python3
"""
Debug script to test the UserSyncAPIView logic
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append('/home/<USER>/Documents/projects/optiven/crm2.0')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from users.models import User, Departments, Teams
from users.serializers import UserSerializer
from users.helpers import deactivate_user_email

def debug_user_sync():
    """Debug the user sync logic with the provided data"""
    
    # Test data from the user
    data = {
        "emp_no": "OL/HR/659",
        "fullname": "DENNIS MWENDWA MUTINDA",
        "first_name": "<PERSON>",
        "last_name": "<PERSON><PERSON><PERSON>",
        "company_email": "<EMAIL>",
        "personal_email": "",
        "phone_number": "",
        "gender": "Female",
        "department": "",
        "office": "ABSA",
        "status": "Active",
        "team": "",
        "region": "",
        "Manager": "OL/HR/161",
        "is_marketer": "No",
        "ERP_user_id": "OPTIVEN-SERVER-DENNIS.MWENDWA"
    }
    
    print("=== DEBUG USER SYNC ===")
    print(f"Input data: {data}")
    print()
    
    # Transform data like in the actual code
    data["email"] = data.get("company_email")
    data["erp_user_id"] = data.get("ERP_user_id")
    data["employee_no"] = data.get("emp_no")
    data["is_marketer"] = True if data.get("is_marketer") == 'Yes' else False
    
    print(f"After initial transformation: {data}")
    print()
    
    # Clean and transform status
    if data.get("status") == "Active":
        data["status"] = "Active"
        data["is_active"] = True
        data["is_staff"] = True
        data["is_available"] = True
    else:
        data["status"] = "Inactive"
        prefix = User.objects.filter(email__startswith="inactive.").count() + 1
        data["email"] = f'inactive.{prefix}.{data["email"]}'
        data["is_active"] = False
        data["is_staff"] = False
        data["is_available"] = False
    
    print(f"After status transformation: {data}")
    print()
    
    # Office transformation
    if data.get("office") == "ABSA":
        data["office"] = "HQ"
    elif data.get("office") == "KAREN":
        data["office"] = "Global"
    else:
        data["office"] = "HQ"
    
    print(f"After office transformation: {data}")
    print()
    
    # Department handling
    try:
        department = Departments.objects.get(dp_name=data["department"])
        data["department"] = department.dp_name
        print(f"Found department: {department.dp_name}")
    except Departments.DoesNotExist:
        if data.get("is_marketer") in ["Yes","yes", True, 'true']:
            data["department"] = "CONVERSION"
        else:
            data["department"] = 'UNKNOWN'
        print(f"Department not found, set to: {data['department']}")
    
    # Team handling
    try:
        team = Teams.objects.get(team=data["team"])
        data["team"] = team.team
        data["tl_code"] = team.tl_code
        data["tl_name"] = team.tl_name
        print(f"Found team: {team.team}")
    except Teams.DoesNotExist:
        data["team"] = None
        print("Team not found, set to None")
    
    data["group"] = None
    
    print(f"Final data before user lookup: {data}")
    print()
    
    # Check if user exists
    try:
        user = User.objects.get(employee_no=data["emp_no"])
        print(f"Found existing user: {user.employee_no} - {user.fullnames} - {user.email}")
        
        # Check email conditions
        if data.get('company_email') == '' and user.email:
            print("Condition 1: Empty company_email and user has email - deactivating")
            deactivate_email = deactivate_user_email(user.email)
            data['email'] = deactivate_email
            data['status'] = "Inactive"
        elif data.get('company_email') and user.email != data.get('company_email'):
            print("Condition 2: Different company_email - updating")
            data['email'] = data.get('company_email')
        elif data.get('company_email') and user.email.startswith('newuser'):
            print("Condition 3: User has newuser email - activating")
            data['email'] = data.get('company_email')
            data['status'] = 'Active'
            data["is_active"] = True
            data["is_staff"] = True
            data["is_available"] = True
        else:
            print("No email condition matched")
        
        print(f"Data after email logic: {data}")
        
        # Test serializer
        serializer = UserSerializer(user, data=data, partial=True)
        if serializer.is_valid():
            print("Serializer is valid!")
            print("Would save user successfully")
        else:
            print("Serializer errors:")
            print(serializer.errors)
            
    except User.DoesNotExist:
        print("User does not exist - would create new user")
        
        # Test new user creation logic
        if not data.get("user_id"):
            import random
            data["user_id"] = str(random.randint(1000000, 9999999))
        
        if not data.get("fullname"):
            print("ERROR: Fullname is required")
            return
        
        names = data["fullname"].split()
        data["first_name"] = names[0]
        data["last_name"] = names[-1] if len(names) > 1 else ""
        
        base_username = f"{data['first_name']}.{data['last_name']}".lower()
        import re
        sanitized_base_username = re.sub(r'[^a-zA-Z0-9]', '', base_username)
        username = sanitized_base_username
        counter = 1
        while User.objects.filter(username=username).exists():
            username = f"{sanitized_base_username}{counter}"
            counter += 1
        
        data["username"] = username
        data["fullnames"] = data.pop("fullname")
        data["password"] = "password123"
        
        if data.get('company_email') == '': 
            mkcode = data["employee_no"].split('/')[-1]
            data["email"] = f'newuser{mkcode}@optiven.co.ke'
            data["status"] = "Inactive"
            data["mkcode"] = f'SM-{mkcode}'
        
        print(f"New user data: {data}")
        
        # Test serializer for new user
        serializer = UserSerializer(data=data)
        if serializer.is_valid():
            print("New user serializer is valid!")
        else:
            print("New user serializer errors:")
            print(serializer.errors)

if __name__ == "__main__":
    debug_user_sync()
