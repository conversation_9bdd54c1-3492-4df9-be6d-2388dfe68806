# Generated by Django 5.1.7 on 2025-06-04 12:50

import django.contrib.auth.validators
import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Departments',
            fields=[
                ('dp_id', models.AutoField(primary_key=True, serialize=False)),
                ('dp_name', models.CharField(max_length=255, unique=True)),
                ('dep_head_id', models.CharField(blank=True, max_length=50, null=True)),
                ('dep_head_name', models.CharField(blank=True, max_length=255, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Teams',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('team', models.CharField(max_length=255, unique=True)),
                ('tl_code', models.CharField(blank=True, max_length=50, null=True, unique=True)),
                ('tl_name', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('inactive', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='TeamsPermissions',
            fields=[
                ('permission_id', models.IntegerField(primary_key=True, serialize=False)),
                ('permission_name', models.CharField(max_length=255)),
                ('comments', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserGroupPermissions',
            fields=[
                ('permission_id', models.IntegerField(primary_key=True, serialize=False)),
                ('permission_name', models.CharField(max_length=255)),
                ('comments', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserGroups',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('group_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255, unique=True)),
                ('comments', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='UserPermissions',
            fields=[
                ('permission_id', models.IntegerField(primary_key=True, serialize=False)),
                ('permission_name', models.CharField(max_length=255, unique=True)),
                ('comments', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='User',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('username', models.CharField(error_messages={'unique': 'A user with that username already exists.'}, help_text='Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.', max_length=150, unique=True, validators=[django.contrib.auth.validators.UnicodeUsernameValidator()], verbose_name='username')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('employee_no', models.CharField(max_length=50, unique=True)),
                ('user_id', models.IntegerField(blank=True, null=True, unique=True)),
                ('category', models.CharField(choices=[('Pillar', 'Pillar'), ('Converter', 'Converter')], default='Converter', max_length=100)),
                ('office', models.CharField(choices=[('HQ', 'HQ'), ('Global', 'Global')], default='HQ', max_length=100)),
                ('fullnames', models.CharField(max_length=255)),
                ('email', models.EmailField(max_length=254, unique=True)),
                ('status', models.CharField(choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Converted', 'Converted')], default='Active', max_length=50)),
                ('created_date', models.DateTimeField(auto_now_add=True)),
                ('reset_code', models.CharField(blank=True, max_length=100, null=True)),
                ('reset_code_creation_time', models.DateTimeField(blank=True, null=True)),
                ('user_group', models.CharField(blank=True, max_length=100, null=True)),
                ('is_available', models.BooleanField(default=True)),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True)),
                ('personal_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('is_marketer', models.BooleanField(default=True)),
                ('gender', models.CharField(blank=True, choices=[('Male', 'Male'), ('Female', 'Female')], max_length=10, null=True)),
                ('qr_code', models.CharField(blank=True, max_length=255, null=True)),
                ('mkcode', models.IntegerField(unique=True)),
                ('erp_user_id', models.CharField(blank=True, max_length=255, null=True)),
                ('manager', models.CharField(blank=True, max_length=255, null=True)),
                ('is_driver', models.BooleanField(default=False)),
                ('groups', models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups')),
                ('user_permissions', models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions')),
                ('department', models.ForeignKey(default='UNKNOWN', on_delete=django.db.models.deletion.SET_DEFAULT, related_name='user_department', to='users.departments', to_field='dp_name')),
                ('team', models.ForeignKey(blank=True, default='UNKNOWN', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_team', to='users.teams', to_field='tl_name')),
                ('group', models.ForeignKey(blank=True, default='UNKNOWN', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_groups', to='users.usergroups', to_field='name')),
            ],
            options={
                'verbose_name': 'user',
                'verbose_name_plural': 'users',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Teams_2_TeamsPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('team', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='team_permissions', to='users.teams')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='team_permissions', to='users.teamspermissions')),
            ],
            options={
                'unique_together': {('team', 'permission')},
            },
        ),
        migrations.CreateModel(
            name='UserGroup_2_UserGroupPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_group_permissions', to='users.usergrouppermissions')),
                ('user_group', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_group_permissions', to='users.usergroups')),
            ],
            options={
                'unique_together': {('user_group', 'permission')},
            },
        ),
        migrations.CreateModel(
            name='User_2_UserPermissions',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_custom_permissions', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('permission', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_permissions', to='users.userpermissions')),
            ],
            options={
                'unique_together': {('user', 'permission')},
            },
        ),
    ]
