from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.db import models
from .models import (User,Departments,Teams,UserGroups,UserPermissions,
                     TeamsPermissions,UserGroupPermissions,User_2_UserPermissions,
                     Teams_2_TeamsPermissions,UserGroup_2_UserGroupPermissions)
from .models import UploadedFile
#  auth serializers
class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        # fields = "__all__" 
        exclude = ['password', 'is_superuser', 'is_staff', 'is_active', 'last_login', 'date_joined']    
        extra_kwargs = {
            'password': {'write_only': True},
            # 'employee_no': {'read_only': True},
        }
 
    def create(self, validated_data):
        password = validated_data.pop('password', None)
        user = User(**validated_data)
        user.set_password(password)
        user.save()
        return user
    


class LoginSerializer(serializers.Serializer):
    employee_no = serializers.Char<PERSON>ield(required=False)  # Primary key
    username = serializers.EmailField(required=False)
    password = serializers.CharField(write_only=True)

    def validate(self, attrs):
       
        email = attrs.get('username')  
        password = attrs.get('password')
        # print('attrs', attrs)
        # Retrieve user by email
        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:  
            raise serializers.ValidationError("Invalids scredentials.")

        # Authenticate user
        if not user.check_password(password):
            raise serializers.ValidationError("Invalids credentials.")

        if not user.is_active:
            raise serializers.ValidationError("User is inactive.")

        self.context['user'] = user
        attrs['user'] = user
        # print('attrs', attrs)
        return attrs

class GETUserSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = User
        # fields = ['employee_no', 'email', 'fullnames','department']   
        exclude = ['password', 'is_superuser', 'is_staff', 'is_active', 'last_login', 'date_joined']

class DepartmentsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Departments
        fields =   "__all__"

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        user = User.objects.filter(employee_no=instance.dep_head_id).first()
        if user:
            representation['dep_head_name'] = user.fullnames
        else:
            representation['dep_head_name'] = None
        return representation

class TeamsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Teams
        fields =   "__all__"

class GroupsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserGroups
        fields =   "__all__"

class UserPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserPermissions
        fields =   "__all__"

class TeamsPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = TeamsPermissions
        fields =   "__all__"

class UserGroupPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserGroupPermissions
        fields =   "__all__"

class User_2_UserPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = User_2_UserPermissions
        fields =   "__all__"

    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['permission_name'] = instance.permission.permission_name
        return representation

class Teams_2_TeamsPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = Teams_2_TeamsPermissions
        fields =   "__all__"
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['permission_name'] = instance.permission.permission_name
        return representation

class UserGroup_2_UserGroupPermissionsSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserGroup_2_UserGroupPermissions
        fields =   "__all__"
    
    def to_representation(self, instance):
        representation = super().to_representation(instance)
        representation['permission_name'] = instance.permission.permission_name
        return representation
    
class UploadedFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UploadedFile
        fields =   "__all__"