from django.urls import path, include
from rest_framework.routers import Default<PERSON>outer

from users import views

users_router = DefaultRouter(trailing_slash=False)



users_router.register('users', views.GETEmployeeView, basename='users')
users_router.register('departments', views.DepartmentsView, basename='departments')
users_router.register('teams', views.TeamsView, basename='teams')
users_router.register('groups', views.GroupsView, basename='groups')
users_router.register('user_permissions', views.UserPermissionsView, basename='userPermissions')
users_router.register('team_permissions', views.TeamsPermissionsView, basename='TeamPermissions')
users_router.register('group_permissions', views.UserGroupPermissionsView, basename='GroupPermissions')
users_router.register('user_2_userpermissions', views.User_2_UserPermissionsView, basename='User_2_UserPermissions')
users_router.register('teams_2_teamspermissions', views.Teams_2_TeamsPermissionsView, basename='Teams_2_TeamsPermissions')
users_router.register('usergroup_2_usergrouppermissions', views.UserGroup_2_UserGroupPermissionsView, basename='UserGroup_2_UserGroupPermissions')
users_router.register('file_uploads', views.UploadedFileViewSet, basename='UploadedFile')


urlpatterns = [
    path('sys/healthz', views.health_check, name="healthz"),
    path('users/login', views.login_view, name='login_view'),
    path('users/logout', views.logout, name="account_logout"),
    path("users/reset-link", views.reset_password, name="account_reset_link"),
    path('users/registration', views.account_registration, name='account_registration'),
    path('users/refresh-token', views.refresh_access_token, name='account_refresh_access_token'),
    path('users/all_permissions', views.user_permissions_view, name='all_permissions'),
    
    path('users/', include(users_router.urls)),
    
    
]
