from django.contrib.auth import authenticate
from rest_framework import status, viewsets, filters, generics
from rest_framework.permissions import AllowAny
from django.conf import settings
from django_filters.rest_framework import DjangoFilterBackend, FilterSet, CharFilter
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.viewsets import ReadOnlyModelViewSet
from django.db.models import OuterRef, Subquery
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from drf_yasg.utils import swagger_auto_schema
from rest_framework.exceptions import ValidationError
from drf_yasg import openapi
from rest_framework.views import APIView
from functools import wraps
import secrets,string
from django.utils import timezone
# from django_celery_beat.models import PeriodicTask, IntervalSchedule


from .serializers import (UserSerializer,LoginSerializer,GETUserSerializer,DepartmentsSerializer,
                          TeamsSerializer,GroupsSerializer,UserPermissionsSerializer,
                          TeamsPermissionsSerializer,UserGroupPermissionsSerializer,
                          User_2_UserPermissionsSerializer,Teams_2_TeamsPermissionsSerializer,
                          UserGroup_2_UserGroupPermissionsSerializer,
                          )
from .models import ( User,Departments,Teams,UserGroups,UserPermissions,
                      TeamsPermissions,UserGroupPermissions,User_2_UserPermissions,Teams_2_TeamsPermissions,
                      UserGroup_2_UserGroupPermissions
                    )

from .serializers import UploadedFileSerializer


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator


# Define request and response schemas
registration_request_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'user': openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'email': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_EMAIL),
                'password': openapi.Schema(type=openapi.TYPE_STRING),
            },
            required=['email', 'password'],
        ),
    },
)
@swagger_auto_schema(method='post', request_body=registration_request_schema, responses={201: UserSerializer()},tags=['Authentication'])
@api_view(['POST'])
@permission_classes([AllowAny])
def account_registration(request):
    try:
        user_data = request.data.get("user")

        if user_data is None:
            return Response ({"error": "Invalid payload. 'user' object is required."},status=status.HTTP_400_BAD_REQUEST)

        serializer = UserSerializer(data=user_data)
        serializer.is_valid(raise_exception=True)
        serializer.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    
    except ValidationError as e:
        # Return the validation error details
        return Response({"error1": e.detail}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        # Handle other exceptions
        return Response({"error2": str(e)}, status=status.HTTP_400_BAD_REQUEST)

# start login
class LoginView(APIView):
    permission_classes = []
@swagger_auto_schema(
    method='post',
    request_body=LoginSerializer,
    responses={200: "Login successful", 400: "Invalid credentials"},
    tags=['Authentication']
)
@api_view(['POST'])
@permission_classes([AllowAny])
def login_view(request):
    """
    Handle user login using email or employee number and password.
    """
    serializer = LoginSerializer(data=request.data)
    if serializer.is_valid():
        user = serializer.validated_data['user']
        # Generate access and refresh tokens
        access_token = AccessToken.for_user(user)
        refresh_token = RefreshToken.for_user(user)
        emp_no = user.employee_no
        department= user.department.dp_name
        office= user.office
        user_group= user.group
        team= user.team.team if user.team is not None else ""
        user_full_name = user.fullnames
        user_erp_id = user.erp_user_id  

        user_perms = User_2_UserPermissions.objects.filter(user=emp_no)

        user_permissions = [
            {
             str(perm.permission.permission_id),  # Convert permission to string
            }
            for perm in user_perms
        ]

        return Response({
            "message": "Login successful",
            "employee_no": emp_no,
            "email": user.email,
            "fullnames": user_full_name,
            "AccessToken": str(access_token),
            "RefreshToken": str(refresh_token),
            "department": department,
            "office": office,
            "team": team,
            "user_group": user_group,
            "user_permissions": user_permissions,
            'region': user.region,
            "erp_user_id": user_erp_id,
        }, status=status.HTTP_200_OK)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# end login
# start refresh token

refresh_token_request_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'refresh_token': openapi.Schema(type=openapi.TYPE_STRING)
    },
    required=['refresh_token']
)
refresh_token_response_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'access_token': openapi.Schema(type=openapi.TYPE_STRING),
        'refresh_token': openapi.Schema(type=openapi.TYPE_STRING)
    },
    required=['access_token', 'refresh_token']
)

@swagger_auto_schema(method="post", request_body=refresh_token_request_schema, responses={201: refresh_token_response_schema},tags=['Authentication'])
@api_view(['POST'])
@permission_classes([AllowAny])
def refresh_access_token(request):
    try:
        refresh_token = request.data.get('refresh_token')
        if not refresh_token:
            return Response({"message": "Invalid Request"}, status=status.HTTP_401_UNAUTHORIZED)

        jwt_token = RefreshToken(refresh_token)
        access_token = str(jwt_token.access_token)
        refresh_token = str(jwt_token)
        response_data = {
            "access_token": access_token,
            "refresh_token": refresh_token
        }

        return Response(response_data, status=status.HTTP_201_CREATED)
    except Exception as e:
        return Response({"error": str(e)},status=status.HTTP_400_BAD_REQUEST)
# end refresh token


#log  out start
logout_request_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'refresh_token': openapi.Schema(type=openapi.TYPE_STRING)
    },
    required=['refresh_token']
)
logout_response_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        "detail": openapi.Schema(type=openapi.TYPE_STRING)
    }
)

@swagger_auto_schema(method="post", request_body=logout_request_schema, responses={200: logout_response_schema},tags=['Authentication'])
@api_view(["POST"])
@permission_classes([IsAuthenticated])
def logout(request):
    try:
        refresh_token = request.data.get('refresh_token', None)

        if refresh_token is None:
            return Response({"detail": "Refresh token is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Create the RefreshToken instance from the token provided
        token = RefreshToken(refresh_token)

        # Blacklist the refresh token to invalidate it
        token.blacklist()

        return Response({"detail": "Logged out successfully."}, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)
#log out end

# start reset password
reset_password_request_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        'email': openapi.Schema(type=openapi.TYPE_STRING)
    },
    required=['email']
)
reset_password_response_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        "message": openapi.Schema(type=openapi.TYPE_STRING)
    }
)

@swagger_auto_schema(method="post", request_body=reset_password_request_schema, response={200: reset_password_response_schema},tags=['Authentication'])
@api_view(["POST"])
@permission_classes([AllowAny])
def reset_password(request):
    try:
        user_email  = request.data.get('email', None)

        if user_email is None:
            return Response({"message": "Email address must be provided"}, status=status.HTTP_400_BAD_REQUEST)
        
        user = User.objects.filter(email=user_email).first()
        if user is None:
            return Response({"message": "User with email address does not exist"}, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate password reset link
        def generate_random_string(size=32):
            characters = string.ascii_letters + string.digits  # Uppercase, lowercase, and digits
            return ''.join(secrets.choice(characters) for _ in range(size))

        resetcode= generate_random_string()
        reset_code_creation_time = timezone.now()
        #save reset code to user
        user.reset_code = resetcode
        user.reset_code_creation_time = reset_code_creation_time
        user.save()
        #password_reset_link
        password_reset_link = f"https://crm.optiven.co.ke/reset-password/{resetcode}"

        # Send password reset link to user email
        # send_email_task.delay(
        #     subject="Password Reset Link",
        #     recipients=[user_email],
        #     body_text=f"Click the link below to reset your password.\n{password_reset_link}"
        # )
        return Response({"message": f"Visit your email Account to activate password reset link {password_reset_link}"}, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
# end reset password


# start health check
@swagger_auto_schema(method="get", response={200},tags=['Authentication'])
@api_view(["GET"])
@permission_classes([AllowAny])
def health_check(request):
    return Response(status=status.HTTP_200_OK)
# end health check

class GETEmployeeView(ReadOnlyModelViewSet):
    queryset = User.objects.filter(status='Active') 
    serializer_class = GETUserSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
        
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]    
    filterset_fields = ['fullnames', 'username', 'email', 'employee_no', 'status', 'department__dp_name' ]
    search_fields = ['fullnames', 'username', 'email', 'employee_no', 'status', 'department__dp_name' ]
    ordering_fields = ['employee_no', 'email',]

    @swagger_auto_schema(
        tags=['Employee Details'],
        operation_description="Partial Information from User and EmployeeBioDetails",
        responses={200: GETUserSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Employee Details'],
        operation_description="GET Employee Data (Partial Information from User and EmployeeBioDetails)",
        responses={200: GETUserSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
class DepartmentsView(ReadOnlyModelViewSet):
    queryset = Departments.objects.all() 
    serializer_class = DepartmentsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['dp_name', 'dep_head_id', 'dep_head_name' ]
    ordering_fields = ['dp_name', 'dep_head_id', 'dep_head_name' ]

    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="Departments information",
        responses={200: DepartmentsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="GET Departments information (with id)",
        responses={200: DepartmentsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    
class TeamsView(ReadOnlyModelViewSet):
    queryset = Teams.objects.all() 
    serializer_class = TeamsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['team', 'tl_code', 'tl_name' ]
    ordering_fields = ['team', 'tl_code', 'tl_name' ]

    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="Teams information",
        responses={200: TeamsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="GET Teams information (with id)",
        responses={200: TeamsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)

class GroupsView(viewsets.ModelViewSet):
    queryset = UserGroups.objects.all() 
    serializer_class = GroupsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['group_id', 'name', 'comments' ]
    ordering_fields = ['group_id', 'name', 'comments' ]

    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="Groups information",
        responses={200: GroupsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="GET Groups information (with id)",
        responses={200: GroupsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="Create Groups ",
        responses={200: GroupsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="update Group",
        responses={200: GroupsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Organization Details'],
        operation_description="delete Groups ",
        responses={200: GroupsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
        
class UserPermissionsView(viewsets.ModelViewSet):
    queryset = UserPermissions.objects.all() 
    serializer_class = UserPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['permission_id', 'permission_name', 'comments' ]
    ordering_fields = ['permission_id', 'permission_name', 'comments' ]

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="UserPermissions information",
        responses={200: UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET UserPermissions information (with id)",
        responses={200: UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create UserPermissions information (with id)",
        responses={200: UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserPermissions information (with id)",
        responses={200: UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserPermissions information (with id)",
        responses={200: UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
class TeamsPermissionsView(viewsets.ModelViewSet):
    queryset = TeamsPermissions.objects.all() 
    serializer_class = TeamsPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['permission_id', 'permission_name', 'comments' ]
    ordering_fields = ['permission_id', 'permission_name', 'comments' ]

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="TeamsPermissions information",
        responses={200: TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET TeamsPermissions information (with id)",
        responses={200: TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create TeamsPermissions information (with id)",
        responses={200: TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update TeamsPermissions information (with id)",
        responses={200: TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update TeamsPermissions information (with id)",
        responses={200: TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class UserGroupPermissionsView(viewsets.ModelViewSet):
    queryset = UserGroupPermissions.objects.all() 
    serializer_class = UserGroupPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['permission_id', 'permission_name', 'comments' ]
    ordering_fields = ['permission_id', 'permission_name', 'comments' ]

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="UserGroupPermissions information",
        responses={200: UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET UserGroupPermissions information (with id)",
        responses={200: UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create UserGroupPermissions information (with id)",
        responses={200: UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserGroupPermissions information (with id)",
        responses={200: UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserGroupPermissions information (with id)",
        responses={200: UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
class User_2_UserPermissionsView(viewsets.ModelViewSet):
    queryset = User_2_UserPermissions.objects.all() 
    serializer_class = User_2_UserPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['user','permission']
    search_fields = ['user', 'user__employee_no','permission']
    ordering_fields = ['user','user__employee_no', 'permission']

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="User_2_UserPermissions information",
        responses={200: User_2_UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET User_2_UserPermissions information (with id)",
        responses={200: User_2_UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create User_2_UserPermissions information (with id)",
        responses={200: User_2_UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update User_2_UserPermissions information (with id)",
        responses={200: User_2_UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        
        
        
        
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update User_2_UserPermissions information (with id)",
        responses={200: User_2_UserPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)

class Teams_2_TeamsPermissionsView(viewsets.ModelViewSet):
    queryset = Teams_2_TeamsPermissions.objects.all() 
    serializer_class = Teams_2_TeamsPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['team', 'permission']
    search_fields = ['team', 'permission']
    ordering_fields = ['team', 'permission']

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="Teams_2_TeamsPermissions information",
        responses={200: Teams_2_TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET Teams_2_TeamsPermissions information (with id)",
        responses={200: Teams_2_TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create Teams_2_TeamsPermissions information (with id)",
        responses={200: Teams_2_TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update Teams_2_TeamsPermissions information (with id)",
        responses={200: Teams_2_TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update Teams_2_TeamsPermissions information (with id)",
        responses={200: Teams_2_TeamsPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
class UserGroup_2_UserGroupPermissionsView(viewsets.ModelViewSet):
    queryset = UserGroup_2_UserGroupPermissions.objects.all() 
    serializer_class = UserGroup_2_UserGroupPermissionsSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post', 'patch', 'delete']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['user_group', 'permission']
    search_fields = ['user_group', 'permission']
    ordering_fields = ['user_group', 'permission']

    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="UserGroup_2_UserGroupPermissions information",
        responses={200: UserGroup_2_UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="GET UserGroup_2_UserGroupPermissions information (with id)",
        responses={200: UserGroup_2_UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="create UserGroup_2_UserGroupPermissions information (with id)",
        responses={200: UserGroup_2_UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserGroup_2_UserGroupPermissions information (with id)",
        responses={200: UserGroup_2_UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['Permissions'],
        operation_description="partial_update UserGroup_2_UserGroupPermissions information (with id)",
        responses={200: UserGroup_2_UserGroupPermissionsSerializer(many=True), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
def gather_user_permissions(user):
        user_permissions = list(User_2_UserPermissions.objects.filter(user=user).values_list('permission__permission_name', flat=True))
        team_permissions = list(Teams_2_TeamsPermissions.objects.filter(team=user.team).values_list('permission__permission_name', flat=True)) if user.team else []
        group_permissions = list(UserGroup_2_UserGroupPermissions.objects.filter(user_group=user.group).values_list('permission__permission_name', flat=True)) if user.group else []
        return list(set(user_permissions + team_permissions + group_permissions))

user_permissions_response_schema = openapi.Schema(
    type=openapi.TYPE_OBJECT,
    properties={
        "permissions": openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Schema(type=openapi.TYPE_STRING)
        )
    }
)

@swagger_auto_schema(
        method='get',
        tags=['Permissions'],
        operation_description="Get all permissions for the authenticated user (direct, team, and group permissions).",
        responses={200: user_permissions_response_schema, 401: "Unauthorized", 403: "Forbidden"}
    )
@api_view(['GET'])
@permission_classes([IsAuthenticated])
def user_permissions_view(request):
        """
        Returns all permissions for the authenticated user, including direct, team, and group permissions.
        """
        permissions = gather_user_permissions(request.user)
        return Response({"permissions": permissions})

# users/views.py
from rest_framework.parsers import MultiPartParser, FormParser

from .models import UploadedFile
from .serializers import UploadedFileSerializer

class UploadedFileViewSet(viewsets.ModelViewSet):
    queryset = UploadedFile.objects.all()
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    serializer_class = UploadedFileSerializer
    parser_classes = [MultiPartParser, FormParser]
    
    
    
    @swagger_auto_schema(
        tags=['uploads'],
        operation_description="n",
        responses={200: UploadedFileSerializer(), 401: "Unauthorized", 403: "Forbidden"}
    )
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['uploads'],
        operation_description="n",
        responses={200: UploadedFileSerializer(), 401: "Unauthorized", 403: "Forbidden"}
    )
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['uploads'],
        operation_description="n",
        responses={200: UploadedFileSerializer(), 401: "Unauthorized", 403: "Forbidden"}
    )
    def create(self, request, *args, **kwargs):
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['uploads'],
        operation_description="n",
        responses={200: UploadedFileSerializer(), 401: "Unauthorized", 403: "Forbidden"}
    )
    def partial_update(self, request, *args, **kwargs):
        return super().partial_update(request, *args, **kwargs)
    @swagger_auto_schema(
        tags=['uploads'],
        operation_description="n",
        responses={200: UploadedFileSerializer(), 401: "Unauthorized", 403: "Forbidden"}
    )
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)


