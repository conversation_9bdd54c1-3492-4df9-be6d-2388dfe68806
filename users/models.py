from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models


class Departments(models.Model):
    dp_id = models.AutoField(primary_key=True)
    dp_name = models.CharField(max_length=255,unique=True)
    dep_head_id = models.CharField(max_length=50, blank=True, null=True)
    dep_head_name = models.CharField(max_length=255,blank=True, null=True)

    def __str__(self):
        return self.dp_name
    
class Teams(models.Model):
    
    team = models.CharField(max_length=255, unique=True)
    tl_code = models.CharField(max_length=50,blank=True, null=True)
    tl_name = models.CharField( unique=True, max_length=255,blank=True, null=True)
    inactive = models.BooleanField(default=False)
    office= models.CharField( max_length=255,blank=True, null=True)
    
    def __str__(self):
        return f"Team {self.team} lead by: {self.tl_name}"

class UserGroups(models.Model):
    group_id = models.IntegerField( unique=True)
    name = models.Char<PERSON>ield(max_length=255, unique=True)
    comments = models.TextField(blank=True, null=True)
    
    
    class Meta:
        ordering = ['name'] 

    def __str__(self):
        return self.name
    
class UserManager(BaseUserManager):
    def create_user(self, email: str, password: str | None  = None, **extra_fields):
        user = self.model(email=email, **extra_fields)  # Use self.model instead of User

        if password:
            user.set_password(password)
        else:
            user.set_unusable_password()

        user.save(using=self._db)  # Ensure correct database usage
        return user

    def create_superuser(self, email: str, password: str | None = None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must be assigned to is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must be assigned to is_superuser=True.")

        return self.create_user(email, password, **extra_fields)

class User(AbstractUser):
    OFFICES= [('HQ','HQ'),('Global','Global')]

    employee_no = models.CharField(max_length=50, unique=True, null=False, blank=False)
    user_id = models.IntegerField( unique=True, blank=True, null=True)
    category = models.CharField(max_length=100, choices=[('Pillar', 'Pillar'), ('Converter', 'Converter')], default='Converter')
    office = models.CharField(max_length=100, choices=OFFICES,default='HQ')
    department = models.ForeignKey(Departments, to_field='dp_name', on_delete=models.SET_DEFAULT, blank=False, null=False, related_name='user_department',default='UNKNOWN')    
    fullnames = models.CharField(max_length=255)
    email = models.EmailField(unique=True)
    status = models.CharField(max_length=50, choices=[('Active', 'Active'), ('Inactive', 'Inactive'), ('Converted', 'Converted')], default='Active')
    created_date = models.DateTimeField(auto_now_add=True)
    reset_code = models.CharField(max_length=100, blank=True, null=True)
    reset_code_creation_time = models.DateTimeField(blank=True, null=True)
    user_group = models.CharField(max_length=100, blank=True, null=True)
    is_available = models.BooleanField(default=True)
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    personal_email = models.EmailField(blank=True, null=True)
    
    team = models.ForeignKey(Teams, on_delete=models.SET_NULL, to_field='team', blank=True, null=True, related_name='user_teamz',default='UNKNOWN')
    is_marketer = models.BooleanField(default=True)
    gender = models.CharField(max_length=10, choices=[('Male', 'Male'), ('Female', 'Female')], blank=True, null=True)
    qr_code = models.CharField(max_length=255, blank=True, null=True)  # You may use an ImageField if storing QR code images
    mkcode = models.CharField(max_length=15, default='SM-000001') 
    erp_user_id = models.CharField(max_length=255,blank=True, null=True)
    manager = models.CharField(max_length=255, blank=True, null=True)
    group = models.ForeignKey(UserGroups, on_delete=models.SET_NULL,to_field='name', blank=True, null=True, related_name='user_groups',default='UNKNOWN')
    is_driver = models.BooleanField(default=False)
    

    objects = UserManager()

    USERNAME_FIELD = 'email'  # Use email instead of username for authentication
    REQUIRED_FIELDS = ['fullnames']

class UserPermissions(models.Model):
    permission_id = models.IntegerField(primary_key=True)
    permission_name = models.CharField(max_length=255, unique=True)
    comments = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.permission_name

class TeamsPermissions(models.Model):
    permission_id = models.IntegerField(primary_key=True)
    permission_name = models.CharField(max_length=255)
    comments = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.permission_name
    
class UserGroupPermissions(models.Model):
    permission_id = models.IntegerField(primary_key=True)
    permission_name = models.CharField(max_length=255)
    comments = models.TextField(blank=True, null=True)

    def __str__(self):
        return self.permission_name
    
class User_2_UserPermissions(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, to_field='employee_no', related_name='user_custom_permissions')
    permission = models.ForeignKey(UserPermissions, on_delete=models.CASCADE, related_name='user_permissions')

    class Meta:
        unique_together = ('user', 'permission')

class Teams_2_TeamsPermissions(models.Model):
    team = models.ForeignKey(Teams, on_delete=models.CASCADE, related_name='team_id')
    permission = models.ForeignKey(TeamsPermissions, on_delete=models.CASCADE, related_name='team_permissions')

    class Meta:
        unique_together = ('team', 'permission')
        ordering = ['id']

class UserGroup_2_UserGroupPermissions(models.Model):
    user_group = models.ForeignKey(UserGroups, on_delete=models.CASCADE, related_name='user_group_permissions')
    permission = models.ForeignKey(UserGroupPermissions, on_delete=models.CASCADE, related_name='user_group_permissions')

    class Meta:
        unique_together = ('user_group', 'permission')
        ordering = ['id']
             
class UploadedFile(models.Model):
    file = models.FileField(upload_to='uploads/')
    uploaded_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.file.name
        