from users.models import User


def deactivate_user_email(user_email): 
    current_email = user_email
    email_local_part = current_email.split('@')[0]
    email_domain = current_email.split('@')[1]
    deactivate_email = ''

    # Determine the base email (without inactive prefix)
    if email_local_part.startswith('inactive'):
        # Extract base email by removing inactive prefix
        if '.' in email_local_part and email_local_part.split('.', 1)[0].startswith('inactive'):
            # Format: inactive{number}.original@domain
            base_email = '.'.join(email_local_part.split('.')[1:]) + '@' + email_domain
        else:
            # Format: inactive{number}original@domain (no dot separator)
            import re
            base_part = re.sub(r'^inactive\d*', '', email_local_part)
            base_email = base_part + '@' + email_domain if base_part else current_email
    else:
        # Email doesn't have inactive prefix
        base_email = current_email

    # Find all emails with inactive prefix that are similar to this base email
    from django.db.models import Q
    # Look for emails that start with 'inactive' and contain the base email pattern
    base_local = base_email.split('@')[0]
    similar_emails = User.objects.filter(
        Q(email__startswith='inactive') &
        Q(email__contains=base_local)
    ).values_list('email', flat=True)

    # Find the highest number used in inactive prefixes
    max_number = 0
    import re
    for email in similar_emails:
        local_part = email.split('@')[0]
        # Match inactive followed by optional number
        match = re.match(r'^inactive(\d+)?', local_part)
        if match:
            number_str = match.group(1)
            if number_str:
                try:
                    number = int(number_str)
                    max_number = max(max_number, number)
                except ValueError:
                    continue
            else:
                # 'inactive' without number is treated as inactive1
                max_number = max(max_number, 1)

    # Generate new email with incremented number
    new_number = max_number + 1

    if current_email.startswith('inactive'):
        # Current email already has inactive prefix, replace with new number
        if '.' in email_local_part and email_local_part.split('.', 1)[0].startswith('inactive'):
            base_part = '.'.join(email_local_part.split('.')[1:])
            deactivate_email = f'inactive{new_number}.{base_part}@{email_domain}'
        else:
            # No dot separator, extract base part
            import re
            base_part = re.sub(r'^inactive\d*', '', email_local_part)
            if base_part:
                deactivate_email = f'inactive{new_number}{base_part}@{email_domain}'
            else:
                deactivate_email = f'inactive{new_number}@{email_domain}'
    else:
        # Add inactive prefix to original email
        deactivate_email = f'inactive{new_number}.{current_email}'

    return deactivate_email