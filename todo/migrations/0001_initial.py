# Generated by Django 5.1.7 on 2025-07-25 16:31

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ToDo',
            fields=[
                ('todo_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('title', models.CharField(help_text='Title of the todo item', max_length=255)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the todo')),
                ('due_date', models.DateField(blank=True, help_text='Due date for the todo item', null=True)),
                ('due_time', models.TimeField(blank=True, help_text='Due time for the todo item', null=True)),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='PENDING', help_text='Current status of the todo item', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('URGENT', 'Urgent')], default='MEDIUM', help_text='Priority level of the todo item', max_length=20)),
                ('set_reminder', models.BooleanField(default=False)),
                ('reminder_time', models.DateTimeField(blank=True, null=True)),
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', max_length=20, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='todo_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='todo', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_todo', to='leads.prospects')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_todo', to='sales.leadfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
