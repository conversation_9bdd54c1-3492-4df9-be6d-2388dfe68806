from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer
from leads.models import Prospects
from sales.models import LeadFile
from todo.utils import generate_todo_id



class ToDo(models.Model):
    TODO_STATUS = (
        ('PENDING', 'Pending'),
        ('IN_PROGRESS', 'In Progress'),
        ('COMPLETED', 'Completed'),
        ('CANCELLED', 'Cancelled'),
    )
    TODO_PRIORITY = (
        ('LOW', 'Low'),
        ('MEDIUM', 'Medium'),
        ('HIGH', 'High'),
        ('URGENT', 'Urgent'),
    )
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]
    todo_id = models.CharField(max_length=50, unique=True, primary_key=True)
    title = models.CharField(max_length=255, help_text="Title of the todo item")
    description = models.TextField(blank=True, help_text="Detailed description of the todo")
    due_date = models.DateField(null=True, blank=True, help_text="Due date for the todo item")
    due_time = models.TimeField(null=True, blank=True, help_text="Due time for the todo item")
    status = models.CharField(max_length=20, choices=TODO_STATUS, default='PENDING', help_text="Current status of the todo item")
    priority = models.CharField(max_length=20, choices=TODO_PRIORITY, default='MEDIUM', help_text="Priority level of the todo item")
    
    
    
    set_reminder = models.BooleanField(default=False)
    reminder_time = models.DateTimeField(blank=True, null=True)
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect', null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='todo_created', to_field='employee_no')
    customer = models.ForeignKey( Customer, on_delete=models.CASCADE, related_name='todo', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_todo',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_todo', to_field='lead_file_no', null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure todo_id is unique"""

        if not self.todo_id:
            self.todo_id = generate_todo_id()
            while ToDo.objects.filter(todo_id=self.todo_id).exists():
                self.todo_id = generate_todo_id()
        print(f"Saving todo:with ID: {self.todo_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.todo_id} - {self.title}"

    class Meta:
        ordering = ['-created_at']

