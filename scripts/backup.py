#!/usr/bin/env python3
"""
CRM 2.0 Automated Backup System

This script performs comprehensive backups of:
1. MySQL database dump (compressed with gzip)
2. Django project source code (zipped)
3. MinIO S3 bucket files (tar.gz archive)

Backups are uploaded to Google Drive with automatic retention management.
"""

import os
import sys
import logging
import shutil
import tempfile
import subprocess
import gzip
import zipfile
import tarfile
from datetime import datetime, timedelta
from pathlib import Path
import json

# Add Django project to path for settings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

import django
django.setup()

from django.conf import settings
from django.core.mail import send_mail

# Import Google Drive API
try:
    from googleapiclient.discovery import build
    from googleapiclient.http import MediaFileUpload
    from google.oauth2.service_account import Credentials
except ImportError:
    print("Google API client not installed. Run: pip install google-api-python-client google-auth")
    sys.exit(1)

# Import MinIO client
try:
    from minio import Minio
    from minio.error import S3Error
except ImportError:
    print("MinIO client not installed. Run: pip install minio")
    sys.exit(1)


class BackupLogger:
    """Configure logging for backup operations"""
    
    def __init__(self):
        self.setup_logging()
    
    def setup_logging(self):
        """Setup rotating log file and console logging"""
        log_dir = Path('/var/log')
        if not log_dir.exists() or not os.access(log_dir, os.W_OK):
            # Fallback to project logs directory
            log_dir = Path(settings.BASE_DIR) / 'logs'
            log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / 'backup.log'
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)


class DatabaseBackup:
    """Handle database backup operations"""
    
    def __init__(self, logger):
        self.logger = logger
        self.db_config = settings.DATABASES['default']
    
    def create_dump(self, output_path):
        """Create compressed MySQL database dump"""
        try:
            self.logger.info("Starting database backup...")
            
            # Build mysqldump command
            cmd = [
                'mysqldump',
                f'--host={self.db_config["HOST"]}',
                f'--port={self.db_config["PORT"]}',
                f'--user={self.db_config["USER"]}',
                f'--password={self.db_config["PASSWORD"]}',
                '--single-transaction',
                '--routines',
                '--triggers',
                '--add-drop-table',
                '--extended-insert',
                self.db_config['NAME']
            ]
            
            # Execute mysqldump and compress with gzip
            dump_file = output_path / 'db_backup.sql.gz'
            
            with open(dump_file, 'wb') as f_out:
                with gzip.GzipFile(fileobj=f_out, mode='wb') as gz_out:
                    result = subprocess.run(
                        cmd,
                        stdout=gz_out,
                        stderr=subprocess.PIPE,
                        check=True,
                        text=False
                    )
            
            # Get file size for logging
            file_size = dump_file.stat().st_size / (1024 * 1024)  # MB
            self.logger.info(f"Database backup completed: {dump_file} ({file_size:.2f} MB)")
            
            return dump_file
            
        except subprocess.CalledProcessError as e:
            error_msg = f"Database backup failed: {e.stderr.decode()}"
            self.logger.error(error_msg)
            raise Exception(error_msg)
        except Exception as e:
            error_msg = f"Database backup error: {str(e)}"
            self.logger.error(error_msg)
            raise


class CodeBackup:
    """Handle source code backup operations"""
    
    def __init__(self, logger):
        self.logger = logger
        self.project_root = Path(settings.BASE_DIR)
    
    def create_archive(self, output_path):
        """Create compressed archive of Django project"""
        try:
            self.logger.info("Starting code backup...")
            
            archive_file = output_path / 'code_backup.zip'
            
            # Define exclusions
            exclude_patterns = {
                '__pycache__',
                '.git',
                '.venv',
                '*.pyc',
                '*.pyo',
                '.pytest_cache',
                'node_modules',
                'staticfiles',
                'media',
                'logs',
                '*.log',
                'tmp',
                '.env*',
                'backups'
            }
            
            def should_exclude(path):
                """Check if file/directory should be excluded"""
                path_str = str(path)
                for pattern in exclude_patterns:
                    if pattern in path_str:
                        return True
                return False
            
            with zipfile.ZipFile(archive_file, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                for root, dirs, files in os.walk(self.project_root):
                    # Remove excluded directories
                    dirs[:] = [d for d in dirs if not should_exclude(Path(root) / d)]
                    
                    for file in files:
                        file_path = Path(root) / file
                        if not should_exclude(file_path):
                            # Calculate relative path from project root
                            rel_path = file_path.relative_to(self.project_root)
                            zipf.write(file_path, rel_path)
            
            # Get file size for logging
            file_size = archive_file.stat().st_size / (1024 * 1024)  # MB
            self.logger.info(f"Code backup completed: {archive_file} ({file_size:.2f} MB)")
            
            return archive_file
            
        except Exception as e:
            error_msg = f"Code backup error: {str(e)}"
            self.logger.error(error_msg)
            raise


class MinIOBackup:
    """Handle MinIO S3 bucket backup operations"""
    
    def __init__(self, logger):
        self.logger = logger
        self.setup_client()
    
    def setup_client(self):
        """Initialize MinIO client"""
        try:
            # Extract endpoint without protocol
            endpoint = settings.MINIO_ENDPOINT.replace('https://', '').replace('http://', '')
            secure = settings.MINIO_ENDPOINT.startswith('https://')
            
            self.client = Minio(
                endpoint,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                secure=secure
            )
            self.bucket_name = settings.MINIO_BUCKET_NAME
            
        except Exception as e:
            error_msg = f"MinIO client setup error: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def create_archive(self, output_path):
        """Create tar.gz archive of MinIO bucket contents"""
        try:
            self.logger.info("Starting MinIO backup...")
            
            # Create temporary directory for bucket contents
            temp_dir = tempfile.mkdtemp(prefix='minio_backup_')
            bucket_dir = Path(temp_dir) / self.bucket_name
            bucket_dir.mkdir(parents=True)
            
            # Download all objects from bucket
            objects = self.client.list_objects(self.bucket_name, recursive=True)
            downloaded_count = 0
            
            for obj in objects:
                object_path = bucket_dir / obj.object_name
                object_path.parent.mkdir(parents=True, exist_ok=True)
                
                try:
                    self.client.fget_object(self.bucket_name, obj.object_name, str(object_path))
                    downloaded_count += 1
                except S3Error as e:
                    self.logger.warning(f"Failed to download {obj.object_name}: {e}")
            
            if downloaded_count == 0:
                self.logger.warning("No files found in MinIO bucket or all downloads failed")
                # Create empty archive
                archive_file = output_path / 'minio_backup.tar.gz'
                with tarfile.open(archive_file, 'w:gz') as tar:
                    pass  # Create empty archive
            else:
                # Create tar.gz archive
                archive_file = output_path / 'minio_backup.tar.gz'
                with tarfile.open(archive_file, 'w:gz') as tar:
                    tar.add(bucket_dir, arcname=self.bucket_name)
            
            # Cleanup temporary directory
            shutil.rmtree(temp_dir)
            
            # Get file size for logging
            file_size = archive_file.stat().st_size / (1024 * 1024)  # MB
            self.logger.info(f"MinIO backup completed: {archive_file} ({file_size:.2f} MB, {downloaded_count} files)")
            
            return archive_file
            
        except Exception as e:
            # Cleanup on error
            if 'temp_dir' in locals():
                shutil.rmtree(temp_dir, ignore_errors=True)
            
            error_msg = f"MinIO backup error: {str(e)}"
            self.logger.error(error_msg)
            raise


class GoogleDriveUploader:
    """Handle Google Drive operations"""
    
    def __init__(self, logger):
        self.logger = logger
        self.setup_service()
    
    def setup_service(self):
        """Initialize Google Drive API service"""
        try:
            # Get service account credentials
            creds_path = os.getenv('GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE')
            if not creds_path or not Path(creds_path).exists():
                raise FileNotFoundError(f"Google Drive service account file not found: {creds_path}")
            
            credentials = Credentials.from_service_account_file(
                creds_path,
                scopes=['https://www.googleapis.com/auth/drive']
            )
            
            self.service = build('drive', 'v3', credentials=credentials)
            self.folder_id = os.getenv('GOOGLE_DRIVE_BACKUP_FOLDER_ID')
            
            self.logger.info("Google Drive service initialized successfully")
            
        except Exception as e:
            error_msg = f"Google Drive setup error: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def upload_file(self, file_path, filename=None):
        """Upload file to Google Drive"""
        try:
            if filename is None:
                filename = Path(file_path).name
            
            file_metadata = {
                'name': filename,
                'parents': [self.folder_id] if self.folder_id else []
            }
            
            media = MediaFileUpload(file_path, resumable=True)
            
            self.logger.info(f"Uploading {filename} to Google Drive...")
            
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id'
            ).execute()
            
            file_id = file.get('id')
            file_size = Path(file_path).stat().st_size / (1024 * 1024)  # MB
            
            self.logger.info(f"Upload completed: {filename} ({file_size:.2f} MB) - ID: {file_id}")
            
            return file_id
            
        except Exception as e:
            error_msg = f"Google Drive upload error for {filename}: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def list_backup_files(self):
        """List backup files in Google Drive folder"""
        try:
            query = f"'{self.folder_id}' in parents and trashed=false" if self.folder_id else "trashed=false"
            query += " and name contains 'backup_'"
            
            results = self.service.files().list(
                q=query,
                orderBy='createdTime desc',
                fields='nextPageToken, files(id, name, createdTime, size)'
            ).execute()
            
            return results.get('files', [])
            
        except Exception as e:
            error_msg = f"Error listing Google Drive files: {str(e)}"
            self.logger.error(error_msg)
            return []
    
    def delete_file(self, file_id, filename):
        """Delete file from Google Drive"""
        try:
            self.service.files().delete(fileId=file_id).execute()
            self.logger.info(f"Deleted old backup: {filename}")
            
        except Exception as e:
            self.logger.error(f"Error deleting {filename}: {str(e)}")
    
    def cleanup_old_backups(self, retention_days=30):
        """Delete backups older than retention period"""
        try:
            self.logger.info(f"Cleaning up backups older than {retention_days} days...")
            
            files = self.list_backup_files()
            cutoff_date = datetime.utcnow() - timedelta(days=retention_days)
            
            deleted_count = 0
            for file in files:
                # Parse creation time
                created_time = datetime.strptime(
                    file['createdTime'].split('T')[0], 
                    '%Y-%m-%d'
                )
                
                if created_time < cutoff_date:
                    self.delete_file(file['id'], file['name'])
                    deleted_count += 1
            
            self.logger.info(f"Cleanup completed: {deleted_count} old backups deleted")
            
        except Exception as e:
            self.logger.error(f"Cleanup error: {str(e)}")


class BackupOrchestrator:
    """Main backup orchestration class"""
    
    def __init__(self):
        self.backup_logger = BackupLogger()
        self.logger = self.backup_logger.logger
        
        # Initialize backup components
        self.db_backup = DatabaseBackup(self.logger)
        self.code_backup = CodeBackup(self.logger)
        self.minio_backup = MinIOBackup(self.logger)
        self.drive_uploader = GoogleDriveUploader(self.logger)
        
        # Configuration
        self.retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))
    
    def send_notification(self, subject, message, is_error=False):
        """Send email notification"""
        try:
            recipient = os.getenv('BACKUP_NOTIFICATION_EMAIL', '<EMAIL>')
            
            send_mail(
                subject=f"CRM 2.0 Backup: {subject}",
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient],
                fail_silently=False
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send email notification: {str(e)}")
    
    def create_backup_structure(self):
        """Create timestamped backup directory structure"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"backup_{timestamp}"
        
        # Create temporary backup directory
        temp_dir = tempfile.mkdtemp(prefix='crm_backup_')
        backup_root = Path(temp_dir) / backup_name
        
        # Create subdirectories
        (backup_root / 'db').mkdir(parents=True)
        (backup_root / 'code').mkdir(parents=True)
        (backup_root / 'files').mkdir(parents=True)
        
        return backup_root, backup_name
    
    def create_final_archive(self, backup_root, backup_name):
        """Create final zip archive of all backup components"""
        try:
            archive_path = backup_root.parent / f"{backup_name}.zip"
            
            with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED, compresslevel=6) as zipf:
                for root, dirs, files in os.walk(backup_root):
                    for file in files:
                        file_path = Path(root) / file
                        rel_path = file_path.relative_to(backup_root.parent)
                        zipf.write(file_path, rel_path)
            
            # Get final archive size
            archive_size = archive_path.stat().st_size / (1024 * 1024)  # MB
            self.logger.info(f"Final backup archive created: {archive_path} ({archive_size:.2f} MB)")
            
            return archive_path
            
        except Exception as e:
            error_msg = f"Error creating final archive: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def run_backup(self):
        """Execute complete backup process"""
        backup_start_time = datetime.now()
        temp_dirs = []
        
        try:
            self.logger.info("="*50)
            self.logger.info("CRM 2.0 Backup Process Started")
            self.logger.info("="*50)
            
            # Create backup structure
            backup_root, backup_name = self.create_backup_structure()
            temp_dirs.append(backup_root.parent)
            
            self.logger.info(f"Backup name: {backup_name}")
            self.logger.info(f"Temporary directory: {backup_root}")
            
            # Perform individual backups
            db_backup_file = self.db_backup.create_dump(backup_root / 'db')
            code_backup_file = self.code_backup.create_archive(backup_root / 'code')
            minio_backup_file = self.minio_backup.create_archive(backup_root / 'files')
            
            # Create final archive
            final_archive = self.create_final_archive(backup_root, backup_name)
            
            # Upload to Google Drive
            file_id = self.drive_uploader.upload_file(final_archive, f"{backup_name}.zip")
            
            # Cleanup old backups
            self.drive_uploader.cleanup_old_backups(self.retention_days)
            
            # Calculate backup duration and size
            backup_duration = datetime.now() - backup_start_time
            total_size = final_archive.stat().st_size / (1024 * 1024)  # MB
            
            success_message = (
                f"Backup completed successfully!\n"
                f"Backup name: {backup_name}\n"
                f"Duration: {backup_duration}\n"
                f"Total size: {total_size:.2f} MB\n"
                f"Google Drive file ID: {file_id}\n"
                f"Retention: {self.retention_days} days"
            )
            
            self.logger.info(success_message)
            self.send_notification("SUCCESS", success_message)
            
            return True
            
        except Exception as e:
            error_message = (
                f"Backup failed with error: {str(e)}\n"
                f"Duration before failure: {datetime.now() - backup_start_time}\n"
                f"Check logs for detailed error information."
            )
            
            self.logger.error(error_message)
            self.send_notification("FAILED", error_message, is_error=True)
            
            return False
            
        finally:
            # Cleanup temporary directories
            for temp_dir in temp_dirs:
                try:
                    if Path(temp_dir).exists():
                        shutil.rmtree(temp_dir)
                        self.logger.info(f"Cleaned up temporary directory: {temp_dir}")
                except Exception as e:
                    self.logger.warning(f"Failed to cleanup {temp_dir}: {str(e)}")
            
            self.logger.info("="*50)
            self.logger.info("CRM 2.0 Backup Process Completed")
            self.logger.info("="*50)


def main():
    """Main entry point"""
    try:
        orchestrator = BackupOrchestrator()
        success = orchestrator.run_backup()
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\nBackup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()