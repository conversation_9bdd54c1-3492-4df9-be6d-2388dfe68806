#!/bin/bash

# =============================================================================
# CRM 2.0 Backup System - Cron Setup Script
# =============================================================================
# 
# This script sets up automated backup scheduling using cron jobs.
# It creates a cron job that runs the backup script daily at 2:00 AM.
#
# Usage:
#   ./setup_cron.sh [--user username] [--time "hour:minute"] [--remove]
#
# Examples:
#   ./setup_cron.sh                           # Setup for current user at 2:00 AM
#   ./setup_cron.sh --user www-data           # Setup for www-data user
#   ./setup_cron.sh --time "03:30"            # Setup for 3:30 AM
#   ./setup_cron.sh --remove                  # Remove existing cron job
# =============================================================================

set -e  # Exit on any error

# Default configuration
DEFAULT_USER=$(whoami)
DEFAULT_TIME="02:00"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKUP_SCRIPT="$SCRIPT_DIR/backup.py"
LOG_FILE="/var/log/cron_backup.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# Function to show help
show_help() {
    cat << EOF
CRM 2.0 Backup System - Cron Setup

USAGE:
    $0 [OPTIONS]

OPTIONS:
    --user USER         User to install cron job for (default: current user)
    --time TIME         Time to run backup in HH:MM format (default: 02:00)
    --remove           Remove existing backup cron job
    --help             Show this help message

EXAMPLES:
    $0                          # Setup backup at 2:00 AM for current user
    $0 --user www-data          # Setup for www-data user
    $0 --time "03:30"           # Setup backup at 3:30 AM
    $0 --remove                 # Remove existing backup cron job

NOTES:
    - The script will create a cron job that runs daily
    - Logs are written to $LOG_FILE
    - Python virtual environment is automatically activated if present
    - Email notifications are sent on backup completion/failure

EOF
}

# Function to validate time format
validate_time() {
    local time="$1"
    if [[ ! "$time" =~ ^[0-9]{2}:[0-9]{2}$ ]]; then
        print_error "Invalid time format: $time. Use HH:MM format (e.g., 02:00)"
        return 1
    fi
    
    local hour=${time%:*}
    local minute=${time#*:}
    
    if [[ $hour -lt 0 || $hour -gt 23 ]]; then
        print_error "Invalid hour: $hour. Must be between 00-23"
        return 1
    fi
    
    if [[ $minute -lt 0 || $minute -gt 59 ]]; then
        print_error "Invalid minute: $minute. Must be between 00-59"
        return 1
    fi
    
    return 0
}

# Function to check if user exists
check_user_exists() {
    local user="$1"
    if ! id "$user" &>/dev/null; then
        print_error "User '$user' does not exist"
        return 1
    fi
    return 0
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if backup script exists
    if [[ ! -f "$BACKUP_SCRIPT" ]]; then
        print_error "Backup script not found: $BACKUP_SCRIPT"
        exit 1
    fi
    
    # Check if backup script is executable
    if [[ ! -x "$BACKUP_SCRIPT" ]]; then
        print_warning "Making backup script executable..."
        chmod +x "$BACKUP_SCRIPT"
    fi
    
    # Check if Python is available
    if ! command -v python3 &> /dev/null; then
        print_error "Python3 is not installed or not in PATH"
        exit 1
    fi
    
    # Check if cron is available
    if ! command -v crontab &> /dev/null; then
        print_error "Cron is not installed or not available"
        exit 1
    fi
    
    print_status "Prerequisites check passed"
}

# Function to get Python executable path
get_python_path() {
    # Check for virtual environment
    if [[ -f "$PROJECT_ROOT/.venv/bin/python3" ]]; then
        echo "$PROJECT_ROOT/.venv/bin/python3"
    elif [[ -f "$PROJECT_ROOT/venv/bin/python3" ]]; then
        echo "$PROJECT_ROOT/venv/bin/python3"
    elif [[ -n "$VIRTUAL_ENV" ]]; then
        echo "$VIRTUAL_ENV/bin/python3"
    else
        # Use system Python
        which python3
    fi
}

# Function to create cron job entry
create_cron_entry() {
    local time="$1"
    local hour=${time%:*}
    local minute=${time#*:}
    local python_path=$(get_python_path)
    
    # Remove leading zeros to avoid octal interpretation
    hour=$((10#$hour))
    minute=$((10#$minute))
    
    # Create the cron job command
    local cron_command="cd '$PROJECT_ROOT' && '$python_path' '$BACKUP_SCRIPT' >> '$LOG_FILE' 2>&1"
    
    # Create cron job entry
    echo "$minute $hour * * * $cron_command"
}

# Function to install cron job
install_cron_job() {
    local user="$1"
    local time="$2"
    
    print_status "Installing cron job for user: $user"
    print_status "Backup time: $time daily"
    
    # Get current crontab
    local temp_crontab=$(mktemp)
    
    # Get existing crontab (ignore errors if no crontab exists)
    if crontab -l -u "$user" 2>/dev/null > "$temp_crontab"; then
        print_debug "Found existing crontab for user $user"
    else
        print_debug "No existing crontab found for user $user"
        touch "$temp_crontab"
    fi
    
    # Remove any existing backup cron jobs
    grep -v "backup.py" "$temp_crontab" > "${temp_crontab}.new" || touch "${temp_crontab}.new"
    mv "${temp_crontab}.new" "$temp_crontab"
    
    # Add backup job comment and entry
    echo "" >> "$temp_crontab"
    echo "# CRM 2.0 Automated Backup System" >> "$temp_crontab"
    echo "# Generated on $(date) by $(whoami)" >> "$temp_crontab"
    create_cron_entry "$time" >> "$temp_crontab"
    
    # Install the new crontab
    if crontab -u "$user" "$temp_crontab"; then
        print_status "Cron job installed successfully"
    else
        print_error "Failed to install cron job"
        rm -f "$temp_crontab"
        exit 1
    fi
    
    # Cleanup
    rm -f "$temp_crontab"
    
    # Show the installed cron job
    print_status "Installed cron job:"
    crontab -l -u "$user" | grep -A1 -B1 "backup.py" || true
}

# Function to remove cron job
remove_cron_job() {
    local user="$1"
    
    print_status "Removing backup cron job for user: $user"
    
    # Get current crontab
    local temp_crontab=$(mktemp)
    
    if ! crontab -l -u "$user" 2>/dev/null > "$temp_crontab"; then
        print_warning "No crontab found for user $user"
        rm -f "$temp_crontab"
        return 0
    fi
    
    # Count backup-related lines
    local backup_lines=$(grep -c "backup.py\|CRM 2.0 Automated Backup" "$temp_crontab" || echo "0")
    
    if [[ $backup_lines -eq 0 ]]; then
        print_warning "No backup cron jobs found for user $user"
        rm -f "$temp_crontab"
        return 0
    fi
    
    # Remove backup-related lines
    grep -v "backup.py\|CRM 2.0 Automated Backup" "$temp_crontab" > "${temp_crontab}.new" || touch "${temp_crontab}.new"
    
    # Install the cleaned crontab
    if crontab -u "$user" "${temp_crontab}.new"; then
        print_status "Backup cron job removed successfully"
        print_status "Removed $backup_lines backup-related lines"
    else
        print_error "Failed to remove cron job"
        rm -f "$temp_crontab" "${temp_crontab}.new"
        exit 1
    fi
    
    # Cleanup
    rm -f "$temp_crontab" "${temp_crontab}.new"
}

# Function to show current backup cron jobs
show_current_jobs() {
    local user="$1"
    
    print_status "Current backup cron jobs for user: $user"
    echo
    
    if crontab -l -u "$user" 2>/dev/null | grep -A1 -B1 "backup.py\|CRM 2.0 Automated Backup"; then
        echo
    else
        print_warning "No backup cron jobs found for user $user"
    fi
}

# Function to test cron job
test_cron_job() {
    local user="$1"
    
    print_status "Testing backup script execution..."
    
    local python_path=$(get_python_path)
    
    # Test running the backup script
    print_debug "Using Python: $python_path"
    print_debug "Project root: $PROJECT_ROOT"
    print_debug "Backup script: $BACKUP_SCRIPT"
    
    cd "$PROJECT_ROOT"
    
    # Run a dry test (you might want to add a --test flag to your backup script)
    if timeout 30s "$python_path" "$BACKUP_SCRIPT" --version 2>/dev/null || \
       timeout 30s "$python_path" -c "import sys; sys.path.insert(0, '.'); exec(open('scripts/backup.py').read())" 2>/dev/null; then
        print_status "Backup script test successful"
    else
        print_warning "Backup script test failed - please check the script manually"
        print_debug "Try running: cd '$PROJECT_ROOT' && '$python_path' '$BACKUP_SCRIPT'"
    fi
}

# Main function
main() {
    local user="$DEFAULT_USER"
    local time="$DEFAULT_TIME"
    local remove_mode=false
    local show_mode=false
    local test_mode=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --user)
                user="$2"
                shift 2
                ;;
            --time)
                time="$2"
                shift 2
                ;;
            --remove)
                remove_mode=true
                shift
                ;;
            --show)
                show_mode=true
                shift
                ;;
            --test)
                test_mode=true
                shift
                ;;
            --help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # Header
    echo
    print_status "CRM 2.0 Backup System - Cron Setup"
    print_status "==================================="
    echo
    
    # Validate inputs
    if ! check_user_exists "$user"; then
        exit 1
    fi
    
    if ! validate_time "$time"; then
        exit 1
    fi
    
    # Show current jobs mode
    if [[ "$show_mode" == true ]]; then
        show_current_jobs "$user"
        exit 0
    fi
    
    # Test mode
    if [[ "$test_mode" == true ]]; then
        test_cron_job "$user"
        exit 0
    fi
    
    # Check prerequisites
    check_prerequisites
    
    # Remove mode
    if [[ "$remove_mode" == true ]]; then
        remove_cron_job "$user"
        exit 0
    fi
    
    # Install mode
    install_cron_job "$user" "$time"
    
    # Test the installation
    echo
    test_cron_job "$user"
    
    # Show final summary
    echo
    print_status "Setup completed successfully!"
    print_status "Backup will run daily at $time"
    print_status "Logs will be written to: $LOG_FILE"
    print_status "To remove the cron job, run: $0 --remove --user $user"
    print_status "To view current jobs, run: $0 --show --user $user"
    echo
    print_warning "Make sure to:"
    print_warning "1. Configure Google Drive credentials in your environment"
    print_warning "2. Test the backup manually first: cd $PROJECT_ROOT && python3 scripts/backup.py"
    print_warning "3. Check the log file regularly: tail -f $LOG_FILE"
    echo
}

# Run main function with all arguments
main "$@"