#!/usr/bin/env python3
"""
CRM 2.0 Backup Cleanup Utility

This script manages backup retention by cleaning up old backups from Google Drive.
It can be run independently or as part of the main backup process.
"""

import os
import sys
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Add Django project to path for settings
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

import django
django.setup()

from django.conf import settings
from django.core.mail import send_mail

# Import Google Drive API
try:
    from googleapiclient.discovery import build
    from google.oauth2.service_account import Credentials
    from googleapiclient.errors import HttpError
except ImportError:
    print("Google API client not installed. Run: pip install google-api-python-client google-auth")
    sys.exit(1)


class BackupCleanupManager:
    """Manage backup cleanup operations"""
    
    def __init__(self):
        self.setup_logging()
        self.setup_google_drive()
        self.retention_days = int(os.getenv('BACKUP_RETENTION_DAYS', '30'))
        self.dry_run = False
    
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = Path('/var/log')
        if not log_dir.exists() or not os.access(log_dir, os.W_OK):
            # Fallback to project logs directory
            log_dir = Path(settings.BASE_DIR) / 'logs'
            log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / 'backup_cleanup.log'
        
        # Configure logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_google_drive(self):
        """Initialize Google Drive API service"""
        try:
            # Get service account credentials
            creds_path = os.getenv('GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE')
            if not creds_path or not Path(creds_path).exists():
                raise FileNotFoundError(f"Google Drive service account file not found: {creds_path}")
            
            credentials = Credentials.from_service_account_file(
                creds_path,
                scopes=['https://www.googleapis.com/auth/drive']
            )
            
            self.service = build('drive', 'v3', credentials=credentials)
            self.folder_id = os.getenv('GOOGLE_DRIVE_BACKUP_FOLDER_ID')
            
            self.logger.info("Google Drive service initialized successfully")
            
        except Exception as e:
            error_msg = f"Google Drive setup error: {str(e)}"
            self.logger.error(error_msg)
            raise
    
    def list_backup_files(self, include_all=False):
        """List backup files in Google Drive folder"""
        try:
            query = f"'{self.folder_id}' in parents and trashed=false" if self.folder_id else "trashed=false"
            
            if not include_all:
                query += " and name contains 'backup_'"
            
            results = self.service.files().list(
                q=query,
                orderBy='createdTime desc',
                fields='nextPageToken, files(id, name, createdTime, size, modifiedTime)',
                pageSize=1000  # Get up to 1000 files
            ).execute()
            
            files = results.get('files', [])
            
            # Parse and sort files by creation time
            for file in files:
                try:
                    # Convert ISO timestamp to datetime
                    file['created_datetime'] = datetime.strptime(
                        file['createdTime'][:19], 
                        '%Y-%m-%dT%H:%M:%S'
                    )
                    
                    # Calculate file size in MB
                    file['size_mb'] = float(file.get('size', '0')) / (1024 * 1024)
                    
                except (ValueError, KeyError) as e:
                    self.logger.warning(f"Error parsing file metadata for {file.get('name', 'unknown')}: {e}")
                    file['created_datetime'] = datetime.min
                    file['size_mb'] = 0
            
            # Sort by creation time (newest first)
            files.sort(key=lambda x: x['created_datetime'], reverse=True)
            
            self.logger.info(f"Found {len(files)} backup files in Google Drive")
            return files
            
        except HttpError as e:
            error_msg = f"Google Drive API error: {e}"
            self.logger.error(error_msg)
            return []
        except Exception as e:
            error_msg = f"Error listing Google Drive files: {str(e)}"
            self.logger.error(error_msg)
            return []
    
    def delete_file(self, file_id, filename):
        """Delete file from Google Drive"""
        if self.dry_run:
            self.logger.info(f"[DRY RUN] Would delete: {filename}")
            return True
        
        try:
            self.service.files().delete(fileId=file_id).execute()
            self.logger.info(f"Deleted: {filename}")
            return True
            
        except HttpError as e:
            self.logger.error(f"HTTP error deleting {filename}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Error deleting {filename}: {str(e)}")
            return False
    
    def cleanup_by_retention_days(self):
        """Delete backups older than retention period"""
        try:
            self.logger.info("="*50)
            self.logger.info("Starting backup cleanup by retention days")
            self.logger.info(f"Retention period: {self.retention_days} days")
            self.logger.info(f"Dry run mode: {self.dry_run}")
            self.logger.info("="*50)
            
            files = self.list_backup_files()
            if not files:
                self.logger.info("No backup files found")
                return 0, 0
            
            cutoff_date = datetime.utcnow() - timedelta(days=self.retention_days)
            self.logger.info(f"Cutoff date: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')} UTC")
            
            files_to_delete = []
            total_size_to_delete = 0
            
            # Identify files to delete
            for file in files:
                if file['created_datetime'] < cutoff_date:
                    files_to_delete.append(file)
                    total_size_to_delete += file['size_mb']
            
            if not files_to_delete:
                self.logger.info("No files exceed the retention period")
                return 0, 0
            
            self.logger.info(f"Found {len(files_to_delete)} files to delete ({total_size_to_delete:.2f} MB)")
            
            # Delete files
            deleted_count = 0
            deleted_size = 0
            
            for file in files_to_delete:
                if self.delete_file(file['id'], file['name']):
                    deleted_count += 1
                    deleted_size += file['size_mb']
            
            self.logger.info(f"Cleanup completed: {deleted_count} files deleted ({deleted_size:.2f} MB freed)")
            return deleted_count, deleted_size
            
        except Exception as e:
            error_msg = f"Cleanup by retention error: {str(e)}"
            self.logger.error(error_msg)
            return 0, 0
    
    def cleanup_by_count(self, keep_count=30):
        """Keep only the N most recent backups"""
        try:
            self.logger.info("="*50)
            self.logger.info("Starting backup cleanup by count")
            self.logger.info(f"Keep count: {keep_count}")
            self.logger.info(f"Dry run mode: {self.dry_run}")
            self.logger.info("="*50)
            
            files = self.list_backup_files()
            if not files:
                self.logger.info("No backup files found")
                return 0, 0
            
            if len(files) <= keep_count:
                self.logger.info(f"Current file count ({len(files)}) is within limit ({keep_count})")
                return 0, 0
            
            # Files to delete (everything beyond keep_count, since they're sorted newest first)
            files_to_delete = files[keep_count:]
            total_size_to_delete = sum(f['size_mb'] for f in files_to_delete)
            
            self.logger.info(f"Found {len(files_to_delete)} files to delete ({total_size_to_delete:.2f} MB)")
            
            # Delete files
            deleted_count = 0
            deleted_size = 0
            
            for file in files_to_delete:
                if self.delete_file(file['id'], file['name']):
                    deleted_count += 1
                    deleted_size += file['size_mb']
            
            self.logger.info(f"Cleanup completed: {deleted_count} files deleted ({deleted_size:.2f} MB freed)")
            return deleted_count, deleted_size
            
        except Exception as e:
            error_msg = f"Cleanup by count error: {str(e)}"
            self.logger.error(error_msg)
            return 0, 0
    
    def get_backup_statistics(self):
        """Get statistics about current backups"""
        try:
            files = self.list_backup_files()
            
            if not files:
                return {
                    'total_files': 0,
                    'total_size_mb': 0,
                    'oldest_backup': None,
                    'newest_backup': None,
                    'average_size_mb': 0
                }
            
            total_size = sum(f['size_mb'] for f in files)
            oldest_backup = min(files, key=lambda x: x['created_datetime'])
            newest_backup = max(files, key=lambda x: x['created_datetime'])
            
            return {
                'total_files': len(files),
                'total_size_mb': total_size,
                'oldest_backup': oldest_backup['created_datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                'newest_backup': newest_backup['created_datetime'].strftime('%Y-%m-%d %H:%M:%S'),
                'average_size_mb': total_size / len(files)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting backup statistics: {str(e)}")
            return {}
    
    def send_notification(self, subject, message):
        """Send email notification"""
        try:
            recipient = os.getenv('BACKUP_NOTIFICATION_EMAIL', '<EMAIL>')
            
            send_mail(
                subject=f"CRM 2.0 Backup Cleanup: {subject}",
                message=message,
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[recipient],
                fail_silently=False
            )
            
        except Exception as e:
            self.logger.error(f"Failed to send email notification: {str(e)}")
    
    def run_cleanup(self, method='retention', **kwargs):
        """Run cleanup with specified method"""
        start_time = datetime.now()
        
        try:
            # Get initial statistics
            initial_stats = self.get_backup_statistics()
            
            if method == 'retention':
                deleted_count, deleted_size = self.cleanup_by_retention_days()
            elif method == 'count':
                keep_count = kwargs.get('keep_count', 30)
                deleted_count, deleted_size = self.cleanup_by_count(keep_count)
            else:
                raise ValueError(f"Unknown cleanup method: {method}")
            
            # Get final statistics
            final_stats = self.get_backup_statistics()
            
            # Calculate duration
            duration = datetime.now() - start_time
            
            # Create summary message
            summary_message = (
                f"Cleanup method: {method}\n"
                f"Duration: {duration}\n"
                f"Files deleted: {deleted_count}\n"
                f"Space freed: {deleted_size:.2f} MB\n\n"
                f"Before cleanup:\n"
                f"  Total files: {initial_stats.get('total_files', 0)}\n"
                f"  Total size: {initial_stats.get('total_size_mb', 0):.2f} MB\n\n"
                f"After cleanup:\n"
                f"  Total files: {final_stats.get('total_files', 0)}\n"
                f"  Total size: {final_stats.get('total_size_mb', 0):.2f} MB\n"
                f"  Oldest backup: {final_stats.get('oldest_backup', 'None')}\n"
                f"  Newest backup: {final_stats.get('newest_backup', 'None')}"
            )
            
            self.logger.info(summary_message)
            
            if not self.dry_run:
                self.send_notification("Cleanup Completed", summary_message)
            
            return deleted_count > 0
            
        except Exception as e:
            error_message = (
                f"Cleanup failed with error: {str(e)}\n"
                f"Duration before failure: {datetime.now() - start_time}\n"
                f"Method: {method}\n"
                f"Check logs for detailed error information."
            )
            
            self.logger.error(error_message)
            
            if not self.dry_run:
                self.send_notification("Cleanup Failed", error_message)
            
            return False


def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='CRM 2.0 Backup Cleanup Utility')
    parser.add_argument(
        '--method', 
        choices=['retention', 'count'], 
        default='retention',
        help='Cleanup method (default: retention)'
    )
    parser.add_argument(
        '--retention-days', 
        type=int, 
        default=None,
        help='Number of days to retain backups (default: from environment)'
    )
    parser.add_argument(
        '--keep-count', 
        type=int, 
        default=30,
        help='Number of backups to keep (for count method, default: 30)'
    )
    parser.add_argument(
        '--dry-run', 
        action='store_true',
        help='Show what would be deleted without actually deleting'
    )
    parser.add_argument(
        '--stats-only', 
        action='store_true',
        help='Only show backup statistics, do not perform cleanup'
    )
    
    args = parser.parse_args()
    
    try:
        cleanup_manager = BackupCleanupManager()
        
        # Override retention days if specified
        if args.retention_days is not None:
            cleanup_manager.retention_days = args.retention_days
        
        # Set dry run mode
        cleanup_manager.dry_run = args.dry_run
        
        if args.stats_only:
            # Only show statistics
            print("\nBackup Statistics:")
            print("=" * 30)
            stats = cleanup_manager.get_backup_statistics()
            for key, value in stats.items():
                print(f"{key.replace('_', ' ').title()}: {value}")
            return
        
        # Run cleanup
        kwargs = {}
        if args.method == 'count':
            kwargs['keep_count'] = args.keep_count
        
        success = cleanup_manager.run_cleanup(args.method, **kwargs)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\nCleanup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()