"""
Django Management Command for CRM 2.0 Backup System

Usage:
    python manage.py backup [OPTIONS]

Examples:
    python manage.py backup                          # Run full backup
    python manage.py backup --dry-run               # Show what would be backed up
    python manage.py backup --cleanup-only          # Only run cleanup
    python manage.py backup --skip-minio           # Skip MinIO backup
    python manage.py backup --retention-days 14    # Override retention period
"""

import os
import sys
import subprocess
from pathlib import Path
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Run CRM 2.0 automated backup system'

    def add_arguments(self, parser):
        """Add command line arguments"""
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be backed up without actually performing backup'
        )
        
        parser.add_argument(
            '--cleanup-only',
            action='store_true',
            help='Only perform cleanup of old backups, skip backup creation'
        )
        
        parser.add_argument(
            '--skip-database',
            action='store_true',
            help='Skip database backup'
        )
        
        parser.add_argument(
            '--skip-code',
            action='store_true',
            help='Skip source code backup'
        )
        
        parser.add_argument(
            '--skip-minio',
            action='store_true',
            help='Skip MinIO files backup'
        )
        
        parser.add_argument(
            '--skip-upload',
            action='store_true',
            help='Skip upload to Google Drive (keep local backup)'
        )
        
        parser.add_argument(
            '--retention-days',
            type=int,
            default=None,
            help='Number of days to retain backups (overrides environment setting)'
        )
        
        parser.add_argument(
            '--output-dir',
            type=str,
            default=None,
            help='Local output directory for backups (when --skip-upload is used)'
        )
        
        parser.add_argument(
            '--test-config',
            action='store_true',
            help='Test backup configuration without running backup'
        )
        
        parser.add_argument(
            '--stats',
            action='store_true',
            help='Show backup statistics from Google Drive'
        )

    def handle(self, *args, **options):
        """Handle the management command"""
        try:
            # Get project root and scripts directory
            project_root = Path(settings.BASE_DIR)
            scripts_dir = project_root / 'scripts'
            backup_script = scripts_dir / 'backup.py'
            cleanup_script = scripts_dir / 'cleanup_old_backups.py'
            
            # Check if scripts exist
            if not backup_script.exists():
                raise CommandError(f"Backup script not found: {backup_script}")
            
            if not cleanup_script.exists():
                raise CommandError(f"Cleanup script not found: {cleanup_script}")
            
            # Handle stats option
            if options['stats']:
                self.show_backup_stats(cleanup_script)
                return
            
            # Handle test configuration option
            if options['test_config']:
                self.test_configuration()
                return
            
            # Handle cleanup-only option
            if options['cleanup_only']:
                self.run_cleanup(cleanup_script, options)
                return
            
            # Run full backup
            self.run_backup(backup_script, options)
            
        except Exception as e:
            raise CommandError(f"Backup command failed: {str(e)}")

    def test_configuration(self):
        """Test backup configuration"""
        self.stdout.write(self.style.SUCCESS("Testing backup configuration..."))
        
        # Check environment variables
        required_vars = [
            'GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE',
            'GOOGLE_DRIVE_BACKUP_FOLDER_ID',
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            self.stdout.write(
                self.style.ERROR(
                    f"Missing required environment variables: {', '.join(missing_vars)}"
                )
            )
            return
        
        # Check database configuration
        db_config = settings.DATABASES.get('default', {})
        if not all([
            db_config.get('HOST'),
            db_config.get('NAME'),
            db_config.get('USER'),
            db_config.get('PASSWORD')
        ]):
            self.stdout.write(
                self.style.ERROR("Database configuration incomplete")
            )
            return
        
        # Check MinIO configuration
        if not all([
            getattr(settings, 'MINIO_ACCESS_KEY', None),
            getattr(settings, 'MINIO_SECRET_KEY', None),
            getattr(settings, 'MINIO_BUCKET_NAME', None),
            getattr(settings, 'MINIO_ENDPOINT', None)
        ]):
            self.stdout.write(
                self.style.ERROR("MinIO configuration incomplete")
            )
            return
        
        # Check Google Drive credentials file
        creds_file = os.getenv('GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE')
        if not Path(creds_file).exists():
            self.stdout.write(
                self.style.ERROR(f"Google Drive credentials file not found: {creds_file}")
            )
            return
        
        self.stdout.write(self.style.SUCCESS("Configuration test passed!"))
        
        # Show configuration summary
        self.stdout.write("\nConfiguration Summary:")
        self.stdout.write(f"  Database: {db_config['NAME']}@{db_config['HOST']}")
        self.stdout.write(f"  MinIO Bucket: {getattr(settings, 'MINIO_BUCKET_NAME')}")
        self.stdout.write(f"  Retention Days: {os.getenv('BACKUP_RETENTION_DAYS', '30')}")
        self.stdout.write(f"  Notification Email: {os.getenv('BACKUP_NOTIFICATION_EMAIL', '<EMAIL>')}")

    def show_backup_stats(self, cleanup_script):
        """Show backup statistics"""
        self.stdout.write(self.style.SUCCESS("Fetching backup statistics..."))
        
        try:
            # Run cleanup script with --stats-only flag
            result = subprocess.run([
                sys.executable,
                str(cleanup_script),
                '--stats-only'
            ], capture_output=True, text=True, check=True)
            
            self.stdout.write(result.stdout)
            
        except subprocess.CalledProcessError as e:
            self.stdout.write(self.style.ERROR(f"Failed to get stats: {e}"))
            if e.stderr:
                self.stdout.write(self.style.ERROR(e.stderr))

    def run_cleanup(self, cleanup_script, options):
        """Run backup cleanup"""
        self.stdout.write(self.style.SUCCESS("Starting backup cleanup..."))
        
        cmd = [sys.executable, str(cleanup_script)]
        
        # Add retention days if specified
        if options['retention_days']:
            cmd.extend(['--retention-days', str(options['retention_days'])])
        
        # Add dry-run if specified
        if options['dry_run']:
            cmd.append('--dry-run')
        
        try:
            result = subprocess.run(cmd, check=True, capture_output=False)
            self.stdout.write(self.style.SUCCESS("Cleanup completed successfully"))
            
        except subprocess.CalledProcessError as e:
            raise CommandError(f"Cleanup failed with exit code {e.returncode}")

    def run_backup(self, backup_script, options):
        """Run full backup process"""
        if options['dry_run']:
            self.stdout.write(self.style.WARNING("DRY RUN MODE - No actual backup will be performed"))
        
        self.stdout.write(self.style.SUCCESS("Starting backup process..."))
        
        # Prepare environment variables for the backup script
        env = os.environ.copy()
        
        # Set retention days if specified
        if options['retention_days']:
            env['BACKUP_RETENTION_DAYS'] = str(options['retention_days'])
        
        # Set output directory if specified
        if options['output_dir']:
            env['BACKUP_OUTPUT_DIR'] = options['output_dir']
        
        # Set component skip flags
        if options['skip_database']:
            env['BACKUP_SKIP_DATABASE'] = 'true'
        
        if options['skip_code']:
            env['BACKUP_SKIP_CODE'] = 'true'
        
        if options['skip_minio']:
            env['BACKUP_SKIP_MINIO'] = 'true'
        
        if options['skip_upload']:
            env['BACKUP_SKIP_UPLOAD'] = 'true'
        
        if options['dry_run']:
            env['BACKUP_DRY_RUN'] = 'true'
        
        try:
            # Run the backup script
            result = subprocess.run([
                sys.executable,
                str(backup_script)
            ], env=env, check=True, capture_output=False)
            
            self.stdout.write(self.style.SUCCESS("Backup completed successfully"))
            
        except subprocess.CalledProcessError as e:
            raise CommandError(f"Backup failed with exit code {e.returncode}")
        except KeyboardInterrupt:
            self.stdout.write(self.style.ERROR("\nBackup interrupted by user"))
            raise CommandError("Backup interrupted")

    def handle_dry_run(self, options):
        """Handle dry run mode"""
        if not options['dry_run']:
            return
        
        self.stdout.write(self.style.WARNING("="*50))
        self.stdout.write(self.style.WARNING("DRY RUN MODE"))
        self.stdout.write(self.style.WARNING("="*50))
        self.stdout.write("The following operations would be performed:")
        
        if not options['skip_database']:
            self.stdout.write("✓ Database backup (MySQL dump)")
        
        if not options['skip_code']:
            self.stdout.write("✓ Source code backup (ZIP archive)")
        
        if not options['skip_minio']:
            self.stdout.write("✓ MinIO files backup (TAR.GZ archive)")
        
        if not options['skip_upload']:
            self.stdout.write("✓ Upload to Google Drive")
        
        if not options['cleanup_only']:
            retention_days = options['retention_days'] or os.getenv('BACKUP_RETENTION_DAYS', '30')
            self.stdout.write(f"✓ Cleanup backups older than {retention_days} days")
        
        self.stdout.write(self.style.WARNING("="*50))