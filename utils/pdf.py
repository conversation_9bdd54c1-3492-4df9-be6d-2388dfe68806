from django.template.loader import render_to_string
from weasyprint import HTML
from django.core.files.base import ContentFile
from django.core.files.storage import default_storage

def generate_pdf(template_name, context, output_filename="document.pdf"):
    """
    Renders a Django template to PDF and uploads to MinIO (S3-compatible) storage.
    Returns the URL or path to the file.
    """
    html_string = render_to_string(template_name, context)

    # Generate PDF in memory
    pdf_file = HTML(string=html_string).write_pdf()

    # Save PDF to default storage (e.g., MinIO)
    file_path = f"Diaspora_receipts_pdfs/{output_filename}"
    default_storage.save(file_path, ContentFile(pdf_file))

    return default_storage.url(file_path)  # or just return file_path

