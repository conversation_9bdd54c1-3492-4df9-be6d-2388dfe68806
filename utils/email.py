import requests
from django.core.mail import EmailMultiAlternatives
from django.template.loader import render_to_string
from django.conf import settings
import logging
import mimetypes
from urllib.parse import urlparse
import re

logger = logging.getLogger(__name__)

def send_email(
    subject: str,
    recipients,
    body_text: str = None,
    body_html: str = None,
    template_name: str = None,
    context: dict = None,
    from_email: str = settings.DEFAULT_FROM_EMAIL,
    attachments: list = None,
    cc=None,
    bcc=None,
    from_name=None
    
    
):
    """
    Sends an email with optional HTML template, CC, BCC, and attachments.
    Handles string, list, or comma-separated strings for recipients and cc/bcc.
    """
    try:
        def parse_email_list(val):
            if not val:
                return []
            if isinstance(val, str):
                return [email.strip() for email in val.split(",") if email.strip()]
            if isinstance(val, list):
                return val
            return []

        cc = parse_email_list(cc)
        bcc = parse_email_list(bcc)
        recipients = parse_email_list(recipients)
        context = context or {}
        attachments = attachments or []

        if not recipients:
            raise ValueError("Recipient list is empty or invalid.")

        if template_name and context:
            body_html = render_to_string(template_name, context)

        if isinstance(body_text, list):
            body_text = "\n".join(body_text)

        from_email_formatted = f"{from_name or 'Optiven Engage360 CRM'} <{from_email}>"
        email = EmailMultiAlternatives(
                subject=subject,
                body=body_text or body_html or "No content",
                from_email=from_email_formatted,
                to=recipients,
                cc=cc,
                bcc=bcc,
            )

        if body_html:
            email.attach_alternative(body_html, "text/html")

        for file in attachments:
            try:
                filename = ""
                mime_type = "application/octet-stream"
                file_data = None

                if file.startswith("http"):
                    response = requests.get(file)
                    if response.status_code == 200:
                        filename = urlparse(file).path.split("/")[-1]
                        file_data = response.content
                        mime_type, _ = mimetypes.guess_type(filename)
                    else:
                        logger.warning(f"Attachment failed to download: {file}")
                        continue
                else:
                    with open(file, "rb") as f:
                        filename = file.split("/")[-1]
                        file_data = f.read()
                        mime_type, _ = mimetypes.guess_type(file)

                if file_data:
                    email.attach(filename, file_data, mime_type or "application/octet-stream")

            except Exception as e:
                logger.error(f"Failed to attach file: {file}. Reason: {e}")

        logger.info(f"[send_email] Sending: {subject} to: {recipients}, cc: {cc}, bcc: {bcc}")
        email.send(fail_silently=False)

    except Exception as e:
        logger.error(f"[send_email] Failed to send email: {e}", exc_info=True)
        raise

    finally:
        try:
            email.connection.close()
        except Exception:
            pass
def normalize_emails(emails):
    """
    Takes a single email or a list of emails and returns a cleaned list:
    - Removes duplicates
    - Strips whitespace
    - Validates basic email format
    """
    if not emails:
        return []

    if isinstance(emails, str):
        emails = [emails]

    cleaned = set()
    email_pattern = re.compile(r"[^@]+@[^@]+\.[^@]+")

    for email in emails:
        if not isinstance(email, str):
            continue
        email = email.strip().lower()
        if email and email_pattern.match(email):
            cleaned.add(email)

    return list(cleaned)