"""
Export mixins for PDF and Excel report generation.
Provides reusable functionality for ViewSets to export filtered data.
"""
import io
import xlsxwriter
from django.http import HttpResponse
from django.template.loader import render_to_string
from weasyprint import HTML
from rest_framework.decorators import action
from rest_framework.response import Response
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django.utils import timezone
from datetime import datetime


class ReportExportMixin:
    """
    Mixin to add PDF and Excel export functionality to ViewSets.
    
    This mixin provides two actions:
    - export_pdf: Exports filtered data as PDF
    - export_excel: Exports filtered data as Excel
    
    Requirements:
    - ViewSet must have get_queryset() method
    - ViewSet must have serializer_class attribute
    - Model must have meaningful string representations
    """
    
    # Override these in your ViewSet if needed
    export_template_name = 'exports/default_report.html'  # PDF template
    export_filename_prefix = 'report'  # Base filename for exports
    export_title = 'Data Report'  # Title for the report
    
    def get_export_queryset(self, request):
        """
        Get the queryset for export. Override this method to apply custom filtering.
        By default, it uses the same filtering logic as the list view.
        """
        queryset = self.get_queryset()
        
        # Apply the same filtering as list view if available
        if hasattr(self, 'filter_backends'):
            for backend in list(self.filter_backends):
                queryset = backend().filter_queryset(request, queryset, self)
        
        return queryset
    
    def get_export_data(self, queryset):
        """
        Transform queryset into export-ready data.
        Override this method to customize the data structure for exports.
        """
        serializer = self.get_serializer(queryset, many=True)
        return serializer.data
    
    def get_export_context(self, request, data, export_type='pdf'):
        """
        Get context for PDF template or Excel metadata.
        Override this method to add custom context.
        """
        return {
            'title': self.export_title,
            'data': data,
            'timestamp': timezone.now(),
            'export_type': export_type,
            'user': request.user if request.user.is_authenticated else None,
            'total_records': len(data),
            'filters_applied': self.get_applied_filters(request),
        }
    
    def get_applied_filters(self, request):
        """
        Extract applied filters from request for documentation purposes.
        """
        filters = {}
        
        # Common filter parameters
        filter_params = [
            'search', 'ordering', 'page', 'page_size',
            'date_from', 'date_to', 'created_at_from', 'created_at_to',
            'status', 'category', 'type'
        ]
        
        for param in filter_params:
            if request.query_params.get(param):
                filters[param] = request.query_params.get(param)
        
        return filters
    
    def generate_filename(self, export_type, timestamp=None):
        """
        Generate filename for export.
        """
        if timestamp is None:
            timestamp = timezone.now()
        
        timestamp_str = timestamp.strftime('%Y%m%d_%H%M%S')
        return f"{self.export_filename_prefix}_{timestamp_str}.{export_type}"
    
    @swagger_auto_schema(
        tags=['Export'],
        operation_description="Export filtered data as PDF report",
        manual_parameters=[
            openapi.Parameter(
                name='template',
                in_=openapi.IN_QUERY,
                description='Optional: Custom template name for PDF generation',
                type=openapi.TYPE_STRING,
                required=False,
            ),
        ],
        responses={
            200: openapi.Response(
                description="PDF file download",
                content={
                    'application/pdf': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_BINARY)
                }
            ),
            400: openapi.Response(description="Bad request - Invalid parameters"),
            403: openapi.Response(description="Permission denied"),
        }
    )
    @action(detail=False, methods=['get'])
    def export_pdf(self, request):
        """
        Export filtered data as PDF.
        
        Query Parameters:
        - template: Optional custom template name
        - All other parameters: Same as list view for filtering
        
        Returns:
        - PDF file download with filtered data
        """
        try:
            # Get filtered data
            queryset = self.get_export_queryset(request)
            data = self.get_export_data(queryset)
            
            # Get template name
            template_name = request.query_params.get('template', self.export_template_name)
            print("here1")
            # Prepare context
            context = self.get_export_context(request, data, 'pdf')
            print("here2")
            # Render HTML
            html_string = render_to_string(template_name, context)
            print("here3")
            # Generate PDF
            pdf_file = HTML(string=html_string, base_url=request.build_absolute_uri()).write_pdf()
            
            # Create response
            filename = self.generate_filename('pdf')
            response = HttpResponse(pdf_file, content_type='application/pdf')
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            response['Content-Length'] = len(pdf_file)
            
            return response
            
        except Exception as e:
            return Response({
                'error': 'PDF generation failed',
                'detail': str(e)
            }, status=400)
    
    @swagger_auto_schema(
        tags=['Export'],
        operation_description="Export filtered data as Excel spreadsheet",
        manual_parameters=[
            openapi.Parameter(
                name='sheet_name',
                in_=openapi.IN_QUERY,
                description='Optional: Custom sheet name for Excel file',
                type=openapi.TYPE_STRING,
                required=False,
            ),
            openapi.Parameter(
                name='include_metadata',
                in_=openapi.IN_QUERY,
                description='Include metadata sheet with export information (true/false)',
                type=openapi.TYPE_BOOLEAN,
                required=False,
                default=True,
            ),
        ],
        responses={
            200: openapi.Response(
                description="Excel file download",
                content={
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 
                    openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_BINARY)
                }
            ),
            400: openapi.Response(description="Bad request - Invalid parameters"),
            403: openapi.Response(description="Permission denied"),
        }
    )
    @action(detail=False, methods=['get'])
    def export_excel(self, request):
        """
        Export filtered data as Excel spreadsheet.
        
        Query Parameters:
        - sheet_name: Optional custom sheet name
        - include_metadata: Include metadata sheet (default: true)
        - All other parameters: Same as list view for filtering
        
        Returns:
        - Excel file download with filtered data
        """
        try:
            # Get filtered data
            queryset = self.get_export_queryset(request)
            data = self.get_export_data(queryset)
            
            # Create Excel file in memory
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            
            # Get parameters
            sheet_name = request.query_params.get('sheet_name', 'Data')
            include_metadata = request.query_params.get('include_metadata', 'true').lower() == 'true'
            
            # Create main data sheet
            self._create_excel_data_sheet(workbook, data, sheet_name)
            
            # Create metadata sheet if requested
            if include_metadata:
                context = self.get_export_context(request, data, 'excel')
                self._create_excel_metadata_sheet(workbook, context)
            
            # Close workbook and get data
            workbook.close()
            output.seek(0)
            
            # Create response
            filename = self.generate_filename('xlsx')
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="{filename}"'
            
            return response
            
        except Exception as e:
            return Response({
                'error': 'Excel generation failed',
                'detail': str(e)
            }, status=400)
    
    def _create_excel_data_sheet(self, workbook, data, sheet_name):
        """
        Create the main data sheet in Excel workbook.
        """
        worksheet = workbook.add_worksheet(sheet_name)
        
        # Define formats
        header_format = workbook.add_format({
            'bold': True,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1,
            'align': 'center',
            'valign': 'vcenter'
        })
        
        cell_format = workbook.add_format({
            'border': 1,
            'align': 'left',
            'valign': 'top',
            'text_wrap': True
        })
        
        if not data:
            worksheet.write(0, 0, 'No data available', cell_format)
            return
        
        # Get headers from first row
        headers = list(data[0].keys()) if data else []
        
        # Write headers
        for col, header in enumerate(headers):
            worksheet.write(0, col, header.replace('_', ' ').title(), header_format)
        
        # Write data
        for row, item in enumerate(data, start=1):
            for col, header in enumerate(headers):
                value = item.get(header, '')
                
                # Handle nested objects (convert to string)
                if isinstance(value, dict):
                    value = str(value.get('name', value.get('id', str(value))))
                elif isinstance(value, list):
                    value = ', '.join(str(v) for v in value)
                elif value is None:
                    value = ''
                
                worksheet.write(row, col, str(value), cell_format)
        
        # Auto-adjust column widths
        for col, header in enumerate(headers):
            max_length = max(
                len(str(header)),
                max((len(str(item.get(header, ''))) for item in data), default=0)
            )
            worksheet.set_column(col, col, min(max_length + 2, 50))
    
    def _create_excel_metadata_sheet(self, workbook, context):
        """
        Create metadata sheet with export information.
        """
        worksheet = workbook.add_worksheet('Export Info')
        
        # Define formats
        label_format = workbook.add_format({
            'bold': True,
            'bg_color': '#E7E6E6',
            'border': 1
        })
        
        value_format = workbook.add_format({
            'border': 1,
            'text_wrap': True
        })
        
        # Metadata rows
        metadata = [
            ('Report Title', context.get('title', 'Data Export')),
            ('Export Date', context.get('timestamp', timezone.now()).strftime('%Y-%m-%d %H:%M:%S')),
            ('Export Type', 'Excel'),
            ('Total Records', context.get('total_records', 0)),
            ('Exported By', str(context.get('user', 'Anonymous'))),
            ('Applied Filters', str(context.get('filters_applied', 'None'))),
        ]
        
        # Write metadata
        for row, (label, value) in enumerate(metadata):
            worksheet.write(row, 0, label, label_format)
            worksheet.write(row, 1, str(value), value_format)
        
        # Auto-adjust column widths
        worksheet.set_column(0, 0, 20)
        worksheet.set_column(1, 1, 50)


class AdvancedReportExportMixin(ReportExportMixin):
    """
    Extended export mixin with additional features for complex reports.
    """
    
    def get_export_statistics(self, queryset, request):
        """
        Generate statistics for the export data.
        Override this method to add custom statistics.
        """
        return {
            'total_count': queryset.count(),
            'export_date': timezone.now(),
            'applied_filters': self.get_applied_filters(request),
        }
    
    @swagger_auto_schema(
        tags=['Export'],
        operation_description="Export data with advanced statistics and multiple sheets",
        responses={
            200: openapi.Response(description="Advanced Excel report download"),
            400: openapi.Response(description="Bad request"),
        }
    )
    @action(detail=False, methods=['get'])
    def export_advanced_excel(self, request):
        """
        Export data with statistics and multiple sheets.
        """
        try:
            # Get filtered data
            queryset = self.get_export_queryset(request)
            data = self.get_export_data(queryset)
            statistics = self.get_export_statistics(queryset, request)
            
            # Create Excel file
            output = io.BytesIO()
            workbook = xlsxwriter.Workbook(output, {'in_memory': True})
            
            # Create main data sheet
            self._create_excel_data_sheet(workbook, data, 'Data')
            
            # Create statistics sheet
            self._create_excel_statistics_sheet(workbook, statistics)
            
            # Create metadata sheet
            context = self.get_export_context(request, data, 'excel')
            self._create_excel_metadata_sheet(workbook, context)
            
            workbook.close()
            output.seek(0)
            
            # Create response
            filename = self.generate_filename('xlsx')
            response = HttpResponse(
                output.getvalue(),
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = f'attachment; filename="advanced_{filename}"'
            
            return response
            
        except Exception as e:
            return Response({
                'error': 'Advanced Excel generation failed',
                'detail': str(e)
            }, status=400)
    
    def _create_excel_statistics_sheet(self, workbook, statistics):
        """
        Create statistics sheet in Excel workbook.
        """
        worksheet = workbook.add_worksheet('Statistics')
        
        # Define formats
        title_format = workbook.add_format({
            'bold': True,
            'font_size': 16,
            'bg_color': '#4472C4',
            'font_color': 'white',
            'border': 1,
            'align': 'center'
        })
        
        label_format = workbook.add_format({
            'bold': True,
            'bg_color': '#E7E6E6',
            'border': 1
        })
        
        value_format = workbook.add_format({
            'border': 1,
            'num_format': '#,##0'
        })
        
        # Title
        worksheet.merge_range(0, 0, 0, 1, 'Export Statistics', title_format)
        
        # Statistics data
        row = 2
        for key, value in statistics.items():
            worksheet.write(row, 0, key.replace('_', ' ').title(), label_format)
            
            if isinstance(value, (int, float)):
                worksheet.write(row, 1, value, value_format)
            else:
                worksheet.write(row, 1, str(value), value_format)
            
            row += 1
        
        # Auto-adjust column widths
        worksheet.set_column(0, 0, 25)
        worksheet.set_column(1, 1, 30)