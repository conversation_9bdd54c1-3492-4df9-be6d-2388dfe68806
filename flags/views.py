from ast import If
from django.shortcuts import render
from rest_framework import viewsets, permissions
from django_filters.rest_framework import DjangoFilterBackend

from users.models import User
from .models import Flag
from .serializer import FlagSerializer
from rest_framework.permissions import IsAuthenticated, AllowAny
from drf_yasg.utils import swagger_auto_schema
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated,AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from drf_yasg.utils import swagger_auto_schema
from django.db import transaction
import os
from config.perm_filters import leads_permission_filters
from rest_framework.exceptions import PermissionDenied
from django.conf import settings


def swagger_tags(tags):
    """
    Class decorator to apply tags to all methods of a ViewSet
    """
    def decorator(cls):
        # List of methods to apply tags to
        methods = ['list', 'create', 'retrieve', 'partial_update', 'destroy']
        
        for method_name in methods:
            if hasattr(cls, method_name):
                method = getattr(cls, method_name)
                # Check if method already has a swagger_auto_schema decorator
                if hasattr(method, '_swagger_auto_schema'):
                    # Update existing schema with tags
                    existing_schema = getattr(method, '_swagger_auto_schema')
                    existing_schema['tags'] = tags
                else:
                    # Apply new decorator with only tags
                    decorated_method = swagger_auto_schema(tags=tags)(method)
                    setattr(cls, method_name, decorated_method)
        
        return cls
    return decorator

# Create your views here.
class FlagViewSet(viewsets.ModelViewSet):
    queryset = Flag.objects.all()
    http_method_names = ['get', 'post', 'put', 'patch', 'delete']
    serializer_class = FlagSerializer
    if settings.DEBUG:
        permission_classes = [AllowAny]
    else:
        permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = [ 'client_type', 'created_by','customer', 'prospect', 'sale', 'follow_up_required', 'set_reminder','customer__customer_name', 'prospect__name', 'sale__plot__plotId']
    search_fields = ['subject', 'description', 'flag_id' , 'flag_reason', 'client_type', 'created_by__username', 'customer__customer_name', 'prospect__name', 'sale__plot__plotId']
    ordering_fields = ['created_at', 'follow_up_date']
    ordering = ['-created_at']

    @swagger_auto_schema(tags=['Flag'], operation_description="List, filter, search, order Flags")
    def list(self, request, *args, **kwargs):
        return super().list(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Flag'], operation_description="Retrieve Flag")
    def retrieve(self, request, *args, **kwargs):
        return super().retrieve(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Flag'], operation_description="Create Flag")
    def create(self, request, *args, **kwargs):
        data = request.data.copy()
        # Set the created_by field to the current user
        if request.user.is_authenticated and hasattr(request.user, 'employee_no'):
            created_by = request.user
        else:
            try:
                created_by = User.objects.get(employee_no='OL/HR/506')
            except User.DoesNotExist:
                return Response({"detail": "User not found."}, status=400)
        
        data['created_by'] = created_by.employee_no  # Use employee_no to match the ForeignKey field
        request._full_data = data  # update request data for serialize
        print(f"Request data: {data}")
        if client_type := data.get('client_type'):
            if client_type == 'Prospect':
                data.pop('customer', None) 
                data.pop('sale', None)  
                if not data.get('prospect'):
                    return Response({"detail": "Prospect must be provided for Prospect client type."}, status=400)
            elif client_type == 'Customer':
                data.pop('prospect', None) 
                data.pop('sale', None)  
                if not data.get('customer'):
                    return Response({"detail": "Customer must be provided for Customer client type."}, status=400)
            elif client_type == 'Sale':
                data.pop('prospect', None)  
                data.pop('customer', None)  
                if not data.get('sale'):
                    return Response({"detail": "Sale must be provided for Sale client type."}, status=400)
        if data.get("follow_up_required") and not data.get("follow_up_date"):
            return Response({"detail": "Follow-up date is required when follow-up is required."}, status=400) 
        # You can add notification logic here if needed

        if data.get("set_reminder") and not data.get("reminder_time"):
            return Response({"detail": "Reminder time is required when set reminder is true."}, status=400)
        # You can add notification logic here if needed

        # If all validations pass, proceed to create the object
        request._full_data = data 
        return super().create(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Flag'], operation_description="Update Flag")
    def update(self, request, *args, **kwargs):
        data = request.data.copy()
        if client_type := data.get('client_type'):
            if client_type == 'Prospect':
                data.pop('customer', None) 
                data.pop('sale', None)  
                if not data.get('prospect'):
                    return Response({"detail": "Prospect must be provided for Prospect client type."}, status=400)
            elif client_type == 'Customer':
                data.pop('prospect', None) 
                data.pop('sale', None)  
                if not data.get('customer'):
                    return Response({"detail": "Customer must be provided for Customer client type."}, status=400)
            elif client_type == 'Sale':
                data.pop('prospect', None)  
                data.pop('customer', None)  
                if not data.get('sale'):
                    return Response({"detail": "Sale must be provided for Sale client type."}, status=400)
        if data.get("follow_up_required") and not data.get("follow_up_date"):
            return Response({"detail": "Follow-up date is required when follow-up is required."}, status=400) 
        else: 
            #create notifications from this  TODO
            pass
          
        if data.get("set_reminder") and not data.get("reminder_time"):
            return Response({"detail": "Reminder time is required when set reminder is true."}, status=400)
        else:
            #create notifications from this TODO
            pass
        request._full_data = data 
        return super().update(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Flag'], operation_description="Partial Update Flag")
    def partial_update(self, request, *args, **kwargs):
        data = request.data.copy()
        if 'client_type' in data:
            if client_type := data.get('client_type'):
                if client_type == 'Prospect':
                    data.pop('customer', None) 
                    data.pop('sale', None)  
                    if not data.get('prospect'):
                        return Response({"detail": "Prospect must be provided for Prospect client type."}, status=400)
                elif client_type == 'Customer':
                    data.pop('prospect', None) 
                    data.pop('sale', None)  
                    if not data.get('customer'):
                        return Response({"detail": "Customer must be provided for Customer client type."}, status=400)
                elif client_type == 'Sale':
                    data.pop('prospect', None)  
                    data.pop('customer', None)  
                    if not data.get('sale'):
                        return Response({"detail": "Sale must be provided for Sale client type."}, status=400)
        if 'follow_up_required' in data and data.get("follow_up_required"):
            if not data.get("follow_up_date"):
                return Response({"detail": "Follow-up date is required when follow-up is required."}, status=400)
            else:
                #create notifications from this  TODO
                pass

        if 'set_reminder' in data and data.get("set_reminder"):
            if not data.get("reminder_time"):
                return Response({"detail": "Reminder time is required when set reminder is true."}, status=400)
            else:
                #create notifications from this  TODO
                pass
        request._full_data = data 
        return super().update(request, *args, **kwargs)
    @swagger_auto_schema(tags=['Flag'], operation_description="Delete Flag")
    def destroy(self, request, *args, **kwargs):
        return super().destroy(request, *args, **kwargs)
    
    
