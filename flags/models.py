from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer
from leads.models import Prospects
from sales.models import LeadFile
from flags.utils import generate_flag_id



class Flag(models.Model):
    FLAG_REASONS = [
        ('Follow-up Required', 'Follow-up Required'),
        ('Payment Issue', 'Payment Issue'),
        ('Customer Complaint', 'Customer Complaint'),
        ('Documentation Missing', 'Documentation Missing'),
        ('Quality Issue', 'Quality Issue'),
        ('Process Violation', 'Process Violation'),
        ('Other', 'Other'),
    ]
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]

   
    flag_id = models.CharField(max_length=50, unique=True, primary_key=True)
    flag_reason = models.CharField(max_length=100, choices=FLAG_REASONS, null=True, blank=True)
    description = models.TextField(help_text="Detailed description of the flag")
    is_resolved = models.<PERSON>olean<PERSON>ield(default=False)
    resolution_date = models.DateTimeField(null=True, blank=True)
    resolution_notes = models.TextField(blank=True, null=True)
    
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(blank=True, null=True)
    set_reminder = models.BooleanField(default=False)
    reminder_time = models.DateTimeField(blank=True, null=True)
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='flags_created', to_field='employee_no')
    customer = models.ForeignKey( Customer, on_delete=models.CASCADE, related_name='flags', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_flags',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_flags', to_field='lead_file_no', null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure flag_id is unique"""

        if not self.flag_id:
            self.flag_id = generate_flag_id()
            while Flag.objects.filter(flag_id=self.flag_id).exists():
                self.flag_id = generate_flag_id()
        print(f"Saving Flag: {self.description} with ID: {self.flag_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.flag_id} - {self.description}"

    class Meta:
        ordering = ['-created_at']

