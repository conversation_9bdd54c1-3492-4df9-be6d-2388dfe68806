# Generated by Django 5.1.7 on 2025-07-25 11:50

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Flag',
            fields=[
                ('flag_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('flag_reason', models.CharField(blank=True, choices=[('Follow-up Required', 'Follow-up Required'), ('Payment Issue', 'Payment Issue'), ('Customer Complaint', 'Customer Complaint'), ('Documentation Missing', 'Documentation Missing'), ('Quality Issue', 'Quality Issue'), ('Process Violation', 'Process Violation'), ('Other', 'Other')], max_length=100, null=True)),
                ('description', models.TextField(help_text='Detailed description of the flag')),
                ('is_resolved', models.BooleanField(default=False)),
                ('resolution_date', models.DateTimeField(blank=True, null=True)),
                ('resolution_notes', models.TextField(blank=True, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('set_reminder', models.BooleanField(default=False)),
                ('reminder_time', models.DateTimeField(blank=True, null=True)),
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='flags_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='flags', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_flags', to='leads.prospects')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_flags', to='sales.leadfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
