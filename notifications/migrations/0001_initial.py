# Generated by Django 5.1.7 on 2025-07-25 12:39

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notifications',
            fields=[
                ('notification_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('notification_type', models.CharField(choices=[('Info', 'Info'), ('Warning', 'Warning'), ('Error', 'Error'), ('Success', 'Success'), ('Reminder', 'Reminder'), ('<PERSON>ert', 'Alert')], default='Info', max_length=20)),
                ('priority', models.CharField(choices=[('Low', 'Low'), ('Normal', 'Normal'), ('High', 'High'), ('Urgent', 'Urgent')], default='Normal', max_length=20)),
                ('title', models.CharField(max_length=255)),
                ('message', models.TextField()),
                ('is_read', models.BooleanField(default=False)),
                ('read_at', models.DateTimeField(blank=True, null=True)),
                ('action_url', models.URLField(blank=True, null=True)),
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_notifications', to='leads.prospects')),
                ('recipient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications_received', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_notifications', to='sales.leadfile')),
                ('sender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notifications_sent', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
