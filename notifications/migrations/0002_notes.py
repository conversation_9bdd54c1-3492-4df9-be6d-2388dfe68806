# Generated by Django 5.1.7 on 2025-07-25 13:38

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        ('leads', '0009_prospects_leadfiles'),
        ('notifications', '0001_initial'),
        ('sales', '0004_alter_cashoncash_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Notes',
            fields=[
                ('note_id', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True)),
                ('note_type', models.CharField(choices=[('General', 'General'), ('Important', 'Important'), ('Reminder', 'Reminder'), ('Follow-up', 'Follow-up'), ('Internal', 'Internal'), ('Customer Facing', 'Customer Facing')], default='General', max_length=50)),
                ('title', models.CharField(max_length=255)),
                ('content', models.TextField()),
                ('is_private', models.BooleanField(default=False)),
                ('is_pinned', models.BooleanField(default=False)),
                ('tags', models.CharField(blank=True, help_text='Comma-separated tags', max_length=500, null=True)),
                ('follow_up_required', models.BooleanField(default=False)),
                ('follow_up_date', models.DateTimeField(blank=True, null=True)),
                ('set_reminder', models.BooleanField(default=False)),
                ('reminder_time', models.DateTimeField(blank=True, null=True)),
                ('client_type', models.CharField(choices=[('Prospect', 'Prospect'), ('Customer', 'Customer'), ('Sale', 'Sale')], default='Prospect', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='notes_created', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='notes', to='customers.customer')),
                ('prospect', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='prospect_notes', to='leads.prospects')),
                ('sale', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='sale_notes', to='sales.leadfile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
    ]
