from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

notification_router = DefaultRouter(trailing_slash=False)

notification_router.register('all-notifications', views.NotificationViewSet, basename='all-notifications')
notification_router.register('all-notes', views.NotesViewSet, basename='all-notes')

urlpatterns = [
    path('notifications/', include(notification_router.urls)),
]

