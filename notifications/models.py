from django.conf import settings
from django.db import models
from django.contrib.auth.models import User
from customers.models import Customer
from leads.models import Prospects
from sales.models import LeadFile
from notifications.utils import generate_notification_id,generate_note_id



class Notifications(models.Model):
    NOTIFICATION_TYPE_CHOICES = [
        ('Info', 'Info'),
        ('Warning', 'Warning'),
        ('Error', 'Error'),
        ('Success', 'Success'),
        ('Reminder', 'Reminder'),
        ('Alert', 'Alert'),
    ]

    PRIORITY_CHOICES = [
        ('Low', 'Low'),
        ('Normal', 'Normal'),
        ('High', 'High'),
        ('Urgent', 'Urgent'),
    ]
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]
    notification_id = models.CharField(max_length=50, unique=True, primary_key=True)
    recipient = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='notifications_received', to_field='employee_no')
    sender = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications_sent', to_field='employee_no')
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES, default='Info')
    priority = models.CharField(max_length=20, choices=PRIORITY_CHOICES, default='Normal')
    title = models.CharField(max_length=255)
    message = models.TextField()
    is_read = models.BooleanField(default=False)
    read_at = models.DateTimeField(blank=True, null=True)
    action_url = models.URLField(blank=True, null=True)
    
    # follow_up_required = models.BooleanField(default=False)
    # follow_up_date = models.DateTimeField(blank=True, null=True)
    # set_reminder = models.BooleanField(default=False)
    # reminder_time = models.DateTimeField(blank=True, null=True)
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='notifications_created', to_field='employee_no')
    customer = models.ForeignKey( Customer, on_delete=models.CASCADE, related_name='notifications', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_notifications',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_notifications', to_field='lead_file_no', null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure notification_id is unique"""

        if not self.notification_id:
            self.notification_id = generate_notification_id()
            while Notifications.objects.filter(notification_id=self.notification_id).exists():
                self.notification_id = generate_notification_id()
        print(f"Saving notification:with ID: {self.notification_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.notification_id} - {self.description}"

    class Meta:
        ordering = ['-created_at']


class Notes(models.Model):
    NOTE_TYPE_CHOICES = [
        ('General', 'General'),
        ('Important', 'Important'),
        ('Reminder', 'Reminder'),
        ('Follow-up', 'Follow-up'),
        ('Internal', 'Internal'),
        ('Customer Facing', 'Customer Facing'),
    ]
    Client_type =[('Prospect', 'Prospect'),
                  ('Customer', 'Customer'),
                  ('Sale', 'Sale')]
    note_id = models.CharField(max_length=50, unique=True, primary_key=True)
    note_type = models.CharField(max_length=50, choices=NOTE_TYPE_CHOICES, default='General')
    title = models.CharField(max_length=255)
    content = models.TextField()
    is_private = models.BooleanField(default=False)
    is_pinned = models.BooleanField(default=False)
    tags = models.CharField(max_length=500, blank=True, null=True, help_text="Comma-separated tags")
    
    follow_up_required = models.BooleanField(default=False)
    follow_up_date = models.DateTimeField(blank=True, null=True)
    set_reminder = models.BooleanField(default=False)
    reminder_time = models.DateTimeField(blank=True, null=True)
    client_type = models.CharField(max_length=20,choices=Client_type,default='Prospect')
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, blank=True, related_name='notes_created', to_field='employee_no')
    customer = models.ForeignKey( Customer, on_delete=models.CASCADE, related_name='notes', to_field='customer_no',  null=True,  blank=True )
    prospect = models.ForeignKey( Prospects, on_delete=models.CASCADE, related_name='prospect_notes',  null=True, blank=True)
    sale= models.ForeignKey( LeadFile, on_delete=models.CASCADE, related_name='sale_notes', to_field='lead_file_no', null=True, blank=True)


    def save(self, *args, **kwargs):
        """Override save method to ensure note_id is unique"""

        if not self.note_id:
            self.note_id = generate_note_id()
            while Notes.objects.filter(note_id=self.note_id).exists():
                self.note_id = generate_note_id()
        print(f"Saving note:with ID: {self.note_id}")
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.note_id} - {self.title}"

    class Meta:
        ordering = ['-created_at']

