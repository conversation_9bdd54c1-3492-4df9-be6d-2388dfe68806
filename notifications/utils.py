
from django.db import models
from django.contrib.auth.models import User
import uuid

def generate_notification_id():
    """
    Generate a unique notification ID with format: NOTIF-XXXXXXXXXX
    """
    return f"NOTIF-{uuid.uuid4().hex[:10].upper()}"
def generate_note_id():
    """
    Generate a unique note ID with format: NOTE-XXXXXXXXXX
    """
    return f"NOTE-{uuid.uuid4().hex[:10].upper()}"