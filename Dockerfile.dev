FROM python:3.13-slim-bullseye

WORKDIR /app

ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1

RUN apt-get update && apt-get install -y \
    libpq-dev \
    gcc \
    curl \
    pkg-config \
    default-libmysqlclient-dev \
    build-essential \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    libcairo2 \
    libjpeg-dev \
    libpng-dev \
    fonts-liberation \
    fonts-freefont-ttf \
    libxml2 \
    libxslt1.1 \
    libfontconfig1 \
    shared-mime-info \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*
 

COPY requirements.txt ./

RUN pip install -r requirements.txt

COPY . .

EXPOSE 8001

# # Run the application with Gun<PERSON>
# CMD ["gunicorn", "--workers=4", "--bind", "0.0.0.0:8001", "config.wsgi:application"]
# Run Gunicorn, Celery Worker, and Celery Beat together
CMD bash -c "celery -A config worker --loglevel=info & celery -A config beat --loglevel=info & gunicorn --workers=4 --bind 0.0.0.0:8001 config.wsgi:application"
