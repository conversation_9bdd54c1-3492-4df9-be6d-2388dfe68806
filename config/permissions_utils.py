from django.db.models import Q
from django.contrib.auth.models import AnonymousUser
from users.models import (User, User_2_UserPermissions, Teams_2_TeamsPermissions, UserGroup_2_UserGroupPermissions,
                            UserPermissions, TeamsPermissions, UserGroupPermissions)

def has_user_custom_permission(user, permission_id):

    # Check User-Level Permissions
    if User_2_UserPermissions.objects.filter(user=user, permission__permission_id =permission_id ).exists():
        return True

    return False


def has_teams_custom_permission(user, permission_id):


    # Check Team-Level Permissions
    if hasattr(user, 'team') and user.team and Teams_2_TeamsPermissions.objects.filter(team=user.team, permission__permission_id =permission_id ).exists():
        return True

    return False



def has_group_custom_permission(user, permission_id):

    # Check Group-Level Permissions
    if hasattr(user, 'group') and user.group and UserGroup_2_UserGroupPermissions.objects.filter(user_group=user.group, permission__permission_id=permission_id ).exists():
        return True

    return False