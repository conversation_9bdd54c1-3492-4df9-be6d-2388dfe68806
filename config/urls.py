from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from rest_framework import permissions
from drf_yasg.views import get_schema_view
from drf_yasg import openapi
import os



schema_info = openapi.Info(
    title="CRM 2.0 API",
    default_version="v1",
    description=(
        "### REST API Routes\n"
        "Comprehensive REST API documentation for CRM 2.0 system.\n"
    ),
    terms_of_service="https://www.google.com/policies/terms/",
    contact=openapi.Contact(email="<EMAIL>"),
    license=openapi.License(name="BSD License"),
    public=True,
    url=f"{settings.SCHEME}://{os.getenv('API_DOMAIN', 'localhost:8000')}/api/",

    
)

schema_view = get_schema_view(
    schema_info,
    public=True,
    permission_classes=[permissions.AllowAny],
)

api_prefix = 'api/'

urlpatterns = [
    path('admin/', admin.site.urls),
    path('swagger/', schema_view.with_ui('swagger', cache_timeout=0), name='schema-swagger-ui'),
    
    
    path(api_prefix, include('inventory.urls')),
    path(api_prefix, include('offerletters.urls')),
    path(api_prefix, include('leads.urls')),
    path(api_prefix, include('sales.urls')),
    path(api_prefix, include('customers.urls')),
    path(api_prefix, include('users.urls')),
    path(f'{api_prefix}services/', include('services.urls')),
    path(api_prefix, include('erp_api.urls')),
    path(api_prefix, include('logistics.urls')),
    path(api_prefix, include('reports.urls')),
    path(api_prefix, include('dashboards.urls')),
    path(api_prefix, include('ticketing.urls')),
    path(api_prefix, include('engagement.urls')),
    path(api_prefix, include('flags.urls')),
    path(api_prefix, include('notifications.urls')),
    path(api_prefix, include('todo.urls')),
    path(api_prefix, include('reminders.urls')),
    path(f'{api_prefix}receipts/', include('receipts.urls')),
    path(f'{api_prefix}amani/', include('amani.urls')),
    path(f'{api_prefix}chatbot/', include('chatbot.urls')),
    path('external_api/', include('external_api.urls')),

]
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)