"""
Django settings for config project.

Generated by 'django-admin startproject' using Django 5.1.1.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path
from dotenv import load_dotenv
import os
from datetime import timedelta
# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent
def str_to_bool(value):
    return value.lower() in ["true", "1", "yes"]
def str2bool(value):
    return str(value).lower() in ("true", "1", "yes")

def str2none(value):
    return None if value in ("None", "none", "", None) else value




CLOUD_ENV = os.getenv("CLOUD_ENV")

# Always load .env.dev for local development
if CLOUD_ENV != "True":
    load_dotenv(BASE_DIR / ".env.dev")
# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/


# SECURITY WARNING: don't run with debug turned on in production!

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = os.getenv("SECRET_KEY")

DEBUG = os.getenv('DEBUG', 'False') == 'True'

ALLOWED_HOSTS = ["*", "127.0.0.1"]


# Application definition

INSTALLED_APPS = [
    'daphne',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django.contrib.humanize',

    

    'channels',
    'corsheaders',
    'rest_framework',
    'rest_framework_simplejwt',
    'rest_framework_simplejwt.token_blacklist',
    'django_filters',
    'drf_yasg',
    'drf_spectacular',
    'storages',
    'django_celery_beat',
   
    
    'inventory',
    'offerletters',
    'leads',
    'sales',
    'customers',
    'users',
    'services',
    
    'erp_api',
    'external_api',
    'logistics',
    'receipts',
    'reports',
    'dashboards',
    'ticketing',
    'engagement',
    'flags',
    'todo',
    'reminders',
    'utils',
    'notifications',
    'amani',
    
    'chatbot',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware'
]

# django-storages settings



MINIO_ACCESS_KEY = os.getenv("MINIO_ACCESS_KEY")
MINIO_SECRET_KEY = os.getenv("MINIO_SECRET_KEY")
MINIO_BUCKET_NAME = os.getenv("MINIO_BUCKET_NAME")
MINIO_ENDPOINT = os.getenv("MINIO_ENDPOINT")
MINIO_FILE_OVERWRITE = str2bool(os.getenv("MINIO_FILE_OVERWRITE", "False"))
MINIO_DEFAULT_ACL = str2none(os.getenv("MINIO_DEFAULT_ACL", None))
MINIO_QUERYSTRING_AUTH = str2bool(os.getenv("MINIO_QUERYSTRING_AUTH", "False"))


AWS_ACCESS_KEY_ID = MINIO_ACCESS_KEY
AWS_SECRET_ACCESS_KEY = MINIO_SECRET_KEY
AWS_STORAGE_BUCKET_NAME = MINIO_BUCKET_NAME
AWS_S3_ENDPOINT_URL = MINIO_ENDPOINT
AWS_S3_FILE_OVERWRITE = MINIO_FILE_OVERWRITE
AWS_DEFAULT_ACL = MINIO_DEFAULT_ACL
AWS_QUERYSTRING_AUTH = MINIO_QUERYSTRING_AUTH
AWS_S3_REGION_NAME = 'us-east-1'
AWS_S3_SIGNATURE_VERSION = 's3v4'
AWS_S3_VERIFY = False



STORAGES = {
    "default": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
    },
    "staticfiles": {
        "BACKEND": "storages.backends.s3boto3.S3Boto3Storage",
    },
}



CORS_ALLOW_ALL_ORIGINS = True
CORS_ALLOW_HEADERS = [
    "content-type",
    "authorization",
    "x-csrftoken",
]
CSP_SCRIPT_SRC = ("'self'", "'unsafe-inline'", "'unsafe-eval'", "*")



ROOT_URLCONF = 'config.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / "templates"],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'config.wsgi.application'

ASGI_APPLICATION = "config.asgi.application"


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

import pymysql
from django.contrib.auth import get_user_model
pymysql.install_as_MySQLdb()



DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv("DB_NAME", "mysql"),
        'USER': os.getenv("DB_USER", "root"),
        'PASSWORD': os.getenv("DB_PASSWORD", "0000"),
        'HOST': os.getenv("DB_HOST", "127.0.0.1"),
        'PORT': os.getenv("DB_PORT", "3306"),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
        },
    },
    "reports": {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': os.getenv("DB_NAME", "mysql"),
        'USER': os.getenv("DB_USER", "root"),
        'PASSWORD': os.getenv("DB_PASSWORD", "0000"),
        'HOST': os.getenv("DB_HOST", "127.0.0.1"),
        'PORT': os.getenv("DB_PORT", "3306"),
        "OPTIONS": {            # Optional but recommended
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
            "charset": "utf8mb4",
        },
    },
    "erp": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": 'defaultdb',
        "USER": os.getenv('DB_USER'),
        "PASSWORD": os.getenv('DB_PASSWORD'),
        "HOST": os.getenv('DB_HOST'),
        "PORT": os.getenv('DB_PORT'),
        'OPTIONS': {
            'init_command': "SET sql_mode='STRICT_TRANS_TABLES'",
            "charset": "utf8mb4",
        },
    },
}



# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Africa/Nairobi'
USE_TZ = True

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/
STATIC_URL = 'static/'

STATIC_ROOT = BASE_DIR / 'staticfiles'

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

AUTH_USER_MODEL = 'users.User'

# Rest Framework settings
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': (
        'rest_framework.permissions.IsAuthenticated',
    ),
    "DEFAULT_PAGINATION_CLASS": "config.pagination.CustomPagination",
    "DEFAULT_SCHEMA_CLASS": "drf_spectacular.openapi.AutoSchema"
}

class CustomTokenLifetime:
    def __init__(self, user):
        # 1 year for superusers, else 30 days
        if user and user.is_superuser:
            self.access = timedelta(days=365)
            self.refresh = timedelta(days=365)
        else:
            self.access = timedelta(days=30)
            self.refresh = timedelta(days=30)

def custom_access_token_lifetime(user):
    return CustomTokenLifetime(user).access

def custom_refresh_token_lifetime(user):
    return CustomTokenLifetime(user).refresh

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(days=30),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=30),
    'ROTATE_REFRESH_TOKENS': True,
    'UPDATE_LAST_LOGIN': True,
    'AUTH_HEADER_TYPES': ('Token',),
    # Custom lifetime functions (requires custom JWT authentication class)
    'ACCESS_TOKEN_LIFETIME_FUNC': custom_access_token_lifetime,
    'REFRESH_TOKEN_LIFETIME_FUNC': custom_refresh_token_lifetime,
}


CELERY_BEAT_SCHEDULER = 'django_celery_beat.schedulers.DatabaseScheduler'

CELERY_BROKER_URL = os.getenv('CELERY_BROKER_URL')

CELERY_ACCEPT_CONTENT = ["json"]
CELERY_TASK_SERIALIZER = "json"
CELERY_BROKER_CONNECTION_RETRY = True
CELERY_BROKER_CONNECTION_MAX_RETRIES = None
CELERY_ENABLE_UTC = False
CELERY_TIMEZONE = 'Africa/Nairobi'
BROKER_HEARTBEAT = 60  # Increase from default 30
broker_connection_retry_on_startup = True



EMAIL_BACKEND = os.getenv("EMAIL_BACKEND")
EMAIL_HOST = os.getenv("EMAIL_HOST")
EMAIL_PORT = int(os.getenv("EMAIL_PORT", 587))  # Convert to integer
EMAIL_USE_TLS = str_to_bool(os.getenv("EMAIL_USE_TLS", "True"))  # Convert to boolean
EMAIL_USE_SSL = str_to_bool(os.getenv("EMAIL_USE_SSL", "False"))  # Convert to boolean
EMAIL_HOST_USER = os.getenv("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = os.getenv("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = os.getenv("DEFAULT_FROM_EMAIL")

USE_HTTPS = os.getenv("USE_HTTPS", "False").lower() in ("true", "1")
SCHEME = "https" if USE_HTTPS else "http"


SWAGGER_SETTINGS = {
    "DEFAULT_API_URL": f"{SCHEME}://{os.getenv('API_DOMAIN', 'localhost:8000')}",
    'DEFAULT_INFO': 'config.urls.schema_info',
    'SECURITY_DEFINITIONS': {
         'Token': {
            'type': 'apiKey',
            'in': 'header',
            'name': 'Authorization',
            'description': 'Enter format: Token <token> or Bearer <token>',
        }
    },
}
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'INFO',
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',  # Using the 'verbose' formatter with timestamp
        },
        'worker_file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': 'celery_worker.log',
            'formatter': 'verbose',  # Using the 'verbose' formatter with timestamp
        },
        'beat_file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'celery_beat.log',
            'formatter': 'verbose',  # Using the 'verbose' formatter with timestamp
        },
    },
    'loggers': {
        'celery': {
            'handlers': ['console', 'worker_file'],
            'level': 'INFO',
            'propagate': True,
        },
        'celery.beat': {
            'handlers': ['console', 'beat_file'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}





