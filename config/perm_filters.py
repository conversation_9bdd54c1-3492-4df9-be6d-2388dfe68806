from config.permissions_utils import has_user_custom_permission

from leads.models import (LeadSource,LeadSourceCategory,LeadSourceSubCategory)

def sales_permission_filters(user, base_queryset):
    queryset = base_queryset

    allowed_permissions = [
        "1001", "1002", "1003", "1004", "1006", "1007", "1008", "1011"
    ]

    # Find the first permission the user has (should only have one)
    user_permission = next((perm for perm in allowed_permissions if has_user_custom_permission(user, perm)), None)

    if not user_permission:
        return False

    # Only allow if the user has exactly one allowed permission
    if sum(1 for perm in allowed_permissions if has_user_custom_permission(user, perm)) != 1:
        return False

    if user_permission == "1001":
        queryset = queryset.filter(marketer_id__office__in=['HQ', 'HQ (ABSA)', ''])
    elif user_permission == "1002":
        queryset = queryset.filter(marketer_id__office__in=['Global Office(KAREN)', 'KITENGELA', 'NANYUKI', 'NAKURU'])
    elif user_permission == "1003":
        pass
    elif user_permission == "1004":
        queryset = queryset.filter(marketer_id=user)
    elif user_permission == "1006":
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(lead_source_category_id='3').values_list('pk', flat=True)
        lead_sources = LeadSource.objects.filter(lead_source_subcategory_id__in=lead_source_subcategories).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(customer_lead_source__in=lead_sources)
    elif user_permission == "1007":
        lead_sources = LeadSource.objects.filter(managing_team='Digital').values_list('leadsource_id', flat=True)
        queryset = queryset.filter(customer_lead_source_id__name__in=lead_sources)
    elif user_permission == "1008":
        lead_sources = LeadSource.objects.filter(managing_team='Telemarketing').values_list('leadsource_id', flat=True)
        queryset = queryset.filter(customer_lead_source_id__name__in=lead_sources)
    elif user_permission == "1011":
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(manager_id=user).values_list('pk', flat=True)
        lead_sources = LeadSource.objects.filter(lead_source_subcategory_id__in=lead_source_subcategories).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(customer_lead_source__in=lead_sources)

    return queryset


def leads_permission_filters(user, base_queryset):
    queryset = base_queryset

    allowed_permissions = {
        "3001", "3002", "3003", "3004", "3006", "3007", "3008", "3011"
    }
    # Find the first permission the user has (should only have one)
    user_permission = next((perm for perm in allowed_permissions if has_user_custom_permission(user, perm)), None)

    # Only allow if the user has exactly one allowed permission
    if not user_permission or sum(1 for perm in allowed_permissions if has_user_custom_permission(user, perm)) != 1:
        return False

    if user_permission == "3001":
        queryset = queryset.filter(marketer_id__office__in=['HQ', 'HQ (ABSA)', ''])
    elif user_permission == "3002":
        queryset = queryset.filter(marketer_id__office__in=['Global Office(KAREN)', 'KITENGELA', 'NANYUKI', 'NAKURU'])
    elif user_permission == "3003":
        pass
    elif user_permission == "3004":
        queryset = queryset.filter(marketer_id=user)
    elif user_permission == "3006":
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(
            lead_source_category_id='3'
        ).values_list('pk', flat=True)
        lead_sources = LeadSource.objects.filter(
            lead_source_subcategory_id__in=lead_source_subcategories
        ).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)
    elif user_permission == "3007":
        queryset = queryset.filter(department_id='10')
    elif user_permission == "3008":
        queryset = queryset.filter(department_id='21')
    elif user_permission == "3011":
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(
            manager_id=user
        ).values_list('pk', flat=True)
        lead_sources = LeadSource.objects.filter(
            lead_source_subcategory_id__in=lead_source_subcategories
        ).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)

    return queryset


def customers_permission_filters(user, base_queryset):
    queryset = base_queryset
    
    allowed_permissions = {
        "2001", "2002", "2003", "2004", "2006", "2007", "2008", "2011"
    }
    user_permissions = [
        perm for perm in allowed_permissions if has_user_custom_permission(user, perm)
    ]
    if len(user_permissions) != 1:
        return False
    if not user_permissions:
        return False

    if "2001" in user_permissions:
       queryset = queryset.filter(marketer_id__office__in=['HQ', 'HQ (ABSA)', ''])
    if "2002" in user_permissions:
        queryset = queryset.filter(marketer_id__office__in=['Global Office(KAREN)', 'KITENGELA', 'NANYUKI','NAKURU'])
    if "2003" in user_permissions:
        pass
    if "2004" in user_permissions:
        queryset = queryset.filter(marketer_id=user)
    if "2006" in user_permissions:
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(lead_source_category_id='3')
        lead_sources = LeadSource.objects.filter(lead_source_subcategory_id__in=lead_source_subcategories).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)
    if "2007" in user_permissions:
        lead_sources = LeadSource.objects.filter(managing_team='Digital').values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)
    if "2008" in user_permissions:
        lead_sources = LeadSource.objects.filter(managing_team='Telemarketing').values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)
    if "2011" in user_permissions:
        lead_source_subcategories = LeadSourceSubCategory.objects.filter(manager_id=user)
        lead_sources = LeadSource.objects.filter(lead_source_subcategory_id__in=lead_source_subcategories).values_list('leadsource_id', flat=True)
        queryset = queryset.filter(lead_source_id__in=lead_sources)

    return queryset

        
