import os
from celery.schedules import crontab
from celery import Celery

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')

app = Celery('config')
app.config_from_object('django.conf:settings', namespace='CELERY')

app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    print(f'Request: {self.request!r}')



# Celery Beat Schedule
app.conf.beat_schedule = {
    
    "expire_plot_booking": {
        "task": "inventory.tasks.expire_plot_booking",
        "schedule": crontab(minute="*/5"),  # Runs every 5 minutes
    },
    "drop_diaspora_reservation": {
        "task": "inventory.tasks.drop_diaspora_reservation",
        "schedule": crontab(minute="*/5"),  # Runs every 5 minutes
    },
    "legal_team_leadfile_offerletter_checker": {
        "task": "inventory.tasks.legal_team_leadfile_offerletter_checker",
        "schedule": crontab(minute="*/3"),  # Runs every 3 minutes
    },
    "update_project_priority": {
        "task": "inventory.tasks.update_project_priority",
        "schedule": crontab(minute="*/30"),  # Runs every 30 minutes
    },
    "send_reminder_emails": {
        "task": "reminders.tasks.send_reminder_emails",
        "schedule": crontab(minute="*/5"),  # Runs every 5 minutes
    },
    "sync_leadfiles_from_erp": {
        "task": "erp_api.tasks.sync_leadfiles_from_erp",
        "schedule": crontab(minute="*/1"),  # Runs every 15 minutes
        
    },
    "truncate_portfolio_models": {
        "task": "sales.tasks.truncate_portfolio_models",
        "schedule": crontab(hour=12, minute=0),  # Runs daily at 12:00 PM
    },
    "sync_installment_schedules_from_erp": {
        "task": "erp_api.tasks.sync_installment_schedules_from_erp",
        "schedule": crontab(minute="*/1"),  # Runs every 10 minutes
    },
    "update_lead_source_counts": {
        "task": "leads.tasks.update_lead_source_counts",
        "schedule": crontab(minute="*/5"),  # Runs every 5 minutes
    },
    
}