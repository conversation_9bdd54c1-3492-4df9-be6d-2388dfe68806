# Generated by Django 5.1.7 on 2025-07-03 13:50

import django.db.models.deletion
import services.models
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('customers', '0003_alter_customer_options_alter_customergroups_options'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomSystemLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('system_level', models.CharField(choices=[('User', 'User'), ('ERP', 'ERP'), ('Celery', 'Celery')], max_length=15)),
                ('action', models.CharField(choices=[('Create', 'Create'), ('Edit', 'Edit'), ('Delete', 'Delete')], max_length=15)),
                ('module', models.CharField(max_length=50)),
                ('message', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='system_logs', to=settings.AUTH_USER_MODEL, to_field='employee_no')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        ]
