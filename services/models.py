from django.db import models
from users.models import User
import logging
logger = logging.getLogger(__name__)


class CustomSystemLog(models.Model):
    """
    Legacy system log model - kept for backward compatibility
    """
    SYSTEM_LEVEL = [
        ('User', 'User'),
        ('ERP', 'ERP'),
        ('Celery', 'Celery'),
    ]

    ACTIONS_CHOICES = [
        ('Create', 'Create'),
        ('Edit', 'Edit'),
        ('Delete', 'Delete'),
    ]
    

    system_level = models.CharField(max_length=15, choices=SYSTEM_LEVEL)
    user = models.ForeignKey(
        User,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name='system_logs',
        to_field='employee_no'
    )
    action = models.CharField(max_length=15, choices=ACTIONS_CHOICES)
    module = models.CharField(max_length=50)
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.system_level} - {self.action} - {self.module}"

    class Meta:
        ordering = ['-timestamp']

