
from rest_framework import serializers
from services.models import (  CustomSystemLog)


class CustomSystemLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = CustomSystemLog
        fields = '__all__'
        read_only_fields = ['timestamp']

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep['user_name'] = instance.user.fullnames if instance.user else None
        return rep




