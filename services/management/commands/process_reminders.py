from django.core.management.base import BaseCommand, CommandError
from django.utils import timezone
from django.db import transaction
from django.conf import settings
import logging
import sys
import traceback
from services.models import ReminderProcessor, NotificationManager, Reminder


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('reminder_processing.log', mode='a')
    ]
)

logger = logging.getLogger(__name__)


class ReminderProcessingService:
    """Service to handle actual reminder processing logic"""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)
    
    def process_batch(self, batch_size):
        """Process reminders in batches"""
        try:
            processed_count = ReminderProcessor.process_due_reminders()
            
            if self.verbose:
                self._log_batch_results()
            
            return processed_count
        except Exception as e:
            self.logger.error(f"Error in batch processing: {str(e)}\n{traceback.format_exc()}")
            return 0
    
    def _log_batch_results(self):
        """Log detailed batch processing results"""
        now = timezone.now()
        
        advance_reminders = Reminder.objects.filter(
            status='Active',
            advance_notification_sent=False,
            remind_at__gt=now,
            remind_at__lte=now + timezone.timedelta(minutes=1440)
        ).count()
        
        due_reminders = Reminder.objects.filter(
            status='Active',
            notification_sent=False,
            remind_at__lte=now
        ).count()
        
        snoozed_reminders = Reminder.objects.filter(
            status='Snoozed',
            snooze_until__lte=now
        ).count()
        
        print(f"Processed {advance_reminders} advance notifications")
        print(f"Processed {due_reminders} due reminders") 
        print(f"Activated {snoozed_reminders} snoozed reminders")

class ReminderSimulationService:
    """Service to handle dry-run simulation"""
    
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.logger = logging.getLogger(__name__)
    
    def simulate_processing(self):
        """Simulate processing for dry run mode"""
        try:
            now = timezone.now()
            
            # Count what would be processed
            advance_reminders = Reminder.objects.filter(
                status='Active',
                advance_notification_sent=False,
                remind_at__gt=now,
                remind_at__lte=now + timezone.timedelta(minutes=1440)
            ).count()
            
            due_reminders = Reminder.objects.filter(
                status='Active',
                notification_sent=False,
                remind_at__lte=now
            ).count()
            
            snoozed_reminders = Reminder.objects.filter(
                status='Snoozed',
                snooze_until__lte=now
            ).count()
            
            recurring_reminders = Reminder.objects.filter(
                status='Active',
                repeat_pattern__in=['Daily', 'Weekly', 'Monthly', 'Yearly'],
                remind_at__lte=now
            ).exclude(repeat_pattern='None').count()
            
            if self.verbose:
                print(f"Would process {advance_reminders} advance notifications")
                print(f"Would process {due_reminders} due reminders")
                print(f"Would activate {snoozed_reminders} snoozed reminders")
                print(f"Would create {recurring_reminders} recurring reminder instances")
            
            return {
                'advance_notifications': advance_reminders,
                'due_notifications': due_reminders,
                'snoozed_activated': snoozed_reminders,
                'recurring_created': recurring_reminders,
                'total': advance_reminders + due_reminders + snoozed_reminders
            }
            
        except Exception as e:
            self.logger.error(f"Error in simulation: {str(e)}\n{traceback.format_exc()}")
            return {'total': 0, 'errors': 1}

class ProcessLockService:
    """Service to handle process locking"""
    
    def __init__(self):
        # Use a more cross-platform approach
        import tempfile
        import os
        self.lock_file = os.path.join(tempfile.gettempdir(), 'reminder_processing.lock')
    
    def check_running(self):
        """Check if another process is running"""
        import os
        return os.path.exists(self.lock_file)
    
    def create_lock(self):
        """Create process lock file"""
        with open(self.lock_file, 'w') as f:
            f.write(str(timezone.now()))
    
    def remove_lock(self):
        """Remove process lock file"""
        import os
        try:
            if os.path.exists(self.lock_file):
                os.remove(self.lock_file)
        except Exception as e:
            logger.warning(f"Could not remove lock file: {str(e)}")

class ReminderStatisticsService:
    """Service to handle statistics collection and display"""
    
    def __init__(self, stdout):
        self.stdout = stdout
    
    def display_statistics(self, stats):
        """Display detailed processing statistics"""
        self.stdout.write(
            self.stdout.style_func_for_success("\n=== Processing Statistics ===")
        )
        self.stdout.write(f"Total processed: {stats.get('total_processed', 0)}")
        self.stdout.write(f"Advance notifications: {stats.get('advance_notifications', 0)}")
        self.stdout.write(f"Due notifications: {stats.get('due_notifications', 0)}")
        self.stdout.write(f"Snoozed activated: {stats.get('snoozed_activated', 0)}")
        self.stdout.write(f"Recurring created: {stats.get('recurring_created', 0)}")
        
        if stats.get('errors', 0) > 0:
            self.stdout.write(
                self.stdout.style_func_for_error(f"Errors encountered: {stats['errors']}")
            )
        
        # Additional system statistics
        try:
            active_count = Reminder.objects.filter(status='Active').count()
            overdue_count = Reminder.objects.filter(
                status='Active',
                remind_at__lt=timezone.now()
            ).count()
            
            self.stdout.write(f"\n=== System Statistics ===")
            self.stdout.write(f"Current active reminders: {active_count}")
            self.stdout.write(f"Current overdue reminders: {overdue_count}")
        except Exception as e:
            self.stdout.write(
                self.stdout.style_func_for_warning(f"Could not get system statistics: {str(e)}")
            )
        
        self.stdout.write("===============================\n")

class Command(BaseCommand):
    help = 'Process due reminders and send notifications with comprehensive error handling'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without actually sending notifications or making changes',
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Print detailed output',
        )
        parser.add_argument(
            '--batch-size',
            type=int,
            default=100,
            help='Number of reminders to process in each batch (default: 100)',
        )
        parser.add_argument(
            '--cleanup',
            action='store_true',
            help='Also cleanup old notifications after processing',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force processing even if another instance might be running',
        )

    def handle(self, *args, **options):
        """
        Simplified handle method using service classes
        Maintains all existing functionality with better organization
        """
        # Extract options
        dry_run = options['dry_run']
        verbose = options['verbose']
        batch_size = options['batch_size']
        cleanup = options['cleanup']
        force = options['force']
        
        start_time = timezone.now()
        
        # Initialize services
        lock_service = ProcessLockService()
        stats_service = ReminderStatisticsService(self.stdout)
        
        self.stdout.write(
            self.style.SUCCESS(f"Starting reminder processing at {start_time}")
        )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING("DRY RUN MODE - No notifications will be sent or changes made")
            )
        
        try:
            # Check for existing processes
            if not force and lock_service.check_running():
                raise CommandError("Another reminder processing instance is already running. Use --force to override.")
            
            if not dry_run:
                lock_service.create_lock()
            
            # Process reminders
            if dry_run:
                sim_service = ReminderSimulationService(verbose)
                stats = sim_service.simulate_processing()
                processed_count = stats['total']
            else:
                with transaction.atomic():
                    processing_service = ReminderProcessingService(verbose)
                    processed_count = processing_service.process_batch(batch_size)
                    stats = {'total_processed': processed_count, 'errors': 0}
            
            self.stdout.write(
                self.style.SUCCESS(f"Successfully processed {processed_count} reminders")
            )
            
            # Display statistics if requested
            if verbose or stats.get('errors', 0) > 0:
                stats_service.display_statistics(stats)
            
            # Cleanup old notifications if requested
            if cleanup and not dry_run:
                self._cleanup_notifications(verbose)
            
        except CommandError:
            raise
        except Exception as e:
            error_msg = f"Unexpected error during reminder processing: {str(e)}"
            self.stdout.write(self.style.ERROR(error_msg))
            logger.error(f"{error_msg}\n{traceback.format_exc()}")
            raise CommandError(error_msg)
        finally:
            if not dry_run:
                lock_service.remove_lock()
            
            self._log_completion(start_time, stats)
    
    def _cleanup_notifications(self, verbose):
        """Handle notification cleanup"""
        try:
            deleted_count = NotificationManager.cleanup_old_notifications(days=30)
            if verbose:
                self.stdout.write(f"Cleaned up {deleted_count} old notifications")
            logger.info(f"Cleaned up {deleted_count} old notifications")
        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f"Error cleaning up notifications: {str(e)}")
            )
            logger.error(f"Error cleaning up notifications: {str(e)}")
    
    def _log_completion(self, start_time, stats):
        """Log completion information"""
            end_time = timezone.now()
            duration = end_time - start_time
            
            self.stdout.write(
                self.style.SUCCESS(
                    f"Reminder processing completed at {end_time} (Duration: {duration})"
                )
            )
            logger.info(f"Processing completed. Duration: {duration}, Stats: {stats}")

# Old methods removed - functionality moved to service classes 