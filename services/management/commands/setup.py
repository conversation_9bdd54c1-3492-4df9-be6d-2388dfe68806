
from django.core.management.base import BaseCommand
from users.models import Departments, Teams, UserGroups

from inventory.models import PlotSizeCategory
from django.db import transaction

class Command(BaseCommand):
    help = 'initial system setup'

    def handle(self, *args, **kwargs):
        set_departments()
        set_teams()
        set_user_groups()
        addPlotSizeCategories()

        print(' 🔥  System setup completed successfully  🔥 ')



# initial default department
def set_departments():
    dep_data = {
        'dp_id':1,
        'dp_name':'UNKNOWN',
        'dep_head_id': None,
        'dep_head_name': None
    }

    department, created = Departments.objects.get_or_create(**dep_data)
    if created:
        print(f' ✅  Department {department.dp_name} added successfully')
    else:
        print(f' 🚩  Department {department.dp_name} already exists') 


# initial default team
def set_teams():
    team_data = {
        'id':1,
        'team':'UNKNOWN',
        'tl_code': None,
        'tl_name': None,
        'inactive': True,
    }

    team, created = Teams.objects.get_or_create(**team_data)
    if created:
        print(f' ✅  Team {team.team} added successfully')
    else:
        print(f' 🚩  Team {team.team} already exists') 


# initial default team
def set_user_groups():
    user_group_data = {
        'id':1,
        'group_id':1,
        'name': 'UNKNOWN',
        'comments': 'Initial Group'
    }

    group, created = UserGroups.objects.get_or_create(**user_group_data)
    if created:
        print(f' ✅  User Group {group.name} added successfully')
    else:
        print(f' 🚩  User Group {group.name} already exists') 


# set default plot size categories
def addPlotSizeCategories():
    """
    Add plot categories to the database.
    """
    categories = [
        {'category': 'A', 'start_size': 0.03999, 'end_size': 0.07999, 'definition': '1/8th Acre'},
        {'category': 'B', 'start_size': 0.07999, 'end_size': 0.17999, 'definition': '1/4th Acre'},
        {'category': 'C', 'start_size': 0.17999, 'end_size': 0.27999, 'definition': '1/2 Acre'},
        {'category': 'D', 'start_size': 0.27999, 'end_size': 0.47999, 'definition': '1 Acre'},
        {'category': 'E', 'start_size': 0.47999, 'end_size': 0.87999, 'definition': '2 Acres'},
        {'category': 'F', 'start_size': 0.87999, 'end_size': 10.99999, 'definition': '5 Acres & Above'},
    ]

    try:
        with transaction.atomic():
            for category in categories:
                cat, created = PlotSizeCategory.objects.get_or_create(**category)
                if created:
                    print(f' ✅  Plot category {cat.category} - {cat.definition} added successfully')
                else:
                    print(f' 🌘  Plot category {cat.category} already exists')
                    
            
            print(' 🔥 Plot categories added successfully')
    
    except Exception as e:
           print(f' ❌  faield with error : {e}') 