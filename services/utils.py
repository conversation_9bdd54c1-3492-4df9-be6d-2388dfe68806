import datetime
from datetime import datetime, time

def parse_date_with_optional_time(date_str):
    try:
        dt = datetime.fromisoformat(date_str)
        return {'date': dt.date(), 'time': dt.time()}
    except (ValueError, TypeError):
        try:
            d = datetime.strptime(date_str, "%Y-%m-%d").date()
            return {'date': d, 'time': time(11, 0)}
        except (ValueError, TypeError):
            return None