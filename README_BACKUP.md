# CRM 2.0 Automated Backup System

A comprehensive, production-ready backup solution for the CRM 2.0 Django application that automatically backs up your database, source code, and MinIO S3 bucket files to Google Drive with intelligent retention management.

## 📋 Table of Contents

- [Features](#-features)
- [Architecture](#-architecture)
- [Quick Start](#-quick-start)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Google Drive Setup](#-google-drive-setup)
- [Usage](#-usage)
- [Scheduling](#-scheduling)
- [Monitoring & Troubleshooting](#-monitoring--troubleshooting)
- [Restore Procedures](#-restore-procedures)
- [Advanced Configuration](#-advanced-configuration)
- [Security Considerations](#-security-considerations)
- [FAQ](#-faq)

## 🌟 Features

- **Complete Backup Coverage**: Database (MySQL), source code, and MinIO S3 bucket files
- **Cloud Storage**: Automated upload to Google Drive with API integration
- **Intelligent Retention**: Configurable retention policies (default: 30 days)
- **Robust Error Handling**: Comprehensive logging and email notifications
- **Flexible Scheduling**: Cron-based scheduling with customizable timing
- **Django Integration**: Management commands for easy integration with Django
- **Production Ready**: Built for high-availability environments with fail-safes
- **Security First**: Service account authentication and encrypted transfers
- **Monitoring**: Detailed logging and backup statistics
- **Restoration Support**: Clear procedures for disaster recovery

## 🏗 Architecture

```
CRM 2.0 Backup System Architecture

┌─────────────────────────────────────────────────────────────────┐
│                    Backup Orchestrator                         │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐            │
│  │  Database   │  │    Code     │  │   MinIO     │            │
│  │   Backup    │  │   Backup    │  │   Backup    │            │
│  └─────────────┘  └─────────────┘  └─────────────┘            │
│           │               │               │                    │
│           ▼               ▼               ▼                    │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │            Backup Archive Creation                     │  │
│  │         (backup_YYYYMMDD_HHMMSS.zip)                  │  │
│  └─────────────────────────────────────────────────────────┘  │
│                           │                                    │
│                           ▼                                    │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │            Google Drive Upload                         │  │
│  └─────────────────────────────────────────────────────────┘  │
│                           │                                    │
│                           ▼                                    │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │         Retention Management & Cleanup                │  │
│  └─────────────────────────────────────────────────────────┘  │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘

Backup Structure:
backup_YYYYMMDD_HHMMSS/
├── db/
│   └── db_backup.sql.gz        (Compressed MySQL dump)
├── code/
│   └── code_backup.zip         (Source code archive)
└── files/
    └── minio_backup.tar.gz     (MinIO files archive)
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+ with Django application
- MySQL database
- MinIO S3-compatible storage
- Google Drive API access
- Cron daemon (for scheduling)

### Installation

1. **Install Dependencies**
   ```bash
   pip install google-api-python-client google-auth minio
   ```

2. **Configure Environment Variables**
   ```bash
   # Add to your .env file
   GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=/path/to/service-account.json
   GOOGLE_DRIVE_BACKUP_FOLDER_ID=your_google_drive_folder_id
   BACKUP_RETENTION_DAYS=30
   BACKUP_NOTIFICATION_EMAIL=<EMAIL>
   ```

3. **Test Configuration**
   ```bash
   python manage.py backup --test-config
   ```

4. **Run Manual Backup**
   ```bash
   python manage.py backup
   ```

5. **Setup Automated Scheduling**
   ```bash
   ./scripts/setup_cron.sh
   ```

## 🛠 Installation

### Step 1: Install Python Dependencies

Add the following to your `requirements.txt`:

```txt
google-api-python-client==2.147.0
google-auth==2.35.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
minio==7.2.15
```

Install the dependencies:

```bash
pip install -r requirements.txt
```

### Step 2: Verify Script Permissions

Make sure the scripts are executable:

```bash
chmod +x scripts/backup.py
chmod +x scripts/cleanup_old_backups.py
chmod +x scripts/setup_cron.sh
```

### Step 3: Create Log Directory

```bash
# System-wide logs (requires sudo)
sudo mkdir -p /var/log
sudo chown $USER:$USER /var/log/backup.log

# Or use project-local logs
mkdir -p logs
```

## ⚙️ Configuration

### Environment Variables

Add these variables to your `.env.dev` or production environment:

```bash
# Google Drive Integration (Required)
GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=/path/to/service-account.json
GOOGLE_DRIVE_BACKUP_FOLDER_ID=your_google_drive_folder_id

# Backup Configuration (Optional)
BACKUP_RETENTION_DAYS=30
BACKUP_NOTIFICATION_EMAIL=<EMAIL>
BACKUP_TEMP_DIR=/tmp/backups

# Backup Schedule (for reference)
BACKUP_SCHEDULE_HOUR=2
BACKUP_SCHEDULE_MINUTE=0

# MySQL Tools (if not in PATH)
MYSQLDUMP_PATH=mysqldump
MYSQL_PATH=mysql
```

### Configuration Validation

Test your configuration:

```bash
python manage.py backup --test-config
```

This will verify:
- Environment variables are set
- Database connectivity
- MinIO configuration
- Google Drive credentials
- File permissions

## 🔐 Google Drive Setup

### Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google Drive API

### Step 2: Create Service Account

1. Go to **IAM & Admin** → **Service Accounts**
2. Click **Create Service Account**
3. Fill in details:
   - **Name**: CRM-Backup-Service
   - **Description**: Service account for automated backups
4. Click **Create and Continue**
5. Skip role assignment (we'll set Drive permissions directly)
6. Click **Done**

### Step 3: Generate Credentials

1. Click on the created service account
2. Go to **Keys** tab
3. Click **Add Key** → **Create New Key**
4. Choose **JSON** format
5. Download and save the file securely

### Step 4: Create Google Drive Folder

1. Open Google Drive
2. Create a new folder for backups (e.g., "CRM-Backups")
3. Right-click the folder → **Share**
4. Add the service account email with **Editor** permissions
5. Copy the folder ID from the URL

### Step 5: Configure Environment

```bash
# Update your .env file
GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE=/secure/path/to/service-account.json
GOOGLE_DRIVE_BACKUP_FOLDER_ID=1a2b3c4d5e6f7g8h9i0j
```

## 💻 Usage

### Django Management Commands

The backup system integrates with Django's management commands:

```bash
# Run full backup
python manage.py backup

# Test configuration
python manage.py backup --test-config

# Show backup statistics
python manage.py backup --stats

# Dry run (show what would be backed up)
python manage.py backup --dry-run

# Skip specific components
python manage.py backup --skip-minio
python manage.py backup --skip-database
python manage.py backup --skip-code

# Custom retention period
python manage.py backup --retention-days 14

# Cleanup only
python manage.py backup --cleanup-only
```

### Direct Script Usage

You can also run the scripts directly:

```bash
# Full backup
cd /path/to/crm2.0
python scripts/backup.py

# Cleanup old backups
python scripts/cleanup_old_backups.py

# Cleanup with custom retention
python scripts/cleanup_old_backups.py --retention-days 14

# Dry run cleanup
python scripts/cleanup_old_backups.py --dry-run

# Show backup statistics
python scripts/cleanup_old_backups.py --stats-only
```

## ⏰ Scheduling

### Automatic Setup (Recommended)

Use the provided setup script:

```bash
# Setup backup at 2:00 AM daily
./scripts/setup_cron.sh

# Setup for specific user
./scripts/setup_cron.sh --user www-data

# Setup at custom time
./scripts/setup_cron.sh --time "03:30"

# Remove existing cron job
./scripts/setup_cron.sh --remove

# Show current backup jobs
./scripts/setup_cron.sh --show
```

### Manual Cron Setup

If you prefer manual setup, add to crontab:

```bash
# Edit crontab
crontab -e

# Add backup job (runs at 2:00 AM daily)
0 2 * * * cd /path/to/crm2.0 && /path/to/python scripts/backup.py >> /var/log/backup.log 2>&1
```

### Systemd Timer (Alternative)

For systems using systemd, you can create a timer:

```ini
# /etc/systemd/system/crm-backup.service
[Unit]
Description=CRM 2.0 Backup Service
After=network.target

[Service]
Type=oneshot
User=www-data
WorkingDirectory=/path/to/crm2.0
ExecStart=/path/to/python /path/to/crm2.0/scripts/backup.py
StandardOutput=append:/var/log/backup.log
StandardError=append:/var/log/backup.log

# /etc/systemd/system/crm-backup.timer
[Unit]
Description=CRM 2.0 Backup Timer
Requires=crm-backup.service

[Timer]
OnCalendar=daily
Persistent=true

[Install]
WantedBy=timers.target
```

Enable the timer:

```bash
sudo systemctl enable crm-backup.timer
sudo systemctl start crm-backup.timer
```

## 📊 Monitoring & Troubleshooting

### Log Files

Monitor backup operations through log files:

```bash
# Main backup log
tail -f /var/log/backup.log

# Cleanup log
tail -f /var/log/backup_cleanup.log

# Cron log
tail -f /var/log/cron_backup.log
```

### Email Notifications

The system sends email notifications on:
- Backup completion (success)
- Backup failure (with error details)
- Cleanup completion
- Configuration errors

### Common Issues

#### 1. Database Connection Errors

**Symptoms**: `mysqldump: Access denied` or connection errors

**Solutions**:
```bash
# Test database connection
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME

# Check environment variables
python manage.py backup --test-config

# Verify MySQL tools are installed
which mysqldump
```

#### 2. Google Drive Authentication Errors

**Symptoms**: `google.auth.exceptions.DefaultCredentialsError`

**Solutions**:
```bash
# Check service account file exists
ls -la $GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE

# Validate JSON format
python -m json.tool $GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE

# Test Google Drive access
python scripts/cleanup_old_backups.py --stats-only
```

#### 3. MinIO Connection Issues

**Symptoms**: `MinIO client setup error` or S3 connection timeouts

**Solutions**:
```bash
# Test MinIO connectivity manually
pip install minio
python -c "
from minio import Minio
client = Minio('endpoint', access_key='key', secret_key='secret')
print(list(client.list_buckets()))
"

# Check MinIO environment variables
echo $MINIO_ENDPOINT
echo $MINIO_BUCKET_NAME
```

#### 4. Storage Space Issues

**Symptoms**: `No space left on device`

**Solutions**:
```bash
# Check available space
df -h

# Clean up old temporary files
find /tmp -name "crm_backup_*" -type d -mtime +1 -exec rm -rf {} \;

# Increase cleanup frequency
python scripts/cleanup_old_backups.py --retention-days 7
```

### Performance Monitoring

Track backup performance:

```bash
# Check backup file sizes
python manage.py backup --stats

# Monitor backup duration
grep "Duration" /var/log/backup.log | tail -10

# Check Google Drive quota
# Visit: https://one.google.com/storage
```

## 🔄 Restore Procedures

### Database Restore

```bash
# 1. Download backup from Google Drive
# 2. Extract the backup archive
unzip backup_20240311_020000.zip
cd backup_20240311_020000/db

# 3. Decompress database dump
gunzip db_backup.sql.gz

# 4. Restore to MySQL
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME < db_backup.sql
```

### Code Restore

```bash
# 1. Extract code backup
cd backup_20240311_020000/code
unzip code_backup.zip

# 2. Copy to desired location
cp -r extracted_code/* /path/to/new/location/

# 3. Install dependencies
pip install -r requirements.txt

# 4. Configure environment
cp .env.example .env
# Edit .env with appropriate values
```

### MinIO Files Restore

```bash
# 1. Extract MinIO backup
cd backup_20240311_020000/files
tar -xzf minio_backup.tar.gz

# 2. Upload to MinIO bucket
mc alias set myminio http://minio-endpoint ACCESS_KEY SECRET_KEY
mc cp --recursive crm/ myminio/crm/
```

### Complete Disaster Recovery

For complete system restore:

```bash
#!/bin/bash
# disaster_recovery.sh

BACKUP_FILE="backup_20240311_020000.zip"
RESTORE_DIR="/tmp/restore"

# 1. Prepare restoration environment
mkdir -p $RESTORE_DIR
cd $RESTORE_DIR
unzip $BACKUP_FILE

# 2. Restore database
cd db
gunzip db_backup.sql.gz
mysql -h $DB_HOST -P $DB_PORT -u $DB_USER -p$DB_PASSWORD $DB_NAME < db_backup.sql

# 3. Restore code
cd ../code
unzip code_backup.zip -d /path/to/application/

# 4. Restore files
cd ../files
tar -xzf minio_backup.tar.gz
# Upload to MinIO...

echo "Disaster recovery completed"
```

## 🔧 Advanced Configuration

### Custom Backup Components

You can extend the backup system by modifying `scripts/backup.py`:

```python
# Add custom backup component
class CustomBackup:
    def __init__(self, logger):
        self.logger = logger
    
    def create_backup(self, output_path):
        # Your custom backup logic here
        pass

# Add to BackupOrchestrator
def run_backup(self):
    # ... existing code ...
    custom_backup = CustomBackup(self.logger)
    custom_file = custom_backup.create_backup(backup_root / 'custom')
    # ... existing code ...
```

### Environment-Specific Configuration

Create environment-specific configuration files:

```bash
# Production settings
cp .env.dev .env.prod

# Update production values
BACKUP_RETENTION_DAYS=90
GOOGLE_DRIVE_BACKUP_FOLDER_ID=prod_folder_id
BACKUP_NOTIFICATION_EMAIL=<EMAIL>
```

### Backup Compression Levels

Modify compression settings in the backup scripts:

```python
# In backup.py, modify compression levels
with zipfile.ZipFile(archive_file, 'w', zipfile.ZIP_DEFLATED, compresslevel=9) as zipf:
    # Maximum compression for code backup

with tarfile.open(archive_file, 'w:gz', compresslevel=6) as tar:
    # Balanced compression for MinIO files
```

### Custom Retention Policies

Implement custom retention logic:

```python
# In cleanup_old_backups.py
def custom_retention_policy(files):
    """
    Custom retention: Keep daily backups for 30 days,
    weekly for 6 months, monthly for 2 years
    """
    # Your custom logic here
    pass
```

## 🔒 Security Considerations

### Credential Security

1. **Service Account Keys**:
   - Store JSON keys outside web root
   - Set restrictive file permissions (600)
   - Use dedicated service accounts
   - Rotate keys regularly

2. **Database Credentials**:
   - Use dedicated backup user with minimal permissions
   - Store credentials in environment variables
   - Never commit credentials to version control

3. **Transport Security**:
   - All transfers use HTTPS/TLS
   - Google Drive API uses OAuth 2.0
   - MinIO connections support SSL/TLS

### Access Control

```bash
# Set restrictive permissions on backup scripts
chmod 750 scripts/backup.py
chmod 750 scripts/cleanup_old_backups.py

# Secure service account file
chmod 600 /path/to/service-account.json
chown backupuser:backupgroup /path/to/service-account.json

# Secure log files
chmod 640 /var/log/backup.log
```

### Database Security

Create a dedicated MySQL user for backups:

```sql
-- Connect as root
CREATE USER 'backup_user'@'%' IDENTIFIED BY 'secure_password';

-- Grant minimal required permissions
GRANT SELECT, LOCK TABLES ON crm_database.* TO 'backup_user'@'%';
GRANT RELOAD ON *.* TO 'backup_user'@'%';

FLUSH PRIVILEGES;
```

### Network Security

- Use VPC/private networks for database connections
- Restrict MinIO access to authorized IPs
- Monitor backup traffic for anomalies
- Use firewall rules to limit backup service access

## ❓ FAQ

### General Questions

**Q: How long does a backup take?**
A: Typical backup times:
- Small database (<1GB): 2-5 minutes
- Medium database (1-10GB): 5-15 minutes
- Large database (>10GB): 15-60 minutes
- Plus time for code and MinIO files (usually <5 minutes combined)

**Q: How much storage space do backups consume?**
A: Storage requirements depend on your data:
- Database: 30-80% of original size (due to compression)
- Code: 10-50% of original size (excluding node_modules, .git)
- MinIO files: 80-95% of original size (depends on file types)

**Q: Can I run backups during business hours?**
A: Yes, the backup uses `--single-transaction` for MySQL dumps, ensuring consistency without locking tables.

### Configuration Questions

**Q: Can I backup to multiple cloud providers?**
A: Currently supports Google Drive only. You can modify the backup script to add other providers like AWS S3, Azure Blob Storage.

**Q: How do I change the backup schedule?**
A: Use the setup script: `./scripts/setup_cron.sh --time "04:30"` or manually edit crontab.

**Q: Can I exclude certain files from the MinIO backup?**
A: Yes, modify the `MinIOBackup.create_archive()` method to add exclusion patterns.

### Troubleshooting Questions

**Q: Backup failed with "Permission denied" error**
A: Check:
1. Script permissions: `chmod +x scripts/*.py`
2. Log directory permissions
3. Database user permissions
4. Service account file permissions

**Q: Google Drive upload is very slow**
A: Consider:
1. Network bandwidth limitations
2. Google Drive API rate limits
3. Large backup file sizes
4. Implementing resumable uploads for large files

**Q: How do I test the backup system?**
A: Use these commands:
```bash
python manage.py backup --test-config
python manage.py backup --dry-run
python manage.py backup --stats
```

### Maintenance Questions

**Q: How do I update Google Drive credentials?**
A: 
1. Generate new service account key in Google Cloud Console
2. Update `GOOGLE_DRIVE_SERVICE_ACCOUNT_FILE` path
3. Test with: `python manage.py backup --test-config`

**Q: How do I migrate to a different Google Drive folder?**
A:
1. Create new folder in Google Drive
2. Share with service account
3. Update `GOOGLE_DRIVE_BACKUP_FOLDER_ID`
4. Optionally move existing backups

**Q: How often should I test restore procedures?**
A: Recommended testing schedule:
- Monthly: Test restore of recent backup
- Quarterly: Full disaster recovery simulation
- Annually: Review and update procedures

---

## 📞 Support

For issues and support:

- **Email**: <EMAIL>
- **Internal Documentation**: Check project wiki
- **Logs**: Always check `/var/log/backup.log` first
- **Testing**: Use `--dry-run` and `--test-config` options

## 🔄 Version History

- **v1.0.0**: Initial release with full backup functionality
- **v1.1.0**: Added Django management commands
- **v1.2.0**: Enhanced error handling and notifications
- **v1.3.0**: Added cleanup utilities and retention management

---

*This backup system is designed for the CRM 2.0 application and can be adapted for other Django projects with minimal modifications.*